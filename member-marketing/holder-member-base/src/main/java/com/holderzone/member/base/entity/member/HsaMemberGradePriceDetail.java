package com.holderzone.member.base.entity.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员等级会员价周期累计明细表
 *
 * <AUTHOR>
 * @since 2022-02-08 16:45:11
 */
@ApiModel("会员等级会员价周期累计明细表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberGradePriceDetail implements Serializable {
    private static final long serialVersionUID = -57323975547772597L;

    /**
     * 会员等级会员价周期累计明细guid
     */
    @ApiModelProperty("会员等级会员价周期累计明细guid")
    private String guid;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 等级guid  或 会员卡guid
     */
    @ApiModelProperty("会员等级GUID")
    private String memberInfoGradeGuid;

    /**
     * 0 等级  1 会员卡
     * @see com.holderzone.member.common.enums.equities.BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 会员权益GUID
     */
    @ApiModelProperty("会员权益GUID")
    private String equitiesGuid;

    /**
     * 会员权益ID
     */
    @ApiModelProperty("会员权益ID")
    private String equitiesId;

    /**
     * 优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountedPrice;

    /**
     * 折扣力度/仅存在等级权益折扣
     */
    @ApiModelProperty("折扣力度")
    private BigDecimal discountDynamics;

    /**
     * 会员消费记录GUID
     */
    @ApiModelProperty("会员消费记录GUID")
    private String consumptionGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 折扣类型
     *
     * @see com.holderzone.member.common.enums.equities.DiscountTypeEnum
     */
    @ApiModelProperty("折扣类型")
    private Integer discountType;

    /**
     * 是否被撤销:0 否，1 是
     */
    @ApiModelProperty("订单编号")
    private Integer isCancel;

    /**
     * 周期时间
     */
    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
