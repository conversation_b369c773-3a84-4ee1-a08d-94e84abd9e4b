package com.holderzone.member.base.service.member.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.member.HsaDataItem;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.member.HsaDataItemMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.member.HsaCustomizeFieldService;
import com.holderzone.member.base.service.member.HsaDataItemService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.CheckDataItemDTO;
import com.holderzone.member.common.dto.member.DataItemDTO;
import com.holderzone.member.common.enums.member.DataItemEnum;
import com.holderzone.member.common.enums.member.DataItemFormatEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.qo.member.DataItemSetQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.member.CustomDataItemWithValues;
import com.holderzone.member.common.vo.member.DataItemFormatVO;
import com.holderzone.member.common.vo.member.DataItemSetVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 会员后台  资料项设置管理 serviceImpl
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaDataItemServiceImpl extends HolderBaseServiceImpl<HsaDataItemMapper, HsaDataItem> implements HsaDataItemService {

    private final HsaDataItemMapper hsaDataItemMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaCustomizeFieldService customizeFieldService;

    private final Executor memberBaseThreadExecutor;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Override
    public List<DataItemSetVO> listInfo(String operSubjectGuid) {
        HsaDataItem hsaDataItem = hsaDataItemMapper.selectOne(new LambdaQueryWrapper<HsaDataItem>()
                .eq(HsaDataItem::getOperSubjectGuid, operSubjectGuid));
        if (Objects.isNull(hsaDataItem)) {
            log.info("当前运营主体未查询到---资料项信息");
            return null;
        }
        return buildDataItemList(hsaDataItem.getDataItem());
    }

    private List<DataItemSetVO> buildDataItemList(String dataItem) {
        List<DataItemSetVO> list = JSON.parseArray(dataItem, DataItemSetVO.class);
        list.forEach(e -> {
            if (ObjectUtil.isNull(e.getType())) {
                e.setType(0);
            }
        });
        return list;
    }

    @Override
    public List<DataItemSetVO> listInfoPartner(String operSubjectGuid, String platform) {
        HsaDataItem hsaDataItem = hsaDataItemMapper.selectOne(new LambdaQueryWrapper<HsaDataItem>()
                .eq(HsaDataItem::getOperSubjectGuid, operSubjectGuid));
        if (Objects.isNull(hsaDataItem)) {
            log.info("当前运营主体未查询到---资料项信息");
            return Collections.emptyList();
        }
        if (StringUtils.isEmpty(hsaDataItem.getPlatform())) {
            //重新初始化数据
            hsaDataItem.setDataItem(DataItemEnum.getInitDataItem(platform));
            hsaDataItem.setPlatform(platform);
            hsaDataItemMapper.updateByGuid(hsaDataItem);
        }
        return buildDataItemList(hsaDataItem.getDataItem());
    }

    @Override
    public Boolean checkUserDataItem(CheckDataItemDTO dataItemDTO) {
        boolean isCheck = Boolean.FALSE;
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .isNotNull(HsaOperationMemberInfo::getDataItemJson)
                .notIn(HsaOperationMemberInfo::getGuid, dataItemDTO.getMemberGuid()));
        if (CollUtil.isEmpty(hsaOperationMemberInfos)) {
            return isCheck;
        }

        Map<String, DataItemDTO> dataItemMap = dataItemDTO.getDataItems().stream()
                .collect(Collectors.toMap(DataItemDTO::getInfoName, Function.identity(), (obj, obj1) -> obj));
        Set<String> aa = dataItemMap.keySet();
        for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfos) {
            List<DataItemSetVO> dataItemDTOS = JSON.parseArray(hsaOperationMemberInfo.getDataItemJson(), DataItemSetVO.class)
                    .stream().filter(in -> aa.contains(in.getInfoName()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(dataItemDTOS)) {
                continue;
            }

            for (DataItemSetVO item : dataItemDTOS) {
                if (dataItemMap.containsKey(item.getInfoName())
                        && (dataItemMap.get(item.getInfoName()).getValue().equals(item.getValue()))) {
                    isCheck = Boolean.TRUE;
                    break;
                }
            }
        }
        return isCheck;
    }

    @Override
    public List<DataItemFormatVO> listDataItemFormat() {
        List<DataItemFormatVO> formatList = new ArrayList<>();
        for (DataItemFormatEnum materialFormatEnum : DataItemFormatEnum.values()) {
            formatList.add(new DataItemFormatVO(materialFormatEnum.getCode(), materialFormatEnum.getDesc()));
        }
        return formatList;
    }

    @Override
    public Boolean updateInfo(List<DataItemSetQO> dataItemSetList) {
        if (CollUtil.isEmpty(dataItemSetList)) {
            return Boolean.FALSE;
        }
        HsaDataItem hsaDataItem = hsaDataItemMapper.selectOne(new LambdaQueryWrapper<HsaDataItem>()
                .eq(HsaDataItem::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (Objects.isNull(hsaDataItem)) {
            return Boolean.FALSE;
        }
        HashSet<String> set = Sets.newHashSet();
        dataItemSetList.forEach(e -> {
            if (ObjectUtil.isNull(e.getType())) {
                e.setType(0);
            }
            set.add(e.getInfoName());
        });
        if (set.size() != dataItemSetList.size()) {
            throw new MallBaseException("设置项名称不能重复");
        }

        deleteOldCustomFiledData(dataItemSetList, hsaDataItem);

        hsaDataItem.setDataItem(JSON.toJSONString(dataItemSetList));
        hsaDataItem.setOperatorName(ThreadLocalCache.getOperatorTelName());
        return hsaDataItemMapper.updateByGuid(hsaDataItem);
    }

    /**
     * 删除自定义字段中已有的值
     *
     * @param dataItemSetList
     * @param hsaDataItem
     */
    private void deleteOldCustomFiledData(List<DataItemSetQO> dataItemSetList, HsaDataItem hsaDataItem) {
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        final List<DataItemSetQO> sourceDataItemList = Optional.ofNullable(hsaDataItem.getDataItem()).map(s -> JacksonUtils.toObjectList(DataItemSetQO.class, s)).orElse(Collections.emptyList());
        final List<String> sourceInfoNameList = sourceDataItemList.stream().filter(s -> Objects.equals(s.getType(), 1)).map(DataItemSetQO::getInfoName).collect(Collectors.toList());
        final List<String> infoNameList = dataItemSetList.stream().filter(d -> Objects.equals(d.getType(), 1)).map(DataItemSetQO::getInfoName).collect(Collectors.toList());
        sourceInfoNameList.removeAll(infoNameList);
        memberBaseThreadExecutor.execute(() -> customizeFieldService.deleteByFiled(sourceInfoNameList, operSubjectGuid));
    }

    @Override
    public Boolean initUpdateInfo() {
        List<HsaDataItem> hsaDataItems = hsaDataItemMapper.selectList(new LambdaQueryWrapper<>());

        for (HsaDataItem hsaDataItem : hsaDataItems) {
            hsaDataItem.setDataItem(DataItemEnum.getInitDataItem(null));
            hsaDataItemMapper.updateByGuid(hsaDataItem);
        }
        return true;
    }

    /**
     * 初始化资料项信息
     *
     * @param operSubjectGuids 运营主体集合
     */
    @Override
    public void initDataItem(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        List<HsaDataItem> hsaDataItems = hsaDataItemMapper.selectList(
                new LambdaQueryWrapper<HsaDataItem>().in(HsaDataItem::getOperSubjectGuid, operSubjectGuids));
        if (CollUtil.isEmpty(hsaDataItems)) {
            saveDateItem(operSubjectGuids);
            return;
        }
        List<String> collect = hsaDataItems.stream()
                .map(HsaDataItem::getOperSubjectGuid).collect(Collectors.toList());
        operSubjectGuids = operSubjectGuids.stream().filter(x -> !collect.contains(x)).collect(Collectors.toList());
        saveDateItem(operSubjectGuids);
    }

    private void saveDateItem(List<String> operSubjectGuids) {
        List<HsaDataItem> hsaDataItems = Lists.newArrayList();
        for (String operSubjectGuid : operSubjectGuids) {
            HsaDataItem hsaDataItem = new HsaDataItem();
            hsaDataItem.setOperSubjectGuid(operSubjectGuid);
            hsaDataItem.setGuid(guidGeneratorUtil.getStringGuid(HsaDataItem.class.getSimpleName()));
            hsaDataItem.setDataItem(DataItemEnum.getInitDataItem(null));
            hsaDataItem.setOperatorName("管理员");
            hsaDataItems.add(hsaDataItem);
        }
        this.saveBatch(hsaDataItems);
    }

    @Override
    public List<CustomDataItemWithValues> listCustomWithValues() {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        // 查询所有资料项设置
        HsaDataItem hsaDataItem = hsaDataItemMapper.selectOne(
                new LambdaQueryWrapper<HsaDataItem>()
                        .eq(HsaDataItem::getOperSubjectGuid, operSubjectGuid)
        );
        if (hsaDataItem == null || hsaDataItem.getDataItem() == null) {
            log.info("未查询当前运营主体资料项信息");
            return Collections.emptyList();
        }

        List<DataItemSetVO> allItems = buildDataItemList(hsaDataItem.getDataItem());
        // 只保留type=1的自定义项，且不为图片
        List<DataItemSetVO> customItems = allItems.stream()
                .filter(item -> item.getType() != null
                        && item.getType() == 1
                        && item.getInfoFormat() != null
                        && !DataItemFormatEnum.IMAGE_OPTION.getDesc().equals(item.getInfoFormat().get(0)))
                .collect(Collectors.toList());

        // 查询所有会员的dataItemJson
        List<HsaOperationMemberInfo> memberInfos = hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                        .isNotNull(HsaOperationMemberInfo::getDataItemJson)
        );

        // infoName -> Set<value>
        Map<String, Set<String>> valueMap = new HashMap<>();
        for (HsaOperationMemberInfo memberInfo : memberInfos) {
            if (memberInfo.getDataItemJson() == null) continue;
            List<DataItemSetVO> memberDataItems;
            try {
                memberDataItems = JSON.parseArray(memberInfo.getDataItemJson(), DataItemSetVO.class);
            } catch (Exception e) {
                log.warn("解析会员资料项失败，guid:{}", memberInfo.getGuid(), e);
                continue;
            }
            for (DataItemSetVO item : memberDataItems) {
                if (item.getType() != null
                        && item.getType() == 1
                        && StringUtils.isNotBlank(item.getInfoName())
                        && StringUtils.isNotBlank(item.getValue())) {
                    valueMap.computeIfAbsent(item.getInfoName(), k -> new HashSet<>()).add(item.getValue());
                }
            }
        }

        // 组装VO
        return buildCustomDataItemWithValues(customItems, valueMap);
    }

    /**
     * 组装自定义资料项
     *
     * @param customItems 自定义资料项
     * @param valueMap    值
     * @return 自定义资料项
     */
    private List<CustomDataItemWithValues> buildCustomDataItemWithValues(List<DataItemSetVO> customItems, Map<String, Set<String>> valueMap) {
        List<CustomDataItemWithValues> result = new ArrayList<>();
        for (DataItemSetVO item : customItems) {
            CustomDataItemWithValues cdi = new CustomDataItemWithValues();
            cdi.setType(item.getType());
            cdi.setInfoName(item.getInfoName());
            cdi.setInfoFormat(item.getInfoFormat());
            cdi.setInfoCheck(item.getInfoCheck());

            String format = item.getInfoFormat().get(0);
            if (DataItemFormatEnum.RADIO_OPTION.getDesc().equals(format)
                    || DataItemFormatEnum.MULTIPLE_OPTIONS.getDesc().equals(format)) {
                // 单选/多选项，取content配置
                cdi.setValues(item.getContent() != null ? Arrays.asList(item.getContent().split(",")) : Collections.emptyList());
            } else if (DataItemFormatEnum.DATE_ITEM_OPTION.getDesc().equals(format)
                    || DataItemFormatEnum.PROVINCES_MUNICIPALITIES.getDesc().equals(format)) {
                // 日期/省市区类型，不聚合历史内容
                cdi.setValues(Collections.emptyList());
            } else {
                // 其它类型，聚合历史内容
                cdi.setValues(new ArrayList<>(valueMap.getOrDefault(item.getInfoName(), Collections.emptySet())));
            }
            result.add(cdi);
        }
        return result;
    }
}
