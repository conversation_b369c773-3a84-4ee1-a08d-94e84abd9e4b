package com.holderzone.member.base.mapper.integral;

import com.holderzone.member.base.entity.integral.HsaIntegralTask;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.integral.IntegralTaskListQO;
import com.holderzone.member.common.vo.integral.IntegralTaskListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @description: 积分任务mapper
 * <AUTHOR>
 */
@Mapper
public interface HsaIntegralTaskMapper extends HolderBaseMapper<HsaIntegralTask> {

    List<HsaIntegralTask> queryUsableTask(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 所有排序位置+1
     *
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    int addOnePosition(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 判断时候存在相同活动的开始任务
     *
     * @param operSubjectGuid 运营主体
     * @param taskAction      活动类型
     * @param applyBusiness   使用业务
     * @return 查询结果
     */
    int findHadExistTaskAction(@Param("operSubjectGuid") String operSubjectGuid,
                               @Param("taskAction") Integer taskAction,
                               @Param("applyBusiness") String applyBusiness);

    /**
     * 查询最大位置
     *
     * @param operSubjectGuid 运营主体
     * @return 查询结果
     */
    int findMaxPosition(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 下移位置
     *
     * @param operSubjectGuid 运营主体
     * @param minPosition     最小位置
     * @param maxPosition     最大位置
     * @return 操作结果
     */
    int moveDownPosition(@Param("operSubjectGuid") String operSubjectGuid,
                         @Param("minPosition") Integer minPosition,
                         @Param("maxPosition") Integer maxPosition);

    /**
     * 上移位置
     *
     * @param operSubjectGuid 运营主体
     * @param minPosition     最小位置
     * @param maxPosition     最大位置
     * @return 操作结果
     */
    int moveUpPosition(@Param("operSubjectGuid") String operSubjectGuid,
                       @Param("minPosition") Integer minPosition,
                       @Param("maxPosition") Integer maxPosition);

    /**
     * 查询积分任务列表
     *
     * @param request 积分任务请求QO
     * @return 积分任务列表返回vo
     */
    List<IntegralTaskListVO> queryIntegralListTask(@Param("request") IntegralTaskListQO request);

    /**
     * 查询门店下应用业务数量
     *
     * @param storeGuids 门店guid
     * @param keywords   关键字查询
     * @return 查询结果
     */
    int findStoreApplyBusinessNumber(@Param("storeGuids") List<String> storeGuids,
                                     @Param("keywords") String keywords);

    /**
     * 查询运营主体下 进行中，未开始的所有活动类型
     *
     * @param operSubjectGuid 运营主体
     * @return 查询结果
     */
    List<Integer> getAllTaskActionType(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 暂停任务或者启用任务
     * @param guid 积分任务guid
     * @param status 积分任务状态
     * @return 操作结果
     */
    int enableOrSuspend(@Param("guid") String guid, @Param("status") Integer status);


    HsaIntegralTask findTaskByTaskAction(@Param("operSubjectGuid") String operSubjectGuid,
                                         @Param("taskAction") Integer taskAction);



}
