package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 会员卡押金策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_deposit_strategy")
public class HsaDepositStrategy extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体Guid
     */
    private String operSubjectGuid;

    /**
     * 押金策略编号
     */
    private String depositStrategyCode;

    /**
     * 押金策略名称
     */
    private String depositStrategyName;

    /**
     * 押金金额
     */
    private BigDecimal depositAmount;

    /**
     * 退卡是否退押金
     */
    private Integer isRefundDeposit;

    /**
     * 可退金额
     */
    private BigDecimal refundAmount;

}
