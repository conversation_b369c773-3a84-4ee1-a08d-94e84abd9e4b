package com.holderzone.member.base.service.pay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.grade.HsaMemberGradePayRecord;
import com.holderzone.member.base.mapper.grade.HsaMemberGradePayRecordMapper;
import com.holderzone.member.base.service.pay.GradeOpenPayService;
import com.holderzone.member.base.service.pay.cache.AggTradeCache;
import com.holderzone.member.base.service.pay.convert.GradeTradeConvert;
import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.enums.mall.order.PayStateEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.SaasAggPayFeign;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @program: member-marketing
 * @description: 等级开通聚合支付
 * @author: rw
 */
@Slf4j
@Service
public class GradeOpenPayServiceImpl implements GradeOpenPayService {
    @Value("${feign.partner}")
    private String callbackHost;

    private final static String BASE_PAY_CALLBACK = "%s/base/member_grade_pay_record/agg_pay_callback";


    private final static String AGG_SUCCESS = "10000";

    private final static String GRADE_PRE_PAY = "GRADE_PRE_PAY:";


    private final static String AGG_PAY_POLLING = "AGG_PAY_POLLING:";

    private final static String PAY_MERCHANT_KEY = "A6041E8B17CA0082EECA481D623137F2";

    private final static String DEVELOPER_ID = "10000";

    private final RedissonClient redissonClient;

    private final AggTradeCache cache;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final SaasAggPayFeign saasAggPayFeign;

    private final HsaMemberGradePayRecordMapper hsaMemberGradePayRecordService;

    public GradeOpenPayServiceImpl(RedissonClient redissonClient, AggTradeCache cache, GuidGeneratorUtil guidGeneratorUtil, SaasAggPayFeign saasAggPayFeign, HsaMemberGradePayRecordMapper hsaMemberGradePayRecordService) {
        this.redissonClient = redissonClient;
        this.cache = cache;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.saasAggPayFeign = saasAggPayFeign;
        this.hsaMemberGradePayRecordService = hsaMemberGradePayRecordService;
    }

    @Override
    public MemberGradePerPayVO aggPrePay(GradePrePayDTO gradePrePayDTO) {
        MemberGradePerPayVO perPayVO = new MemberGradePerPayVO();
        String synKey = gradePrePayDTO.getSynKey();
        String payGuid = guidGeneratorUtil.getStringGuid(HsaMemberGradeInfo.class.getSimpleName());

        //增加重复提交锁
        RLock lock = redissonClient.getLock(GRADE_PRE_PAY + synKey);
        try {
            if (lock.isLocked()) {
                throw new MallBaseException(String.format("订单%s重复发起预下单，稍后再试", synKey));
            }
            lock.lock();
            //判断预下单是否已经成功
            String successPayGuid = cache.getPrePaySuccess(synKey);
            if (successPayGuid != null) {
                log.warn("订单{}预下单已经成功,直接返回", synKey);
                perPayVO.setSynKey(synKey);
                return perPayVO;
            }
            //根据门店获取聚合支付账号
            PaySettingDTO paySettingDTO = new PaySettingDTO();
            paySettingDTO.setAppId(gradePrePayDTO.getAppId());
            paySettingDTO.setStoreId(gradePrePayDTO.getAppId());
            paySettingDTO.setOperSubjectGuid( gradePrePayDTO.getOperSubjectGuid());
            PaySettingBaseRes aggAccountSet = cache.getAggAccountSet(paySettingDTO);

            //保存交易记录
            HsaMemberGradePayRecord hsaMemberGradePayRecord = GradeTradeConvert
                    .convertTransactionRecord(gradePrePayDTO, payGuid);
            hsaMemberGradePayRecordService.insert(hsaMemberGradePayRecord);
            //请求聚合支付预支付接口
            SaasAggPayOdooDTO prePayRequest = GradeTradeConvert
                    .convertPayRequest(gradePrePayDTO, aggAccountSet, payGuid, perPayVO);
            prePayRequest.setSaasCallBackUrl(String.format(BASE_PAY_CALLBACK, callbackHost).intern());
            log.info("预下单请求参数：{}", JacksonUtils.writeValueAsString(prePayRequest));
            AggPayRespDTO preRsp = saasAggPayFeign.pay(prePayRequest);
            log.info("预下单结果返回参数：{}", JacksonUtils.writeValueAsString(preRsp));

            //判断调用预下单是否成功
            String code = preRsp.getCode();
            if (!AGG_SUCCESS.equalsIgnoreCase(code)) {
                hsaMemberGradePayRecord.setState(PayStateEnum.READY.getCode());
                hsaMemberGradePayRecordService.updateById(hsaMemberGradePayRecord);
                //下单失败
                throw new MallBaseException(String.format("等级开通发起聚合支付预下单失败 %s", preRsp.getMsg()));
            }
            //若预下单成功将记录存入redis 防止再次进行预下单
            cache.putPrePaySuccess(synKey, payGuid);
            //存入记录对象 轮训使用
            cache.putPayPolling(gradePrePayDTO.getSynKey(), GradeTradeConvert.convertPolling(gradePrePayDTO, aggAccountSet, payGuid), PayStateEnum.PENDING.getCode());

            hsaMemberGradePayRecord.setState(PayStateEnum.PENDING.getCode());
            hsaMemberGradePayRecordService.updateById(hsaMemberGradePayRecord);
            return perPayVO;
        } catch (MemberBaseException e) {
            throw e;
        } catch (Exception e) {
            //预下单失败
            log.error("升级续费发起聚合支付预下单失败", e);
            //抛出异常
            throw new MallBaseException(String.format("升级续费发起聚合支付预下单失败 %s", e.getMessage()));
        } finally {
            checkUnlock(lock);
        }
    }

    private static void checkUnlock(RLock lock) {
        if (lock.isLocked()) {
            lock.unlock();
        }
    }

    public HsaMemberGradePayRecord getByGuid(String guid) {
        return hsaMemberGradePayRecordService.selectOne(new LambdaQueryWrapper<HsaMemberGradePayRecord>()
                .eq(HsaMemberGradePayRecord::getGuid, guid));
    }

    @Override
    public void aggPayCallback(AggPayCallbackRspDTO aggPayCallbackRspDTO) {
        if (aggPayCallbackRspDTO == null) {
            return;
        }
        AggPayPollingRespDTO aggPayCallbackDTO = aggPayCallbackRspDTO.getAggPayPollingRespDTO();
        String payGuid = cache.getPrePaySuccess(aggPayCallbackDTO.getOrderGUID());
        if (StringUtils.isEmpty(payGuid)) {
            return;
        }
        if (!aggPayCallbackDTO.getPayGUID().equals(payGuid)) {
            log.error("payGuid不一致，忽略回调,订单guid：{}", aggPayCallbackDTO.getOrderGUID());
            return;
        }
        RLock lock = redissonClient.getLock(AGG_PAY_POLLING + aggPayCallbackDTO.getOrderGUID());
        try {
            if (checkCallback(aggPayCallbackDTO, payGuid, lock)){
                return;
            }
            log.info("回调完成");
        } catch (Exception e) {
            log.error("聚合支付回调失败,payGuid：{}", payGuid, e);
        } finally {
            checkUnlock(lock);
        }
    }

    private boolean checkCallback(AggPayPollingRespDTO aggPayCallbackDTO, String payGuid, RLock lock) {
        if (lock.tryLock()) {
            OrderPollingDTO polling = cache.getPayPolling(aggPayCallbackDTO.getOrderGUID());
            if (polling == null) {
                return true;
            }
            boolean finished = ObjectUtil.equal(polling.getState(), PayStateEnum.SUCCESS.getCode()) ||
                    ObjectUtil.equal(polling.getState(), PayStateEnum.FAILURE.getCode());
            if (finished) {
                return true;
            }
            //判断回调参数状态
            String paySt = aggPayCallbackDTO.getPaySt();
            //若回调状态是支付中直接返回
            if (ObjectUtil.equal(paySt, String.valueOf(PayStateEnum.PENDING.getCode()))) {
                return true;
            }
            HsaMemberGradePayRecord record = getByGuid(payGuid);
            if (record == null) {
                log.info("支付记录不存在,payGuid：{}", payGuid);
                return true;
            }
            boolean successPay = ObjectUtil.equal(AGG_SUCCESS, aggPayCallbackDTO.getCode()) && ObjectUtil.equal(paySt, String.valueOf(PayStateEnum.SUCCESS.getCode()));
            //支付成功和失败的处理
            checkSuccessPay(aggPayCallbackDTO, record, successPay);
            //银行交易流水号为空 设置为orderNO
            String bankTransactionId = StringUtils.isEmpty(aggPayCallbackDTO.getBankTransactionId()) ?
                    aggPayCallbackDTO.getOrderNo() : aggPayCallbackDTO.getBankTransactionId();
            record.setBankTransactionId(bankTransactionId);
            record.setOrderHolderNo(aggPayCallbackDTO.getOrderHolderNo());
            cache.deletePrePaySuccess(aggPayCallbackDTO.getOrderGUID());
            hsaMemberGradePayRecordService.updateById(record);
        }
        return false;
    }

    private void checkSuccessPay(AggPayPollingRespDTO aggPayCallbackDTO, HsaMemberGradePayRecord record, boolean successPay) {
        if (successPay) {
            record.setState(PayStateEnum.SUCCESS.getCode());
            //清除预下单缓存
            cache.updatePayPolling(aggPayCallbackDTO.getOrderGUID(), PayStateEnum.SUCCESS.getCode());
        } else {
            record.setState(PayStateEnum.FAILURE.getCode());
        }
    }

    @Override
    public GradePayPollingResultDTO aggPayPolling(String orderGuid) {
        RLock lock = redissonClient.getLock(AGG_PAY_POLLING + orderGuid);
        try {
            GradePayPollingResultDTO result = new GradePayPollingResultDTO(null, PayStateEnum.PENDING.getCode(), null);
            if (lock.tryLock()) {
                OrderPollingDTO polling = cache.getPayPolling(orderGuid);
                if (polling == null) {
                    log.warn("轮训支付结果异常订单guid：{}", orderGuid);
                    return result;
                }
                boolean finished = ObjectUtil.equal(polling.getState(), PayStateEnum.SUCCESS.getCode()) ||
                        ObjectUtil.equal(polling.getState(), PayStateEnum.FAILURE.getCode());
                if (finished) {
                    result.setState(polling.getState());
                    result.setPayGuid(polling.getPayGuid());
                    return result;
                }
                //查询交易
                HsaMemberGradePayRecord record = getByGuid(polling.getPayGuid());
                if (record == null) {
                    result.setState(PayStateEnum.FAILURE.getCode());
                    return result;
                }
                SaasPollingOdooDTO pollingDTO = GradeTradeConvert.convertPollingRequest(polling);
                log.info("轮训请求参数：{}", JacksonUtils.writeValueAsString(pollingDTO));
                AggPayPollingRespDTO pollingRsp = saasAggPayFeign.polling(pollingDTO);
                log.info("轮训返回参数：{}", JacksonUtils.writeValueAsString(pollingRsp));
                //判断聚合支付返回
                int paySt = Integer.parseInt(pollingRsp.getPaySt());
                result.setPrepayInfo(pollingRsp.getPrepayInfo());
                result.setState(paySt);
                result.setPayGuid(polling.getPayGuid());
                cache.putPayPolling(orderGuid, polling, paySt);
                checkPollingPay(record, pollingRsp, paySt);
            }
            return result;
        } finally {
            checkUnlock(lock);
        }
    }

    private void checkPollingPay(HsaMemberGradePayRecord record, AggPayPollingRespDTO pollingRsp, int paySt) {
        if (isFinished(paySt) || ObjectUtil.equal(paySt, PayStateEnum.FAILURE.getCode())) {
            if (ObjectUtil.equal(paySt, PayStateEnum.SUCCESS.getCode())) {
                record.setState(PayStateEnum.SUCCESS.getCode());
            } else {
                record.setState(PayStateEnum.FAILURE.getCode());
            }
            AggPayPollingRespDTO aggPayCallbackDTO = GradeTradeConvert.convertPolling2Callback(pollingRsp);
            //银行交易流水号为空 设置为orderNO
            String bankTransactionId = StringUtils.isEmpty(aggPayCallbackDTO.getBankTransactionId()) ?
                    aggPayCallbackDTO.getOrderNo() : aggPayCallbackDTO.getBankTransactionId();
            record.setBankTransactionId(bankTransactionId);
            record.setOrderHolderNo(aggPayCallbackDTO.getOrderHolderNo());
            hsaMemberGradePayRecordService.updateById(record);
            //清除预下单缓存
            cache.deletePrePaySuccess(aggPayCallbackDTO.getOrderGUID());
        }
    }

    private boolean isFinished(Integer payState) {
        return ObjectUtil.equal(payState, PayStateEnum.SUCCESS.getCode())
                || ObjectUtil.equal(payState, PayStateEnum.REFUNDED.getCode())
                || ObjectUtil.equal(payState, PayStateEnum.CANCEL.getCode());
    }

}
