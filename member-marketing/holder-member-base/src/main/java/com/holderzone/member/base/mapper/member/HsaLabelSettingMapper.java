package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.member.MemberLabelCorrelationPageQO;
import com.holderzone.member.common.qo.member.MemberLabelListQO;
import com.holderzone.member.common.vo.member.MemberLabelListVO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaLabelSettingMapper
 * @Author: pantao
 * @Description: 会员标签设置mapper
 * @Date: 2021/8/24 11:49
 * @Version: 1.0
 */
public interface HsaLabelSettingMapper extends HolderBaseMapper<HsaLabelSetting> {

    /**
     * 分页查询会员标签
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<MemberLabelListVO> listMemberLabel(@Param("request") MemberLabelListQO request);

    /**
     * 查询所有会员标签
     *
     * @param request 会员标签列表请求vo
     * @return 查询结果
     */
    List<MemberLabelListVO> findAllMemberLabel(@Param("request") MemberLabelListQO request);


    List<MemberLabelVO> getNotMemberInfoLabel(@Param("request") MemberLabelCorrelationPageQO memberLabelCorrelationPageQO);

    List<HsaLabelSetting> getHsaLabelSetting(@Param("operSubjectGuid") String operSubjectGuid, @Param("triggerType") int triggerType,
                                             @Param("list") List<String> list);

    void updateMemberSettingNum(@Param("request") List<HsaLabelSetting> hsaLabelSettingList);

}
