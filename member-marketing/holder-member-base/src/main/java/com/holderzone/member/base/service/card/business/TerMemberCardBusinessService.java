package com.holderzone.member.base.service.card.business;

import cn.hutool.core.lang.Pair;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.common.dto.base.OrderPartBackDTO;
import com.holderzone.member.common.dto.terminal.SettlementBalanceDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.vo.card.StoreCardRuleVO;
import com.holderzone.member.common.vo.card.TerBaseLoginMemberCardVO;
import com.holderzone.member.common.vo.card.TerLoginMemberCardVO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TerMemberCardBusinessService {

    /**
     * 登录处理冻结金额以及充值提示
     */
    void dealFreeze(TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                    TerLoginMemberCardQO terLoginMemberCardQO,
                    HsaOperationMemberInfo hsaOperationMemberInfo);

    /**
     * 卡列表处理冻结金额以及充值提示
     */
    void dealFreeze(List<TerLoginMemberCardVO> terLoginMemberCardListVOS,
                    String storeGuid,
                    HsaOperationMemberInfo hsaOperationMemberInfo);

    /**
     * 会员卡列表状态过滤以及排序
     */
    List<TerLoginMemberCardVO> doCheckMemberInfoCardList(List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                                         HsaOperationMemberInfo hsaOperationMemberInfo,
                                                         HsaCardBalanceRule hsaCardBalanceRule,
                                                         TerCheckMemberCardQO terCheckMemberCardQO);

    /**
     * 卡门店范围校验
     */
     boolean checkNormalCard(HsaCardBaseInfo hsaCardBaseInfo,
                                   int state,
                                   HsaMemberInfoCard hsaMemberInfoCard,
                                   String storeGuid,
                                   Map<String, List<HsaStoreCardRule>> storeRuleGroupingMap);


    /**
     * 根据登录方式校验是否输入密码
     */
     Integer getCheckPassword(HsaCardBalanceRule hsaCardBalanceRule,
                                    HsaCardBaseInfo hsaCardBaseInfo,
                                    Integer terminalStatus);

    /**
     * 获取会员卡门店范围
     */
    List<StoreCardRuleVO> getStoreCardRuleVOS(HsaMemberInfoCard hsaMemberInfoCard);

    /**
     * 支付明细处理
     */
    SettlementBalanceDTO payByBalance(RequestConfirmPayVO requestConfirmPay,
                                              HsaMemberInfoCard memberInfoCard,
                                              HsaOperationMemberInfo hsaOperationMemberInfo);

    /**
     * 卡门店范围分组
     */
    Map<String, List<HsaStoreCardRule>> getStoreRuleGroupingMap(List<HsaMemberInfoCard> hsaMemberInfoCardList);

    /**
     * 保存订单充值活动赠送金额记录
     */
     void saveGiftAmountRecord(HsaMemberConsumption hsaMemberConsumption,
                                      HsaMemberInfoCard hsaMemberInfoCard,
                                      HsaOperationMemberInfo hsaOperationMemberInfo,
                                      RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO,
                                      HsaMemberFundingDetail hsaMemberFundingDetail,
                                      HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay);


    /**
     * 开通实体卡的参数校验并返回通过手机号查询出来的会员信息
     *
     * @param producePhysicalCardQO 实体卡请求参数
     * @return 返回会员信息
     */
     Pair<HsaOperationMemberInfo, Long> parameterVerify(ProducePhysicalCardQO producePhysicalCardQO);

    /**
     * 获取会员卡
     */
    HsaMemberInfoCard getHsaMemberInfoCard(TerMemberCardRechargeQO terMemberCardRechargeQO, HeaderUserInfo headerUserInfo);

    /**
     * 校验会员卡
     */
    void checkMemberInfoCard(HsaMemberInfoCard hsaMemberInfoCard, Integer cardType, HsaCardBaseInfo hsaCardBaseInfo, HsaOperationMemberInfo hsaOperationMemberInfo);

    /**
     * 返回差额
     */
    BigDecimal checkBalanceAmount(BigDecimal cardBalancePayAmount, HsaMemberInfoCard memberInfoCard, HsaCardBaseInfo hsaCardBaseInfo);


    void subsidyRecordAmountAdd(HsaMemberInfoCard hsaMemberInfoCard, BigDecimal subsidyAmount);

    OrderPartBackDTO getOrderPartBackAmountDTO(HsaMemberConsumption memberConsumption, HsaMemberFundingDetail memberFundingDetail);
}
