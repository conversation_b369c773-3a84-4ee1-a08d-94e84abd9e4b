package com.holderzone.member.base.service.credit;

import com.holderzone.member.base.entity.credit.HsaClearingStatementOperateRecord;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.vo.credit.CreditAdjustRecordVO;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-14 17:14
 */
public interface HsaClearingStatementOperateRecordService extends IHolderBaseService<HsaClearingStatementOperateRecord> {

    /**
     * 通过结算单编号查询应收调整记录
     * @param clearingStatementNumber 结算单编号
     * @return 操作结果
     */
    List<CreditAdjustRecordVO> getCreditAdjustRecord(String clearingStatementNumber);
}
