package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.member.HsaMemberGradePriceDetail;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import com.holderzone.member.base.dto.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.qo.equities.*;
import com.holderzone.member.common.vo.equities.CalculateMemberPriceCommodityVO;
import com.holderzone.member.common.vo.equities.MemberPriceApplyCommodityVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 等级规则会员价
 * @date 2022/2/8 15:13
 */
public interface HsaMemberGradePriceDetailService extends IHolderBaseService<HsaMemberGradePriceDetail> {

    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    MemberPriceApplyCommodityVO getMemberPriceApplyCommodity(MemberPriceApplyCommodityQO qo);

    /**
     * 计算订单折扣
     *
     * @param qo CalculateMemberPriceCommodityQO
     * @return CalculateMemberPriceCommodityVO
     */
    CalculateMemberPriceCommodityVO calculateMemberPriceCommodity(CalculateMemberPriceCommodityQO qo, CalculateCumulativeDiscountQO cumulativeDiscountQO, HsaBusinessEquities gradeEquities);


    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    MemberPriceApplyCommodityVO getMemberPriceApplyCommodityVO(MemberPriceApplyCommodityQO qo,
                                                               HsaBusinessEquities hsaBusinessEquities);

    /**
     * 获取会员等级折扣商品
     *
     * @param memberInfoGuid 会员GUID
     * @param orderNum       订单编号
     * @return HsaBusinessEquities
     */
    HsaBusinessEquities getHsaGradeEquities(String memberInfoGuid,String orderNum);

    /**
     * 会员折扣累计权益锁定释放
     *
     * @param qo AccumulationReleaseKeyQO
     * @return 折扣类型
     */
    int accumulationDiscountReleaseKey(List<AccumulationReleaseKeyQO> qo);


    /**
     * 释放权益并删除折扣记录
     *
     * @param accumulationReleaseKeyQO
     */
    void accumulationDiscountRelease(AccumulationReleaseKeyQO accumulationReleaseKeyQO);

    /**
     * 折扣释放
     *
     * @param discountDTO 会员、订单
     * @return 是否
     */
    boolean unLockedDiscount(SettlementUnLockedDiscountDTO discountDTO);

    /**
     * 订单生成回调处理
     *
     * @param qo BindingReleaseKeyQO
     */
    void orderGenerateCallbackProcessing(List<OrderGenerateCallbackQO> qo);

    /**
     * 关联累计订单
     *
     * @param memberGradePriceDetail memberGradePriceDetail
     * @return
     */
    boolean saveHsaMemberGradePriceDetail(MemberGradePriceDetailQO memberGradePriceDetail);

    /**
     * 锁定折扣
     *
     * @param orderNum       订单号
     * @param businessType   0等级 1会员卡
     * @param memberInfoGuid 会员guid
     * @param vo             折扣结果
     */
    void lockedDiscount(String orderNum, Integer businessType, String memberInfoGuid, SettlementLockedDiscountReqDTO vo);
}
