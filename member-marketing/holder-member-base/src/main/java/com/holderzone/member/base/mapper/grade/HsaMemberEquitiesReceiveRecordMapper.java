package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.equities.RollReceiveRecordQO;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface HsaMemberEquitiesReceiveRecordMapper extends HolderBaseMapper<HsaMemberEquitiesReceiveRecord> {


    List<MemberGrowthValueRelationVO> selectNewAllGrowthValue(@Param("gradeEquitiesGuid") String gradeEquitiesGuid, @Param("type") Integer type);


    /**
     * 查询会员赠送成长值或积分
     * @param gradeEquitiesGuid 等级权益guid
     * @param memberInfoGuid 会员Guid
     * @param type 0:表示翻倍成长值类型，1：翻倍积分类型
     * @return 操作结果
     */
    MemberGrowthValueRelationVO selectAllGrowthValue(@Param("gradeEquitiesGuid") String gradeEquitiesGuid,
                                                     @Param("memberInfoGuid") String memberInfoGuid,
                                                     @Param("type") Integer type);

    /**
     * 回退翻倍成长值\积分使用次数以及累计的成长值
     * @param request 请求参数
     * @return 操作结果
     */
    int rollReceiveRecord(@Param("request") RollReceiveRecordQO request);
}
