package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>
 * 会员卡余额规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_card_balance_rule")
public class HsaCardBalanceRule extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 是否开启使用校验 0关闭，1开启
     */
    private int useCheck;

    /**
     * 经营终端校验状态 13 微信支付 22 扫码支付  23 刷卡支付 24 输入手机号/卡号支付
     *
     * @see com.holderzone.member.common.enums.member.TerminalCheckStatusEnum
     */
    private String terminalCheckStatus;

    /**
     * 小程序端校验状态 0关闭，1开启
     */
    private Integer appletsCheckState;

    /**
     * 余额扣款顺序 扣款顺序根据1＞2＞3，默认顺序实充余额＞赠送余额＞补贴余额
     */
    private String deductionBalanceOrder;

    /**
     * 是否开启余额不足通知 0关闭，1开启
     */
    private int balanceNotification;

    /**
     * 余额不足N元：当会员卡余额小于等于设置金额时，触发通知；
     */
    private BigDecimal balanceThreshold;

    /**
     * 0=短信通知、1=微信公众号通知、2=小程序通知。可以多选，用逗号隔开
     */
    private String notificationType;

    /**
     * 退卡规则：0 退卡并退款（余额清零） 1 仅退卡（余额保留至电子卡）
     */
    private Integer refundCardRule;
}
