package com.holderzone.member.base.manage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.holderzone.member.base.entity.integral.HsaIntegralConsumeRule;
import com.holderzone.member.base.entity.integral.HsaIntegralConsumeRuleHistory;
import com.holderzone.member.base.entity.integral.HsaIntegralDeductCommodity;
import com.holderzone.member.base.entity.integral.HsaIntegralDeductCommodityHistory;
import com.holderzone.member.base.mapper.integral.HsaIntegralConsumeRuleHistoryMapper;
import com.holderzone.member.base.service.integral.HsaIntegralDeductCommodityHistoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 积分规则历史
 *
 * <AUTHOR>
 * @date 2024/1/4
 * @since 1.8
 */

@Component
public class IntegralConsumeRuleHistoryManage {


    @Resource
    private HsaIntegralConsumeRuleHistoryMapper hsaIntegralConsumeRuleHistoryMapper;

    @Resource
    private HsaIntegralDeductCommodityHistoryService hsaIntegralDeductCommodityHistoryService;

    /**
     * 保存积分规则历史
     *
     * @param hsaIntegralConsumeRule 积分规则
     * @param deductCommodityList    积分规则商品
     */
    public void saveHistory(HsaIntegralConsumeRule hsaIntegralConsumeRule, List<HsaIntegralDeductCommodity> deductCommodityList) {
        saveRule(hsaIntegralConsumeRule);
        saveRuleCommodity(deductCommodityList);

    }

    /**
     * 保存积分规则历史
     *
     * @param hsaIntegralConsumeRule 积分规则
     * @return 历史规则
     */
    public void saveRule(HsaIntegralConsumeRule hsaIntegralConsumeRule) {
        HsaIntegralConsumeRuleHistory hsaIntegralConsumeRuleHistory = new HsaIntegralConsumeRuleHistory();
        BeanUtils.copyProperties(hsaIntegralConsumeRule, hsaIntegralConsumeRuleHistory);

        hsaIntegralConsumeRuleHistoryMapper.insert(hsaIntegralConsumeRuleHistory);


    }

    /**
     * 保存积分规则商品历史
     *
     * @param deductCommodityList 积分规则
     */
    public void saveRuleCommodity(List<HsaIntegralDeductCommodity> deductCommodityList) {
        if (CollUtil.isEmpty(deductCommodityList)) {
            return;
        }

        List<HsaIntegralDeductCommodityHistory> hsaIntegralDeductCommodityHistoryList = new ArrayList<>(deductCommodityList.size());
        for (HsaIntegralDeductCommodity hsaIntegralDeductCommodity : deductCommodityList) {

            HsaIntegralDeductCommodityHistory hsaIntegralDeductCommodityHistory = new HsaIntegralDeductCommodityHistory();
            BeanUtil.copyProperties(hsaIntegralDeductCommodity, hsaIntegralDeductCommodityHistory);
            hsaIntegralDeductCommodityHistoryList.add(hsaIntegralDeductCommodityHistory);
        }
        hsaIntegralDeductCommodityHistoryService.saveBatch(hsaIntegralDeductCommodityHistoryList);


    }
}
