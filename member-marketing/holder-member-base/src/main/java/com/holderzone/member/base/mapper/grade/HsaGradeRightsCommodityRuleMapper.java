package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaGradeRightsCommodityRule;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @author: rw
 * @create: 2022-01-18 18:20
 */
public interface HsaGradeRightsCommodityRuleMapper extends HolderBaseMapper<HsaGradeRightsCommodityRule> {

    int updateByEquitiesInfoGuid(@Param("type") int type, @Param("gradeInfoGuid") String gradeInfoGuid);

    int updateByEquitiesStoreGuid(@Param("type") int type, @Param("gradeInfoGuid") String gradeInfoGuid);

    int deleteByEquitiesInfoGuid(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int deleteByEquitiesStoreGuid(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int editCommodityStatus(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int editStoreStatus(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int deleteByEquitiesCommodityCardGuid(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int deleteByEquitiesStoreCardGuid(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int deleteByEquitiesCardGuid(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);
}
