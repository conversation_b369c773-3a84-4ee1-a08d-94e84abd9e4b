package com.holderzone.member.base.service.sync;

import com.holderzone.member.base.entity.sync.HsaSyncStore;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.base.store.dto.SyncStoreQo;

/**
 * <p>
 * 同步门店 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface IHsaSyncStoreService extends IHolderBaseService<HsaSyncStore> {

    void save(SyncStoreDTO dto);

    PageResult<SyncStoreDTO> page(SyncStoreQo qo);
}
