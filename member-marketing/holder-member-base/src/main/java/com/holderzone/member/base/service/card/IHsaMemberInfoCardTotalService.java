package com.holderzone.member.base.service.card;


import com.holderzone.member.base.entity.card.HsaMemberInfoCardTotal;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

/**
 * <p>
 * 会员卡余额历史 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
public interface IHsaMemberInfoCardTotalService extends IHolderBaseService<HsaMemberInfoCardTotal> {

    @RedissonLock(lockName = "SAVE_CARD_AMOUNT_HISTORY", tryLock = true, leaseTime = 30)
    void saveTodayCardAmount();
}
