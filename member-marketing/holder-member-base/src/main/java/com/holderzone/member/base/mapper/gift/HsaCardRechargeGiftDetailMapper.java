package com.holderzone.member.base.mapper.gift;

import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @author: rw
 * @create: 2023-06-27 14:37
 */
public interface HsaCardRechargeGiftDetailMapper extends HolderBaseMapper<HsaCardRechargeGiftDetail> {

    List<HsaCardRechargeGiftDetail> getFixedRechargeGiftDetail(@Param("request") CardFreezeBalanceAmountQO request);

}
