package com.holderzone.member.base.entity.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员资金来往明细表
 *
 * <AUTHOR>
 * @since 2021-10-19 16:45:11
 */
@ApiModel("会员资金来往明细表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberFundingDetail implements Serializable {
    private static final long serialVersionUID = -57323975547772597L;

    /**
     * 账户资金明细GUID
     */
    @ApiModelProperty("账户资金明细GUID")
    private String guid;
    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 实体卡号或卡号
     */
    @ApiModelProperty("实体卡号或卡号")
    private String cardNum;

    /**
     * 卡名称
     */
    @ApiModelProperty("卡名称")
    private String cardName;

    private String cardGuid;

    @ApiModelProperty("卡类型 0实体卡 1电子卡")
    private Integer cardType;

    /**
     * 会员持卡GUID
     */
    @ApiModelProperty("会员持卡GUID")
    private String memberInfoCardGuid;
    /**
     * 企业GUID
     */
    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;

    /**
     * 门店GUID
     */
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 资金来源类型,0管理员修改，1充值,2反结账,3消费
     *
     * @see com.holderzone.member.common.enums.member.AmountSourceTypeEnum
     */
    @ApiModelProperty("资金来源类型,0后台调整，1充值,2反结账（消费退卡）,3消费，4后台导入，5开卡，6赠送，7补贴发放，8补贴金过期，9退卡，10预存余额")
    private Integer amountSourceType;

    /**
     * 资金来往类型(实充),0增加,1减少
     */
    @ApiModelProperty("卡资金来往类型(实充),0增加,1减少")
    private Integer amountRechargeFundingType;

    /**
     * 资金来往类型(赠送,0增加,1减少
     */
    @ApiModelProperty("卡资金来往类型(赠送,0增加,1减少")
    private Integer amountGiftFundingType;

    /**
     * 资金来往类型(补贴,0增加,1减少
     */
    @ApiModelProperty("卡资金来往类型(补贴),0增加,1减少")
    private Integer amountSubsidyFundingType;

    /**
     * 卡实充金额
     */
    @ApiModelProperty("实充金额")
    private BigDecimal rechargeAmount;

    /**
     * 卡赠送金额
     */
    @ApiModelProperty("赠送金额")
    private BigDecimal giftAmount;

    /**
     * 卡补贴余额
     */
    @ApiModelProperty("补贴余额")
    private BigDecimal subsidyAmount;

    /**
     * 会员消费记录GUID
     */
    @ApiModelProperty("会员消费记录GUID")
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 卡实充剩余余额
     */
    @ApiModelProperty("卡实充剩余余额")
    private BigDecimal cardRechargeResidualBalance;

    /**
     * 卡赠送剩余余额
     */
    @ApiModelProperty("卡赠送剩余余额")
    private BigDecimal cardGiftResidualBalance;

    /**
     * 卡补贴剩余余额
     */
    @ApiModelProperty("卡补贴剩余余额")
    private BigDecimal cardSubsidyResidualBalance;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    /**
     * 是否有效
     */
    @ApiModelProperty("是否有效")
    private Integer isValid;

    /**
     * 来源名称，门店：在门店进行的充值，显示操作门店；
     * 微信公众号：通过微信公众号进行充值或支付消费的；
     * 微信小程序：通过小程序进行充值或支付消费的；
     * 充值赠送：通过充值赠送活动获得的赠送余额，显示活动名称；
     * 营销活动：通过营销活动获得的赠送余额，显示活动名称；
     * 预充值：开通实体卡获取卡中的预充值金额；
     */
    private String sourceName;

    /**
     * 变动来源 0：管理后台 2：一体机 53：小程序
     */
    private Integer changeSource;

    /**
     * 补贴明细记录
     */
    private String subsidyDetailRecordGuid;

    /**
     * 过期时间
     */
    private LocalDateTime outOfDate;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 所属单位
     */
    private String workName;

    /**
     * 所属部门
     */
    private String departmentName;
}