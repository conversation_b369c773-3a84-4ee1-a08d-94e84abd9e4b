package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 订单优惠记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="HsaMemberOrderDiscount对象", description="订单优惠记录")
public class HsaMemberOrderDiscount extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    @ApiModelProperty(value = "会员消费记录GUID")
    private String orderNumber;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "优惠具体优惠项： SettlementDiscountOptionEnum")
    private Integer discountOption;

    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    @ApiModelProperty(value = "优惠名称")
    private String discountName;

    @ApiModelProperty(value = "优惠项主键,eg:优惠券id")
    private String discountId;
    
    @ApiModelProperty(value = "优惠力度")
    private String discountDynamic;

    @ApiModelProperty(value = "数量")
    private Integer discountNum;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal discountFee;


    @ApiModelProperty(value = "折扣状态 0表示正常，1表示反结账折扣")
    private Integer discountState;

    /**
     * 退款是否退优惠：0否 1是
     */
    @ApiModelProperty("退款是否退优惠：0否 1是")
    private Integer couponRollback;

    /**
     * 商品json
     * @see ApplyRecordCommodity
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String commodityJson;

    /**
     * 订单是否支付
     */
    private Integer isPay;
}
