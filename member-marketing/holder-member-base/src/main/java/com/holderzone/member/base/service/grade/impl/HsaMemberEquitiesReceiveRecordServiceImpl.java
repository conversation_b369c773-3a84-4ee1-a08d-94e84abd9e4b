package com.holderzone.member.base.service.grade.impl;


import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.base.mapper.grade.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.base.service.grade.HsaMemberEquitiesReceiveRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import org.springframework.stereotype.Service;


/**
 * 记录会员已经使用的权益信息
 *
 * <AUTHOR>
 */
@Service
public class HsaMemberEquitiesReceiveRecordServiceImpl extends HolderBaseServiceImpl<HsaMemberEquitiesReceiveRecordMapper,
        HsaMemberEquitiesReceiveRecord> implements HsaMemberEquitiesReceiveRecordService {


}
