package com.holderzone.member.base.handler;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.holderzone.member.common.enums.card.SubsidyActivityEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 给内容匹配的单元格配置红色字体
 */
@Slf4j
public class SubsidyContentHandler implements CellWriteHandler {

    /**操作列*/
    private List<Integer> columnIndex;


    public SubsidyContentHandler(List<Integer> columnIndex) {
        this.columnIndex = columnIndex;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // Do nothing because of X and Y.
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // Do nothing because of X and Y.
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        boolean changeCell = CollectionUtils.isNotEmpty(columnIndex) && columnIndex.contains(cell.getColumnIndex());
        if(Boolean.FALSE.equals(aBoolean) && Boolean.TRUE.equals(changeCell)) {
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            // 设置字体颜色
            WriteFont headWriteFont = new WriteFont();
            String cellValue = cell.getStringCellValue();
            if (SubsidyActivityEnum.SUBSIDY_SUCCESS.getDes().equals(cellValue) && cell.getColumnIndex() == 10) {
                // 设置字体颜色
                headWriteFont.setColor(IndexedColors.BLACK.index);
            }else {
                headWriteFont.setColor(IndexedColors.RED.index);
            }
            writeCellStyle.setWriteFont(headWriteFont);
            CellStyle cellStyle = StyleUtil.buildContentCellStyle(workbook, writeCellStyle);
            cell.setCellStyle(cellStyle);
        }
    }
}
