package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.card.ECardCreateCountDTO;
import com.holderzone.member.common.dto.card.ReturnCardRuleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡开卡规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface HsaCardOpenRuleMapper extends HolderBaseMapper<HsaCardOpenRule> {

    void removeOpenPhysicalCardStrategyGuid(@Param("operSubjectGuid") String operSubjectGuid,
                                            @Param("openPhysicalCardStrategyGuid") Long openPhysicalCardStrategyGuid);

    void updatePhysicalCreateCount(@Param("cardGuid") String cardGuid, @Param("createCount")Integer createCount);

    void updateElectronicCreateCount(@Param("countList")List<ECardCreateCountDTO> countList);

    /**
     * 批量更新会员卡-发卡状态
     * @param cardGuids  会员卡guids
     * @param status 会员卡状态
     * @return 影响的行数
     */
    int batchUpdateCardSendStatus(@Param("cardGuids") List<String> cardGuids, @Param("status") Integer status);

    /**
     *
     * @param cardGuids 会员卡guid
     * @param operSubjectGuid 运营主体guid
     * @return 会员卡可以押金
     */
    List<ReturnCardRuleDTO> queryReturnAmount(@Param("cardGuids") List<String> cardGuids, @Param("operSubjectGuid") String operSubjectGuid);
}
