package com.holderzone.member.base.service.coupon;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.base.dto.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.qo.coupon.CouponDtlQO;
import com.holderzone.member.common.qo.member.MemberSendCouponQO;
import com.holderzone.member.common.vo.coupon.CouponQrCodeVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员优惠券 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface IHsaMemberCouponLinkService extends IHolderBaseService<HsaMemberCouponLink> {

    /**
     * 获取二维码
     *
     * @param memberCouponGuid 券guid
     * @return 券码
     */
    CouponQrCodeVO getQrCode(String memberCouponGuid);

    /**
     * 发送优惠券通知
     *
     * @param memberCouponLinks 优惠券
     */
    void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponLinks);

    /**
     * 发送优惠券过期通知
     */
    void sendMemberCouponExpireNotice();

    /**
     * 更新券状态为使用
     *
     * @param couponCode 券类型code
     * @param codes      券code
     * @return
     */
    boolean updateStateByApply(String memberInfoGuid, String couponCode, List<String> codes);

    /**
     * 查询券guid
     *
     * @param operSubjectGuid 主体
     * @param couponMap       券类型
     * @return
     */
    List<String> list(String operSubjectGuid, Map<String, List<String>> couponMap);

    /**
     * 查券guid
     *
     * @param couponMap couponCode,List<code>
     * @return guid
     */
    List<String> listGuid(String operSubjectGuid, Map<String, List<String>> couponMap);

    /**
     * 查询优惠券
     *
     * @param operSubjectGuid 主体
     * @param couponMap       couponCode,List<code>
     * @return 券
     */
    List<HsaMemberCouponLink> listAllByCode(String operSubjectGuid, Map<String, List<String>> couponMap);

    /**
     * 优惠券锁定操作
     *
     * @param lockedDiscount 订单信息
     */
    void locked(SettlementOrderLockDTO lockedDiscount);

    /**
     * 优惠券使用
     *
     * @param qo 优惠券使用信息
     * @return
     */
    void markUse(CouponMarkUseQO qo);

    /**
     * 优惠券作废
     *
     * @param qo 作废参数
     *
     */
    void invalid(CouponMarkInvalidQO qo);

    /**
     * 优惠券释放
     *
     * @param discountDTO 订单信息
     */
    void unlocked(SettlementUnLockedDiscountDTO discountDTO);

    /**
     * 支付完成后处理
     *
     * @param discountDTO 订单信息
     */
    void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO);

    /**
     * 根据码库记录查询券
     */
    Map<String, Integer> listByCodeRecord(CouponDtlQO couponDtlQO);

    /**
     * 手动发券
     */
    void sendMemberCoupon(MemberSendCouponQO memberSendCouponQO);

}
