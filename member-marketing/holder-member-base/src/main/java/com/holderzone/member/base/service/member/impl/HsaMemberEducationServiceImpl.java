package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.member.HsaMemberEducation;
import com.holderzone.member.base.mapper.member.HsaMemberEducationMapper;
import com.holderzone.member.base.service.member.HsaMemberEducationService;
import com.holderzone.member.base.service.member.HsaMemberPersonalService;
import com.holderzone.member.base.transform.member.MemberBusinessTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.business.EducationalDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaMemberEducationServiceImpl extends HolderBaseServiceImpl<HsaMemberEducationMapper, HsaMemberEducation> implements HsaMemberEducationService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public void edit(EducationalDTO educationalDTO) {
        educationalDTO.validatedEdit();
        this.updateByGuid(MemberBusinessTransform.INSTANCE.fromEducationDO(educationalDTO));
    }

    @Override
    public void batchHandleEducational(List<EducationalDTO> educationalDTOList, String memberGuid) {
        if (CollectionUtils.isEmpty(educationalDTOList)) {
            this.deleteByMemberGuid(memberGuid);
            return;
        }
        educationalDTOList.forEach(EducationalDTO::validated);
        this.deleteByMemberGuid(memberGuid);
        List<HsaMemberEducation> educationalList = MemberBusinessTransform.INSTANCE.fromEducationDOList(educationalDTOList);
        educationalList.forEach(e -> {
            e.setMemberGuid(memberGuid);
            e.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            e.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberPersonalService.class.getSimpleName()));
        });
        this.saveBatch(educationalList);
    }

    @Override
    public void deleteByMemberGuid(String memberGuid) {
        this.remove(new LambdaQueryWrapper<HsaMemberEducation>().eq(HsaMemberEducation::getMemberGuid, memberGuid));
    }

    @Override
    public void deleteByGuid(String guid) {
        this.removeByGuid(guid);
    }

    @Override
    public void add(EducationalDTO educationalDTO) {
        educationalDTO.validatedAdd();
        HsaMemberEducation education = MemberBusinessTransform.INSTANCE.fromEducationDO(educationalDTO);
        education.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberEducationService.class.getSimpleName()));
        education.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        this.save(education);
    }

    @Override
    public List<EducationalDTO> listByMemberGuid(String memberInfoGuid, String operSubjectGuid) {
        return MemberBusinessTransform.INSTANCE.toEducationList(this.list(new LambdaQueryWrapper<HsaMemberEducation>()
                .eq(HsaMemberEducation::getMemberGuid, memberInfoGuid)
                .eq(ObjectUtil.isNotNull(operSubjectGuid),HsaMemberEducation::getOperSubjectGuid, operSubjectGuid)));
    }

    @Override
    public Set<String> listMemberGuidBySchoolList(String excludeMemberGuid, String operSubjectGuid, Set<String> selfSchoolList,List<String> memberGuidSet) {
        LambdaQueryWrapper<HsaMemberEducation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(HsaMemberEducation::getMemberGuid)
                .eq(ObjectUtil.isNotNull(operSubjectGuid),HsaMemberEducation::getOperSubjectGuid, operSubjectGuid)
                .ne(ObjectUtil.isNotNull(excludeMemberGuid),HsaMemberEducation::getMemberGuid, excludeMemberGuid)
                .in(HsaMemberEducation::getSchool,selfSchoolList);
        if(CollectionUtil.isNotEmpty(memberGuidSet)){
            queryWrapper.in(HsaMemberEducation::getMemberGuid,memberGuidSet);
        }
        List<HsaMemberEducation> list = this.list(queryWrapper);
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptySet();
        }
        return list.stream().map(HsaMemberEducation::getMemberGuid).collect(Collectors.toSet());
    }
}
