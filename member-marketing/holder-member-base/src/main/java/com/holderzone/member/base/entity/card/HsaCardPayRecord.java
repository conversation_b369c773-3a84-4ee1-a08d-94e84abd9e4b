package com.holderzone.member.base.entity.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡支付记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCardPayRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;


    private String memberInfoGuid;

    private String memberInfoCardGuid;

    /**
     * cardGuid
     */
    private String cardGuid;

    private String orderGuid;

    private String payGuid;

    @ApiModelProperty(value = "支付状态 0 待支付 1 已支付", required = true)
    private Integer payCondition;

    @ApiModelProperty(value = "10 充值开卡、20 消费开卡 30 微信充值 40 微信支付", required = true)
    private Integer businessType;

    /**
     * 开卡方式(0 免费开通 1付费开通 2充值开通 3注册开通 4满足条件 5指定开通 6实体卡同步开通)
     */
    private Integer electronicOpenWay;

    /**
     * 来源,0后台添加,2一体机注册 53 小程序
     */
    private Integer source;

    /**
     * 自主开通类型（0免费；1直接付款；2充值）
     */
    private Integer selfType;

    @ApiModelProperty(value = "支付方式，0 现金支付 1聚合支付，2银行卡支付，3卡余额支付 ，4人脸支付 ，13微信支付 ，5自定义", required = true)
    private Integer payWay;

    /**
     * 开通付款金额
     */
    private BigDecimal paymentMoney;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
