package com.holderzone.member.base.service.growth;

import com.holderzone.member.base.entity.growth.HsaGrowthValueRule;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.growth.GrowthValueRuleQO;
import com.holderzone.member.common.vo.growth.GrowthValueRuleVO;

public interface HsaGrowthValueRuleService extends IHolderBaseService<HsaGrowthValueRule> {

    /**
     * 新增或者修改成长值规则名称
     * @param request 修改参数
     * @return guid
     */
    String saveOrUpdate(GrowthValueRuleQO request);

    /**
     * 获取当前运营主体下的成长值规则名称
     * @return 成长值规则信息
     */
    GrowthValueRuleVO getGrowthRuleName(String operSubjectGuid);
}
