package com.holderzone.member.base.entity.card;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 废弃了
 * <p>
 * 会员导入
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberCardExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    private String guid;

    private String cardName;

    private String cardNum;

    private String code;

    private String name;

    private String phone;

    private BigDecimal balance;

    private Integer cardStatus;

    private String expiryDate;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
