package com.holderzone.member.base.entity.activity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 补贴活动记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaSubsidyActivityRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动编号
     */
    private String activityNum;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * cardGuid
     */
    private String cardGuid;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyMoney;

    /**
     * 补贴活动guid
     */
    private String subsidyActivityGuid;

    /**
     * 补贴频率 0 单次 1 每天 2 每周 3 每月 4 每年
     */
    private Integer subsidyFrequencyType;

    /**
     * 补贴发放状态 0 发放中 1 发放结束
     */
    private Integer subsidySendState;

    /**
     * 补贴机制时间(整点触发时间或补发时间或立即发放)
     */
    private LocalDateTime subsidyTime;

    /**
     * 补贴金有效期
     */
    private LocalDateTime validityTime;

    /**
     * 补贴数量
     */
    private Integer subsidyNum;

    /**
     * 补贴失败数量
     */
    private Integer subsidyErrorNum;

    /**
     * 补贴金有效期类型 0 永久有效 1 固定清零 2 领取后过期
     */
    private Integer subsidyMoneyPeriodType;

    /**
     * 回收频率 1 每天 2 每周 3 每月 4 每年 5 指定日期
     */
    private Integer subsidyRecycleFrequencyType;

    /**
     * 指定日期
     */
    private LocalDateTime subsidyRecycleAppointTime;

    /**
     * 领取后过期（数字标识）
     */
    private Integer subsidyRecycle;

    /**
     * 单位 1日、2周、3月、4年
     */
    private Integer validityUnit;

    /**
     * 回收机制  具体日期时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String subsidyRecycleJson;

    /**
     * 补贴金是否可退 0 不可退  1 可退
     */
    private Integer isRetreat;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 补贴活动是否删除
     * 1：删除   0：未删除
     */
    private Integer isDestroy;
}
