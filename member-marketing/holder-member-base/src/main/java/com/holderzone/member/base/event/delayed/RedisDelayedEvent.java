package com.holderzone.member.base.event.delayed;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Rw
 * Redis延时队列
 */

@Slf4j
public class RedisDelayedEvent<T> {

    private static final Integer COUNT = 10;

    private static final Integer MIN = 0;

    private static final Integer OFFSET = 0;

    private static final Integer INTERVAL = 10;

    private volatile boolean started = false;

    private final String queueKey;

    private final Consumer<T> handler;

    private final Class<T> classOfT;

    private final StringRedisTemplate stringRedisTemplate;


    public RedisDelayedEvent(String queueKey, Class<T> classOfT, Consumer<T> handler, StringRedisTemplate stringRedisTemplate) {
        this.queueKey = queueKey;
        this.handler = handler;
        this.classOfT = classOfT;
        this.stringRedisTemplate = stringRedisTemplate;
    }


    /**
     * 插入队列
     *
     * @param value    数据
     * @param deadLine 截止时间戳，单位是毫秒
     * @return 是否执行成功
     */
    public boolean putForDeadLine(T value, long deadLine) {
        if (value == null) {
            return false;
        }
        long current = System.currentTimeMillis();
        if (deadLine < current) {
            throw new IllegalArgumentException(String.format("deadline: %d 小于当前时间: %d !", deadLine, current));
        }
        if (stringRedisTemplate == null) {
            throw new IllegalStateException("请设置stringRedisTemplate!");
        }
        String json = JSON.toJSONString(value);
        Boolean flag = stringRedisTemplate.opsForZSet().add(queueKey, json, deadLine);
        return Boolean.TRUE.equals(flag);
    }

    /**
     * 队列中放入数据,指定时间后执行
     *
     * @param value       数据
     * @param delayedTime 时间
     * @return 是否执行成功
     */
    public boolean putForDelayedTime(T value, long delayedTime) {
        return putForDeadLine(value, delayedTime);
    }

    /**
     * 清除队列中的数据
     */
    public void clear() {
        stringRedisTemplate.opsForZSet().removeRangeByScore(queueKey, Double.MIN_VALUE, Double.MAX_VALUE);
    }

    /**
     * 验证队列是否存在 true 存在  false 不存在
     */
    public Boolean verify() {
        Long value = stringRedisTemplate.opsForZSet().zCard(queueKey);
        return value != null && value > 0;
    }

    public void sendRefresh() {
        if (!started) {
            new Thread(this::startDelayRefreshMachine).start();
            this.started = true;
            log.info("延时刷新启动");
        }
    }


    /**
     * 执行刷新
     */
    private void startDelayRefreshMachine() {
        while (started) {
            try {
                long current = System.currentTimeMillis();
                log.info(current + " delay refresh run:{}", queueKey);
                Set<ZSetOperations.TypedTuple<String>> typedTuples = stringRedisTemplate.opsForZSet()
                        .rangeByScoreWithScores(queueKey, MIN, current, OFFSET, COUNT);
                taskHandler(typedTuples);
            } catch (Exception e) {
                log.error("delayQueue exception Error: " + e.getMessage());
            } finally {
                try {
                    TimeUnit.SECONDS.sleep(INTERVAL);
                } catch (InterruptedException e) {
                    log.error("delayQueue sleep Error", e);
                    Thread.currentThread().interrupt();
                    started = false;
                }
            }
        }
    }

    private void taskHandler(Set<ZSetOperations.TypedTuple<String>> typedTuples) {
        if (CollectionUtils.isNotEmpty(typedTuples)) {
            for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
                Long removedCount = stringRedisTemplate.opsForZSet().remove(queueKey, typedTuple.getValue());
                if (removedCount != null && removedCount > 0) {
                    handler.accept(JSON.parseObject(typedTuple.getValue(), classOfT));
                }
            }
        } else {
            Long count = stringRedisTemplate.opsForZSet().zCard(queueKey);
            log.info("消费类型：{}，待消费数量:{}", queueKey, count);
            if (Objects.isNull(count) || count.intValue() == 0) {
                log.info("延时刷新关闭，消费类型:{}", queueKey);
                started = false;
            }
        }
    }

}

