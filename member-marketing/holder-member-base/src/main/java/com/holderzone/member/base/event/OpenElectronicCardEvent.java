package com.holderzone.member.base.event;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCardRightsEvent
 * @program holder-member-xxl-job
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class OpenElectronicCardEvent {

    @Resource
    private BinderChannel binderChannel;

    /**
     * 触发事件
     *
     * @param request 请求参数
     */
    public void send(SendOpenElectronicCardEvent request) {
        MessageBuilder builder = MessageBuilder.withPayload(request);
        boolean result = binderChannel.outputSendOpenElectronicCard().send(builder.build());
        log.info("[SendSubsidyRightsEvent] 事件发送结果{}. 参数={}", result, JSONObject.toJSONString(request));
    }
}
