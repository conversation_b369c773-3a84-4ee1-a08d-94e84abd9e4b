package com.holderzone.member.base.event;


import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @description 等级变更
 * @date 2021/11/23 11:48
 */
@EnableBinding(BinderChannel.class)
public class MemberGradeChangeEvent {

    @Resource
    private BinderChannel binderChannel;

    /**
     * 触发事件
     *
     * @param request request model
     */
    public boolean send(SendMemberGradeChangeEvent request) {
        Message<SendMemberGradeChangeEvent> build = MessageBuilder.withPayload(request).build();
        return binderChannel.outputMemberGradeChange().send(build);
    }
}
