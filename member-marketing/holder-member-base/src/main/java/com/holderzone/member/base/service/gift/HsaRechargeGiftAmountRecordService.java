package com.holderzone.member.base.service.gift;

import com.holderzone.member.base.entity.recharge.HsaRechargeGiftAmountRecord;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.gift.QueryGiftAmountRecordPage;
import com.holderzone.member.common.vo.excel.GiftAmountRecordVO;
import com.holderzone.member.common.vo.gift.CalculateRechargeGiftRecordVO;

import java.util.List;

/**
 * HsaRechargeGiftAmountRecordService
 */
public interface HsaRechargeGiftAmountRecordService extends IHolderBaseService<HsaRechargeGiftAmountRecord> {


    PageResult queryRechargeGiftAmountRecordPage(QueryGiftAmountRecordPage query);


    GiftAmountRecordVO exportRechargeGiftAmountRecord(QueryGiftAmountRecordPage query);


    CalculateRechargeGiftRecordVO calculateRechargeAmountRecord(QueryGiftAmountRecordPage query);

    void updateRechargeStatus(String operSubjectGuid, String memberFundingDetailGuid,
                              Integer rechargeStatus);

    List<String> getSuccessOrder(List<String> activityGuidList);
}