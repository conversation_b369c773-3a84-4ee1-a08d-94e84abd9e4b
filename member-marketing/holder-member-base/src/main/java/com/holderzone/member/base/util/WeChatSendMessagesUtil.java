package com.holderzone.member.base.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.entity.member.HsaMemberInfoWeChat;
import com.holderzone.member.base.entity.member.HsaMemberInfoWxMp;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.member.HsaMemberInfoWeChatMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.member.HsaMemberInfoWxMpService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.constant.WechatApiConstant;
import com.holderzone.member.common.dto.base.ResWeChat;
import com.holderzone.member.common.dto.crm.CrmAppIdQueryDTO;
import com.holderzone.member.common.dto.crm.AppIdRespDTO;
import com.holderzone.member.common.dto.message.WeChatMessageDataItemVm;
import com.holderzone.member.common.dto.message.WechatMessageVo;
import com.holderzone.member.common.enums.wechat.MsgReceiveType;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.util.HttpsClientUtils;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.wx.WechatAuthorizerInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
public class WeChatSendMessagesUtil {

    /**
     * 微信accessToken缓存key
     */
    private static final String MINI_PROGRAM_ACCESS_TOKEN_KEY = "miniProgramAccessToken:%s";

    @Resource
    private StringRedisTemplate stringredisTemplate;

    @Resource
    private StoreBaseService storeBaseService;

    @Resource
    private RequestGoalgoService hsaRequestGoalgoService;

    @Resource
    private HsaMemberInfoWeChatMapper hsaMemberInfoWeChatMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private HsaMemberInfoWxMpService memberInfoWxMpService;

    @Value("${miniProgram.miniprogramState}")
    private String miniprogramState;

    public String getAccessTokenByAppId(String appId, String appSecret) {
        String accessToken = stringredisTemplate.opsForValue().get(String.format(MINI_PROGRAM_ACCESS_TOKEN_KEY, appId));

        if (StringUtils.isEmpty(accessToken)) {
            String result;
            try {
                HashMap<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("grant_type", "client_credential");
                paramMap.put("appid", appId);
                paramMap.put("secret", appSecret);
                log.info("get weChat access token, appId:{}, appSecret:{}", appId, appSecret);
                result = HttpsClientUtils.doGet(WechatApiConstant.MINI_PROGRAM_TOKEN_URL, paramMap);
                log.info("get weChat access token:{} ", result);
                Map<String, Object> accessTokenMap = JSON.parseObject(result, Map.class);
                accessToken = accessTokenMap.get("access_token").toString();
                stringredisTemplate.opsForValue().set(String.format(MINI_PROGRAM_ACCESS_TOKEN_KEY, appId), accessToken, 55, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("get weiChat access token fail:", e);
            }
        }

        return accessToken;
    }


    /**
     * 小程序发送消息
     */
    public void sendAppletMsg(MessagesSendQO messagesSend) {
        log.info("小程序" + messagesSend.getTemplateName() + "订阅消息发送：{}", JSON.toJSONString(messagesSend));
        // 获取openid
        messagesSend.setOpenId(getOpenId(messagesSend));
        if (StringUtils.isEmpty(messagesSend.getOpenId())) {
            log.error("openId为空, messagesSend：{}", JacksonUtils.writeValueAsString(messagesSend));
            return;
        }
        // 获取appId
        ResWeChat appInfo = getAppInfo(messagesSend);
        messagesSend.setAppId(appInfo.getAppId());
        messagesSend.setAppSecret(appInfo.getAppsecret());
        // 统装数据
        messagesSend.setMessageType(MsgReceiveType.MINI_PROGRAM.getCode());
        WechatMessageVo wechatMessageVo = msgParamWrapper(messagesSend, null);
        String accessToken = getAccessTokenByAppId(messagesSend.getAppId(), messagesSend.getAppSecret());
        appletRequest(accessToken, wechatMessageVo, messagesSend);
    }


    /**
     * 小程序批量发送消息
     * 只支持同一运营主体 同一模板发送
     */
    public void sendBatchAppletMsg(List<MessagesSendQO> messagesSendList) {
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return;
        }
        MessagesSendQO messagesSend = messagesSendList.get(0);
        log.info("小程序" + messagesSend.getTemplateName() + "订阅消息批量发送：{}", JacksonUtils.writeValueAsString(messagesSend));
        // 批量获取openid
        Map<String, String> openIdMap = getOpenIds(messagesSendList);
        // 获取appId
        ResWeChat appInfo = getAppInfo(messagesSend);
        // 获取accessToken
        String accessToken = getAccessTokenByAppId(appInfo.getAppId(), appInfo.getAppsecret());
        for (MessagesSendQO msg : messagesSendList) {
            String openId = openIdMap.get(msg.getPhone());
            if (StringUtils.isEmpty(openId)) {
                log.warn("会员openId不存在,手机号:{}", msg.getPhone());
                return;
            }
            msg.setOpenId(openId);
            msg.setAppId(appInfo.getAppId());
            msg.setAppSecret(appInfo.getAppsecret());
            msg.setMessageType(MsgReceiveType.MINI_PROGRAM.getCode());
            WechatMessageVo wechatMessageVo = msgParamWrapper(msg, null);
            appletRequest(accessToken, wechatMessageVo, msg);
        }
    }

    /**
     * 发送公众号模板
     */
    public void sendWxMpMsg(MessagesSendQO messagesSend) {
        log.info("公众号" + messagesSend.getMpTemplateName() + "订阅消息发送：{}", JSON.toJSONString(messagesSend));
        // 获取授权信息
        WechatAuthorizerInfoVO wechatAuthorizerInfo = memberMallToolFeign.getAuthorizerAccessToken(messagesSend.getOperSubjectGuid());
        if (Objects.isNull(wechatAuthorizerInfo)) {
            log.error("公众号授权信息为空, messagesSendQO：{}", JacksonUtils.writeValueAsString(messagesSend));
            return;
        }
        log.info("获取授权信息,operSubjectGuid:{}, wechatAuthorizerInfo:{}", messagesSend.getOperSubjectGuid(),
                JacksonUtils.writeValueAsString(wechatAuthorizerInfo));
        // 获取openId
        String wxMpOpenId = getWxMpOpenId(wechatAuthorizerInfo.getAuthorizerAppid(), messagesSend);
        if (StringUtils.isEmpty(wxMpOpenId)) {
            log.error("获取用户openId失败");
            return;
        }
        messagesSend.setOpenId(wxMpOpenId);
        // 获取小程序appId
        String appletAppId = queryAppIdByOperSubjectGuid(messagesSend.getOperSubjectGuid());
        // 组装数据
        messagesSend.setMessageType(MsgReceiveType.THE_PUBLIC.getCode());
        WechatMessageVo wechatMessageVo = msgParamWrapper(messagesSend, appletAppId);
        mpRequest(wechatAuthorizerInfo.getAuthorizerAccessToken(), wechatMessageVo, messagesSend);
    }


    /**
     * 批量发送公众号模板
     * 只支持同一运营主体 同一模板发送
     */
    public void sendBatchWxMpMsg(List<MessagesSendQO> messagesSendList) {
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return;
        }
        MessagesSendQO messagesSend = messagesSendList.get(0);
        log.info("公众号" + messagesSend.getTemplateName() + "订阅消息批量发送：{}", JacksonUtils.writeValueAsString(messagesSendList));
        // 获取授权信息
        WechatAuthorizerInfoVO wechatAuthorizerInfo = memberMallToolFeign.getAuthorizerAccessToken(messagesSend.getOperSubjectGuid());
        if (Objects.isNull(wechatAuthorizerInfo)) {
            log.error("公众号授权信息为空, messagesSendQO：{}", JacksonUtils.writeValueAsString(messagesSend));
            return;
        }
        log.info("获取授权信息,operSubjectGuid:{}, wechatAuthorizerInfo:{}", messagesSend.getOperSubjectGuid(),
                JacksonUtils.writeValueAsString(wechatAuthorizerInfo));
        // 获取openId
        Map<String, String> wxMpOpenIdMap = getWxMpOpenIds(wechatAuthorizerInfo.getAuthorizerAppid(), messagesSendList);
        // 获取小程序appId
        String appletAppId = queryAppIdByOperSubjectGuid(messagesSend.getOperSubjectGuid());
        for (MessagesSendQO msg : messagesSendList) {
            String openId = wxMpOpenIdMap.get(msg.getPhone());
            if (StringUtils.isEmpty(openId)) {
                log.warn("会员openId不存在,手机号:{}", msg.getPhone());
                return;
            }
            msg.setOpenId(openId);
            msg.setMessageType(MsgReceiveType.THE_PUBLIC.getCode());
            WechatMessageVo wechatMessageVo = msgParamWrapper(msg, appletAppId);
            mpRequest(wechatAuthorizerInfo.getAuthorizerAccessToken(), wechatMessageVo, messagesSend);
        }
    }


    private ResWeChat getAppInfo(MessagesSendQO messagesSendQO) {
        ResWeChat resWeChat = storeBaseService.getWeChatAppId(Integer.parseInt(messagesSendQO.getOperSubjectGuid()));

        if (Objects.isNull(resWeChat) || StringUtils.isEmpty(resWeChat.getAppId())) {
            log.error("未获取到appId授权失败, messagesSendQO:{}", JacksonUtils.writeValueAsString(messagesSendQO));
            throw new MemberBaseException("未获取到appId授权失败: " + messagesSendQO.getOperSubjectGuid());
        }
        return resWeChat;
    }

    /**
     * 获取用户小程序openId
     */
    private String getOpenId(MessagesSendQO messagesSend) {
        if (!StringUtils.isEmpty(messagesSend.getOpenId())) {
            return messagesSend.getOpenId();
        }
        HsaMemberInfoWeChat memberInfoWeChat = queryMemberInfoWechat(messagesSend.getOperSubjectGuid(), messagesSend.getPhone());
        if (Objects.isNull(memberInfoWeChat)) {
            return null;
        }
        return memberInfoWeChat.getOpenId();
    }


    /**
     * 批量获取用户小程序openId
     * key : 手机号
     * value： openId
     */
    private Map<String, String> getOpenIds(List<MessagesSendQO> messagesSendList) {
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return Maps.newHashMap();
        }
        messagesSendList = messagesSendList.stream().filter(e -> StringUtils.isEmpty(e.getOpenId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return Maps.newHashMap();
        }
        MessagesSendQO messagesSend = messagesSendList.get(0);
        List<String> phoneList = messagesSendList.stream().map(MessagesSendQO::getPhone).distinct().collect(Collectors.toList());
        return queryMemberInfoOpenId(messagesSend.getOperSubjectGuid(), phoneList);
    }

    /**
     * 获取微信公众号 openId
     */
    private String getWxMpOpenId(String appId, MessagesSendQO messagesSend) {
        // 查询unionId
        String unionId = getUnionId(messagesSend);
        if (StringUtils.isEmpty(unionId)) {
            return null;
        }
        return memberInfoWxMpService.getOpenIdByAppIdAndUnionId(appId, unionId);
    }


    /**
     * 批量获取 微信公众号 openId
     * key: 手机号
     * value: openId
     */
    private Map<String, String> getWxMpOpenIds(String appId, List<MessagesSendQO> messagesSendList) {
        MessagesSendQO messagesSend = messagesSendList.get(0);
        List<String> phoneNumList = messagesSendList.stream().map(MessagesSendQO::getPhone).collect(Collectors.toList());
        // 批量查询unionId
        Map<String, String> memberInfoUnionIdMap = queryMemberInfoUnionId(messagesSend.getOperSubjectGuid(), phoneNumList);
        if (MapUtils.isEmpty(memberInfoUnionIdMap)) {
            return Maps.newHashMap();
        }
        List<String> unionIdList = new ArrayList<>(memberInfoUnionIdMap.values());
        List<HsaMemberInfoWxMp> openIdByAppIdAndUnionIds = memberInfoWxMpService.getOpenIdByAppIdAndUnionIds(appId, unionIdList);
        Map<String, String> unionIdAndOpenIdMap = openIdByAppIdAndUnionIds.stream()
                .collect(Collectors.toMap(HsaMemberInfoWxMp::getUnionId, HsaMemberInfoWxMp::getOpenId, (key1, key2) -> key2));
        Map<String, String> wxMpOpenIdMap = Maps.newHashMap();
        for (Map.Entry<String, String> entry : memberInfoUnionIdMap.entrySet()) {
            String unionId = entry.getValue();
            String openId = unionIdAndOpenIdMap.get(unionId);
            if (!StringUtils.isEmpty(openId)) {
                wxMpOpenIdMap.put(entry.getKey(), openId);
            }
        }
        return wxMpOpenIdMap;
    }

    private String getUnionId(MessagesSendQO messagesSend) {
        HsaMemberInfoWeChat hsaMemberInfoWeChat = queryMemberInfoWechat(messagesSend.getOperSubjectGuid(), messagesSend.getPhone());
        if (Objects.isNull(hsaMemberInfoWeChat)) {
            return null;
        }
        return hsaMemberInfoWeChat.getUnionId();
    }

    /**
     * 查询会员 微信信息
     */
    private HsaMemberInfoWeChat queryMemberInfoWechat(String operSubjectGuid, String phone) {
        HsaOperationMemberInfo operationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaOperationMemberInfo::getPhoneNum, phone));
        if (Objects.isNull(operationMemberInfo)) {
            throw new MemberBaseException("会员不存在: " + phone);
        }
        return hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOperationMemberInfoGuid, operationMemberInfo.getGuid()));
    }

    /**
     * 批量查询会员 微信信息
     */
    private Map<String, String> queryMemberInfoOpenId(String operSubjectGuid, List<String> phoneList) {
        Map<String, HsaMemberInfoWeChat> memberInfoWeChatMap = queryMemberInfoWechats(operSubjectGuid, phoneList);
        if (MapUtils.isEmpty(memberInfoWeChatMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> memberOpenIdMap = Maps.newHashMap();
        for (Map.Entry<String, HsaMemberInfoWeChat> entry : memberInfoWeChatMap.entrySet()) {
            String openId = entry.getValue().getOpenId();
            if (!StringUtils.isEmpty(openId)) {
                memberOpenIdMap.put(entry.getKey(), openId);
            }
        }
        return memberOpenIdMap;
    }


    private Map<String, String> queryMemberInfoUnionId(String operSubjectGuid, List<String> phoneList) {
        Map<String, HsaMemberInfoWeChat> memberInfoWeChatMap = queryMemberInfoWechats(operSubjectGuid, phoneList);
        if (MapUtils.isEmpty(memberInfoWeChatMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> memberUnionIdMap = Maps.newHashMap();
        for (Map.Entry<String, HsaMemberInfoWeChat> entry : memberInfoWeChatMap.entrySet()) {
            String unionId = entry.getValue().getUnionId();
            if (!StringUtils.isEmpty(unionId)) {
                memberUnionIdMap.put(entry.getKey(), unionId);
            }
        }
        return memberUnionIdMap;
    }


    private Map<String, HsaMemberInfoWeChat> queryMemberInfoWechats(String operSubjectGuid, List<String> phoneList) {
        List<HsaMemberInfoWeChat> memberInfoWeChatList = hsaMemberInfoWeChatMapper.getMemberInfoOpenIds(operSubjectGuid, phoneList);
        return memberInfoWeChatList.stream()
                .collect(Collectors.toMap(HsaMemberInfoWeChat::getPhoneNum, Function.identity(), (key1, key2) -> key1));
    }

    /**
     * 查询运营主体绑定的appId
     *
     * @return appId
     */
    private String queryAppIdByOperSubjectGuid(String operSubjectGuid) {
        CrmAppIdQueryDTO crmAppIdQueryDTO = new CrmAppIdQueryDTO(Long.valueOf(operSubjectGuid));
        AppIdRespDTO crmResponse = externalSupport.storeServer(ThreadLocalCache.getSystem()).getAppId(crmAppIdQueryDTO);
        log.info("查询运营主体绑定的appId, 返回：{}", JacksonUtils.writeValueAsString(crmResponse));
        if (Objects.isNull(crmResponse)) {
            return null;
        }
        return crmResponse.getAppId();
    }


    /**
     * 小程序订阅消息参数封装
     */
    private WechatMessageVo msgParamWrapper(MessagesSendQO messagesSendQO, String appletAppId) {
        WechatMessageVo wechatMessageVo = new WechatMessageVo();
        wechatMessageVo.setTouser(messagesSendQO.getOpenId());
        if (MsgReceiveType.MINI_PROGRAM.getCode().equals(messagesSendQO.getMessageType())) {
            // 小程序
            // 跳转
            if (messagesSendQO.getPageParams() != null) {
                wechatMessageVo.setPage(messagesSendQO.getPageParams());
            }
            // 模板id
            wechatMessageVo.setTemplate_id(messagesSendQO.getAppletTemplateId());
            // 消息模板参数
            if (CollUtil.isNotEmpty(messagesSendQO.getPrams())) {
                wechatMessageVo.setData(initDataValue(new HashMap<>(messagesSendQO.getPrams())));
            }
        } else {
            // 公众号
            // 跳转
            if (messagesSendQO.getPageParams() != null) {
                WechatMessageVo.InnerMiniProgram miniProgram = new WechatMessageVo.InnerMiniProgram();
                miniProgram.setAppid(appletAppId);
                miniProgram.setPagepath(messagesSendQO.getPageParams());
                wechatMessageVo.setMiniprogram(miniProgram);
            }
            // 模板id
            wechatMessageVo.setTemplate_id(messagesSendQO.getMsgTemplateId());
            // 消息模板参数
            if (CollUtil.isNotEmpty(messagesSendQO.getMpParams())) {
                wechatMessageVo.setData(initDataValue(new HashMap<>(messagesSendQO.getMpParams())));
            }
        }
        //线上为空
        if (!"online".equals(miniprogramState)) {
            wechatMessageVo.setMiniprogram_state(miniprogramState);
        }
        return wechatMessageVo;
    }

    private WeakHashMap<String, Object> initDataValue(Map<String, Object> params) {
        WeakHashMap<String, Object> data = new WeakHashMap<>();
        if (CollUtil.isEmpty(params)) {
            return data;
        }
        params.forEach((k, v) -> {
            if (Objects.nonNull(v)) {
                data.put(k, new WeChatMessageDataItemVm(v.toString()));
            } else {
                data.put(k, Strings.EMPTY);
            }
        });
        return data;
    }

    /**
     * 小程序请求
     */
    private void appletRequest(String accessToken, WechatMessageVo wechatMessageVo, MessagesSendQO messagesSend) {
        try {
            String url = String.format(WechatApiConstant.APPLET_SEND_MESSAGE_URL, accessToken).intern();
            String requestParam = JSON.toJSONString(wechatMessageVo);
            log.info("小程序推送参数:{}", requestParam);
            String result = HttpsClientUtils.doPost(url, requestParam);
            log.info("小程序推送" + messagesSend.getTemplateName() + "订阅消息成功,返回参数：{}", result + "推送订单号：{}" + messagesSend.getOrderNum());
        } catch (Exception e) {
            log.error("小程序推送订阅消息出错：{}", e.getMessage());
        }
    }

    /**
     * 公众号请求
     */
    private void mpRequest(String accessToken, WechatMessageVo wechatMessageVo, MessagesSendQO messagesSend) {
        try {
            String url = String.format(WechatApiConstant.PUBLIC_SEND_MESSAGE_URL, accessToken);
            String requestParam = JSON.toJSONString(wechatMessageVo);
            log.info("公众号推送参数:{}", requestParam);
            String result = HttpsClientUtils.doPost(url, requestParam);
            log.info("公众号推送" + messagesSend.getMpTemplateName() + "订阅消息成功,返回参数：{}", result + "推送订单号：{}" + messagesSend.getOrderNum());
        } catch (Exception e) {
            log.error("公众号推送订阅消息出错：{}", e.getMessage());
        }
    }
}
