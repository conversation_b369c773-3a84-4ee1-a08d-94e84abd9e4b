package com.holderzone.member.base.client;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.system.HsaInitSubjectData;
import com.holderzone.member.base.listener.CustomerImportRelationLabelListener;
import com.holderzone.member.base.manage.EquitiesInitManage;
import com.holderzone.member.base.mapper.grade.HsaBusinessEquitiesMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.grade.HsaGiftBagBaseInfoService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.integral.IntegralGeneralRulesService;
import com.holderzone.member.base.service.member.HsaCardBalanceRuleService;
import com.holderzone.member.base.service.member.HsaDataItemService;
import com.holderzone.member.base.service.setting.IHsaAccountSettingService;
import com.holderzone.member.base.service.system.IHsaSystemRoleService;
import com.holderzone.member.base.service.system.InitSubjectDataService;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.InitSubjectDataDTO;
import com.holderzone.member.common.dto.excel.InitializeGrowthValueDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.*;
import com.holderzone.member.common.util.ServletUtils;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.ipass.TeamInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RequestGoalgoService {

    @Resource
    private ExternalSupport externalSupport;

    @Lazy
    @Resource
    private HsaCardBalanceRuleService hsaCardBalanceRuleService;

    @Lazy
    @Resource
    private HsaGiftBagBaseInfoService hsaGiftBagBaseInfoService;

    @Lazy
    @Resource
    private HsaControlledGradeStateService gradeAndGrowthService;

    @Lazy
    @Resource
    private IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Lazy
    @Resource
    private IntegralGeneralRulesService integralGeneralRulesService;

    @Lazy
    @Resource
    private IHsaSystemRoleService hsaSystemRoleService;

    @Lazy
    @Resource
    private HsaDataItemService hsaDataItemService;

    @Resource
    private MemberMallFeign memberMallFeign;

    @Resource
    private MemberMarketingFeign marketingFeign;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Lazy
    @Resource
    private IHsaAccountSettingService accountSettingService;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private MemberSettlementFeign memberSettlementFeign;

    /**
     * 权益中心初始化
     */
    @Lazy
    @Resource
    private EquitiesInitManage entitiesInitManage;

    @Resource
    private InitSubjectDataService initSubjectDataService;

    @Resource
    private IPaasFeign iPaasFeign;

    private static final String ALL_PHONE_NUMBER = "完整手机号";

    private static final String SUBJECT_BODY = "运营主体";

    public OperSubjectInfoVO getOperatingSubjectInfo() {
        OperSubjectInfoVO subjectInfoVO = new OperSubjectInfoVO();
        List<OperSubjectInfo> subjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem())
                .queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid());
        HeaderUserInfo userInfo = queryUserInformation();
        subjectInfoVO.setOperSubjectInfos(subjectInfoList);
        if(userInfo != null){
            BeanUtils.copyProperties(userInfo, subjectInfoVO);
        }
        return subjectInfoVO;
    }

    public List<OperSubjectInfo> queryOperatingSubjectByTeamId(String teamId) {

        log.info("获取主体信息teamId：{}", teamId);
        List<OperSubjectInfo> subjectInfoList = null;
        try {
            subjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubject(teamId);

        } catch (Exception e) {
            log.error("查询运营主体失败：{}", e.getMessage());
        }
        return subjectInfoList;
    }

    /**
     * 获取账号信息
     *
     * @return 请求结果
     */
    public HeaderUserInfo queryUserInformation() {
        return externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid(),ThreadLocalCache.getHeaderUserInfo().getToken());
    }

    /**
     * 获取账号信息
     *
     * @return 请求结果
     */
    public HeaderUserInfo queryUserInformation(String token) {
        return externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid(), token);
    }

    public  boolean phonePermission(String identification) {
        HeaderUserInfo userInfo;
        try {
            userInfo = queryUserInformation();
        } catch (Exception e) {
            log.error("获取账号信息出错");
            return false;
        }
        return judgePermissionByUsr(identification, userInfo.getUserGuid());
    }

    public boolean judgePermissionByUsr(String identification, String userGuid) {
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        assert httpServletRequest != null;
        String token = httpServletRequest.getHeader(FilterConstant.TOKEN);
        //通过holder 查询对应模块操作权限
        OperationPermissionQO operationPermissionQO = new OperationPermissionQO();
        operationPermissionQO.setIdentifications(Collections.singletonList(identification));
        operationPermissionQO.setUserId(userGuid);
        operationPermissionQO.setTeamId(ThreadLocalCache.getEnterpriseGuid());
        operationPermissionQO.setPermissionName(ALL_PHONE_NUMBER);
        operationPermissionQO.setFunctionName(SUBJECT_BODY);
        return externalSupport.baseServer(ThreadLocalCache.getSystem()).getHasPermissionName(operationPermissionQO,token);
    }

    /**
     * 初始化运营主体相关数据信息
     *
     * @param subjectInfoList 运营主体集合
     * @param enterpriseGuid
     */
    public void initializeSubjectData(List<String> subjectInfoList, String enterpriseGuid) {
        RLock lock = redissonClient.getLock("initialize_subject_data");
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_1, NumberConstant.NUMBER_1, TimeUnit.MINUTES)) {
                log.info("锁返回");
                return;
            }
            log.info("执行初始化开始：{}", JSON.toJSONString(subjectInfoList));
            //初始化默认等级
            hsaMemberGradeInfoService.addDefaultMemberGrade(subjectInfoList);
            //获取运营主体时，初始化余额扣除顺序
            hsaCardBalanceRuleService.isSaveBalanceRule(subjectInfoList);
            //初始化运营主体升级礼包基础信息
            hsaGiftBagBaseInfoService.addGiftBag(subjectInfoList);
            //初始化会员等级开启状态信息
            gradeAndGrowthService.addGradeState(subjectInfoList);
            //初始化积分通用规则
            integralGeneralRulesService.batchSaveBySubject(subjectInfoList);
            // 初始化资料项设置
            hsaDataItemService.initDataItem(subjectInfoList);
            // 初始化账户设置
            accountSettingService.init(subjectInfoList);
            //初始化系统角色名
            hsaSystemRoleService.initSystemRole(subjectInfoList);

            //权益中心初始化
            entitiesInitManage.init(enterpriseGuid);
            
            //初始化消息订阅

            final String userStr = JSON.toJSONString(new HeaderUserInfo());

            //营销中心
            memberBaseThreadExecutor.execute(() -> {
                // 远程调用需要
                ThreadLocalCache.put(userStr);
                // 远程调用 todo 
//                marketingFeign.initCertifiedTheme(subjectInfoList);
                marketingFeign.initializeSubjectData(subjectInfoList);
            });

            //会员商城
            memberBaseThreadExecutor.execute(() -> {
                // 远程调用需要
                ThreadLocalCache.put(userStr);
                // 远程调用初始化默认主题
                memberMallFeign.initializeSubjectData(subjectInfoList);
            });

            //初始化消息订阅
            memberBaseThreadExecutor.execute(() -> {
                // 远程调用需要
                ThreadLocalCache.put(userStr);
                // 远程调用会员商城初始化设置
                memberMallToolFeign.initMessagesBySubjectGuid(subjectInfoList);
                ThreadLocalCache.remove();
            });

            //结算中心
            memberBaseThreadExecutor.execute(() -> {
                ThreadLocalCache.put(userStr);
                memberSettlementFeign.initializeSubjectData(subjectInfoList);
            });


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(e.getMessage());
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    public void initializePartnerData(String operSubjectGuid) {
        //删除成长值等级，只保留付费等级
        hsaMemberGradeInfoService.deleteGrowthGrade(operSubjectGuid);
    }


    public void initializeOldSubjectData() {
        List<String> operationSubjectIdList = externalSupport.baseServer(ThreadLocalCache.getSystem()).listAllOperationSubjectId();
        if(CollUtil.isEmpty(operationSubjectIdList)){
            return;
        }
        initializeSubjectData(operationSubjectIdList, null);

    }

    public void multipleGrowthDataHandler(String operSubjectGuid) {
        //赠送成长值详情
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = hsaGrowthValueDetailMapper.selectList(
                new LambdaQueryWrapper<HsaGrowthValueDetail>()
                        .eq(HsaGrowthValueDetail::getOperSubjectGuid, operSubjectGuid)
                        .isNull(HsaGrowthValueDetail::getMultipleEquitiesGuid)
                        .isNotNull(HsaGrowthValueDetail::getCurrentMemberLevel)
                        .isNotNull(HsaGrowthValueDetail::getMultipleGrowth));
        //当前运营主体所有会员
        List<HsaMemberGradeInfo> hsaMemberGradeInfos = hsaMemberGradeInfoMapper.selectList(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaMemberGradeInfo::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2));
        //当前运营主体所有权益
        List<HsaBusinessEquities> gradeEquitiesList = hsaBusinessEquitiesMapper.selectList(
                new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode())
                        .eq(HsaBusinessEquities::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.GRADE_EQUITIES)
                        .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2));
        //当前运营主体下权益领取记录
        List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords = hsaMemberEquitiesReceiveRecordMapper.selectList(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getOperSubjectGuid, operSubjectGuid));

        if (CollUtil.isEmpty(hsaGrowthValueDetails) ||
                CollUtil.isEmpty(hsaMemberGradeInfos) ||
                CollUtil.isEmpty(gradeEquitiesList) ||
                CollUtil.isEmpty(equitiesReceiveRecords)) {
            return;
        }
        //会员等级所关联的有效翻倍成长值权益Map
        Map<String, HsaBusinessEquities> equitiesMap = gradeEquitiesList.stream()
                .collect(Collectors.toMap(HsaBusinessEquities::getMemberGradeInfoGuid, Function.identity(), (obj, obj1) -> obj));
        //当前运营主体中所有会员根据名称分组
        Map<String, HsaMemberGradeInfo> gradeNameMap = hsaMemberGradeInfos.stream()
                .map(x -> x.setName(x.getName() + StringConstant.LEFT_BRACKET + StringConstant.VIP + x.getVipGrade() + StringConstant.RIGHT_BRACKET))
                .collect(Collectors.toMap(HsaMemberGradeInfo::getName, Function.identity(), (obj, obj1) -> obj));

        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = getHsaGrowthValueDetails(hsaGrowthValueDetails, equitiesReceiveRecords, equitiesMap, gradeNameMap);
        if (CollUtil.isNotEmpty(hsaGrowthValueDetailList)) {
            hsaGrowthValueDetailMapper.batchUpdateGrowth(hsaGrowthValueDetailList);
        }

    }

    public void deleteMultipleGrowthDataHandler(String operSubjectGuid) {
        //当前运营主体下权益领取记录
        List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords = hsaMemberEquitiesReceiveRecordMapper.selectList(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getOperSubjectGuid, operSubjectGuid));
        if (CollUtil.isEmpty(equitiesReceiveRecords)) {
            return;
        }
        List<HsaGrowthValueDetail> rollGrowthValueDetailList = getHsaGrowthValueDetails(operSubjectGuid, equitiesReceiveRecords);
        if (CollUtil.isNotEmpty(rollGrowthValueDetailList)) {
            hsaGrowthValueDetailMapper.batchUpdateGrowth(rollGrowthValueDetailList);
        }

    }

    private List<HsaGrowthValueDetail> getHsaGrowthValueDetails(List<HsaGrowthValueDetail> hsaGrowthValueDetails, List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords, Map<String, HsaBusinessEquities> equitiesMap, Map<String, HsaMemberGradeInfo> gradeNameMap) {
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        for (HsaGrowthValueDetail hsaGrowthValueDetail : hsaGrowthValueDetails) {
            //会员等级
            String currentMemberLevel = hsaGrowthValueDetail.getCurrentMemberLevel();
            HsaBusinessEquities hsaBusinessEquities = null;

            if (gradeNameMap.containsKey(currentMemberLevel)) {
                HsaMemberGradeInfo hsaMemberGradeInfo = gradeNameMap.get(currentMemberLevel);
                String gradeInfoGuid = hsaMemberGradeInfo.getGuid();
                if (equitiesMap.containsKey(gradeInfoGuid)) {
                    hsaBusinessEquities = equitiesMap.get(gradeInfoGuid);
                }
            }
            if (Objects.isNull(hsaBusinessEquities)) {
                continue;
            }
            hsaGrowthValueDetail.setMultipleEquitiesGuid(hsaBusinessEquities.getGuid());

            //计算翻倍的成长值
            Integer multipleGrowthValue = calculateGrowthValue(hsaGrowthValueDetail);
            hsaGrowthValueDetail.setMultipleGrowthValue(multipleGrowthValue);
            hsaGrowthValueDetailList.add(hsaGrowthValueDetail);
        }
        return hsaGrowthValueDetailList;
    }

    /**
     * 根据翻倍成长值记录，进行更新成长值记录中的权益guid以及翻倍了多少成长值
     * 注意：（有的翻倍成长值有累计成长值上限 比如说翻倍成长值累计上限为100成长值
     * 用户消费两笔订单，每一笔订单可以获取80成长值，这个时候使用，翻倍成长值权益累计上限为100.
     * 所以第一笔订单可以翻倍80成长值
     * 第二笔订单只能翻倍20成长值
     * 我们在这个方法处理翻倍成长值数据的时候，两笔订单都会翻倍80成长值，因此需要再更新处理一下最后一笔订单多出的翻倍成长值
     * ）
     *
     * @param operSubjectGuid        运营主体
     * @param equitiesReceiveRecords 翻倍成长值权益使用记录
     * @return 需要更新的成长值记录
     */
    private List<HsaGrowthValueDetail> getHsaGrowthValueDetails(String operSubjectGuid, List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords) {
        List<HsaGrowthValueDetail> rollGrowthValueDetailList = Lists.newArrayList();
        List<HsaGrowthValueDetail> growthValueDetailList = hsaGrowthValueDetailMapper.selectList(
                new LambdaQueryWrapper<HsaGrowthValueDetail>()
                        .eq(HsaGrowthValueDetail::getOperSubjectGuid, operSubjectGuid)
                        .isNotNull(HsaGrowthValueDetail::getMultipleEquitiesGuid)
                        .isNotNull(HsaGrowthValueDetail::getMultipleGrowthValue)
                        .isNotNull(HsaGrowthValueDetail::getMultipleGrowth));
        //回退多增加的成长值
        for (HsaMemberEquitiesReceiveRecord equitiesReceiveRecord : equitiesReceiveRecords) {
            String memberInfoGuid = equitiesReceiveRecord.getMemberInfoGuid();
            String gradeEquitiesGuid = equitiesReceiveRecord.getGradeEquitiesGuid();
            //当前会员权益的翻倍成长值记录
            List<HsaGrowthValueDetail> growthValueDetails = growthValueDetailList.stream()
                    .filter(x -> x.getMemberInfoGuid().equals(memberInfoGuid) &&
                            x.getMultipleEquitiesGuid().equals(gradeEquitiesGuid)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(growthValueDetails)) {
                //用户累计使用的翻倍成长值
                BigDecimal totalDoubleUpperValue = new BigDecimal(equitiesReceiveRecord.getTotalDoubleUpperValue());
                int sum = growthValueDetails.stream()
                        .mapToInt(HsaGrowthValueDetail::getMultipleGrowthValue).sum();
                //翻倍成长值
                BigDecimal multipleGrowthCount = new BigDecimal(sum);

                //用户最后一次使用翻倍成长值的记录
                Optional<HsaGrowthValueDetail> first = growthValueDetails.stream().max(Comparator.comparing(HsaGrowthValueDetail::getGmtCreate));
                if (BigDecimalUtil.greaterEqual(totalDoubleUpperValue, multipleGrowthCount) || !first.isPresent()) {
                    continue;
                }
                HsaGrowthValueDetail hsaGrowthValueDetail = first.get();
                BigDecimal multipleGrowthValue = new BigDecimal(hsaGrowthValueDetail.getMultipleGrowthValue());

                //多曾加的成长值
                BigDecimal subtract = multipleGrowthCount.subtract(totalDoubleUpperValue);

                if (BigDecimalUtil.greaterThan(multipleGrowthValue, subtract)) {
                    hsaGrowthValueDetail.setMultipleGrowthValue(multipleGrowthValue.subtract(subtract).intValue());
                    rollGrowthValueDetailList.add(hsaGrowthValueDetail);
                }
            }
        }
        return rollGrowthValueDetailList;
    }

    /**
     * 计算翻倍的成长值
     *
     * @param hsaGrowthValueDetail 成长值详情
     * @return 翻倍的成长值
     */
    private Integer calculateGrowthValue(HsaGrowthValueDetail hsaGrowthValueDetail) {
        //翻倍数
        BigDecimal multipleGrowth = hsaGrowthValueDetail.getMultipleGrowth();
        //翻倍后的成长值
        BigDecimal growthValue = new BigDecimal(hsaGrowthValueDetail.getGrowthValue());
        //翻倍之前的成长值
        int beforeGrowthValue = growthValue.divide(multipleGrowth, 1, BigDecimal.ROUND_HALF_UP).intValue();
        //验算的翻倍成长值
        BigDecimal multipleGrowthValue = multipleGrowth.multiply(new BigDecimal(beforeGrowthValue));
        //验算
        if (BigDecimalUtil.equal(growthValue, multipleGrowthValue)) {
            return growthValue.subtract(new BigDecimal(beforeGrowthValue)).intValue();
        }
        if (BigDecimalUtil.greaterThan(growthValue, multipleGrowthValue)) {
            return growthValue.subtract(new BigDecimal(beforeGrowthValue)).intValue() + 1;
        }
        return growthValue.subtract(new BigDecimal(beforeGrowthValue)).intValue() - 1;
    }

    private boolean isConditions(List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords, String memberInfoGuid, HsaBusinessEquities hsaBusinessEquities) {
        if (Objects.isNull(hsaBusinessEquities)) {
            return false;
        }
        Optional<HsaBusinessEquities> gradeEquities = Optional.of(hsaBusinessEquities);
        //累计翻倍限制 0:不限制 1:限制
        Integer totalDoubleCountLimited = gradeEquities
                .map(HsaBusinessEquities::getTotalDoubleCountLimited).orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == totalDoubleCountLimited) {
            return true;
        }
        Optional<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecord = equitiesReceiveRecords.stream()
                .filter(x -> x.getMemberInfoGuid().equals(memberInfoGuid) &&
                        x.getGradeEquitiesGuid().equals(hsaBusinessEquities.getGuid())).findFirst();
        //用户已经使用的累计翻倍成长值
        Integer totalDoubleUpperValue = equitiesReceiveRecord.map(HsaMemberEquitiesReceiveRecord::getTotalDoubleUpperValue).orElse(0);
        //当前权益累计赠送成长值数量
        Integer totalDoubleNumber = gradeEquities
                .map(HsaBusinessEquities::getTotalDoubleCountUpperLimit).orElse(0);
        return totalDoubleNumber >= totalDoubleUpperValue;
    }

    public void initializeGrowthValue(MultipartFile file, String operSubjectGuid) throws IOException {

        InputStream inputStream = file.getInputStream();
        //实例化实现了AnalysisEventListener接口的类
        CustomerImportRelationLabelListener listener = new CustomerImportRelationLabelListener();
        //传入参数
        ExcelReader excelReader = new ExcelReader(inputStream, ExcelTypeEnum.XLSX, null, listener);
        //读取信息
        excelReader.read(new Sheet(NumberConstant.NUMBER_1, NumberConstant.NUMBER_0, InitializeGrowthValueDTO.class));
        //获取数据
        List<Object> list = listener.getDatas();
        list.remove(0);
        List<InitializeGrowthValueDTO> importMemberInfoQOS = JSONArray.parseArray(JSONArray.toJSONString(list),
                InitializeGrowthValueDTO.class);
        log.info("importMemberInfoQOS===>" + JSONObject.toJSONString(importMemberInfoQOS));

    }

    public void autoInitSubjectData(InitSubjectDataDTO initSubjectData) {
        //查询数据库是否已经初始化
        int count = initSubjectDataService.count(new LambdaQueryWrapper<HsaInitSubjectData>()
                .eq(HsaInitSubjectData::getSystem, initSubjectData.getSystem())
                .eq(HsaInitSubjectData::getOperSubjectGuid, initSubjectData.getOperSubjectGuid())
                .eq(HsaInitSubjectData::getInitialized, BooleanEnum.TRUE.getCode()));
        if(count > 0){
            return;
        }
        //初始化数据
        initializeSubjectData(Collections.singletonList(initSubjectData.getOperSubjectGuid()), initSubjectData.getEnterpriseGuid());
        //入库
        HsaInitSubjectData hsaInitSubjectData = new HsaInitSubjectData();
        hsaInitSubjectData.setOperSubjectGuid(initSubjectData.getOperSubjectGuid());
        hsaInitSubjectData.setSystem(initSubjectData.getSystem());
        hsaInitSubjectData.setInitialized(BooleanEnum.TRUE.getCode());
        initSubjectDataService.save(hsaInitSubjectData);
    }

    public TeamInfoVO getTeamTree() {
        // 企业id
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();

        // 查询企业信息
        IPaasFeignModel<TeamInfoVO> teamTreeResult = iPaasFeign.findTeamTreeByTeamId(Long.valueOf(enterpriseGuid));
        if (teamTreeResult == null || teamTreeResult.getData() == null) {
            return null;
        }

        return teamTreeResult.getData();
    }
}
