package com.holderzone.member.base.entity.system;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class HsaSystemRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 角色名
     */
    private String name;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
