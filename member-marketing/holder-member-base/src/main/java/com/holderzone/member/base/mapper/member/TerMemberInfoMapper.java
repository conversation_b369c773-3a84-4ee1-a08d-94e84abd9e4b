package com.holderzone.member.base.mapper.member;

import com.holderzone.member.common.dto.card.TerMemberCardDTO;
import com.holderzone.member.common.dto.card.TerOpenCardDetailDTO;
import com.holderzone.member.common.qo.card.TerAbleCardDetailQO;
import com.holderzone.member.common.vo.card.TerAbleCardVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 一体机操作数据库
 * @date 2021/11/18
 */
public interface TerMemberInfoMapper {

    /**
     * 功能描述：一体机上会员拥有的会员卡列表
     * @date 2021/11/18
     * @param memberInfoGuid 会员guid
     * @return java.util.List<com.holderzone.member.common.dto.card.TerMemberCardDTO>
     */
    List<TerMemberCardDTO> listTerminalMemberCard(@Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 功能描述：查询可开通的会员中已经开通的会员卡guid
     * @date 2021/11/18
     * @param memberInfoGuid 会员guid
     * @return java.util.List<java.lang.String>
     */
    List<String> listTerminalOpenedCardGuid(@Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 功能描述：根据主体去查询可以开通的会员卡列表
     * @date 2021/11/18
     * @param operSubjectGuid 主体guid
     * @return java.util.List<com.holderzone.member.common.vo.card.TerAbleCardVO>
     */
    List<TerAbleCardVO> listTerminalAbleCard(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 功能描述：查询可开通会员卡详情
     * @date 2021/11/18
     * @param qo 查询参数
     * @return com.holderzone.member.common.dto.card.TerOpenCardDetailDTO
     */
    TerOpenCardDetailDTO getTerAbleCardDetail(TerAbleCardDetailQO qo);
}
