package com.holderzone.member.base.mapper.grade;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.member.base.entity.grade.HsaMemberGradePurchaseHistory;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 会员等级购买流水Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface HsaMemberGradePurchaseHistoryMapper extends HolderBaseMapper<HsaMemberGradePurchaseHistory> {

    /**
     * 根据订单号查询
     *
     * @param orderNo 订单号
     * @return 会员等级购买流水
     */
    HsaMemberGradePurchaseHistory selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据会员GUID查询
     *
     * @param memberInfoGuid 会员GUID
     * @return 会员等级购买流水列表
     */
    Collection<HsaMemberGradePurchaseHistory> selectByMemberInfoGuid(@Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 根据查询条件获取列表数据
     * @param qo 查询条件
     * @return 会员等级购买流水列表
     */
    List<HsaMemberGradePurchaseHistory> selectByCondition(MemberGradePurchaseHistoryQO qo);
}




