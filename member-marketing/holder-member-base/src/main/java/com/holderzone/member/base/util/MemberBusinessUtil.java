package com.holderzone.member.base.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class MemberBusinessUtil {

    private MemberBusinessUtil(){

    }

    /**
     * 电子开卡范围满足条件校验
     *
     * @param hsaCardOpenRule hsaCardOpenRule
     * @param sourceType      sourceType
     * @return
     */
    public static boolean checkOpenCardScopeType(HsaCardOpenRule hsaCardOpenRule,
                                           Integer sourceType,
                                           Set<String> hsaMemberLabelList) {
        if (hsaCardOpenRule.getOpenCardScopeType() == 1) {
            List<String> channel = Lists.newArrayList();
            List<String> labels = Lists.newArrayList();
            //注册来源
            channel = getChannel(hsaCardOpenRule, channel);
            //标签
            labels = getLabel(hsaCardOpenRule, labels);
            if (hsaCardOpenRule.getOpenCardScopeConditionType() == 0) {
                if (CollUtil.isNotEmpty(channel) && (!channel.contains(sourceType + ""))) {
                        return false;

                }
                return !CollUtil.isNotEmpty(labels) || (!Collections.disjoint(labels, hsaMemberLabelList));
            } else {
                return channel.contains(String.valueOf(sourceType)) || !Collections.disjoint(labels, hsaMemberLabelList);
            }
        } else {
            return true;
        }
    }


    private static List<String> getLabel(HsaCardOpenRule hsaCardOpenRule, List<String> labels) {
        if (!StringUtils.isEmpty(hsaCardOpenRule.getOpenCardMemberLabelGuid())) {
            labels = JSON.parseArray(hsaCardOpenRule.getOpenCardMemberLabelGuid(), String.class);
        }
        return labels;
    }

    private static List<String> getChannel(HsaCardOpenRule hsaCardOpenRule, List<String> channel) {
        if (!StringUtils.isEmpty(hsaCardOpenRule.getOpenCardRegisterChannel())) {
            channel = JSON.parseArray(hsaCardOpenRule.getOpenCardRegisterChannel(), String.class);
        }
        return channel;
    }
}
