package com.holderzone.member.base.entity.grade;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 记录会员已经使用的权益信息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberEquitiesReceiveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 会员等级guid
     */
    private String memberGradeGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 等级权益guid
     */
    private String  gradeEquitiesGuid;

    /**
     * 翻倍成长值使用次数
     */
    private Integer doubleValueCount;

    /**
     * 累计翻倍成长值
     */
    private Integer totalDoubleUpperValue;

    /**
     * 赠送成长值数量
     */
    private Integer giveGrowthValueNumber;

    /**
     * 权益类型（0：成长值，1：积分）默认为成长值
     */
    private Integer type;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
