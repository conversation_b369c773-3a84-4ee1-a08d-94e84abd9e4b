package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.grade.GradeMemberDiscountDTO;
import com.holderzone.member.common.dto.partner.PartnerMemberGradeDTO;
import com.holderzone.member.common.vo.grade.DoubleGradeEquitiesVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @program: member-marketing
 * @description: 会员等级基础信息mapper
 * @author: pan tao
 * @create: 2021-12-30 18:32
 */
public interface HsaMemberGradeInfoMapper extends HolderBaseMapper<HsaMemberGradeInfo> {

    int updateMemberGrade(@Param("roleType") String roleType, @Param("vipGrade") int vipGrade,
                          @Param("operSubjectGuid") String operSubjectGuid);

    int deleteAllDeleteAndEffective(@Param("gradeInfoGuidList") List<String> gradeInfoGuidList);

    int deleteAndGradeEquities(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int editEffectiveStatus(@Param("gradeInfoGuidList") List<String> gradeInfoGuidList);

    int editVipGrade(@Param("gradeInfoGuidList") List<String> gradeInfoGuidList);


    int editEquitieEffectiveStatus(@Param("gradeEquitieGuidList") List<String> gradeEquitieGuidList);

    int subtractMemberGrade(@Param("roleType") String roleType, @Param("vipGrade") int vipGrade,
                            @Param("operSubjectGuid") String operSubjectGuid,@Param("type") Integer type);

    HsaMemberGradeInfo getMaxMemberGradeInfo(@Param("roleType") String roleType,
                                             @Param("operSubjectGuid") String operSubjectGuid);

    HsaMemberGradeInfo queryBySubjectAndVipGrade(@Param("roleType") String roleType, @Param("vipGrade") int vipGrade,
                                                 @Param("type")Integer type,
                                                 @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 通过会员等级guid查询，翻倍成长值权益的guid
     *
     * @param operSubjectGuid  运营主体
     * @param memberGradeGuid  会员等级Guid
     * @param equitiesRuleType 权益规则类型（5：翻倍成长值，7：翻倍积分）
     * @return 翻倍成长值权益的guid
     */
    DoubleGradeEquitiesVO getGradeEquitiesGuid(@Param("operSubjectGuid") String operSubjectGuid,
                                               @Param("memberGradeGuid") String memberGradeGuid,
                                               @Param("equitiesRuleType") Integer equitiesRuleType);

    List<PartnerMemberGradeDTO> listCircleMemberGrade(@Param("guidList") Set<String> memberGuidList);

    List<GradeMemberDiscountDTO> listMemberDiscount(@Param("operSubjectGuid") String operSubjectGuid, @Param("roleType") String roleType);

}
