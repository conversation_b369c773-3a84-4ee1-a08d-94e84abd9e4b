package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaClearingStatementOperateRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.credit.CreditAdjustRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-14 16:58
 */
public interface HsaClearingStatementOperateRecordMapper extends HolderBaseMapper<HsaClearingStatementOperateRecord> {

    /**
     * 通过结算单编号查询应收调整记录
     *
     * @param clearingStatementNumber 结算单编号
     * @return 操作结果
     */
    List<CreditAdjustRecordVO> getCreditAdjustRecord(@Param("clearingStatementNumber") String clearingStatementNumber);

    /**
     * 根据结算单编号查询操作记录
     *
     * @param clearingStatementNumber 结算单编号
     * @return 查询结果
     */
    List<HsaClearingStatementOperateRecord> queryByClearingStatement(@Param("clearingStatementNumber") String clearingStatementNumber);

}
