package com.holderzone.member.base.mapper.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.base.entity.member.HsaMemberLabel;
import com.holderzone.member.base.entity.member.HsaMemberLabelRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.label.CancelConnectionTypeDTO;
import com.holderzone.member.common.qo.member.MemberLabelRecordQO;
import com.holderzone.member.common.vo.member.MemberLabelRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员标签记录
 * @date 2021/9/27 10:06
 */
public interface HsaMemberLabelRecordMapper extends HolderBaseMapper<HsaMemberLabelRecord> {

    void updateIsConnectionByGuid(@Param("request") List<HsaMemberLabelRecord> updateMemberLabelRecordList);

    List<MemberLabelRecordVO> queryMemberLabelRecord(@Param("request") MemberLabelRecordQO memberLabelRecordQO);

    int queryMemberLabelRecordCount(@Param("request") MemberLabelRecordQO memberLabelRecordQO);

    List<CancelConnectionTypeDTO> findLastCancelConnectionType(@Param("labelSettingGuid") String labelSettingGuid);

    CancelConnectionTypeDTO findNewestCancelConnectionType(@Param("labelSettingGuid") String labelSettingGuid,
                                                                 @Param("memberInfoGuid") String memberInfoGuid);

    List<CancelConnectionTypeDTO> findLastCancelConnectionTypeInMember(@Param("labelSettingGuid") String labelSettingGuid,
                                                                       @Param("memberInfoGuids") Set<String> memberInfoGuids);
}
