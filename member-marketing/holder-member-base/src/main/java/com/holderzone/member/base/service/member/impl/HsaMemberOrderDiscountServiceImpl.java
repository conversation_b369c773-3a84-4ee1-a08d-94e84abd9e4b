package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.mapper.member.HsaMemberOrderDiscountMapper;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.specials.LimitSpecialsCommodityRecordQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单优惠记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Service
public class HsaMemberOrderDiscountServiceImpl extends HolderBaseServiceImpl<HsaMemberOrderDiscountMapper, HsaMemberOrderDiscount> implements IHsaMemberOrderDiscountService {

    /**
     * 生成guid
     */
    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public void saveBatchByLock(SettlementOrderLockDTO dto) {
        final SettlementLockedOrderInfoDTO orderInfo = dto.getOrderInfo();

        final List<SettlementLockedDiscountReqDTO> checkDiscountList = dto.getCheckDiscountList();
        final int size = checkDiscountList.size();
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaMemberOrderDiscount.class.getSimpleName(), size);
        int i = 0;
        List<HsaMemberOrderDiscount> discountList = new ArrayList<>();

        for (SettlementLockedDiscountReqDTO detailVo : checkDiscountList) {
            final String guid = guids.get(i++);

            //优惠项展示
            final String discountName = SettlementDiscountOptionEnum.getEnum(detailVo.getDiscountOption()).getDes();
            final HsaMemberOrderDiscount orderDiscount = new HsaMemberOrderDiscount();
            orderDiscount
                    .setIsPay(BooleanEnum.FALSE.getCode())
                    .setOperSubjectGuid(orderInfo.getOperSubjectGuid())
                    .setOrderNumber(orderInfo.getOrderNumber())
                    .setMemberGuid(orderInfo.getMemberInfoGuid())
                    .setDiscountOption(detailVo.getDiscountOption())
                    .setDiscountGuid(detailVo.getDiscountGuid())
                    .setDiscountName(discountName)
                    //优惠id
                    .setDiscountId(Optional.ofNullable(detailVo.getDiscountOptionId())
                            .orElse(detailVo.getDiscountGuid())
                    )
                    .setDiscountDynamic(detailVo.getDiscountDynamic())
                    .setCouponRollback(orderInfo.getCouponRollback())
                    //优惠金额
                    .setDiscountFee(detailVo.getDiscountAmount())
                    .setGuid(guid)
            ;
            if (CollUtil.isNotEmpty(dto.getLimitSpecialsRecordCommodities())) {
                orderDiscount.setCommodityJson(JSON.toJSONString(dto.getLimitSpecialsRecordCommodities()));
            }
            discountList.add(orderDiscount);
        }
        //锁定等级折扣
        this.saveBatch(discountList);
    }

    /**
     * 查询优惠列表： 未退款
     *
     * @param operSubjectGuid 主体
     * @param orderNumber     订单号
     * @return 优惠列表
     */
    @Override
    public List<HsaMemberOrderDiscount> list(String operSubjectGuid, String orderNumber) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper = new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .eq(HsaMemberOrderDiscount::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberOrderDiscount::getOrderNumber, orderNumber)
                // 0表示正常，1表示反结账折扣
                .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode());
        return this.list(wrapper);
    }

    /**
     * 查询优惠列表： 未退款
     *
     * @param operSubjectGuid 主体
     * @param orderNumber     订单号
     * @return 优惠列表
     */
    @Override
    public List<HsaMemberOrderDiscount> listByOrderNumber(String operSubjectGuid, String orderNumber) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper = new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .eq(HsaMemberOrderDiscount::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberOrderDiscount::getOrderNumber, orderNumber);
        return this.list(wrapper);
    }

    @Override
    public List<Integer> listOptions(String operSubjectGuid, String orderNumber) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper =
                new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                        .select(HsaMemberOrderDiscount::getDiscountOption)
                        .eq(HsaMemberOrderDiscount::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaMemberOrderDiscount::getOrderNumber, orderNumber);
        final List<HsaMemberOrderDiscount> discountList = this.list(wrapper);
        return discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountOption)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void removeByGuids(List<String> guids) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> query = new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .in(HsaMemberOrderDiscount::getGuid, guids);
        this.remove(query);
    }


    @Override
    public List<HsaMemberOrderDiscount> getCommodityRecord(LimitSpecialsCommodityRecordQO limitSpecialsCommodityRecordQO) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper =
                new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                        .eq(HsaMemberOrderDiscount::getMemberGuid, limitSpecialsCommodityRecordQO.getMemberInfoGuid())
                        .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                        .eq(HsaMemberOrderDiscount::getDiscountOption, SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode())
                        .eq(StringUtils.isNotEmpty(limitSpecialsCommodityRecordQO.getOperSubjectGuid()), HsaMemberOrderDiscount::getOperSubjectGuid, limitSpecialsCommodityRecordQO.getOperSubjectGuid())
                        .in(HsaMemberOrderDiscount::getDiscountGuid, limitSpecialsCommodityRecordQO.getActivityCodeList());
        return this.list(wrapper);
    }

    @Override
    public Map<String, String> filterUsedCoupon(String memberInfoGuid,
                                                Integer isPay,
                                                Integer discountOption) {
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = baseMapper.selectList(new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .eq(HsaMemberOrderDiscount::getMemberGuid, memberInfoGuid)
                .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                .eq(Objects.nonNull(isPay), HsaMemberOrderDiscount::getIsPay, isPay)
                .eq(HsaMemberOrderDiscount::getDiscountOption, discountOption));

        Map<String, String> usedCouponMap = new HashMap<>();
        if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {
            if (discountOption == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode()) {
                Map<String, List<HsaMemberOrderDiscount>> orderDiscountByDiscountIdMap = hsaMemberOrderDiscounts.stream()
                        .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountId));
                for (Map.Entry<String, List<HsaMemberOrderDiscount>> entry : orderDiscountByDiscountIdMap.entrySet()) {
                    usedCouponMap.put(entry.getKey(), String.valueOf(entry.getValue().size()));
                }
            } else {
                usedCouponMap = hsaMemberOrderDiscounts.stream().collect(Collectors.
                        toMap(HsaMemberOrderDiscount::getDiscountId, HsaMemberOrderDiscount::getDiscountId, (entity1, entity2) -> entity1));
            }
            return usedCouponMap;
        }
        return usedCouponMap;
    }

    @Override
    public Map<String, Integer> getUsedCouponNum(List<String> couponCodes,
                                                 Integer isPay,
                                                 Integer discountOption,
                                                 String orderNumber) {
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = baseMapper.selectList(new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .in(HsaMemberOrderDiscount::getDiscountId, couponCodes)
                .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                .eq(Objects.nonNull(isPay), HsaMemberOrderDiscount::getIsPay, isPay)
                .notIn(StringUtils.isNotEmpty(orderNumber), HsaMemberOrderDiscount::getOrderNumber, orderNumber)
                .eq(HsaMemberOrderDiscount::getDiscountOption, discountOption));

        Map<String, Integer> usedCouponMap = new HashMap<>();
        if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {
            Map<String, List<HsaMemberOrderDiscount>> orderDiscountByDiscountIdMap = hsaMemberOrderDiscounts.stream()
                    .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountId));
            for (Map.Entry<String, List<HsaMemberOrderDiscount>> entry : orderDiscountByDiscountIdMap.entrySet()) {
                usedCouponMap.put(entry.getKey(), entry.getValue().size());
            }
        }
        return usedCouponMap;
    }
}
