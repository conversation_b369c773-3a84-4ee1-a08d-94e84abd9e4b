package com.holderzone.member.base.helper;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.gift.HsaCardRechargeGiftDetailMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.member.HsaLabelSettingMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.gift.RechargeGiftEntity;
import com.holderzone.member.common.dto.gift.RechargeGiftThresholdEntity;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.enums.gift.*;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.card.ListMiniProgramCardQO;
import com.holderzone.member.common.qo.coupon.CouponPackageDetailsDTO;
import com.holderzone.member.common.qo.equities.MemberCalculatePreMoneyQO;
import com.holderzone.member.common.qo.gift.RechargeGiftActivityAppletQO;
import com.holderzone.member.common.qo.gift.TerRechargeActivityQO;
import com.holderzone.member.common.qo.gift.TerRechargeGiftActivityQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.vo.card.RechargeActivityDescVO;
import com.holderzone.member.common.vo.card.RechargeThresholdVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.gift.TerRechargeGiftActivityVO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年06月15日 下午2:30
 * @description 充值赠送活动辅助类
 */
@Slf4j
@Component
@AllArgsConstructor
public class RechargeGiftActivityHelper {

    public static final int ZERO = 0;

    private HsaOperationMemberInfoMapper operationMemberInfoMapper;

    private HsaMemberInfoCardMapper memberInfoCardMapper;

    private HsaCardBaseInfoMapper cardBaseInfoMapper;

    private HsaMemberLabelMapper memberLabelMapper;

    private HsaCardRechargeGiftDetailMapper rechargeGiftDetailMapper;

    private MemberMarketingFeign marketingFeign;

    private HsaLabelSettingMapper hsaLabelSettingMapper;

    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    /**
     * 充值提示处理
     * 所有有效卡
     */
    public String buildRechargeTipsByAll(String memberInfoGuid, String memberGradeGuid) {
        // 没有有效会员卡则不处理充值活动
        ListMiniProgramCardQO qo = new ListMiniProgramCardQO();
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        qo.setMemberInfoGuid(memberInfoGuid);
        List<String> memberCardGuidList = memberInfoCardMapper.queryValidElectronicCardGuidList(qo);
        if (!CollectionUtils.isEmpty(memberCardGuidList)) {

            TerRechargeGiftActivityQO activityAppletQO = new TerRechargeGiftActivityQO();
            activityAppletQO.setMemberInfoGuid(memberInfoGuid);
            activityAppletQO.setMemberCardGuidList(memberCardGuidList);
            activityAppletQO.setMemberGradeGuid(memberGradeGuid);

            activityAppletQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            activityAppletQO.setSource(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
            activityAppletQO.setIsUnderway(BooleanEnum.TRUE.getCode());

            List<MemberLabelVO> labelVOList = memberLabelMapper.listMemberLabel(memberInfoGuid);

            if (CollUtil.isNotEmpty(labelVOList)) {
                List<String> memberLabelGuidList = labelVOList.stream()
                        .map(MemberLabelVO::getLabelGuid)
                        .distinct()
                        .collect(Collectors.toList());
                activityAppletQO.setMemberLabelGuidList(memberLabelGuidList);
            }
            activityAppletQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            List<TerRechargeGiftActivityVO> rechargeGiftActivityList = marketingFeign.listSuitableNew(activityAppletQO);
            if (!CollectionUtils.isEmpty(rechargeGiftActivityList)) {
                List<RechargeGiftActivityVO> rechargeGiftActivityVOS = Lists.newArrayList();
                getRechargeGiftActivityVOS(rechargeGiftActivityList, rechargeGiftActivityVOS);
                return handleRechargeGiftActivity(rechargeGiftActivityVOS);
            }
        }
        return StringUtils.EMPTY;
    }

    private void getRechargeGiftActivityVOS(List<TerRechargeGiftActivityVO> rechargeGiftActivityList, List<RechargeGiftActivityVO> rechargeGiftActivityVOS) {
        for (TerRechargeGiftActivityVO terRechargeGiftActivityVO : rechargeGiftActivityList) {
            RechargeGiftActivityVO rechargeGiftActivityVO = new RechargeGiftActivityVO();
            BeanUtils.copyProperties(terRechargeGiftActivityVO, rechargeGiftActivityVO);
            dealLabel(terRechargeGiftActivityVO, rechargeGiftActivityVO);

            dealGrade(terRechargeGiftActivityVO, rechargeGiftActivityVO);
            rechargeGiftActivityVOS.add(rechargeGiftActivityVO);
        }
    }

    public List<RechargeThresholdVO> terBuildRechargeThreshold(TerRechargeActivityQO terRechargeActivityQO,
                                                               List<String> rechargeMoneyList,
                                                               List<RechargeGiftActivityVO> rechargeGiftActivityVOS) {

        String memberInfoGuid = terRechargeActivityQO.getMemberInfoGuid();
        String storeGuid = terRechargeActivityQO.getStoreGuid();
        String memberInfoCardGuid = terRechargeActivityQO.getMemberInfoCardGuid();
        List<RechargeThresholdVO> rechargeThresholdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rechargeMoneyList)) {
            rechargeMoneyList.forEach(rechargeMoneyStr -> {
                RechargeThresholdVO thresholdVO = new RechargeThresholdVO();
                BigDecimal rechargeMoney = BigDecimal.valueOf(Long.parseLong(rechargeMoneyStr));
                thresholdVO.setGiftMoney(BigDecimal.ZERO);
                thresholdVO.setGiftIntegral(ZERO);
                thresholdVO.setGiftGrowth(ZERO);
                thresholdVO.setPreMoney(rechargeMoney);
                thresholdVO.setRechargeMoney(rechargeMoney);
                rechargeThresholdList.add(thresholdVO);
            });
        }

        TerRechargeGiftActivityQO terRechargeGiftActivityQO = new TerRechargeGiftActivityQO();
        terRechargeGiftActivityQO.setStoreGuid(storeGuid);
        terRechargeGiftActivityQO.setIsUnderway(BooleanEnum.FALSE.getCode());

        if (StringUtils.isNotBlank(memberInfoGuid)) {
            HsaOperationMemberInfo operationMemberInfo = getHsaOperationMemberInfo(memberInfoGuid);
            if (ObjectUtils.isEmpty(operationMemberInfo)) {
                log.warn("[buildRechargeThresholdByCard],未查询到会员,memberInfoGuid={}", memberInfoGuid);
                return rechargeThresholdList;
            }
            terRechargeGiftActivityQO.setMemberInfoGuid(memberInfoGuid);
            terRechargeGiftActivityQO.setMemberGradeGuid(operationMemberInfo.getMemberGradeInfoGuid());
        }

        // 没有会员卡则不处理充值活动
        if (CollUtil.isNotEmpty(terRechargeActivityQO.getCardGuidList())) {
            terRechargeGiftActivityQO.setMemberCardGuidList(terRechargeActivityQO.getCardGuidList());
        }

        terRechargeGiftActivityQO.setIsUnderway(BooleanEnum.FALSE.getCode());
        List<RechargeGiftActivityVO> rechargeGiftActivityList = getTerRechargeGiftActivityNew(terRechargeGiftActivityQO);
        if (CollectionUtils.isEmpty(rechargeGiftActivityList)) {
            return rechargeThresholdList;
        }

        Map<String, HsaCardBaseInfo> finalCardGuidMap = getHsaCardBaseInfoMap(rechargeGiftActivityList);

        if (!CollectionUtils.isEmpty(rechargeThresholdList)) {
            rechargeThresholdList.forEach(thresholdVO -> {
                rechargeGiftActivityList.forEach(activity -> calculateGiftActivity(thresholdVO, thresholdVO.getRechargeMoney(), activity, memberInfoGuid, finalCardGuidMap, memberInfoCardGuid));
                thresholdVO.setPreMoney(thresholdVO.getGiftMoney().add(thresholdVO.getRechargeMoney()));
            });
        }

        rechargeGiftActivityVOS.addAll(rechargeGiftActivityList);

        return rechargeThresholdList;
    }

    public static void checkCard(String cardGuid, TerRechargeGiftActivityQO terRechargeGiftActivityQO) {
        if (!StringUtils.isEmpty(cardGuid)) {
            terRechargeGiftActivityQO.setSource(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
            terRechargeGiftActivityQO.setIsStore(BooleanEnum.FALSE.getCode());
            terRechargeGiftActivityQO.setMemberCardGuidList(Collections.singletonList(cardGuid));
        } else {
            terRechargeGiftActivityQO.setIsStore(BooleanEnum.TRUE.getCode());
        }
    }


    public List<RechargeGiftActivityVO> getTerRechargeGiftActivityNew(TerRechargeGiftActivityQO terRechargeGiftActivityQO) {
        List<RechargeGiftActivityVO> rechargeGiftActivityVOS = Lists.newArrayList();
        TerRechargeGiftActivityQO activityQO = new TerRechargeGiftActivityQO();
        BeanUtils.copyProperties(terRechargeGiftActivityQO, activityQO);
        if (StringUtils.isNotBlank(terRechargeGiftActivityQO.getMemberInfoGuid())) {

            List<MemberLabelVO> labelVOList = memberLabelMapper.listMemberLabel(terRechargeGiftActivityQO.getMemberInfoGuid());
            List<String> memberLabelGuidList = labelVOList.stream()
                    .map(MemberLabelVO::getLabelGuid)
                    .distinct()
                    .collect(Collectors.toList());
            activityQO.setMemberLabelGuidList(memberLabelGuidList);
        }

        activityQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        activityQO.setSource(2);
        activityQO.setIsUnderway(terRechargeGiftActivityQO.getIsUnderway());
        List<TerRechargeGiftActivityVO> rechargeGiftActivityList = marketingFeign.listSuitableNew(activityQO);
        log.warn("会员满足的所有活动，rechargeGiftActivityList={},activityQO={}", JacksonUtils.writeValueAsString(rechargeGiftActivityList),
                JacksonUtils.writeValueAsString(activityQO));
        if (CollUtil.isNotEmpty(rechargeGiftActivityList)) {
            getRechargeGiftActivityVOS(rechargeGiftActivityList, rechargeGiftActivityVOS);
        }
        return rechargeGiftActivityVOS;
    }

    private void dealGrade(TerRechargeGiftActivityVO terRechargeGiftActivityVO, RechargeGiftActivityVO rechargeGiftActivityVO) {
        if (StringUtils.isNotEmpty(terRechargeGiftActivityVO.getGradeGuidJson())) {
            List<String> gradeGuid = JSON.parseArray(terRechargeGiftActivityVO.getGradeGuidJson(), String.class);
            List<HsaMemberGradeInfo> hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .in(HsaMemberGradeInfo::getGuid, gradeGuid)
                    .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < hsaMemberGradeInfo.size(); i++) {
                sb.append(hsaMemberGradeInfo.get(i).getName());
                if (i < hsaMemberGradeInfo.size() - 1) {
                    sb.append(StringConstant.MARK);
                }
            }
            rechargeGiftActivityVO.setGradeGuidJson(sb.toString());
        }
    }

    private void dealLabel(TerRechargeGiftActivityVO terRechargeGiftActivityVO, RechargeGiftActivityVO rechargeGiftActivityVO) {
        if (StringUtils.isNotEmpty(terRechargeGiftActivityVO.getLabelGuidJson())) {
            List<String> labelGuid = JSON.parseArray(terRechargeGiftActivityVO.getLabelGuidJson(), String.class);
            List<HsaLabelSetting> hsaLabelSettings = hsaLabelSettingMapper.queryByGuids(labelGuid);
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < hsaLabelSettings.size(); i++) {
                sb.append(hsaLabelSettings.get(i).getLabelName());
                if (i < hsaLabelSettings.size() - 1) {
                    sb.append(StringConstant.MARK);
                }
            }
            rechargeGiftActivityVO.setLabelGuidJson(sb.toString());
        }
    }

    /**
     * 处理充值赠送活动
     */
    public String handleRechargeGiftActivity(List<RechargeGiftActivityVO> rechargeGiftActivityList) {
        List<RechargeGiftThresholdEntity> rechargeGiftThresholdList = rechargeGiftActivityList.stream()
                .flatMap(r -> r.getRechargeGiftThresholdList().stream())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rechargeGiftThresholdList)) {
            log.warn("充值活动没有充值赠送门槛,rechargeGiftActivityList={}", JacksonUtils.writeValueAsString(rechargeGiftActivityList));
            return StringUtils.EMPTY;
        }
        List<RechargeGiftEntity> rechargeGiftDTOList = rechargeGiftThresholdList.stream()
                .flatMap(r -> r.getRechargeGiftEntityList().stream())
                .collect(Collectors.toList());

        // 提示优先级：金额＞积分（根据配置的积分名称）＞会员卡（根据会员卡名称，多活动多张卡显示最新编辑的活动的最高档位赠送的卡名称）＞成长值（根据配置的成长值名称），暂不考虑优惠券
        List<Integer> giftTypeList = rechargeGiftDTOList.stream()
                .map(RechargeGiftEntity::getGiftType)
                .collect(Collectors.toList());
        // 如满足，则提示“充值有礼，最高获赠XXX元/积分/成长值（XXX=满足的活动中，赠送金额多的金额/积分/成长值；）”/“充值有礼，可获赠XXX（会员卡名称）”；
        StringBuilder builder = new StringBuilder();
        if (giftTypeList.contains(GiftTypeEnum.GIFT_MONEY_AMOUNT.getCode())) {
            giftAmount(rechargeGiftDTOList, builder);
            return builder.toString();
        }
        if (giftTypeList.contains(GiftTypeEnum.GIFT_INTEGRAL_VALUE.getCode())) {
            giftIntegral(rechargeGiftDTOList, builder);
            return builder.toString();
        }

        // 会员卡（根据会员卡名称，多活动多张卡显示最新编辑的活动的最高档位赠送的卡名称）
        if (giftTypeList.contains(GiftTypeEnum.GIFT_MEMBER_CARD.getCode())) {
            String empty = getMemberCard(rechargeGiftActivityList, builder);
            if (empty != null) return empty;
            return builder.toString();
        }
        if (giftTypeList.contains(GiftTypeEnum.GIFT_MEMBER_GROWTH.getCode())) {
            getGrowth(rechargeGiftDTOList, builder);
            return builder.toString();
        }

        return StringUtils.EMPTY;
    }

    private static void getGrowth(List<RechargeGiftEntity> rechargeGiftDTOList, StringBuilder builder) {
        Integer growthValue = rechargeGiftDTOList.stream()
                .filter(r -> !ObjectUtils.isEmpty(r.getGiftType())
                        && Objects.equals(GiftTypeEnum.GIFT_MEMBER_GROWTH.getCode(), r.getGiftType())
                        && !ObjectUtils.isEmpty(r.getGrowthValue()))
                .max(Comparator.comparing(RechargeGiftEntity::getGrowthValue))
                .map(RechargeGiftEntity::getGrowthValue).orElse(0);
        builder.append(StringConstant.RECHARGE_GIFT_MAX_GET)
                .append(growthValue)
                .append(StringConstant.GROWTH_VALUE);
    }

    private String getMemberCard(List<RechargeGiftActivityVO> rechargeGiftActivityList, StringBuilder builder) {
        List<RechargeGiftActivityVO> allCardActivityList = rechargeGiftActivityList.stream()
                .filter(r -> {
                    List<Integer> filterGiftTypeList = r.getRechargeGiftThresholdList().stream()
                            .flatMap(g -> g.getRechargeGiftEntityList().stream())
                            .map(RechargeGiftEntity::getGiftType)
                            .collect(Collectors.toList());
                    return filterGiftTypeList.contains(GiftTypeEnum.GIFT_MEMBER_CARD.getCode());
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allCardActivityList)) {
            log.warn("会员卡类型异常");
            return StringUtils.EMPTY;
        }
        String cardGuid = getCardGuid(allCardActivityList);
        if (cardGuid == null) return StringUtils.EMPTY;
        HsaCardBaseInfo cardBaseInfo = cardBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(HsaCardBaseInfo::getGuid, cardGuid));
        if (ObjectUtils.isEmpty(cardBaseInfo)) {
            log.warn(MemberAccountExceptionEnum.GIFT_CARD_NOT_EXIST.getDes());
            return StringUtils.EMPTY;
        }
        builder.append(StringConstant.RECHARGE_GIFT_GET)
                .append(cardBaseInfo.getCardName());
        return null;
    }

    private static void giftIntegral(List<RechargeGiftEntity> rechargeGiftDTOList, StringBuilder builder) {
        Integer integralValue = rechargeGiftDTOList.stream()
                .filter(r -> !ObjectUtils.isEmpty(r.getGiftType())
                        && Objects.equals(GiftTypeEnum.GIFT_INTEGRAL_VALUE.getCode(), r.getGiftType())
                        && !ObjectUtils.isEmpty(r.getIntegralValue()))
                .max(Comparator.comparing(RechargeGiftEntity::getIntegralValue))
                .map(RechargeGiftEntity::getIntegralValue).orElse(0);
        builder.append(StringConstant.RECHARGE_GIFT_MAX_GET)
                .append(integralValue)
                .append(StringConstant.INTEGRAL_VALUE);
    }

    private static void giftAmount(List<RechargeGiftEntity> rechargeGiftDTOList, StringBuilder builder) {
        BigDecimal moneyAmount = rechargeGiftDTOList.stream()
                .filter(r -> !ObjectUtils.isEmpty(r.getGiftType())
                        && Objects.equals(GiftTypeEnum.GIFT_MONEY_AMOUNT.getCode(), r.getGiftType())
                        && !ObjectUtils.isEmpty(r.getMoneyAmount()))
                .max(Comparator.comparing(RechargeGiftEntity::getMoneyAmount))
                .map(RechargeGiftEntity::getMoneyAmount).orElse(BigDecimal.ZERO);
        builder.append(StringConstant.RECHARGE_GIFT_MAX_GIVE)
                .append(moneyAmount)
                .append(StringConstant.YUAN);
    }

    private String getCardGuid(List<RechargeGiftActivityVO> allCardActivityList) {
        List<RechargeGiftThresholdEntity> cardThresholdEntityList = allCardActivityList.stream()
                .flatMap(r -> r.getRechargeGiftThresholdList().stream())
                .sorted(Comparator.comparing(RechargeGiftThresholdEntity::getGiftThresholdAmount).reversed())
                .collect(Collectors.toList());
        Map<BigDecimal, List<RechargeGiftThresholdEntity>> giftThresholdAmountMap = cardThresholdEntityList.stream()
                .collect(Collectors.groupingBy(RechargeGiftThresholdEntity::getGiftThresholdAmount));
        BigDecimal maxGiftThresholdAmount = cardThresholdEntityList.stream()
                .map(RechargeGiftThresholdEntity::getGiftThresholdAmount)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
        List<RechargeGiftThresholdEntity> giftThresholdEntityList = giftThresholdAmountMap.get(maxGiftThresholdAmount);
        RechargeGiftThresholdEntity cardGiftThreshold = giftThresholdEntityList.get(0);
        // 取最新编辑的活动
        if (giftThresholdEntityList.size() > 1) {
            List<RechargeGiftThresholdEntity> gmtModifiedMaxThreshold = allCardActivityList.stream()
                    .max(Comparator.comparing(RechargeGiftActivityVO::getGmtModified))
                    .map(RechargeGiftActivityVO::getRechargeGiftThresholdList)
                    .orElse(null);
            if (!CollectionUtils.isEmpty(gmtModifiedMaxThreshold)) {
                cardGiftThreshold = gmtModifiedMaxThreshold.stream()
                        .filter(r -> !ObjectUtils.isEmpty(r.getGiftThresholdAmount()))
                        .max(Comparator.comparing(RechargeGiftThresholdEntity::getGiftThresholdAmount))
                        .orElse(null);
            }
        }
        if (ObjectUtils.isEmpty(cardGiftThreshold)) {
            log.warn("充值赠送门槛异常");
            return null;
        }
        List<RechargeGiftEntity> giftDTOList = cardGiftThreshold.getRechargeGiftEntityList();
        if (CollectionUtils.isEmpty(giftDTOList)) {
            log.warn("赠送设置异常");
            return null;
        }
        List<RechargeGiftEntity> memberCardList = giftDTOList.stream()
                .filter(g -> Objects.equals(g.getGiftType(), GiftTypeEnum.GIFT_MEMBER_CARD.getCode()))
                .collect(Collectors.toList());
        RechargeGiftEntity rechargeGiftDTO = memberCardList.get(0);
        return rechargeGiftDTO.getCardGuid();
    }

    /**
     * 充值提示处理
     * 当前会员当前卡
     */
    public String buildRechargeTipsByCard(String memberInfoGuid, String memberCardGuid) {
        HsaOperationMemberInfo operationMemberInfo = getHsaOperationMemberInfo(memberInfoGuid);
        if (ObjectUtils.isEmpty(operationMemberInfo)) {
            log.warn("未查询到会员,memberInfoGuid={}", memberInfoGuid);
            return StringUtils.EMPTY;
        }

        // 没有会员卡则不处理充值活动
        if (!StringUtils.isEmpty(memberCardGuid)) {

            List<RechargeGiftActivityVO> rechargeGiftActivityList = getRechargeGiftActivityVOList(memberInfoGuid, memberCardGuid, operationMemberInfo, "");
            if (!CollectionUtils.isEmpty(rechargeGiftActivityList)) {
                return handleRechargeGiftActivity(rechargeGiftActivityList);
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 充值提示处理
     * 多卡
     */
    public String buildRechargeTipsByCard(String memberCardGuid, List<RechargeGiftActivityVO> rechargeGiftActivityList) {
        // 没有会员卡则不处理充值活动
        if (!StringUtils.isEmpty(memberCardGuid) && !CollectionUtils.isEmpty(rechargeGiftActivityList)) {
            final List<RechargeGiftActivityVO> activityVOList = rechargeGiftActivityList.stream()
                    .filter(v -> v.getCardGuid().contains(memberCardGuid)).collect(Collectors.toList());
            return handleRechargeGiftActivity(activityVOList);
        }
        return StringUtils.EMPTY;
    }

    public List<RechargeGiftActivityVO> getActivityList(String memberInfoGuid, List<String> memberCardGuid, String storeGuid) {
        HsaOperationMemberInfo operationMemberInfo = getHsaOperationMemberInfo(memberInfoGuid);
        if (ObjectUtils.isEmpty(operationMemberInfo)) {
            log.warn("未查询到会员,memberInfoGuid={}", memberInfoGuid);
            return Collections.emptyList();
        }

        // 没有会员卡则不处理充值活动
        if (!CollectionUtils.isEmpty(memberCardGuid)) {
            return getRechargeGiftActivityVOList(memberInfoGuid, memberCardGuid, operationMemberInfo, storeGuid);
        }
        return Collections.emptyList();
    }


    public List<RechargeThresholdVO> buildRechargeThresholdByCard(String memberInfoGuid,
                                                                  String cardGuid,
                                                                  List<String> rechargeMoneyList,
                                                                  String storeGuid,
                                                                  List<RechargeGiftActivityVO> rechargeGiftActivityVOS,
                                                                  String memberInfoCardGuid) {
        List<RechargeThresholdVO> rechargeThresholdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rechargeMoneyList)) {
            rechargeMoneyList.forEach(rechargeMoneyStr -> {
                RechargeThresholdVO thresholdVO = new RechargeThresholdVO();
                BigDecimal rechargeMoney = BigDecimal.valueOf(Long.parseLong(rechargeMoneyStr));
                thresholdVO.setRechargeMoney(rechargeMoney);
                thresholdVO.setGiftMoney(BigDecimal.ZERO);
                thresholdVO.setGiftIntegral(ZERO);
                thresholdVO.setGiftGrowth(ZERO);
                thresholdVO.setPreMoney(rechargeMoney);
                rechargeThresholdList.add(thresholdVO);
            });
        }

        HsaOperationMemberInfo operationMemberInfo = getHsaOperationMemberInfo(memberInfoGuid);
        if (ObjectUtils.isEmpty(operationMemberInfo)) {
            log.warn("[buildRechargeThresholdByCard],未查询到会员,memberInfoGuid={}", memberInfoGuid);
            return rechargeThresholdList;
        }
        // 没有会员卡则不处理充值活动
        if (!StringUtils.isEmpty(cardGuid)) {
            List<RechargeGiftActivityVO> rechargeGiftActivityList = getRechargeGiftActivityVOList(memberInfoGuid, cardGuid, operationMemberInfo, storeGuid);
            if (CollectionUtils.isEmpty(rechargeGiftActivityList)) {
                return rechargeThresholdList;
            }

            Map<String, HsaCardBaseInfo> finalCardGuidMap = getHsaCardBaseInfoMap(rechargeGiftActivityList);
            if (!CollectionUtils.isEmpty(rechargeThresholdList)) {
                rechargeThresholdList.forEach(thresholdVO -> {
                    rechargeGiftActivityList.forEach(activity -> calculateGiftActivity(thresholdVO, thresholdVO.getRechargeMoney(), activity, memberInfoGuid, finalCardGuidMap, memberInfoCardGuid));
                    thresholdVO.setPreMoney(thresholdVO.getGiftMoney().add(thresholdVO.getRechargeMoney()));
                });
            }


            rechargeGiftActivityVOS.addAll(rechargeGiftActivityList);
        }
        return rechargeThresholdList;
    }

    private void calculateGiftActivity(RechargeThresholdVO thresholdVO,
                                       BigDecimal rechargeMoney,
                                       RechargeGiftActivityVO activity,
                                       String memberInfoGuid,
                                       Map<String, HsaCardBaseInfo> cardGuidMap,
                                       String memberInfoCardGuid) {
        List<RechargeGiftThresholdEntity> thresholdList = activity.getRechargeGiftThresholdList();
        if (CollectionUtils.isEmpty(thresholdList)) {
            return;
        }

        //过滤门槛次数
        checkGiftThresholdLimitSum(ThreadLocalCache.getOperSubjectGuid(),
                memberInfoCardGuid,
                memberInfoGuid,
                activity.getGuid(),
                thresholdList);

        RechargeGiftThresholdEntity thresholdEntity = thresholdList.stream()
                .filter(t -> rechargeMoney.compareTo(t.getGiftThresholdAmount()) >= 0)
                .max(Comparator.comparing(RechargeGiftThresholdEntity::getGiftThresholdAmount))
                .orElse(null);
        if (ObjectUtils.isEmpty(thresholdEntity)) {
            return;
        }
        // 满
        if (Objects.equals(GiftThresholdEnum.THRESHOLD_FULL.getCode(), thresholdEntity.getGiftThresholdType())) {
            calculateThreshold(thresholdVO, thresholdEntity, cardGuidMap, 1);
            return;
        }

        // 每满
        int fullTime = rechargeMoney.divide(thresholdEntity.getGiftThresholdAmount(), RoundingMode.DOWN).intValue();
        // 门槛次数限制，根据活动和会员id查询完成次数
        if (Objects.equals(EquitiesLimitedTypeEnum.LIMITED.getCode(), thresholdEntity.getGiftThresholdLimitType())) {
            final int thresholdSum = getResidueGiftThresholdSum(activity, memberInfoGuid, thresholdEntity, memberInfoCardGuid);
            //若剩余次数为0  则不在累计赠送金额
            if (thresholdSum <= 0) {
                fullTime = 0;
            }
        }

        //计算赠品
        calculateThreshold(thresholdVO, thresholdEntity, cardGuidMap, fullTime);

    }

    private void checkGiftThresholdLimitSum(String operSubjectGuid,
                                            String memberInfoCardGuid,
                                            String memberGuid,
                                            String activityGuid,
                                            List<RechargeGiftThresholdEntity> thresholdList) {

        List<String> thresholdGuidList = thresholdList.stream().map(RechargeGiftThresholdEntity::getGuid).collect(Collectors.toList());

        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList = rechargeGiftDetailMapper
                .selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .eq(HsaCardRechargeGiftDetail::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaCardRechargeGiftDetail::getMemberCardGuid, memberInfoCardGuid)
                        .eq(StringUtils.isNotEmpty(memberGuid), HsaCardRechargeGiftDetail::getMemberGuid, memberGuid)
                        .in(HsaCardRechargeGiftDetail::getThresholdGuid, thresholdGuidList)
                        .and(wq -> wq.isNull(HsaCardRechargeGiftDetail::getIsRefresh)
                                .or().notIn(HsaCardRechargeGiftDetail::getIsRefresh, GiftDetailRefreshEnum.CANCEL.getCode())
                        )
                        .eq(HsaCardRechargeGiftDetail::getActivityGuid, activityGuid));


        if (CollUtil.isNotEmpty(hsaCardRechargeGiftDetailList)) {
            Map<String, List<HsaCardRechargeGiftDetail>> rechargeGiftDetailHashMap = hsaCardRechargeGiftDetailList.stream()
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getThresholdGuid));

            List<String> deleteThresholdList = Lists.newArrayList();
            //需要校验次数
            for (RechargeGiftThresholdEntity rechargeGiftThresholdEntity : thresholdList) {
                if (rechargeGiftDetailHashMap.containsKey(rechargeGiftThresholdEntity.getGuid())
                        && rechargeGiftThresholdEntity.getGiftThresholdLimitType() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
                    List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = rechargeGiftDetailHashMap.get(rechargeGiftThresholdEntity.getGuid());

                    int num = hsaCardRechargeGiftDetails.size();

                    if (rechargeGiftThresholdEntity.getGiftThresholdLimitSum() <= num) {
                        deleteThresholdList.add(rechargeGiftThresholdEntity.getGuid());
                        log.info("门槛达到次数限制! 门槛guid:{} 限制次数:{}",
                                rechargeGiftThresholdEntity.getGuid(),
                                rechargeGiftThresholdEntity.getGiftThresholdLimitSum());
                    }
                }
            }
            if (CollUtil.isNotEmpty(deleteThresholdList)) {
                thresholdList.removeIf(in -> deleteThresholdList.contains(in.getGuid()));
            }

        }
    }

    private int getResidueGiftThresholdSum(RechargeGiftActivityVO activity, String memberInfoGuid, RechargeGiftThresholdEntity thresholdEntity
            , String memberInfoCardGuid) {

        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList = rechargeGiftDetailMapper
                .selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .eq(HsaCardRechargeGiftDetail::getMemberCardGuid, memberInfoCardGuid)
                        .eq(StringUtils.isNotEmpty(memberInfoGuid), HsaCardRechargeGiftDetail::getMemberGuid, memberInfoGuid)
                        .eq(HsaCardRechargeGiftDetail::getThresholdGuid, thresholdEntity.getGuid())
                        .and(wq -> wq.isNull(HsaCardRechargeGiftDetail::getIsRefresh)
                                .or().notIn(HsaCardRechargeGiftDetail::getIsRefresh, GiftDetailRefreshEnum.CANCEL.getCode())
                        )
                        .eq(HsaCardRechargeGiftDetail::getActivityGuid, thresholdEntity.getRechargeGiftGuid()));

        int giftThresholdSum = hsaCardRechargeGiftDetailList.size();
        log.info("充值活动 {} 要限制次数：,门槛：{}，限制次数：{}，已有次数：{}", activity.getGuid(),
                thresholdEntity.getGuid(), thresholdEntity.getGiftThresholdLimitSum(), giftThresholdSum);
        return thresholdEntity.getGiftThresholdLimitSum() - giftThresholdSum;
    }

    /**
     * 计算赠品
     *
     * @param thresholdVO     充值档位列表
     * @param thresholdEntity 充值赠送门槛
     * @param cardGuidMap     卡
     * @param fullTime        次数
     */
    private void calculateThreshold(RechargeThresholdVO thresholdVO,
                                    RechargeGiftThresholdEntity thresholdEntity,
                                    Map<String, HsaCardBaseInfo> cardGuidMap,
                                    int fullTime) {
        //不赠送
        if (fullTime <= 0) {
            return;
        }

        List<RechargeGiftEntity> giftEntityList = thresholdEntity.getRechargeGiftEntityList();

        giftEntityList.forEach(gift -> {
            GiftTypeEnum giftTypeEnum = GiftTypeEnum.getEnum(gift.getGiftType());
            switch (Objects.requireNonNull(giftTypeEnum)) {
                case GIFT_MONEY_AMOUNT:
                    final BigDecimal giftMoney = gift.getMoneyAmount().multiply(BigDecimal.valueOf(fullTime));
                    thresholdVO.setGiftMoney(thresholdVO.getGiftMoney().add(giftMoney));
                    break;

                case GIFT_INTEGRAL_VALUE:
                    final int giftIntegralValue = gift.getIntegralValue() * fullTime;
                    thresholdVO.setGiftIntegral(thresholdVO.getGiftIntegral() + giftIntegralValue);
                    break;

                case GIFT_MEMBER_CARD:
                    //只送一张卡
                    handelGiftCard(thresholdVO, gift, cardGuidMap);

                    break;
                case GIFT_MEMBER_GROWTH:
                    final int giftGrowthValue = gift.getGrowthValue() * fullTime;
                    thresholdVO.setGiftGrowth(thresholdVO.getGiftGrowth() + giftGrowthValue);
                    break;
                case GIFT_MEMBER_COUPON:
                    break;
                default:
                    log.info("赠送类型错误");
            }
        });
    }

    private void handelGiftCard(RechargeThresholdVO thresholdVO, RechargeGiftEntity gift, Map<String, HsaCardBaseInfo> cardGuidMap) {
        HsaCardBaseInfo cardBaseInfo = cardGuidMap.get(gift.getCardGuid());
        if (ObjectUtils.isEmpty(cardBaseInfo)) {
            log.warn(MemberAccountExceptionEnum.GIFT_CARD_NOT_EXIST.getDes());
            return;
        }
        if (StringUtils.isEmpty(thresholdVO.getGiftCardName())) {
            thresholdVO.setGiftCardName(cardBaseInfo.getCardName());
            return;
        }
        List<String> giftCardNameList = Arrays.stream(thresholdVO.getGiftCardName().split(StringConstant.MARK))
                .distinct().collect(Collectors.toList());
        if (!giftCardNameList.contains(cardBaseInfo.getCardName())) {
            giftCardNameList.add(cardBaseInfo.getCardName());
        }
        thresholdVO.setGiftCardName(StringBaseHandlerUtil.arrayConvert(giftCardNameList));
    }

    /**
     * 充值活动说明
     *
     * @return 充值活动说明
     */
    public List<RechargeActivityDescVO> buildActivityDesc(List<RechargeGiftActivityVO> rechargeGiftActivityList) {
        List<RechargeActivityDescVO> rechargeActivityDescList = new ArrayList<>();
        // 没有会员卡则不处理充值活动
        if (CollUtil.isNotEmpty(rechargeGiftActivityList)) {
            rechargeGiftActivityList.forEach(activity -> {
                RechargeActivityDescVO descVO = new RechargeActivityDescVO();
                descVO.setActivityGuid(activity.getGuid());
                descVO.setActivityName(activity.getActivityName());
                List<RechargeGiftThresholdEntity> giftThresholdList = activity.getRechargeGiftThresholdList();
                // 充值满50元，赠送金额x50元（发放后24小时候生效）；（可参与1次）
                // 充值满100元，赠送金额x50元、成长值x200；
                // 充值满200元，赠送金额x50元、成长值x200、积分x200、会员折扣卡x1；
                List<String> rechargeThresholdDescList = new ArrayList<>();
                giftThresholdList.forEach(threshold -> {
                    String thresholdDescStr = buildThresholdDescStr(threshold);
                    rechargeThresholdDescList.add(thresholdDescStr);
                });
                descVO.setRechargeThresholdDescList(rechargeThresholdDescList);
                String activityTimeDesc = getActivityTimeDesc(activity);
                descVO.setActivityTimeDesc(activityTimeDesc);
                descVO.setActivityDesc(activity.getDescription());
                descVO.setRechargeFilterType(activity.getRechargeFilterType());
                descVO.setTerminalType(activity.getTerminalType());
                descVO.setLabelGuidJson(activity.getLabelGuidJson());
                descVO.setGradeGuidJson(activity.getGradeGuidJson());
                descVO.setDescriptionDay(activity.getDescriptionDay());
                rechargeActivityDescList.add(descVO);
            });
        }
        return rechargeActivityDescList;
    }

    private String getActivityTimeDesc(RechargeGiftActivityVO activity) {
        String activityStartTime = DateUtil.formatLocalDateTime(activity.getActivityStartTime(), StringConstant.FORMAT_TIME);
        String activityEndTime = DateUtil.formatLocalDateTime(activity.getActivityEndTime(), StringConstant.FORMAT_TIME);
        return activityStartTime + StringConstant.TO + activityEndTime;
    }

    private String buildThresholdDescStr(RechargeGiftThresholdEntity threshold) {
        StringBuilder thresholdDesc = new StringBuilder();
        thresholdDesc.append(StringConstant.RECHARGE);
        thresholdDesc.append(GiftThresholdEnum.getNameByCode(threshold.getGiftThresholdType()));
        thresholdDesc.append(threshold.getGiftThresholdAmount());
        thresholdDesc.append(StringConstant.YUAN);
        thresholdDesc.append(StringConstant.CHINESE_COMMA);

        String giftDesc = buildGiftDesc(threshold);
        thresholdDesc.append(giftDesc);
        thresholdDesc.append(StringConstant.CHINESE_SEMICOLON);
        if (Objects.equals(EquitiesLimitedTypeEnum.LIMITED.getCode(), threshold.getGiftThresholdLimitType())) {
            thresholdDesc.append(StringConstant.LEFT_BRACKET);
            thresholdDesc.append(String.format(StringConstant.JOIN_TIMES, threshold.getGiftThresholdLimitSum()));
            thresholdDesc.append(StringConstant.RIGHT_BRACKET);
        }
        return thresholdDesc.toString();
    }

    private String buildGiftDesc(RechargeGiftThresholdEntity threshold) {
        List<String> giftEntityStrList = new ArrayList<>();
        List<RechargeGiftEntity> giftEntityList = threshold.getRechargeGiftEntityList();
        giftEntityList.forEach(gift -> {
            String giftEntityStr = buildGiftEntityStr(gift);
            giftEntityStrList.add(giftEntityStr);
        });
        return StringBaseHandlerUtil.arrayConvert(giftEntityStrList);
    }

    private String buildGiftEntityStr(RechargeGiftEntity gift) {
        StringBuilder giftMoneyAmount = new StringBuilder();
        GiftTypeEnum giftTypeEnum = GiftTypeEnum.getEnum(gift.getGiftType());
        switch (Objects.requireNonNull(giftTypeEnum)) {
            case GIFT_MONEY_AMOUNT:
                giftMoneyAmount.append(String.format(StringConstant.RECHARGE_GIFT_AMOUNT, gift.getMoneyAmount()));
                break;
            case GIFT_INTEGRAL_VALUE:
                giftMoneyAmount.append(StringConstant.INTEGRAL_VALUE_X);
                giftMoneyAmount.append(gift.getIntegralValue());
                break;
            case GIFT_MEMBER_CARD:
                jointGiftCard(gift, giftMoneyAmount);
                break;
            case GIFT_MEMBER_GROWTH:
                giftMoneyAmount.append(StringConstant.GROWTH_VALUE_X);
                giftMoneyAmount.append(gift.getGrowthValue());
                break;
            case GIFT_MEMBER_COUPON:
                giftMoneyAmount.append(StringConstant.COUPON_GIFT);
                for (int i = 0; i < gift.getCouponGuidList().size(); i++) {
                    CouponPackageDetailsDTO dto = gift.getCouponGuidList().get(i);
                    giftMoneyAmount.append(dto.getCouponName())
                            .append("*")
                            .append(dto.getNum());
                    if (i < gift.getCouponGuidList().size() - 1) {
                        giftMoneyAmount.append(StringConstant.MARK);
                    }
                }
                break;
            default:
                log.warn("任务说明错误赠送类型");
        }
        if (Objects.equals(GiftEffectiveEnum.FIXED_EFFECTIVE.getCode(), gift.getMoneyEffectiveType())) {
            giftMoneyAmount.append(StringConstant.LEFT_BRACKET);
            if (Objects.equals(GiftEffectiveUnitEnum.HOUR_EFFECTIVE.getCode(), gift.getMoneyEffectiveUnitType())) {
                giftMoneyAmount.append(String.format(StringConstant.GET_EXPIRE_HOUR, gift.getMoneyEffectiveValue()));
            }
            if (Objects.equals(GiftEffectiveUnitEnum.DAY_EFFECTIVE.getCode(), gift.getMoneyEffectiveUnitType())) {
                giftMoneyAmount.append(String.format(StringConstant.GET_EXPIRE_DAY, gift.getMoneyEffectiveValue()));
            }
            giftMoneyAmount.append(StringConstant.RIGHT_BRACKET);
        }
        return giftMoneyAmount.toString();
    }

    private void jointGiftCard(RechargeGiftEntity gift, StringBuilder giftMoneyAmount) {
        HsaCardBaseInfo cardBaseInfo = cardBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(HsaCardBaseInfo::getGuid, gift.getCardGuid()));
        if (ObjectUtils.isEmpty(cardBaseInfo)) {
            log.warn(MemberAccountExceptionEnum.GIFT_CARD_NOT_EXIST.getDes());
            return;
        }
        giftMoneyAmount.append(cardBaseInfo.getCardName());
        giftMoneyAmount.append(StringConstant.X_ONE);
    }

    private List<RechargeGiftActivityVO> getRechargeGiftActivityVOList(String memberInfoGuid, String cardGuid,
                                                                       HsaOperationMemberInfo operationMemberInfo,
                                                                       String storeGuid) {
        return getRechargeGiftActivityVOList(memberInfoGuid, Collections.singletonList(cardGuid), operationMemberInfo, storeGuid);
    }

    private List<RechargeGiftActivityVO> getRechargeGiftActivityVOList(String memberInfoGuid,
                                                                       List<String> cardGuids,
                                                                       HsaOperationMemberInfo operationMemberInfo,
                                                                       String storeGuid) {
        TerRechargeGiftActivityQO activityQO = new TerRechargeGiftActivityQO();
        activityQO.setMemberCardGuidList(cardGuids);
        if (StringUtils.isNotBlank(memberInfoGuid)) {
            activityQO.setMemberInfoGuid(memberInfoGuid);
            if (Objects.nonNull(operationMemberInfo)) {
                activityQO.setMemberGradeGuid(operationMemberInfo.getMemberGradeInfoGuid());
            }

            List<MemberLabelVO> labelVOList = memberLabelMapper.listMemberLabel(memberInfoGuid);
            List<String> memberLabelGuidList = labelVOList.stream()
                    .map(MemberLabelVO::getLabelGuid)
                    .distinct()
                    .collect(Collectors.toList());
            activityQO.setMemberLabelGuidList(memberLabelGuidList);
        }
        activityQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        activityQO.setStoreGuid(storeGuid);
        activityQO.setSource(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
        activityQO.setIsUnderway(BooleanEnum.TRUE.getCode());
        Long startTime = System.currentTimeMillis();

        List<RechargeGiftActivityVO> rechargeGiftActivityVOS = Lists.newArrayList();

        List<TerRechargeGiftActivityVO> rechargeGiftActivityList = marketingFeign.listSuitableNew(activityQO);
        log.warn("会员满足的所有活动，rechargeGiftActivityList={},activityQO={}", JacksonUtils.writeValueAsString(rechargeGiftActivityList),
                JacksonUtils.writeValueAsString(activityQO));
        Long endTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>远程耗时：    " + (endTime - startTime));

        if (CollUtil.isNotEmpty(rechargeGiftActivityList)) {
            for (TerRechargeGiftActivityVO terRechargeGiftActivityVO : rechargeGiftActivityList) {
                RechargeGiftActivityVO rechargeGiftActivityVO = new RechargeGiftActivityVO();
                BeanUtils.copyProperties(terRechargeGiftActivityVO, rechargeGiftActivityVO);
                rechargeGiftActivityVOS.add(rechargeGiftActivityVO);
            }
        }

        return rechargeGiftActivityVOS;
    }

    private HsaOperationMemberInfo getHsaOperationMemberInfo(String memberInfoGuid) {
        return operationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getGuid, memberInfoGuid)
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    public RechargeThresholdVO calculatePreMoney(MemberCalculatePreMoneyQO preMoneyQO) {
        RechargeThresholdVO thresholdVO = new RechargeThresholdVO();
        thresholdVO.setRechargeMoney(preMoneyQO.getRechargeMoney());
        thresholdVO.setGiftMoney(BigDecimal.ZERO);
        thresholdVO.setGiftIntegral(ZERO);
        thresholdVO.setGiftGrowth(ZERO);
        thresholdVO.setPreMoney(preMoneyQO.getRechargeMoney());

        HsaOperationMemberInfo operationMemberInfo = null;
        if (StringUtils.isNotEmpty(preMoneyQO.getMemberInfoGuid())) {
            operationMemberInfo = getHsaOperationMemberInfo(preMoneyQO.getMemberInfoGuid());
        }

        // 没有会员卡则不处理充值活动
        if (!StringUtils.isEmpty(preMoneyQO.getCardGuid())) {

            List<RechargeGiftActivityVO> rechargeGiftActivityList = getRechargeGiftActivityVOList(
                    preMoneyQO.getMemberInfoGuid(),
                    Collections.singletonList(preMoneyQO.getCardGuid()),
                    operationMemberInfo,
                    preMoneyQO.getStoreGuid());

            if (CollectionUtils.isEmpty(rechargeGiftActivityList)) {
                return thresholdVO;
            }


            Map<String, HsaCardBaseInfo> finalCardGuidMap = getHsaCardBaseInfoMap(rechargeGiftActivityList);
            rechargeGiftActivityList.forEach(activity -> calculateGiftActivity(thresholdVO, preMoneyQO.getRechargeMoney(), activity, preMoneyQO.getMemberInfoGuid(), finalCardGuidMap, preMoneyQO.getMemberInfoCardGuid()));
            thresholdVO.setPreMoney(preMoneyQO.getRechargeMoney().add(thresholdVO.getGiftMoney()));
        }
        return thresholdVO;
    }

    private Map<String, HsaCardBaseInfo> getHsaCardBaseInfoMap(List<RechargeGiftActivityVO> rechargeGiftActivityList) {
        Set<String> cardGuidSet = new HashSet<>();
        Map<String, HsaCardBaseInfo> cardGuidMap = new HashMap<>();
        for (RechargeGiftActivityVO rechargeGiftActivityVO : rechargeGiftActivityList) {
            forRechargeGiftActivity(cardGuidSet, rechargeGiftActivityVO);
        }

        if (CollUtil.isNotEmpty(cardGuidSet)) {
            cardGuidMap = cardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                            .in(HsaCardBaseInfo::getGuid, cardGuidSet))
                    .stream()
                    .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return cardGuidMap;
    }

    private static void forRechargeGiftActivity(Set<String> cardGuidSet, RechargeGiftActivityVO rechargeGiftActivityVO) {
        if (CollUtil.isNotEmpty(rechargeGiftActivityVO.getRechargeGiftThresholdList())) {
            for (RechargeGiftThresholdEntity rechargeGiftThresholdEntity : rechargeGiftActivityVO.getRechargeGiftThresholdList()) {
                if (CollUtil.isNotEmpty(rechargeGiftThresholdEntity.getRechargeGiftEntityList())) {
                    for (RechargeGiftEntity rechargeGiftEntity : rechargeGiftThresholdEntity.getRechargeGiftEntityList()) {
                        if (StringUtils.isNotEmpty(rechargeGiftEntity.getCardGuid())) {
                            cardGuidSet.add(rechargeGiftEntity.getCardGuid());
                        }
                    }
                }
            }
        }
    }
}
