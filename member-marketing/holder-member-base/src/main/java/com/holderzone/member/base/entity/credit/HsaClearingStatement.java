package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.enums.credit.PayStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 结算单
 * @author: pan tao
 * @create: 2022-06-13 15:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaClearingStatement implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 挂账信息guid
     */
    private String creditInfoGuid;

    /**
     * 结算单编号
     */
    private String clearingStatementNumber;

    /**
     * 结算账户
     */
    private String clearingAccount;

    /**
     * 开始结算周期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startClearingDate;

    /**
     * 结束结算周期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endClearingDate;

    /**
     * 本期应收
     */
    private BigDecimal currentPeriodReceivable;

    /**
     * 应收调整
     */
    private BigDecimal receivableAdjust;

    /**
     * 本期实收
     */
    private BigDecimal currentPeriodOfficialReceipts;

    /**
     * 生成结算单操作人
     */
    private String createClearingStatementUser;

    /**
     * 对账状态 0:待确认 1:已确认 2:自动确认
     *
     * @see com.holderzone.member.common.enums.credit.ReconciliationStatusEnum
     */
    private Integer reconciliationStatus;

    /**
     * 自动确认时间
     */
    private LocalDateTime automaticAffirmDate;

    /**
     * 结算状态 0:未支付 1:已支付 2:已作废
     *
     * @see PayStatusEnum
     */
    private Integer payStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime affirmDate;

    /**
     * 确认备注
     */
    private String affirmRemark;

    /**
     * 收款方式
     */
    private String payeeWay;

    /**
     * 收款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payeeDate;

    /**
     * 收款备注
     */
    private String payeeRemark;

    /**
     * 操作人(收款人或作废人)
     */
    private String operationUser;

    /**
     * 操作时间(作废时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationDate;

    /**
     * 作废备注
     */
    private String cancellationRemark;

}
