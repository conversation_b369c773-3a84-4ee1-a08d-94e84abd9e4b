package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员微信公众号关联表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberInfoWxMp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * appId
     */
    private String appId;

    /**
     * 微信用户union_id
     */
    private String unionId;

    /**
     * 微信用户open_id
     */
    private String openId;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
