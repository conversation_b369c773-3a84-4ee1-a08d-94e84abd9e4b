package com.holderzone.member.base.entity.member;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HsaCustomizeField {

    private Long id;

    /**
     * 会员GUID
     */
    private String memberGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 字段名称
     */
    private String field;

    /**
     * 字段内容
     */
    private String content;

    /**
     * 信息格式
     * 例如：图片、文本或者自定义类型
     */
    private String infoFormat;
}
