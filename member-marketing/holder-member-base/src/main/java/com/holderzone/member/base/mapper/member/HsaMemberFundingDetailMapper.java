package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.card.AppletBalanceRecordDTO;
import com.holderzone.member.common.dto.card.FundingDTO;
import com.holderzone.member.common.qo.member.BalanceRecordQO;
import com.holderzone.member.common.vo.card.BalanceDetailTotalVO;
import com.holderzone.member.common.vo.member.BalanceRecordVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * author: pantao
 */
public interface HsaMemberFundingDetailMapper extends HolderBaseMapper<HsaMemberFundingDetail> {

    /**
     * 查询余额记录
     *
     * @param request 余额记录查询请求qo
     * @return 查询结果
     */
    List<BalanceRecordVO> queryBalanceRecordList(@Param("request") BalanceRecordQO request);

    HsaMemberFundingDetail queryByConsumptionGuid(@Param("memberConsumptionGuid") String memberConsumptionGuid);

    List<BalanceRecordVO> pageQueryBalanceRecordList(@Param("request") BalanceRecordQO request);

    int queryBalanceRecordCount(@Param("request") BalanceRecordQO request);

    /**
     * 消费明细表，增加会员guid
     * @param memberGuid
     * @param memberInfoCardGuid
     */
    void updateMemberGuid(@Param("memberGuid") String memberGuid, @Param("memberInfoCardGuid") String memberInfoCardGuid);

    HsaMemberFundingDetail getLastDetailByMemberCardGuid(@Param("memberInfoCardGuid") String memberInfoCardGuid);

    BigDecimal getAllGift(@Param("memberGuid") String memberGuid,@Param("type") Integer type);

    BigDecimal getAllSubsidy(@Param("memberGuid") String memberGuid);

    /**
     * 查询会员卡下即将过期的补贴余额
     *
     * @param cardGuid 会员卡guid
     * @return 查询结果
     */
    List<String> soonExpiredSubsidyAmount(@Param("cardGuid") String cardGuid);

    List<FundingDTO> getCurrentMonthConsume(@Param("cardGuid") String cardGuid, @Param("startTime") String startTime,
                                            @Param("endTime") String endTime,
                                            @Param("memberGuid") String memberGuid);

    /**
     * 获取用户卡是否绑定
     */
    int getCardIsMemberGuid(@Param("cardGuid") String cardGuid);

    /**
     * 查询余额总计
     *
     * @param cardGuid 会员卡guid
     * @return 查询结果
     */
    BalanceDetailTotalVO findNewestBalance(@Param("cardGuid") String cardGuid);

    /**
     * 小程序余额记录
     *
     * @param cardGuid 会员卡guid
     * @return 查询结果
     */
    List<AppletBalanceRecordDTO> findAppletBalanceRecord(@Param("cardGuid") String cardGuid,
                                                         @Param("memberGuid") String memberGuid);

    void updateCompany(@Param("memberInfo") HsaOperationMemberInfo hsaOperationMemberInfo);
}
