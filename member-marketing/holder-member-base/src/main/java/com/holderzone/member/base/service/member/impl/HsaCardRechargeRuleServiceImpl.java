package com.holderzone.member.base.service.member.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.base.entity.member.HsaCardRechargeRule;
import com.holderzone.member.base.mapper.member.HsaCardRechargeRuleMapper;
import com.holderzone.member.base.service.member.HsaCardRechargeRuleService;
import com.holderzone.member.base.transform.member.MemberCardTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.qo.member.HsaCardRechargeRuleQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.member.HsaCardRechargeRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ProjectName: member-marketing
 * @ClassName: hsaCardRechargeRule
 * @Author: 张林
 * @Description: 会员卡充值规则service
 * @Version: 1.0.1
 */
@Slf4j
@Service
public class HsaCardRechargeRuleServiceImpl extends HolderBaseServiceImpl<HsaCardRechargeRuleMapper, HsaCardRechargeRule> implements HsaCardRechargeRuleService {

    @Resource
    private HsaCardRechargeRuleMapper hsaCardRechargeRuleMapper;

//    @Resource
//    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    public static final String CHARACTER = ",";

    public static final Integer CUSTOMIZE_AMOUNT = 1;  // 1代表充值类型为：自定义充值  或者 充值金额有限制

    @Override
    public int addCardRechargeRule(HsaCardRechargeRuleQO hsaCardRechargeRuleQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaCardRechargeRule hsaCardRechargeRule = new HsaCardRechargeRule();
        BeanUtil.copyProperties(hsaCardRechargeRuleQO, hsaCardRechargeRule);

        String guid = guidGeneratorUtil.getStringGuid(HsaCardRechargeRule.class.getSimpleName());
        hsaCardRechargeRule.setGuid(guid);
        hsaCardRechargeRule.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        return hsaCardRechargeRuleMapper.insert(hsaCardRechargeRule);
    }

    @Override
    public int updateCardRechargeRule(HsaCardRechargeRuleQO hsaCardRechargeRuleQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        //校验自定义充值金额
        verifyMoney(hsaCardRechargeRuleQO);
        HsaCardRechargeRule hsaCardRechargeRule = hsaCardRechargeRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardRechargeRule>()
                        .eq(HsaCardRechargeRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .eq(HsaCardRechargeRule::getGuid, hsaCardRechargeRuleQO.getGuid()));

        BeanUtil.copyProperties(hsaCardRechargeRuleQO, hsaCardRechargeRule);
        hsaCardRechargeRule.setRechargeMoneys(StringUtils.join(hsaCardRechargeRuleQO.getRechargeMoneyList(), CHARACTER));
        hsaCardRechargeRule.setRechargeType(StringUtils.join(hsaCardRechargeRuleQO.getRechargeTypeList(), CHARACTER));
        return hsaCardRechargeRuleMapper.update(hsaCardRechargeRule,
                new LambdaQueryWrapper<HsaCardRechargeRule>()
                        .eq(HsaCardRechargeRule::getGuid, hsaCardRechargeRuleQO.getGuid())
                        .eq(HsaCardRechargeRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
    }

    @Override
    public HsaCardRechargeRuleVO getRechargeRule() {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaCardRechargeRule hsaCardRechargeRule = hsaCardRechargeRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardRechargeRule>()
                        .eq(HsaCardRechargeRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        if (ObjectUtils.isEmpty(hsaCardRechargeRule)) {
            hsaCardRechargeRule = new HsaCardRechargeRule();
            String guid = guidGeneratorUtil.getStringGuid(HsaCardRechargeRule.class.getSimpleName());
            hsaCardRechargeRule.setGuid(String.valueOf(guid));
            hsaCardRechargeRule.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
            //充值类型  1:任意金额;2:固定金额  默认为 1
            hsaCardRechargeRule.setRechargeType(String.valueOf(CUSTOMIZE_AMOUNT));
            hsaCardRechargeRule.setRechargeLimit(BooleanEnum.FALSE.getCode());
            hsaCardRechargeRuleMapper.insert(hsaCardRechargeRule);
        }
        HsaCardRechargeRuleVO hsaCardRechargeRuleVO = MemberCardTransform.INSTANCE.hsaCardRechargeRuleToVO(hsaCardRechargeRule);
        hsaCardRechargeRuleVO.setRechargeMoneyList(conversion(hsaCardRechargeRule.getRechargeMoneys()));
        hsaCardRechargeRuleVO.setRechargeTypeList(conversion(hsaCardRechargeRule.getRechargeType()));
        // 当前接口要求返回为null（如果需要Description，需另写逻辑）
        hsaCardRechargeRuleVO.setDescription(null);
        return hsaCardRechargeRuleVO;
    }

    @Override
    public List<HsaCardRechargeRuleVO> list() {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaCardRechargeRule> hsaCardRechargeRules = hsaCardRechargeRuleMapper.selectList(
                new LambdaQueryWrapper<HsaCardRechargeRule>()
                        .eq(HsaCardRechargeRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        return MemberCardTransform.INSTANCE.hsaCardRechargeRuleToVO(hsaCardRechargeRules);
    }

//    @Override
//    public void refreshLabel(List<String> memberGuid, List<HsaLabelSetting> hsaLabelSettings, int assignLabel) {
//        hsaLabelSettingService.refreshLabel(memberGuid, hsaLabelSettings, BooleanEnum.FALSE.getCode());
//    }

    /**
     * 将字符串转换成集合
     *
     * @param message 字符串
     * @return 操作结果
     */
    public List<Integer> conversion(String message) {
        List<Integer> list = new ArrayList<>();
        if (StringUtils.isEmpty(message)) {
            return list;
        }
        String[] messageList = message.split(CHARACTER);
        return Arrays.stream(messageList).filter(x -> !StringUtils.isEmpty(x))
                .map(Integer::parseInt).limit(6) // 充值金额只需要前六个数据
                .collect(Collectors.toList());
    }

    /**
     * 充值金额有限制时
     * 单次最少充值金额＜单次最多充值金额
     *
     * @param hsaCardRechargeRuleQO
     */
    public void verifyMoney(HsaCardRechargeRuleQO hsaCardRechargeRuleQO) {
        List<Integer> rechargeTypeList = hsaCardRechargeRuleQO.getRechargeTypeList();
        if (CollectionUtils.isEmpty(rechargeTypeList)) {
            return;
        }

        Optional<Integer> type = rechargeTypeList.stream()  //判断充值类型是否为：自定义充值
                .filter(x -> !ObjectUtils.isEmpty(x) && CUSTOMIZE_AMOUNT.equals(x)).findFirst();

        Optional<Integer> limit = Optional.ofNullable(hsaCardRechargeRuleQO.getRechargeLimit())  //判断自定义充值是否为：有限制
                .filter(x -> !ObjectUtils.isEmpty(x) && CUSTOMIZE_AMOUNT.equals(x));

        // 如果充值类型是自定义并且开启了充值金额限制
        // 那么 单次最少充值金额＜单次最多充值金额
        if (type.isPresent() && limit.isPresent()) {
            BigDecimal rechargeMin = BigDecimalUtil.nonNullValue(hsaCardRechargeRuleQO.getRechargeMin());
            BigDecimal rechargeMax = BigDecimalUtil.nonNullValue(hsaCardRechargeRuleQO.getRechargeMax());
            if (BigDecimalUtil.lessEqual(rechargeMax, rechargeMin)) {
                throw new BusinessException(MemberAccountExceptionEnum.ERROR_MIN_LESS_THAN_DEPOSIT.getDes());
            }
        }
    }
}

