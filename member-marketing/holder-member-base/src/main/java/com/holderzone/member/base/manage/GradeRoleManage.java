package com.holderzone.member.base.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.grade.HsaControlledGradeState;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.member.HsaMemberEducationService;
import com.holderzone.member.base.transform.member.MemberBusinessTransform;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.member.EnableEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.GradeRoleReqVO;
import com.holderzone.member.common.vo.grade.GradeRoleRespVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年03月29日 下午12:22
 * @description 角色体系管理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class GradeRoleManage {

    private HsaControlledGradeStateService gradeStateService;

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Transactional(rollbackFor = Exception.class)
    public void save(GradeRoleReqVO gradeRoleReqVO) {
        gradeRoleReqVO.validatedSave();
        HsaControlledGradeState one = gradeStateService.getOne(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getRoleType, gradeRoleReqVO.getRoleType())
                .eq(HsaControlledGradeState::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaControlledGradeState::getIsDelete, 0)
        );
        if (!ObjectUtils.isEmpty(one)) {
            throw new MemberBaseException("体系" + gradeRoleReqVO.getRoleType() + "已存在");
        }
        HsaControlledGradeState gradeState = MemberBusinessTransform.INSTANCE.gradeRoleReqVO2GradeRoleDO(gradeRoleReqVO);
        gradeState.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberEducationService.class.getSimpleName()));
        gradeState.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //默认启用
        gradeState.setState(EnableEnum.ENABLE.getCode());
        gradeState.setOperatorName(ThreadLocalCache.getUserName());
        gradeStateService.save(gradeState);
    }

    public GradeRoleRespVO get(String guid) {
        HsaControlledGradeState gradeRole = gradeStateService.getById(guid);
        return MemberBusinessTransform.INSTANCE.gradeRoleDO2GradeRoleRespVO(gradeRole);
    }

    public void update(GradeRoleReqVO gradeRoleReqVO) {
        gradeRoleReqVO.validatedUpdate();
        int count = gradeStateService.count(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getRoleType, gradeRoleReqVO.getRoleType())
                .eq(HsaControlledGradeState::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaControlledGradeState::getIsDelete, 0)
                .ne(HsaControlledGradeState::getGuid, gradeRoleReqVO.getGuid())
        );
        if (count > 0) {
            throw new MemberBaseException("体系" + gradeRoleReqVO.getRoleType() + "已存在");
        }
        HsaControlledGradeState gradeState = MemberBusinessTransform.INSTANCE.gradeRoleReqVO2GradeRoleDO(gradeRoleReqVO);
        gradeState.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        gradeState.setOperatorName(ThreadLocalCache.getUserName());
        gradeStateService.updateById(gradeState);
    }

    public void delete(String guid) {
        gradeStateService.removeById(guid);
    }

    public List<GradeRoleRespVO> list() {
        List<HsaControlledGradeState> list = gradeStateService.list(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaControlledGradeState::getIsDelete, 0));
        return MemberBusinessTransform.INSTANCE.gradeRoleDOList2GradeRoleRespVOList(list);
    }
}
