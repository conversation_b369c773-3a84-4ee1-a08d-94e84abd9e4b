package com.holderzone.member.base.service.pay.convert;

import com.holderzone.member.base.entity.grade.HsaMemberGradePayRecord;
import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.WeChatAuthParamDTO;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.enums.mall.order.PayStateEnum;
import com.holderzone.member.common.util.wechat.WeChatUtil;
import com.holderzone.member.common.vo.grade.PayMemberGradeDTO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class GradeTradeConvert {

    private GradeTradeConvert() {

    }

    public static AggPayPollingRespDTO convertPolling2Callback(AggPayPollingRespDTO pollingDTO) {
        AggPayPollingRespDTO aggPayCallbackDTO = new AggPayPollingRespDTO();
        aggPayCallbackDTO.setOrderNo(pollingDTO.getOrderNo());
        aggPayCallbackDTO.setBankTransactionId(pollingDTO.getBankTransactionId());
        aggPayCallbackDTO.setOrderGUID(pollingDTO.getOrderGUID());
        aggPayCallbackDTO.setOrderHolderNo(pollingDTO.getOrderHolderNo());
        return aggPayCallbackDTO;
    }

    public static GradePrePayDTO convertFromTrade(PayMemberGradeDTO saveMemberGradeDTO, String synKey) {
        GradePrePayDTO ag = new GradePrePayDTO();
        ag.setAmount(saveMemberGradeDTO.getPrice());
        ag.setSynKey(synKey);
        ag.setStoreName(saveMemberGradeDTO.getStoreName());
        ag.setStoreGuid(saveMemberGradeDTO.getStoreGuid());
        ag.setEnterpriseGuid(saveMemberGradeDTO.getEnterpriseGuid());
        ag.setOperSubjectGuid(saveMemberGradeDTO.getOperSubjectGuid());
        ag.setAppId(saveMemberGradeDTO.getAppId());
        ag.setSubOpenId(saveMemberGradeDTO.getSubOpenId());
        return ag;
    }

    public static SaasPollingOdooDTO convertPollingRequest(OrderPollingDTO orderPolling) {
        SaasPollingOdooDTO saasPollingOdooDTO = new SaasPollingOdooDTO();
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setEnterpriseGuid(orderPolling.getEnterpriseGuid());
        paymentInfoDTO.setAppSecret(orderPolling.getAppSecret());
        paymentInfoDTO.setAppId(orderPolling.getAppId());
        saasPollingOdooDTO.setPaymentInfo(paymentInfoDTO);
        saasPollingOdooDTO.setOrderGuid(orderPolling.getOrderGuid());
        saasPollingOdooDTO.setPayGuid(orderPolling.getPayGuid());
        saasPollingOdooDTO.setEnterpriseGuid(orderPolling.getEnterpriseGuid());
        return saasPollingOdooDTO;
    }

    public static HsaMemberGradePayRecord convertTransactionRecord(GradePrePayDTO aggPrePayDTO, String payGuid) {
        HsaMemberGradePayRecord record = new HsaMemberGradePayRecord();
        record.setGuid(payGuid);
        record.setOperSubjectGuid(aggPrePayDTO.getOperSubjectGuid());
        record.setIsDelete(false);
        record.setState(PayStateEnum.READY.getCode());
        record.setPayTime(LocalDateTime.now());
        record.setStoreName(aggPrePayDTO.getStoreName());
        record.setPayAmount(aggPrePayDTO.getAmount());
        return record;
    }

    public static OrderPollingDTO convertPolling(GradePrePayDTO gradePrePayDTO, PaySettingBaseRes aggAccountSet, String payGuid) {
        OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setPayGuid(payGuid);
        orderPollingDTO.setOrderGuid(gradePrePayDTO.getSynKey());
        orderPollingDTO.setAppId(aggAccountSet.getAppId());
        orderPollingDTO.setAppSecret(aggAccountSet.getAppSecret());
        orderPollingDTO.setEnterpriseGuid(gradePrePayDTO.getEnterpriseGuid());
        return orderPollingDTO;
    }

    public static SaasAggPayOdooDTO convertPayRequest(GradePrePayDTO aggPrePayDTO, PaySettingBaseRes aggAccountSet
            , String payGuid, MemberGradePerPayVO memberGradeUpgradeVO) {
        SaasAggPayOdooDTO saasAggPayOdooDTO = new SaasAggPayOdooDTO();
        long currentTime = System.currentTimeMillis();
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setAppId(aggAccountSet.getAppId());
        paymentInfoDTO.setAppSecret(aggAccountSet.getAppSecret());
        paymentInfoDTO.setEnterpriseGuid(aggPrePayDTO.getEnterpriseGuid());

        AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setOrderGUID(aggPrePayDTO.getSynKey());
        aggPayPreTradingReqDTO.setPayGUID(payGuid);
        //聚合支付以分为单位
        aggPayPreTradingReqDTO.setAmount(aggPrePayDTO.getAmount());
        aggPayPreTradingReqDTO.setDeveloperId(aggAccountSet.getDeveloperId());
        aggPayPreTradingReqDTO.setEnterpriseGuid(aggPrePayDTO.getEnterpriseGuid());
        aggPayPreTradingReqDTO.setEnterpriseName(aggAccountSet.getEnterpriseName());
        aggPayPreTradingReqDTO.setSubAppId(aggAccountSet.getSubAppId());
        aggPayPreTradingReqDTO.setTimestamp(currentTime);
        aggPayPreTradingReqDTO.setSubOpenId(aggPrePayDTO.getSubOpenId());
        aggPayPreTradingReqDTO.setStoreName(aggPrePayDTO.getStoreName());
        WeChatAuthParamDTO weChatAuthParamDTO = new WeChatAuthParamDTO();
        weChatAuthParamDTO.setAppId(aggAccountSet.getAppId());
        weChatAuthParamDTO.setAppSecret(aggAccountSet.getAppSecret());
        weChatAuthParamDTO.setDeveloperKey(aggAccountSet.getDeveloperKey());
        weChatAuthParamDTO.setOrderGUID(aggPrePayDTO.getSynKey());
        weChatAuthParamDTO.setPayGUID(payGuid);
        weChatAuthParamDTO.setTimestamp(currentTime + "");
        aggPayPreTradingReqDTO.setSignature(WeChatUtil.getWeChatAuthSignature(weChatAuthParamDTO));
        aggPayPreTradingReqDTO.setBody("聚合支付订单：" + aggPrePayDTO.getSynKey());
        aggPayPreTradingReqDTO.setGoodsName("商品");
        aggPayPreTradingReqDTO.setPayPowerId("53");
        aggPayPreTradingReqDTO.setCurrency("RMB");

        saasAggPayOdooDTO.setPaymentInfo(paymentInfoDTO);
        saasAggPayOdooDTO.setIsQuickReceipt(false);
        saasAggPayOdooDTO.setIsLast(true);
        saasAggPayOdooDTO.setEnterpriseGuid(aggPrePayDTO.getEnterpriseGuid());
        saasAggPayOdooDTO.setEnterpriseName(aggAccountSet.getEnterpriseName());
        saasAggPayOdooDTO.setReqDTO(aggPayPreTradingReqDTO);
        saasAggPayOdooDTO.setRequestTimestamp(currentTime);


        memberGradeUpgradeVO.setAmount(aggPrePayDTO.getAmount());
        memberGradeUpgradeVO.setAppSecret(aggAccountSet.getAppSecret());
        memberGradeUpgradeVO.setDeveloperId(saasAggPayOdooDTO.getReqDTO().getDeveloperId());
        memberGradeUpgradeVO.setEnterpriseGuid(aggPrePayDTO.getEnterpriseGuid());
        memberGradeUpgradeVO.setEnterpriseName(saasAggPayOdooDTO.getReqDTO().getEnterpriseName());
        memberGradeUpgradeVO.setSynKey(aggPrePayDTO.getSynKey());
        memberGradeUpgradeVO.setPayGuid(payGuid);
        memberGradeUpgradeVO.setSignature(saasAggPayOdooDTO.getReqDTO().getSignature());
        return saasAggPayOdooDTO;
    }


}
