package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaMemberGradePriceDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.growth.MemberDiscountedPriceVO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员等级会员价周期累计明细表
 * @date 2022/2/8 16:39
 */
public interface HsaMemberGradePriceDetailMapper extends HolderBaseMapper<HsaMemberGradePriceDetail> {

    /**
     * 查询会员累计使用会员价
     * @param gradeEquitiesGuid 等级权益guid
     * @param memberInfoGuid 会员Guid
     * @return 操作结果
     */
    MemberDiscountedPriceVO selectDiscountedPrice(@Param("gradeEquitiesGuid") String gradeEquitiesGuid,
                                                  @Param("memberInfoGuid") String memberInfoGuid);
}
