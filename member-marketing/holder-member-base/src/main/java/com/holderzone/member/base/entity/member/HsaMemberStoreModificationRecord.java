package com.holderzone.member.base.entity.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberStoreModificationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private long id;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 门店变更之前的数据
     */
    private String beforeModification;

    /**
     * 门店变更之后的数据
     */
    private String afterModification;

    /**
     * 变更原因(商家解绑或者绑定-操作人员名称+账号)
     */
    private String reason;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
