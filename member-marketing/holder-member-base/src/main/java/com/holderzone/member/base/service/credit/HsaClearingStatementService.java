package com.holderzone.member.base.service.credit;

import com.holderzone.member.base.entity.credit.HsaClearingStatement;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.credit.*;
import com.holderzone.member.common.vo.credit.ClearingStatementDetailVO;
import com.holderzone.member.common.vo.credit.ClearingStatementTotalVO;
import com.holderzone.member.common.vo.credit.ClearingStatementUserVO;
import com.holderzone.member.common.vo.credit.OperateRecordVO;

import java.math.BigDecimal;
import java.util.List;


/**
 * @program: member-marketing
 * @description: 结算单service
 * @author: pan tao
 * @create: 2022-06-14 11:17
 */
public interface HsaClearingStatementService extends IHolderBaseService<HsaClearingStatement> {

    /**
     * 查询结算单使用人
     *
     * @param request 请求参数
     * @return 返回结果
     */
    ClearingStatementUserVO queryClearingStatementUser(CreditOrderRecordQO request);

    /**
     * 新增结算单
     *
     * @param request 新增结算单请求参数
     * @return 操作结果
     */
    boolean saveClearingStatement(AddClearingStatementQO request);

    /**
     * 查询小程序挂账结算单列表
     *
     * @param request 请求参数
     * @return 操作结果
     */
    PageResult queryClearingStatementList(AuditClearingStatementQO request);

    /**
     * 查询小程序挂账结算单列表total
     *
     * @param request 请求参数
     * @return 操作结果
     */
    ClearingStatementTotalVO queryClearingStatementTotal(AuditClearingStatementQO request);

    /**
     * 修改结算单对账状态
     *
     * @param request 请求参数
     * @return 操作结果
     */
    Boolean updateReconciliationStatus(ReconciliationStatusQO request);

    /**
     * 查询结算单详情
     *
     * @param clearingStatementNumber 结算单guid
     * @return 查询结果
     */
    ClearingStatementDetailVO queryClearingStatementDetail(String clearingStatementNumber);

    /**
     * 结算单操作记录
     *
     * @param clearingStatementNumber 结算单编号
     * @return 查询结果
     */
    List<OperateRecordVO> queryOperateRecord(String clearingStatementNumber);

    /**
     * 操作结算单订单
     *
     * @param request 操作结算单订单qo
     * @return 操作结果
     */
    boolean operateClearingStatementOrder(OperateClearingOrderQO request);

    /**
     * 应收调整
     *
     * @param request 操作结算单订单qo
     */
    void receivableAdjust(OperateClearingOrderQO request);

    /**
     * 查询实际挂账金额
     *
     * @param guid 挂账订单记录guid
     * @return 查询结果
     */
    BigDecimal queryRealityClearingAmount(String guid, String clearingStatementNumber);

    /**
     * 去收款
     *
     * @param request 去收款QO
     */
    boolean goToReceiving(GoToReceivingQO request);

    /**
     * 查询结算单列表
     *
     * @param request 查询结算单列表请求qo
     * @return 查询结果
     */
    PageResult queryClearingStatement(ClearingStatementQO request);

}
