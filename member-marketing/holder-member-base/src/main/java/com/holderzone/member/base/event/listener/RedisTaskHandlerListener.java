package com.holderzone.member.base.event.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 * Redis 监听器
 * @date 2022/4/13 14:04
 */

@Slf4j
@Service
public class RedisTaskHandlerListener {


    @Async("memberBaseThreadExecutor")
    public <T> void sendFixedGiftAmount(T t) {
        String memberCertifiedLabel = JSON.parseObject(Objects.requireNonNull(t).toString(), String.class);
        log.info(memberCertifiedLabel);
    }


    @Async("memberBaseThreadExecutor")
    public <T> void sendTest(T t) {
        String memberCertifiedLabel = JSON.parseObject(Objects.requireNonNull(t).toString(), String.class);
        log.info(memberCertifiedLabel);
    }


}

