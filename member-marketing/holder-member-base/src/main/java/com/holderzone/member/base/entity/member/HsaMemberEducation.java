package com.holderzone.member.base.entity.member;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 会员教育经历信息表
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberEducation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String guid;

    /**
     * 会员GUID
     */
    private String memberGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;


    /**
     * 学历
     */
    private String education;

    /**
     * 学校名称
     */
    private String school;

    /**
     * 专业
     */
    private String major;

    /**
     * 入学时间
     */
    private String admissionTime;

    /**
     * 毕业时间
     */
    private String graduationTime;
}
