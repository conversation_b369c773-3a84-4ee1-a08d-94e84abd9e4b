package com.holderzone.member.base.entity.grade;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员等级付费记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HsaMemberGradePayRecord对象", description = "会员等级付费记录表")
public class HsaMemberGradePayRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @TableLogic
    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    /**
     * @see com.holderzone.member.common.enums.mall.order.PayStateEnum
     */
    private Integer state;

    @ApiModelProperty(value = "会员GUID")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberInfoName;

    @ApiModelProperty(value = "会员等级名称")
    private String memberInfoGradeName;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "会员手机号")
    private String phoneNum;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    @ApiModelProperty(value = "角色类型")
    private String roleType;

    /**
     * 银行流水号
     */
    private String bankTransactionId;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expireTime;

    /**
     * 门店名
     */
    private String storeName;

    @ApiModelProperty(value = "有效数量")
    private Integer num;

    @ApiModelProperty(value = "有效期单位：3月 4年")
    private Integer unit;

    @ApiModelProperty(value = "付费时间")
    private LocalDateTime payTime;

    /**
     * 付费金额
     */
    private BigDecimal payAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 聚合支付单号
     */
    private String orderHolderNo;

    /**
     * 变动类型
     *
     * @see com.holderzone.member.common.enums.grade.GradeRecordChangeEnum
     */
    @ApiModelProperty(value = "变动类型")
    private Integer changeType;

    /**
     * 门店guid
     */
    private String storeGuid;

}
