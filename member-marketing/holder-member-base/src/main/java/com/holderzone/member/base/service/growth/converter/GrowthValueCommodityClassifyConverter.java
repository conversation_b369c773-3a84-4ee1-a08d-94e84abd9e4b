package com.holderzone.member.base.service.growth.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityClassify;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.growth.AppointClassifyGoodsDTO;
import com.holderzone.member.common.dto.growth.GoodsCategoryDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.StoreTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.StoreBoothCardRuleQO;
import com.holderzone.member.common.qo.growth.GrowthCommodityBaseQO;
import com.holderzone.member.common.qo.growth.GrowthValueStoreRuleQO;
import com.holderzone.member.common.qo.growth.GrowthValueTaskQO;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-05-31
 * @description
 */
public class GrowthValueCommodityClassifyConverter {

    private GrowthValueCommodityClassifyConverter(){
        throw new MemberBaseException("实例化对象异常");
    }

    /**
     * 英文逗号
     */
    private static final String COMMA = ",";

    public static List<HsaGrowthValueCommodityClassify> fromTaskAndGrowthValueCommodityList(String taskGuid,List<AppointClassifyGoodsDTO> appointGoodsTypeList){
        List<HsaGrowthValueCommodityClassify> commodityClassifylist = Lists.newArrayList();
        appointGoodsTypeList.forEach(e -> commodityClassifylist.add(fromTaskAndGrowthValueCommodity(taskGuid,e)));
        return commodityClassifylist;
    }

    private static HsaGrowthValueCommodityClassify fromTaskAndGrowthValueCommodity(String taskGuid, AppointClassifyGoodsDTO appointClassifyGoodsDTO) {
        appointClassifyGoodsDTO.verifyGoodsCategory();
        HsaGrowthValueCommodityClassify commodityClassify = new HsaGrowthValueCommodityClassify();
        commodityClassify.setTaskGuid(taskGuid);
        commodityClassify.setBusinessType(appointClassifyGoodsDTO.getBusinessType());
        commodityClassify.setGoodsCategory(JSONArray.toJSONString(appointClassifyGoodsDTO.getGoodsCategory()));
        commodityClassify.setStoreName(appointClassifyGoodsDTO.getStoreName());
        commodityClassify.setStoreGuid(appointClassifyGoodsDTO.getStoreGuid());
        commodityClassify.setStrategyId(appointClassifyGoodsDTO.getStrategyId());
        commodityClassify.setStrategyName(appointClassifyGoodsDTO.getStrategyName());

        List<Integer> childCategoryIds = appointClassifyGoodsDTO.getChildCategoryIds();
        if(CollectionUtil.isNotEmpty(childCategoryIds)){
            commodityClassify.setCategoryIds(JSONArray.toJSONString(Sets.newHashSet(childCategoryIds)));
        }else {
            commodityClassify.setCategoryIds(JSONArray.toJSONString(appointClassifyGoodsDTO.getGoodsCategory().stream().map(e -> Integer.parseInt(e.getCategoryId())).collect(Collectors.toSet())));
        }

        return commodityClassify;
    }

    public static List<AppointClassifyGoodsDTO> toGrowthValueCommodityList(List<HsaGrowthValueCommodityClassify> growthValueCommodityClassifyList) {
        List<AppointClassifyGoodsDTO> commodityClassifylist = Lists.newArrayList();
        growthValueCommodityClassifyList.forEach(e -> commodityClassifylist.add(toGrowthValueCommodity(e)));
        return commodityClassifylist;
    }

    private static AppointClassifyGoodsDTO toGrowthValueCommodity(HsaGrowthValueCommodityClassify commodityClassify) {
        AppointClassifyGoodsDTO appointClassifyGoods = new AppointClassifyGoodsDTO();

        appointClassifyGoods.setGoodsCategory(JSONArray.parseArray(commodityClassify.getGoodsCategory(), GoodsCategoryDTO.class));
        appointClassifyGoods.setStrategyName(commodityClassify.getStrategyName());
        appointClassifyGoods.setStrategyId(commodityClassify.getStrategyId());
        appointClassifyGoods.setStoreGuid(commodityClassify.getStoreGuid());
        appointClassifyGoods.setBusinessType(commodityClassify.getBusinessType());
        appointClassifyGoods.setStoreName(commodityClassify.getStoreName());
        return appointClassifyGoods;
    }

    public static void getGrowthValueTask(GrowthValueTaskQO growthValueTaskQO, HsaGrowthValueTask hsaGrowthValueTask, HeaderUserInfo headerUserInfo) {
        hsaGrowthValueTask.setTaskName(growthValueTaskQO.getTaskName());
        hsaGrowthValueTask.setTaskType(growthValueTaskQO.getTaskType());
        hsaGrowthValueTask.setTaskAction(growthValueTaskQO.getTaskAction());
        hsaGrowthValueTask.setDescriptionType(growthValueTaskQO.getDescriptionType());
        hsaGrowthValueTask.setDescription(growthValueTaskQO.getDescription());
        hsaGrowthValueTask.setGrowthValue(growthValueTaskQO.getGrowthValue());
        hsaGrowthValueTask.setLimitedNumber(growthValueTaskQO.getLimitedNumber());
        hsaGrowthValueTask.setSourceTypeJson(StringUtils.join(growthValueTaskQO.getSourceTypeJson(), COMMA));
        hsaGrowthValueTask.setTaskValidityType(growthValueTaskQO.getTaskValidityType());
        hsaGrowthValueTask.setStartFixedTaskValidityDate(growthValueTaskQO.getStartFixedTaskValidityDate());
        hsaGrowthValueTask.setEndFixedTaskValidityDate(growthValueTaskQO.getEndFixedTaskValidityDate());
        hsaGrowthValueTask.setGrowthValueValidityType(growthValueTaskQO.getGrowthValueValidityType());
        hsaGrowthValueTask.setDynamicValidityType(growthValueTaskQO.getDynamicValidityType());
        hsaGrowthValueTask.setDynamicValidityNumber(growthValueTaskQO.getDynamicValidityNumber());
        hsaGrowthValueTask.setFixedGrowthValueValidityDate(growthValueTaskQO.getFixedGrowthValueValidityDate());
        hsaGrowthValueTask.setPersonalDetailsTypeJson(StringUtils.join(growthValueTaskQO.getPersonalDetailsTypeJson(),
                COMMA));
        hsaGrowthValueTask.setAmount(growthValueTaskQO.getAmount());
        hsaGrowthValueTask.setApplyBusinessJson(StringUtils.join(growthValueTaskQO.getApplyBusinessJson(), COMMA));
        hsaGrowthValueTask.setGetCountType(growthValueTaskQO.getGetCountType());
        hsaGrowthValueTask.setPeriodLimitedType(growthValueTaskQO.getPeriodLimitedType());
        hsaGrowthValueTask.setConsumptionGoodsType(growthValueTaskQO.getConsumptionGoodsType());
        hsaGrowthValueTask.setChooseGoodsType(growthValueTaskQO.getChooseGoodsType());

        hsaGrowthValueTask.setApplicableAllStore(growthValueTaskQO.getApplicableAllStore());
        hsaGrowthValueTask.setBuyType(growthValueTaskQO.getBuyType());
        hsaGrowthValueTask.setBuyNumber(growthValueTaskQO.getBuyNumber());
        hsaGrowthValueTask.setBuyPeriodType(growthValueTaskQO.getBuyPeriodType());
        hsaGrowthValueTask.setTotalPeriodType(growthValueTaskQO.getTotalPeriodType());
        hsaGrowthValueTask.setTotalPeriod(growthValueTaskQO.getTotalPeriod());
        hsaGrowthValueTask.setConsumptionCount(growthValueTaskQO.getConsumptionCount());
        hsaGrowthValueTask.setConsumptionIgnoreAmount(growthValueTaskQO.getConsumptionIgnoreAmount());
        hsaGrowthValueTask.setOperatorTelName(headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.
                getTel());
        //如果是指定商品重新设置
        if (hsaGrowthValueTask.isAppointGoods()) {
            hsaGrowthValueTask.setApplyBusinessJson(growthValueTaskQO.getApplyBusinessType());
        }
    }

    public static void addStoreRuleInfoList(String guid, StoreBoothCardRuleQO storeBoothCardRuleQO, HsaStoreRuleInfo booth, HsaStoreRuleInfo hsaGrowthValueStoreRule, List<HsaStoreRuleInfo> hsaStoreRuleInfoList) {
        booth.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        booth.setStoreGuid(storeBoothCardRuleQO.getStoreGuid());
        booth.setStoreName(storeBoothCardRuleQO.getStoreName());
        booth.setStoreNumber(storeBoothCardRuleQO.getStoreNumber());
        booth.setTime(storeBoothCardRuleQO.getTime());
        booth.setAddress(storeBoothCardRuleQO.getAddress());
        booth.setTypeGuid(guid);
        booth.setType(StoreTypeEnum.GROWTH_VALUE.getCode());
        booth.setParentGuid(hsaGrowthValueStoreRule.getGuid());
        booth.setAddressPoint(storeBoothCardRuleQO.getAddressPoint());
        booth.setGmtCreate(LocalDateTime.now());
        booth.setGmtModified(LocalDateTime.now());
        hsaStoreRuleInfoList.add(booth);
    }

    public static void getHsaStoreRuleInfo(GrowthValueStoreRuleQO growthValueStoreRuleQO, String guid, HsaStoreRuleInfo hsaGrowthValueStoreRule) {
        hsaGrowthValueStoreRule.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaGrowthValueStoreRule.setStoreGuid(growthValueStoreRuleQO.getStoreGuid());
        hsaGrowthValueStoreRule.setStoreName(growthValueStoreRuleQO.getStoreName());
        hsaGrowthValueStoreRule.setStoreNumber(growthValueStoreRuleQO.getStoreNumber());
        hsaGrowthValueStoreRule.setTime(growthValueStoreRuleQO.getTime());
        hsaGrowthValueStoreRule.setAddress(growthValueStoreRuleQO.getAddress());
        hsaGrowthValueStoreRule.setTypeGuid(guid);
        hsaGrowthValueStoreRule.setType(StoreTypeEnum.GROWTH_VALUE.getCode());
        hsaGrowthValueStoreRule.setAddressPoint(growthValueStoreRuleQO.getAddressPoint());
        hsaGrowthValueStoreRule.setStoreLogo(growthValueStoreRuleQO.getStoreLogo());
        hsaGrowthValueStoreRule.setGmtCreate(LocalDateTime.now());
        hsaGrowthValueStoreRule.setGmtModified(LocalDateTime.now());
    }

    public static void getGrowthValueCommodityRule(HsaGrowthValueTask hsaGrowthValueTask, GrowthCommodityBaseQO growthCommodityBaseQO, HsaGrowthValueCommodityRule rule) {
        rule.setCommodityId(growthCommodityBaseQO.getCommodityId())
                .setCommodityName(growthCommodityBaseQO.getCommodityName())
                .setCommodityCode(growthCommodityBaseQO.getCommodityCode())
                .setStrategyCode(growthCommodityBaseQO.getStrategyCode())
                .setCommodityPrice(growthCommodityBaseQO.getBasePrice())
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setComboType(growthCommodityBaseQO.getCommodityComboType())
                .setGrowthValueTaskGuid(hsaGrowthValueTask.getGuid())
                .setCategoryId(growthCommodityBaseQO.getCategoryId())
                .setCategoryName(growthCommodityBaseQO.getCategoryName())
                .setStoreState(growthCommodityBaseQO.getStoreState())
                .setStrategyId(growthCommodityBaseQO.getStrategyId())
                .setStrategyName(growthCommodityBaseQO.getStrategyName());
    }

    public static GrowthValueTaskQO getGrowthValueTaskQO(HsaGrowthValueTask hsaGrowthValueTask) {
        GrowthValueTaskQO growthValueTaskQO = new GrowthValueTaskQO();
        growthValueTaskQO.setGuid(hsaGrowthValueTask.getGuid());
        growthValueTaskQO.setTaskName(hsaGrowthValueTask.getTaskName());
        growthValueTaskQO.setTaskType(hsaGrowthValueTask.getTaskType());
        growthValueTaskQO.setTaskAction(hsaGrowthValueTask.getTaskAction());
        growthValueTaskQO.setDescriptionType(hsaGrowthValueTask.getDescriptionType());
        growthValueTaskQO.setDescription(hsaGrowthValueTask.getDescription());
        growthValueTaskQO.setGrowthValue(hsaGrowthValueTask.getGrowthValue());
        growthValueTaskQO.setLimitedNumber(hsaGrowthValueTask.getLimitedNumber());
        growthValueTaskQO.setSourceTypeJson(StringUtils.isEmpty(hsaGrowthValueTask.getSourceTypeJson()) ?
                null : hsaGrowthValueTask.getSourceTypeJson().split(COMMA));
        growthValueTaskQO.setTaskValidityType(hsaGrowthValueTask.getTaskValidityType());
        growthValueTaskQO.setStartFixedTaskValidityDate(hsaGrowthValueTask.getStartFixedTaskValidityDate());
        growthValueTaskQO.setEndFixedTaskValidityDate(hsaGrowthValueTask.getEndFixedTaskValidityDate());
        growthValueTaskQO.setGrowthValueValidityType(hsaGrowthValueTask.getGrowthValueValidityType());
        growthValueTaskQO.setDynamicValidityType(hsaGrowthValueTask.getDynamicValidityType());
        growthValueTaskQO.setDynamicValidityNumber(hsaGrowthValueTask.getDynamicValidityNumber());
        growthValueTaskQO.setFixedGrowthValueValidityDate(hsaGrowthValueTask.getFixedGrowthValueValidityDate());
        growthValueTaskQO.setPersonalDetailsTypeJson(StringUtils.isEmpty(hsaGrowthValueTask.getPersonalDetailsTypeJson())
                ? null : hsaGrowthValueTask.getPersonalDetailsTypeJson().split(COMMA));
        growthValueTaskQO.setAmount(hsaGrowthValueTask.getAmount());
        growthValueTaskQO.setApplyBusinessJson(StringUtils.isEmpty(hsaGrowthValueTask.getApplyBusinessJson()) ? null :
                hsaGrowthValueTask.getApplyBusinessJson().split(COMMA));
        growthValueTaskQO.setGetCountType(hsaGrowthValueTask.getGetCountType());
        growthValueTaskQO.setPeriodLimitedType(hsaGrowthValueTask.getPeriodLimitedType());
        growthValueTaskQO.setConsumptionGoodsType(hsaGrowthValueTask.getConsumptionGoodsType());
        growthValueTaskQO.setChooseGoodsType(hsaGrowthValueTask.getChooseGoodsType());

        growthValueTaskQO.setApplicableAllStore(hsaGrowthValueTask.getApplicableAllStore());
        growthValueTaskQO.setBuyType(hsaGrowthValueTask.getBuyType());
        growthValueTaskQO.setBuyNumber(hsaGrowthValueTask.getBuyNumber());
        growthValueTaskQO.setBuyPeriodType(hsaGrowthValueTask.getBuyPeriodType());
        growthValueTaskQO.setTotalPeriodType(hsaGrowthValueTask.getTotalPeriodType());
        growthValueTaskQO.setTotalPeriod(hsaGrowthValueTask.getTotalPeriod());
        growthValueTaskQO.setConsumptionCount(hsaGrowthValueTask.getConsumptionCount());
        growthValueTaskQO.setConsumptionIgnoreAmount(hsaGrowthValueTask.getConsumptionIgnoreAmount());
        return growthValueTaskQO;
    }

    public static GrowthValueStoreRuleQO toGrowthValueStoreRuleQO(HsaStoreRuleInfo hsaGrowthValueStoreRule) {
        GrowthValueStoreRuleQO growthValueStoreRuleQO = new GrowthValueStoreRuleQO();
        growthValueStoreRuleQO.setAddress(hsaGrowthValueStoreRule.getAddress());
        growthValueStoreRuleQO.setAddressPoint(hsaGrowthValueStoreRule.getAddressPoint());
        growthValueStoreRuleQO.setParentGuid(hsaGrowthValueStoreRule.getParentGuid());
        growthValueStoreRuleQO.setStoreGuid(hsaGrowthValueStoreRule.getStoreGuid());
        growthValueStoreRuleQO.setStoreName(hsaGrowthValueStoreRule.getStoreName());
        growthValueStoreRuleQO.setStoreNumber(hsaGrowthValueStoreRule.getStoreNumber());
        growthValueStoreRuleQO.setTime(hsaGrowthValueStoreRule.getTime());
        growthValueStoreRuleQO.setStoreLogo(hsaGrowthValueStoreRule.getStoreLogo());
        return growthValueStoreRuleQO;
    }

    public static List<GrowthCommodityBaseQO> getGrowthCommodityBaseQOS(List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules,
                                                                        Map<String, GrowthCommodityBaseVO> finalGrowthCommodityBaseVOMap) {
        return hsaGrowthValueCommodityRules
                .stream()
                .map(in -> {
                    GrowthCommodityBaseQO growthCommodityBaseQO = new GrowthCommodityBaseQO();
                    if (CollUtil.isNotEmpty(finalGrowthCommodityBaseVOMap) && finalGrowthCommodityBaseVOMap.containsKey(in.getCommodityId())) {
                        GrowthCommodityBaseVO growthCommodityBaseVO = finalGrowthCommodityBaseVOMap.get(in.getCommodityId());
                        growthCommodityBaseQO.setGuid(in.getGuid())
                                .setBasePrice(growthCommodityBaseVO.getBasePrice())
                                .setStrategyCode(growthCommodityBaseVO.getStrategyCode())
                                .setCategoryId(growthCommodityBaseVO.getCategoryId())
                                .setCategoryName(growthCommodityBaseVO.getCategoryName())
                                .setCommodityCode(growthCommodityBaseVO.getCommodityCode())
                                .setCommodityComboType(growthCommodityBaseVO.getCommodityComboType())
                                .setCommodityName(growthCommodityBaseVO.getCommodityName())
                                .setCommodityId(growthCommodityBaseVO.getCommodityId())
                                .setStrategyId(growthCommodityBaseVO.getStrategyId())
                                .setStrategyName(growthCommodityBaseVO.getStrategyName())
                                .setStoreState(growthCommodityBaseVO.getStoreState());
                        growthCommodityBaseQO.setIsExist(BooleanEnum.TRUE.getCode());
                    } else {
                        growthCommodityBaseQO.setGuid(in.getGuid())
                                .setBasePrice(in.getCommodityPrice())
                                .setCategoryId(in.getCategoryId())
                                .setCategoryName(in.getCategoryName())
                                .setCommodityCode(in.getCommodityCode())
                                .setCommodityComboType(in.getComboType())
                                .setCommodityName(in.getCommodityName())
                                .setCommodityId(in.getCommodityId())
                                .setStrategyId(in.getStrategyId())
                                .setStrategyName(in.getStrategyName())
                                .setStoreState(in.getStoreState());
                        growthCommodityBaseQO.setIsExist(BooleanEnum.FALSE.getCode());
                    }
                    return growthCommodityBaseQO;
                }).collect(Collectors.toList());
    }
}
