package com.holderzone.member.base.service.growth.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.ShopBaseService;
import com.holderzone.member.base.entity.growth.HsaExtraAwardRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.base.entity.growth.HsaSuspendTaskTimeQuantum;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.base.mapper.growth.*;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueCommodityClassifyService;
import com.holderzone.member.base.service.growth.HsaGrowthValueCommodityRuleService;
import com.holderzone.member.base.service.growth.HsaGrowthValueTaskService;
import com.holderzone.member.base.service.growth.converter.GrowthValueCommodityClassifyConverter;
import com.holderzone.member.base.service.system.HsaStoreRuleInfoService;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.StoreTypeEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.StoreBoothCardRuleQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.growth.*;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityPageVO;
import com.holderzone.member.common.vo.growth.GrowthValueCompleteCountVO;
import com.holderzone.member.common.vo.growth.GrowthValueTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * author: pantao
 */
@Slf4j
@Service
public class HsaGrowthValueTaskServiceImpl extends HolderBaseServiceImpl<HsaGrowthValueTaskMapper, HsaGrowthValueTask>
        implements HsaGrowthValueTaskService {

    /**
     * 生成工具类
     */
    private final GuidGeneratorUtil guidGeneratorUtil;

    /**
     * 成长值任务mapper
     */
    private final HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    /**
     * 额外奖励规则mapper
     */
    private final HsaExtraAwardRuleMapper hsaExtraAwardRuleMapper;

    /**
     * 适用门店mapper
     */
    private final HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    /**
     * 会员卡基础信息表 Mapper 接口
     */
    private final HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    private final HsaGrowthValueCommodityRuleMapper hsaGrowthValueCommodityRuleMapper;

    @Autowired
    @Lazy
    private HsaGrowthValueCommodityRuleService hsaGrowthValueCommodityRuleService;

    @Resource
    @Lazy
    private HsaStoreRuleInfoService hsaStoreRuleInfoService;

    private final ShopBaseService shopBaseService;

    private final RequestGoalgoService hsaRequestGoalgoService;

    private final HsaSuspendTaskTimeQuantumMapper hsaSuspendTaskTimeQuantumMapper;

    @Resource
    private HsaGrowthValueCommodityClassifyService growthValueCommodityClassifyService;

    public HsaGrowthValueTaskServiceImpl(GuidGeneratorUtil guidGeneratorUtil,
                                         HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper,
                                         HsaExtraAwardRuleMapper hsaExtraAwardRuleMapper,
                                         HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper,
                                         HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper,
                                         HsaGrowthValueCommodityRuleMapper hsaGrowthValueCommodityRuleMapper,
                                         ShopBaseService shopBaseService,
                                         RequestGoalgoService hsaRequestGoalgoService,
                                         HsaSuspendTaskTimeQuantumMapper hsaSuspendTaskTimeQuantumMapper) {
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.hsaGrowthValueTaskMapper = hsaGrowthValueTaskMapper;
        this.hsaExtraAwardRuleMapper = hsaExtraAwardRuleMapper;
        this.hsaStoreRuleInfoMapper = hsaStoreRuleInfoMapper;
        this.hsaGrowthValueDetailMapper = hsaGrowthValueDetailMapper;
        this.hsaGrowthValueCommodityRuleMapper = hsaGrowthValueCommodityRuleMapper;
        this.shopBaseService = shopBaseService;
        this.hsaRequestGoalgoService = hsaRequestGoalgoService;
        this.hsaSuspendTaskTimeQuantumMapper = hsaSuspendTaskTimeQuantumMapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "SAVE_GROWTH_VALUE_TASK", leaseTime = 10, tryLock = true)
    public boolean saveOrUpdateGrowthValueTask(GrowthValueTaskQO growthValueTaskQO) {
        HsaGrowthValueTask hsaGrowthValueTask;
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //将运营主体下所有成长值任务位置加1
        hsaGrowthValueTaskMapper.addOnePosition(headerUserInfo.getOperSubjectGuid());
        //新增
        if (StringUtils.isEmpty(growthValueTaskQO.getGuid())) {
            //校验基础任务数量
            checkBaseTaskCount(growthValueTaskQO);
            //校验任务名称
            checkTaskName(growthValueTaskQO);
            hsaGrowthValueTask = getHsaGrowthValueTask(headerUserInfo);
        } else {
            hsaGrowthValueTask = this.queryByGuid(growthValueTaskQO.getGuid());
            //修改了名称，判断名称是否重复
            if (!hsaGrowthValueTask.getTaskName().equals(growthValueTaskQO.getTaskName())) {
                checkTaskName(growthValueTaskQO);
            }
            hsaGrowthValueTask.setGmtModified(LocalDateTime.now());
            //删除所有额外奖励规则
            hsaExtraAwardRuleMapper.delete(new LambdaQueryWrapper<HsaExtraAwardRule>()
                    .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, growthValueTaskQO.getGuid()));
            //删除所有门店数据
            hsaStoreRuleInfoMapper.delete(new LambdaQueryWrapper<HsaStoreRuleInfo>().
                    eq(HsaStoreRuleInfo::getTypeGuid, growthValueTaskQO.getGuid()));
            //任务状态
            Integer taskStatus = getTaskStatus(hsaGrowthValueTask.getTaskValidityType(), hsaGrowthValueTask.
                            getStartFixedTaskValidityDate(), hsaGrowthValueTask.getEndFixedTaskValidityDate(),
                    hsaGrowthValueTask.getIsEnable());
            //如果任务是已结束的，将任务设为已停止
            if (taskStatus == TaskStatusEnum.FINISHED.getCode()) {
                hsaGrowthValueTask.setIsEnable(BooleanEnum.FALSE.getCode());
            }
        }

        GrowthValueCommodityClassifyConverter.getGrowthValueTask(growthValueTaskQO, hsaGrowthValueTask, headerUserInfo);
        this.saveOrUpdate(hsaGrowthValueTask);

        //如果是指定分类商品
        if (hsaGrowthValueTask.isAppointClassifyGoods()) {
            growthValueCommodityClassifyService.saveGrowthValueTaskCommodityClassify(hsaGrowthValueTask.getGuid(), growthValueTaskQO.getAppointGoodsTypeList());
            return true;
        }

        saveHsaExtraAwardRule(growthValueTaskQO.getExtraAwardRuleQOList(), hsaGrowthValueTask.getGuid());
        saveHsaGrowthValueStoreRule(growthValueTaskQO.getGrowthValueStoreRuleList(), hsaGrowthValueTask.getGuid());
        saveHsaGrowthCommodityRule(growthValueTaskQO, hsaGrowthValueTask);
        return true;
    }

    private HsaGrowthValueTask getHsaGrowthValueTask(HeaderUserInfo headerUserInfo) {
        HsaGrowthValueTask hsaGrowthValueTask;
        hsaGrowthValueTask = new HsaGrowthValueTask();
        hsaGrowthValueTask.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueTask.class.getSimpleName()));
        hsaGrowthValueTask.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        hsaGrowthValueTask.setTaskNumber(guidGeneratorUtil.getCode(String.valueOf(hsaGrowthValueTask.getGuid())));
        hsaGrowthValueTask.setGmtCreate(LocalDateTime.now());
        hsaGrowthValueTask.setGmtModified(LocalDateTime.now());
        hsaGrowthValueTask.setPosition(NumberConstant.NUMBER_1);
        hsaGrowthValueTask.setIsDelete(BooleanEnum.FALSE.getCode());
        hsaGrowthValueTask.setIsEnable(BooleanEnum.TRUE.getCode());
        return hsaGrowthValueTask;
    }

    private void checkTaskName(GrowthValueTaskQO growthValueTaskQO) {
        //校验任务名称
        Integer nameCount = hsaGrowthValueTaskMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaGrowthValueTask::getTaskName, growthValueTaskQO.getTaskName())
                .eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (nameCount > NumberConstant.NUMBER_0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.NAME_MORE_THAN_ONE);
        }
    }

    /**
     * 保存门店信息
     *
     * @param growthValueStoreRuleList 成长值适用门店请求qo
     * @param guid                     guid
     */
    private void saveHsaGrowthValueStoreRule(List<GrowthValueStoreRuleQO> growthValueStoreRuleList, String guid) {
        if (CollectionUtils.isEmpty(growthValueStoreRuleList)) {
            return;
        }
        List<HsaStoreRuleInfo> hsaStoreRuleInfoList = new ArrayList<>();
        for (GrowthValueStoreRuleQO growthValueStoreRuleQO : growthValueStoreRuleList) {
            HsaStoreRuleInfo hsaGrowthValueStoreRule = toHsaGrowthValueStoreRule(growthValueStoreRuleQO, guid);
            hsaStoreRuleInfoList.add(hsaGrowthValueStoreRule);
            if (CollectionUtils.isEmpty(growthValueStoreRuleQO.getStoreBoothCardRuleQOList())) {
                continue;
            }
            for (StoreBoothCardRuleQO storeBoothCardRuleQO : growthValueStoreRuleQO.getStoreBoothCardRuleQOList()) {
                HsaStoreRuleInfo booth = new HsaStoreRuleInfo();
                booth.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreRuleInfo.class.getSimpleName()));
                GrowthValueCommodityClassifyConverter.addStoreRuleInfoList(guid, storeBoothCardRuleQO, booth, hsaGrowthValueStoreRule, hsaStoreRuleInfoList);
            }
        }
        hsaStoreRuleInfoService.saveBatch(hsaStoreRuleInfoList);
    }

    private HsaStoreRuleInfo toHsaGrowthValueStoreRule(GrowthValueStoreRuleQO growthValueStoreRuleQO, String guid) {
        HsaStoreRuleInfo hsaGrowthValueStoreRule = new HsaStoreRuleInfo();
        hsaGrowthValueStoreRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreRuleInfo.class.getSimpleName()));
        GrowthValueCommodityClassifyConverter.getHsaStoreRuleInfo(growthValueStoreRuleQO, guid, hsaGrowthValueStoreRule);
        return hsaGrowthValueStoreRule;
    }


    /**
     * 保存适用商品信息
     *
     * @param growthValueTaskQO growthValueTaskQO
     */
    private void saveHsaGrowthCommodityRule(GrowthValueTaskQO growthValueTaskQO, HsaGrowthValueTask hsaGrowthValueTask) {
        if (CollectionUtils.isEmpty(growthValueTaskQO.getGrowthCommodityBaseQOList()) || growthValueTaskQO.getTaskAction() != TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode() || StringUtils.isNotBlank(growthValueTaskQO.getGuid())) {
            return;
        }
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules = Lists.newArrayList();

        for (GrowthCommodityBaseQO growthCommodityBaseQO : growthValueTaskQO.getGrowthCommodityBaseQOList()) {
            HsaGrowthValueCommodityRule rule = new HsaGrowthValueCommodityRule();
            rule.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueCommodityRule.class.getSimpleName()));
            GrowthValueCommodityClassifyConverter.getGrowthValueCommodityRule(hsaGrowthValueTask, growthCommodityBaseQO, rule);
            hsaGrowthValueCommodityRules.add(rule);
        }
        if (CollUtil.isNotEmpty(hsaGrowthValueCommodityRules)) {
            hsaGrowthValueCommodityRuleService.saveBatch(hsaGrowthValueCommodityRules);
        }
    }

    /**
     * 保存额外奖励规则请求
     *
     * @param extraAwardRuleQOList 额外奖励规则请求qo
     * @param guid                 guid
     */
    private void saveHsaExtraAwardRule(List<ExtraAwardRuleQO> extraAwardRuleQOList, String guid) {
        if (CollectionUtils.isEmpty(extraAwardRuleQOList)) {
            return;
        }
        for (ExtraAwardRuleQO extraAwardRuleQO : extraAwardRuleQOList) {
            HsaExtraAwardRule hsaExtraAwardRule = new HsaExtraAwardRule();
            hsaExtraAwardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaExtraAwardRule.class.getSimpleName()));
            hsaExtraAwardRule.setGrowthValueTaskGuid(guid);
            hsaExtraAwardRule.setConsumptionAmount(extraAwardRuleQO.getConsumptionAmount());
            hsaExtraAwardRule.setGrowthValue(extraAwardRuleQO.getGrowthValue());
            hsaExtraAwardRule.setConsumptionFrequency(extraAwardRuleQO.getConsumptionFrequency());
            hsaExtraAwardRuleMapper.insert(hsaExtraAwardRule);
        }
    }

    /**
     * 校验基础任务数量
     *
     * @param growthValueTaskQO 创建成长值任务请求qo
     */
    private void checkBaseTaskCount(GrowthValueTaskQO growthValueTaskQO) {

        //消费任务不限制
        if (growthValueTaskQO.getTaskType() == TaskTypeEnum.CONSUMPTION_TASK.getCode()) {
            return;
        }
        //基础任务充值任务，相同运营主体只能有一个相同的活动
        Integer baseTaskCount = hsaGrowthValueTaskMapper.findHadExistTaskAction(ThreadLocalCache.getOperSubjectGuid(),
                growthValueTaskQO.getTaskAction(), null);
        if (baseTaskCount > NumberConstant.NUMBER_0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.BASE_TASK_MORE_THAN_ONE);
        }

    }

    @Override
    public GrowthValueTaskQO queryGrowthValueTaskDetail(String guid) {

        if (StringUtils.isEmpty(guid)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthValueTaskMapper.queryByGuid(guid);
        if (Objects.isNull(hsaGrowthValueTask)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.TASK_NOT_EXIST);
        }
        GrowthValueTaskQO growthValueTaskQO = GrowthValueCommodityClassifyConverter.getGrowthValueTaskQO(hsaGrowthValueTask);

        //指定分类商品数据
        if (hsaGrowthValueTask.isAppointClassifyGoods()) {
            growthValueTaskQO.setAppointGoodsTypeList(growthValueCommodityClassifyService.listCommodityClassifyByTask(hsaGrowthValueTask.getGuid()));
            return growthValueTaskQO;
        }

        if (hsaGrowthValueTask.isAppointGoods()) {
            growthValueTaskQO.setApplyBusinessType(hsaGrowthValueTask.getApplyBusinessJson());
        }
//        //额外奖励规则
        growthValueTaskQO.setExtraAwardRuleQOList(toExtraAwardRule(hsaGrowthValueTask.getGuid()));
        //适用门店
        growthValueTaskQO.setGrowthValueStoreRuleList(toGrowthValueStoreRule(hsaGrowthValueTask));
        //适用商品
        growthValueTaskQO.setGrowthCommodityBaseQOList(toGrowthCommodityRule(hsaGrowthValueTask));
        return growthValueTaskQO;
    }

    /**
     * 封装适用门店
     *
     * @param hsaGrowthValueTask 成长值任务
     * @return 操作结果
     */
    private List<GrowthValueStoreRuleQO> toGrowthValueStoreRule(HsaGrowthValueTask hsaGrowthValueTask) {
        if (Objects.isNull(hsaGrowthValueTask.getApplicableAllStore()) || GoodsApplicableStoreEnum.ALL_STORE.getCode()
                == hsaGrowthValueTask.getApplicableAllStore()) {
            return null;
        }
        List<HsaStoreRuleInfo> hsaGrowthValueStoreRules = hsaStoreRuleInfoMapper.
                selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                        .eq(HsaStoreRuleInfo::getTypeGuid, hsaGrowthValueTask.getGuid()));
        if (CollectionUtils.isEmpty(hsaGrowthValueStoreRules)) {
            return null;
        }
        Map<String, List<HsaStoreRuleInfo>> boothMap = hsaGrowthValueStoreRules
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getParentGuid()))
                .collect(Collectors.groupingBy(HsaStoreRuleInfo::getParentGuid));
        List<GrowthValueStoreRuleQO> growthValueStoreRuleQOS = new ArrayList<>();
        for (HsaStoreRuleInfo hsaGrowthValueStoreRule : hsaGrowthValueStoreRules) {
            if (StringUtils.isNotEmpty(hsaGrowthValueStoreRule.getParentGuid())) {
                continue;
            }
            GrowthValueStoreRuleQO growthValueStoreRuleQO = GrowthValueCommodityClassifyConverter.toGrowthValueStoreRuleQO(hsaGrowthValueStoreRule);
            List<HsaStoreRuleInfo> boothList = boothMap.get(hsaGrowthValueStoreRule.getGuid());
            if (Objects.isNull(boothList)) {
                growthValueStoreRuleQOS.add(growthValueStoreRuleQO);
                continue;
            }
            List<StoreBoothCardRuleQO> boothVOList = new ArrayList<>();
            for (HsaStoreRuleInfo booth : boothList) {
                StoreBoothCardRuleQO boothVO = new StoreBoothCardRuleQO();
                boothVO.setAddress(booth.getAddress());
                boothVO.setAddressPoint(booth.getAddressPoint());
                boothVO.setStoreGuid(booth.getStoreGuid());
                boothVO.setStoreName(booth.getStoreName());
                boothVO.setStoreNumber(booth.getStoreNumber());
                boothVO.setTime(booth.getTime());
                boothVOList.add(boothVO);
            }
            growthValueStoreRuleQO.setStoreBoothCardRuleQOList(boothVOList);
            growthValueStoreRuleQOS.add(growthValueStoreRuleQO);
        }
        return growthValueStoreRuleQOS;
    }

    /**
     * 封装适用商品
     *
     * @param hsaGrowthValueTask 成长值任务
     * @return 操作结果
     */
    private List<GrowthCommodityBaseQO> toGrowthCommodityRule(HsaGrowthValueTask hsaGrowthValueTask) {
        if (hsaGrowthValueTask.getTaskAction() != TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode() || hsaGrowthValueTask.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode()) {
            return Collections.emptyList();
        }
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules = hsaGrowthValueCommodityRuleMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueCommodityRule>()
                .eq(HsaGrowthValueCommodityRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid()));
        if (CollectionUtils.isEmpty(hsaGrowthValueCommodityRules)) {
            return Collections.emptyList();
        }
        //获取外部商品
        List<String> commodityId = hsaGrowthValueCommodityRules
                .stream()
                .map(commodity -> commodity.getCommodityId())
                .collect(Collectors.toList());
        //过滤掉
        GradeCommodityBasePageQO gradeCommodityBasePageQO = new GradeCommodityBasePageQO();
        //渠道
        gradeCommodityBasePageQO.setBusinessType(Integer.valueOf(hsaGrowthValueTask.getApplyBusinessJson()));
        gradeCommodityBasePageQO.setCommityIds(commodityId);
        GrowthCommodityPageVO growthCommodityPageVO = shopBaseService.queryGradeCommodityPage(gradeCommodityBasePageQO);

        Map<String, GrowthCommodityBaseVO> growthCommodityBaseVOMap = new HashMap<>();
        if (Objects.nonNull(growthCommodityPageVO) && CollUtil.isNotEmpty(growthCommodityPageVO.getGrowthCommodityBaseVOS())) {
            growthCommodityBaseVOMap = growthCommodityPageVO.getGrowthCommodityBaseVOS()
                    .stream()
                    .filter(e -> e.getStoreState() == CommodityStatusEnum.COMMODITY_UP.getCode() || e.getStoreState() == CommodityStatusEnum.COMMODITY_OUT.getCode())
                    .collect(Collectors.toMap(GrowthCommodityBaseVO::getCommodityId, Function.identity(), (entity1, entity2) -> entity1));
        }


        Map<String, GrowthCommodityBaseVO> finalGrowthCommodityBaseVOMap = growthCommodityBaseVOMap;
        return GrowthValueCommodityClassifyConverter.getGrowthCommodityBaseQOS(hsaGrowthValueCommodityRules, finalGrowthCommodityBaseVOMap);
    }



    /**
     * 封装奖励规则
     *
     * @param guid 任务guid
     * @return 操作结果
     */
    private List<ExtraAwardRuleQO> toExtraAwardRule(String guid) {
        List<HsaExtraAwardRule> hsaExtraAwardRules = hsaExtraAwardRuleMapper.selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, guid));
        List<ExtraAwardRuleQO> extraAwardRuleQOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(hsaExtraAwardRules)) {
            return extraAwardRuleQOS;
        }
        for (HsaExtraAwardRule hsaExtraAwardRule : hsaExtraAwardRules) {
            ExtraAwardRuleQO extraAwardRuleQO = new ExtraAwardRuleQO();
            extraAwardRuleQO.setConsumptionAmount(hsaExtraAwardRule.getConsumptionAmount());
            extraAwardRuleQO.setGrowthValue(hsaExtraAwardRule.getGrowthValue());
            extraAwardRuleQO.setConsumptionFrequency(hsaExtraAwardRule.getConsumptionFrequency());
            extraAwardRuleQOS.add(extraAwardRuleQO);
        }
        return extraAwardRuleQOS;
    }

    @Override
    public PageResult queryGrowthValueTaskList(GrowthValueTaskListQO request) {

        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<GrowthValueTaskVO> growthValueTaskList = hsaGrowthValueTaskMapper.queryGrowthValueTask(request);

        if (CollectionUtils.isEmpty(growthValueTaskList)) {
            return PageUtil.getPageResult(new PageInfo<>(growthValueTaskList));
        }
        //成长值任务id
        List<Long> ids = growthValueTaskList.stream().map(GrowthValueTaskVO::getId).collect(Collectors.toList());
        List<GrowthValueCompleteCountVO> growthValueCompleteCounts = hsaGrowthValueDetailMapper.findCompleteCount(ids);
        Map<Long, GrowthValueCompleteCountVO> map = growthValueCompleteCounts.stream().collect(Collectors.
                toMap(GrowthValueCompleteCountVO::getId, Function.identity(), (obj, obj1) -> obj));
        for (GrowthValueTaskVO growthValueTask : growthValueTaskList) {
            GrowthValueCompleteCountVO completeCountVO = map.get(growthValueTask.getId());
            if (Objects.nonNull(completeCountVO)) {
                growthValueTask.setFinishCount(completeCountVO.getFinishCount());
                growthValueTask.setFinishPersonNumber(completeCountVO.getFinishPersonNumber());
            }
            //任务状态
            Integer taskStatus = getTaskStatus(growthValueTask.getTaskValidityType(), growthValueTask.getStartFixedTaskValidityDate(),
                    growthValueTask.getEndFixedTaskValidityDate(), growthValueTask.getIsEnable());
            growthValueTask.setTaskStatus(taskStatus);
        }
        return PageUtil.getPageResult(new PageInfo<>(growthValueTaskList));
    }

    /***
     * 获取任务状态
     * @param taskValidityType 任务有效期类型
     * @param startFixedTaskValidityDate 开始时间
     * @param endFixedTaskValidityDate 结束时间
     * @param isEnable 是否启动
     * @return 操作结果
     */
    private Integer getTaskStatus(int taskValidityType, LocalDateTime startFixedTaskValidityDate,
                                  LocalDateTime endFixedTaskValidityDate, int isEnable) {
        //任务状态 默认进行中
        int taskStatus = TaskStatusEnum.UNDERWAY.getCode();
        //如果固定有效期 并且当前时间大于开始时间 未开始
        if (taskValidityType == TaskValidityTypeEnum.FIXED_VALIDITY.getCode() &&
                !DateUtil.compareCurrentDate(startFixedTaskValidityDate)) {
            if (isEnable == BooleanEnum.TRUE.getCode()) {
                taskStatus = TaskStatusEnum.NOT_START.getCode();
            } else {
                taskStatus = TaskStatusEnum.STOPPED.getCode();
            }
            //进行中任务
        } else if (isUnderwayTask(taskValidityType, startFixedTaskValidityDate, endFixedTaskValidityDate)) {
            //开启成长值任务返回进行中
            if (isEnable == BooleanEnum.TRUE.getCode()) {
                taskStatus = TaskStatusEnum.UNDERWAY.getCode();
                //返回已停止
            } else {
                taskStatus = TaskStatusEnum.STOPPED.getCode();
            }
            //固定有效期 并且任务结束时间小于当前时间 返回已结束
        } else if (taskValidityType == TaskValidityTypeEnum.FIXED_VALIDITY.getCode()
                && DateUtil.compareCurrentDate(endFixedTaskValidityDate)) {
            taskStatus = TaskStatusEnum.FINISHED.getCode();
        }
        return taskStatus;
    }

    /**
     * 判断任务是否在进行中
     *
     * @param taskValidityType           任务有效期类型
     * @param startFixedTaskValidityDate 有效期开始时间
     * @param endFixedTaskValidityDate   有效期结束时间
     * @return 操作结果
     */
    private boolean isUnderwayTask(int taskValidityType, LocalDateTime startFixedTaskValidityDate,
                                   LocalDateTime endFixedTaskValidityDate) {

        return taskValidityType == TaskValidityTypeEnum.PERMANENT_VALIDITY.getCode() ||
                (taskValidityType == TaskValidityTypeEnum.FIXED_VALIDITY.getCode()
                        && DateUtil.compareCurrentDate(startFixedTaskValidityDate)
                        && !DateUtil.compareCurrentDate(endFixedTaskValidityDate));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "OPERATION_GROWTH_VALUE", leaseTime = 10, tryLock = true)
    public boolean operationGrowthValue(String guid, int type, Integer position) {

        HsaGrowthValueTask growthValueTask = this.queryByGuid(guid);
        if (Objects.isNull(growthValueTask)) {
            throw new BusinessException(MemberAccountExceptionEnum.TASK_NOT_EXIST.getDes());
        }
        GrowthValueOperationEnum growthValueOperationEnum = GrowthValueOperationEnum.getEnumByCode(type);
        switch (growthValueOperationEnum) {
            case STOPPED:
            case START:
                //启动的时候，判断是否存在相同任务活动
                if (GrowthValueOperationEnum.START.getCode() == growthValueOperationEnum.getCode() &&
                        !judgeTaskStatus(growthValueTask)) {
                    throw new MemberBaseException(MemberAccountExceptionEnum.TASK_ACTION_MORE_THAN_ONE);
                }
                growthValueTask.setIsEnable(type);
                updateByGuid(growthValueTask);
                saveHsaSuspendTaskTimeQuantum(growthValueOperationEnum.getCode(), growthValueTask.getGuid(), growthValueTask.
                        getTaskName(), GrowthValueOrIntegralEnum.GROWTH_VALUE.getCode(), growthValueTask.getTaskType());
                break;
            case DELETE:
                growthValueTask.setIsDelete(BooleanEnum.TRUE.getCode());
                updateByGuid(growthValueTask);
                hsaSuspendTaskTimeQuantumMapper.delete(new LambdaQueryWrapper<HsaSuspendTaskTimeQuantum>()
                        .eq(HsaSuspendTaskTimeQuantum::getTaskType, GrowthValueOrIntegralEnum.GROWTH_VALUE.getCode())
                        .eq(HsaSuspendTaskTimeQuantum::getTaskGuid, growthValueTask.getGuid()));
                break;
            case SORT:
                //排序
                sort(position, growthValueTask);
                break;
            default:
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        return true;
    }

    @Override
    public void saveHsaSuspendTaskTimeQuantum(Integer operationType, String taskGuid, String taskName,
                                              Integer taskType, Integer type) {
        HsaSuspendTaskTimeQuantum hsaSuspendTaskTimeQuantum = hsaSuspendTaskTimeQuantumMapper.
                selectOne(new LambdaQueryWrapper<HsaSuspendTaskTimeQuantum>()
                        .eq(HsaSuspendTaskTimeQuantum::getTaskType, taskType)
                        .eq(HsaSuspendTaskTimeQuantum::getTaskGuid, taskGuid)
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendStartTime)
                        .isNull(HsaSuspendTaskTimeQuantum::getSuspendEndTime));
        //停止
        if (operationType == GrowthValueOperationEnum.STOPPED.getCode()) {
            if (!Objects.isNull(hsaSuspendTaskTimeQuantum)) {
                log.error("任务=====>" + taskName + "启动时已经有之前暂停的数据");
                return;
            }
            hsaSuspendTaskTimeQuantum = new HsaSuspendTaskTimeQuantum();
            hsaSuspendTaskTimeQuantum.setGuid(guidGeneratorUtil.getStringGuid(HsaSuspendTaskTimeQuantum.class.getSimpleName()));
            hsaSuspendTaskTimeQuantum.setTaskName(taskName);
            hsaSuspendTaskTimeQuantum.setTaskType(taskType);
            hsaSuspendTaskTimeQuantum.setTaskGuid(taskGuid);
            hsaSuspendTaskTimeQuantum.setSuspendStartTime(LocalDateTime.now());
            hsaSuspendTaskTimeQuantum.setType(type);
            hsaSuspendTaskTimeQuantum.setGmtCreate(LocalDateTime.now());
            hsaSuspendTaskTimeQuantum.setGmtModified(LocalDateTime.now());
            hsaSuspendTaskTimeQuantumMapper.insert(hsaSuspendTaskTimeQuantum);
        } else {
            if (Objects.isNull(hsaSuspendTaskTimeQuantum)) {
                log.error("任务=====>" + taskName + "启动时没有查到之前暂停的数据");
                return;
            }
            hsaSuspendTaskTimeQuantum.setSuspendEndTime(LocalDateTime.now());
            hsaSuspendTaskTimeQuantumMapper.updateById(hsaSuspendTaskTimeQuantum);
        }
    }

    /**
     * 排序
     *
     * @param position        位置
     * @param growthValueTask 成长值任务
     */
    private void sort(Integer position, HsaGrowthValueTask growthValueTask) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        if (position <= NumberConstant.NUMBER_0) {
            position = NumberConstant.NUMBER_1;
        }
        //运营主体下最大的位置
        int maxPosition = hsaGrowthValueTaskMapper.findMaxPosition(operSubjectGuid);
        //如果输入的位置超过最大的位置，将其设置成最大位置
        if (position > maxPosition) {
            position = maxPosition;
        }
        //移动到当前位置，不做调整
        if (growthValueTask.getPosition() == position) {
            return;
            //向下调整
        } else if (growthValueTask.getPosition() < position) {
            hsaGrowthValueTaskMapper.moveDownPosition(operSubjectGuid, growthValueTask.getPosition(), position);
            //向上调整
        } else {
            hsaGrowthValueTaskMapper.moveUpPosition(operSubjectGuid, position, growthValueTask.getPosition());
        }
        growthValueTask.setPosition(position);
        updateByGuid(growthValueTask);
    }

    @Override
    public boolean canBeStartTask(String guid) {

        HsaGrowthValueTask growthValueTask = this.queryByGuid(guid);

        return judgeTaskStatus(growthValueTask);
    }

    /**
     * 判断任务状态
     *
     * @param growthValueTask 成长值任务
     * @return 操作结果
     */
    private boolean judgeTaskStatus(HsaGrowthValueTask growthValueTask) {
        //充值任务，和消费任务需要判断是否已经存在
        if (growthValueTask.getTaskType() == TaskTypeEnum.CONSUMPTION_TASK.getCode()) {
            return true;
        }
        int count = hsaGrowthValueTaskMapper.findHadExistTaskAction(ThreadLocalCache.getOperSubjectGuid(),
                growthValueTask.getTaskAction(), null);

        return count <= NumberConstant.NUMBER_0;
    }

    @Override
    public boolean whetherNeedToast(GrowthValueTaskQO growthValueTaskQO) {
        //只需要限制消费任务
        if (growthValueTaskQO.getTaskType() != TaskTypeEnum.CONSUMPTION_TASK.getCode()) {
            return false;
        }
        String[] applyBusiness = growthValueTaskQO.getApplyBusinessJson();
        if (growthValueTaskQO.getTaskAction() == TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode()
                && growthValueTaskQO.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode()) {
            return false;
        }
        //消费指定商品 部分门店
        if (growthValueTaskQO.getTaskAction() == TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode()
                && growthValueTaskQO.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_GOODS.getCode()) {
            if (StringUtils.isEmpty(growthValueTaskQO.getApplyBusinessType())) {
                throw new MemberBaseException("参数缺少字段applyBusinessType");
            }
            if (growthValueTaskQO.getApplicableAllStore() == GoodsApplicableStoreEnum.ALL_STORE.getCode()) {
                return hsaGrowthValueTaskMapper.findHadExistTaskAction(ThreadLocalCache.getOperSubjectGuid(),
                        growthValueTaskQO.getTaskAction(), growthValueTaskQO.getApplyBusinessType()) > 0;
            }
            List<GrowthValueStoreRuleQO> growthValueStoreRuleList = growthValueTaskQO.getGrowthValueStoreRuleList();
            if (CollectionUtils.isEmpty(growthValueStoreRuleList)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_NULL_STORE_CARD);
            }
            List<String> storeGuids = growthValueStoreRuleList.stream().map(GrowthValueStoreRuleQO::getStoreGuid).collect(Collectors.toList());
            return hsaGrowthValueTaskMapper.findStoreApplyBusinessNumber(storeGuids, growthValueTaskQO.getApplyBusinessType()) > 0;
        }
        for (String apply : applyBusiness) {
            int count = hsaGrowthValueTaskMapper.findHadExistTaskAction(ThreadLocalCache.getOperSubjectGuid(),
                    growthValueTaskQO.getTaskAction(), apply);
            if (count > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<Integer> findAllTaskActionType() {

        return hsaGrowthValueTaskMapper.findAllTaskActionType(ThreadLocalCache.getOperSubjectGuid());
    }


}
