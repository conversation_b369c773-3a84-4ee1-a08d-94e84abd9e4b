package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaGradeGiftBag;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.grade.GiftBagPreviewVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: 等级升级礼包mapper
 * @author: pan tao
 * @create: 2022-01-04 11:33
 */
public interface HsaGradeGiftBagMapper extends HolderBaseMapper<HsaGradeGiftBag> {

    /**
     * 根据等级guid删除礼包
     *
     * @param gradeInfoGuid 等级guid
     * @return 操作结果
     */
    int deleteByGradeInfoGuid(@Param("type") int type,@Param("gradeInfoGuid") String gradeInfoGuid);

    int deleteAllDeleteAndEffective(@Param("gradeInfoGuidList") List<String> gradeInfoGuidList);

    int editGiftBagStatus(@Param("gradeInfoGuidList") List<String> gradeInfoGuidList);

    /**
     * 通过会员等级查询升级礼包信息
     * @param operSubjectGuid 运营主体
     * @param memberGradeGuid 会员等级guid
     * @return 升级礼包信息
     */
    List<GiftBagPreviewVO> getGradeGiftBagList(@Param("operSubjectGuid") String operSubjectGuid , @Param("memberGradeGuid") String memberGradeGuid);

    /**
     * 根据数据来源查询会员等级查询升级礼包信息
     * @param operSubjectGuid 运营主体
     * @param memberGradeGuid 会员等级guid
     * @param sourceType 数据来源
     * @see com.holderzone.member.common.enums.equities.EquitiesSourceTypeEnum
     * @return 升级礼包信息
     */
    List<GiftBagPreviewVO> getGradeGiftBagListByType(@Param("operSubjectGuid") String operSubjectGuid ,
                                               @Param("memberGradeGuid") String memberGradeGuid,
                                               @Param("sourceType") String sourceType);

}
