package com.holderzone.member.base.entity.activity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 线下活动关联会员
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class HsaOfflineActivityMemberRelation implements Serializable {

    private static final long serialVersionUID = 1940485357802727086L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 会员guid
     */
    private String memberGuid;
}
