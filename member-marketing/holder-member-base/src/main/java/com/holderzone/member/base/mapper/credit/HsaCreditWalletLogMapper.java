package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaCreditWalletLog;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.credit.CreditWalletLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-02-11 14:30
 */
public interface HsaCreditWalletLogMapper extends HolderBaseMapper<HsaCreditWalletLog> {


    List<CreditWalletLogVO> queryCreditWalletLog(@Param("creditInfoGuid") String creditInfoGuid);
}
