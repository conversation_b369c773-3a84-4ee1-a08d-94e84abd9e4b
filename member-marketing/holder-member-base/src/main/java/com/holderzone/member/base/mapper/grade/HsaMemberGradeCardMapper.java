package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaMemberGradeCard;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【hsa_member_grade_card(用户等级卡)】的数据库操作Mapper
* @createDate 2025-04-27 17:41:54
* @Entity com.holderzone.member.base.entity.grade.HsaMemberGradeCard
*/
@Mapper
public interface HsaMemberGradeCardMapper extends HolderBaseMapper<HsaMemberGradeCard> {

	/**
	 * 获取指定用户当前有效的最高等级卡
	 * @param memberGuid 用户guid
	 * @param gradeType 卡类型
	 * @return
	 */
	HsaMemberGradeCard queryMaxGradeByMemberGuidAndGradeType (@Param("memberInfoGuid") String memberGuid,
															  @Param("gradeType") Integer gradeType);
}




