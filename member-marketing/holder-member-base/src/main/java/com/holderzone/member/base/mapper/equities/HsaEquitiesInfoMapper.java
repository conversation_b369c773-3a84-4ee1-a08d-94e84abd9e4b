package com.holderzone.member.base.mapper.equities;

import com.holderzone.member.base.entity.equities.HsaEquitiesInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.equities.ChooseEquitiesQO;
import com.holderzone.member.common.qo.equities.EquitiesInfoListQO;
import com.holderzone.member.common.vo.equities.ChooseEquitiesVO;
import com.holderzone.member.common.vo.equities.EquitiesCallVO;
import com.holderzone.member.common.vo.equities.EquitiesBaseInfoVO;
import com.holderzone.member.common.vo.equities.EquitiesInfoListVO;
import com.holderzone.member.common.vo.equities.EquitiesTypeStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权益基础信息mapper
 *
 * <AUTHOR>
 */
public interface HsaEquitiesInfoMapper extends HolderBaseMapper<HsaEquitiesInfo> {

    /**
     * 查询权益列表
     *
     * @param request 权益列表请求参数
     * @return 查询结果
     */
    List<EquitiesInfoListVO> queryEquitiesList(@Param("request") EquitiesInfoListQO request);

    /**
     * 获取权益类型统计数
     * @return 权益类型启用统计
     */
    EquitiesTypeStatisticsVO getEquitiesTypeStatistics(@Param("enterpriseGuid") String enterpriseGuid);


    List<ChooseEquitiesVO> getChooseEquities(@Param("request") ChooseEquitiesQO request);

    List<EquitiesCallVO> getEquitiesCallDetail(@Param("equitiesGuid") List<String> equitiesGuid);

    /**
     * 获取会员卡权益
     * @param equitiesGuid
     * @return
     */
    List<EquitiesCallVO> getEquitiesCardDetail(@Param("equitiesGuid") List<String> equitiesGuid);
    /**
     * 请求权益图标和权益名称
     * @param equitiesGuids 权益guid
     * @return 权益图标和权益名称
     */
    List<EquitiesBaseInfoVO> getEquitiesBaseInfo(@Param("equitiesGuids") List<String> equitiesGuids);

    /**
     * 根据等级guid批量查询权益基础信息
     * @param gradeGuids 等级guid
     * @return 权益图标和权益名称
     */
    List<EquitiesBaseInfoVO> batchGetEquitiesBaseInfoByGradeGuid(@Param("gradeGuids") List<String> gradeGuids);


}
