package com.holderzone.member.base.entity.system;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 操作日志实体
 * @date 2021/8/24
 */
@Data
@Accessors(chain = true)
public class OperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private String phoneNum;

    private String operator;

    private LocalDateTime operationTime;

    private String operationContent;

    private LocalDateTime gmtModified;

    private LocalDateTime gmtCreate;

    private Long id;

    private Long guid;

    /**日志对象guid*/
    private String contentGuid;

    /**日志类型 OperationLogTypeEnum*/
    private Integer logType;


}
