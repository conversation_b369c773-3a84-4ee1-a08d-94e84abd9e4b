package com.holderzone.member.base.service.pay;

import com.holderzone.member.common.dto.pay.AggPayCallbackRspDTO;
import com.holderzone.member.common.dto.pay.GradePayPollingResultDTO;
import com.holderzone.member.common.dto.pay.GradePrePayDTO;
import com.holderzone.member.common.dto.pay.MemberGradePerPayVO;

/**
 * @program: member-marketing
 * @description: 等级开通聚合支付
 * @author: rw
 */
public interface GradeOpenPayService {

    /**
     * 聚合支付预下单
     * @param prePayDTO 请求参数
     * @return 返回支付记录guid
     */
    MemberGradePerPayVO aggPrePay(GradePrePayDTO prePayDTO);

    /**
     * 聚合支付回调
     * @param aggPayCallbackDTO 回调参数
     */
    void aggPayCallback(AggPayCallbackRspDTO aggPayCallbackDTO);

    /**
     * 聚合支付轮询
     * @param orderGuid 订单guid
     * @return PayStateEnum 支付状态
     */
    GradePayPollingResultDTO aggPayPolling(String orderGuid);
}
