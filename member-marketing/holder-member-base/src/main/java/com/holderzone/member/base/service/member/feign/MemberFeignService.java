package com.holderzone.member.base.service.member.feign;

import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.vo.card.CardBaseInfoDetailVO;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import com.holderzone.member.common.vo.member.*;


/**
 * <p>
 * 运营主体会员信息表 服务类
 * </p>
 */
public interface MemberFeignService {

    OperationMemberCardInfoVO getOperationMemberCardInfo(String guid);

    CardMemberInfoVO getHsaMemberInfoCard(RequestConfirmPayVO request, String guid);

    MemberLabelGradeVO getMemberLabelAndGradeInfo(String guid);

    CardBaseInfoDetailVO getCardInfoByCardGuid(String cardGuid);

}
