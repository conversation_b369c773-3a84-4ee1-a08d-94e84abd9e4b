package com.holderzone.member.base.entity.integral;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 即将过期积分使用明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="HsaMemberIntegralDeductList对象", description="即将过期积分使用明细")
public class HsaMemberIntegralDeductList extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "积分使用guid")
    private String integralDeductGuid;

    @ApiModelProperty(value = "积分明细guid")
    private String integralDetailGuid;

    @ApiModelProperty(value = "抵扣积分")
    private Integer integralNumber;

    @ApiModelProperty(value = "积分有效期")
    private LocalDateTime protectDate;


}
