package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.holderzone.member.base.entity.card.HsaPhysicalCardCreateRecord;
import com.holderzone.member.base.mapper.card.HsaPhysicalCardCreateRecordMapper;
import com.holderzone.member.base.service.card.HsaPhysicalCardCreateRecordService;
import com.holderzone.member.base.transform.card.HsaPhysicalCardCreateRecordTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.HsaPhysicalCardCreateRecordDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.CreateRecordQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.card.CreateRecordVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 实体卡记录表
 * @date 2021/9/1
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaPhysicalCardCreateRecordServiceImpl extends HolderBaseServiceImpl<HsaPhysicalCardCreateRecordMapper, HsaPhysicalCardCreateRecord> implements HsaPhysicalCardCreateRecordService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaPhysicalCardCreateRecordMapper recordMapper;

    @Override
    public void saveCreateRecord(HsaPhysicalCardCreateRecordDTO recordDTO) {
        if (ObjectUtil.isNull(recordDTO)) {
            return;
        }
        if(ObjectUtil.isNull(recordDTO.getGuid())){
            recordDTO.setGuid(guidGeneratorUtil.getStringGuid(HsaPhysicalCardCreateRecordService.class.getSimpleName()));
        }
        recordDTO.setOperatorName(ThreadLocalCache.getHeaderUserInfo().getUserName());
        recordDTO.setOperatorAccount(ThreadLocalCache.getHeaderUserInfo().getTel());
        this.save(HsaPhysicalCardCreateRecordTransform.INSTANCE.dTO2Entity(recordDTO));
    }

    @Override
    public PageResult listPage(CreateRecordQO qo) {
        PageHelper.startPage(qo.getCurrentPage(),qo.getPageSize());
        List<CreateRecordVO> data = recordMapper.list(qo);
        return PageUtil.getPageResult(new PageInfo<>(data));
    }

}
