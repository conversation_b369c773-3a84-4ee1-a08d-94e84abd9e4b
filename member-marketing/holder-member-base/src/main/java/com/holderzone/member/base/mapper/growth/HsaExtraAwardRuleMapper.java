package com.holderzone.member.base.mapper.growth;

import com.holderzone.member.base.entity.growth.HsaExtraAwardRule;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.growth.HsaExtraAwardRuleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: pantao
 */
public interface HsaExtraAwardRuleMapper  extends HolderBaseMapper<HsaExtraAwardRule> {


    /**
     * 功能描述：根据任务guid查询额外奖励规则
     * @date 2021/11/24
     * @param taskGuid 任务guid
     * @return com.holderzone.member.common.dto.growth.HsaExtraAwardRuleDTO
     */
    List<HsaExtraAwardRuleDTO> listExtraAwardRule(@Param("taskGuid") String taskGuid);

    /**
     * 功能描述：查询额外奖励规则
     * @date 2021/11/24
     * @return java.util.List<com.holderzone.member.common.dto.growth.HsaExtraAwardRuleDTO>
     */
    List<HsaExtraAwardRuleDTO> listAllExtraAwardRule();

    /**
     * 功能描述：查询额外奖励规则根据任务id列表
     * @date 2021/11/24
     * @return java.util.List<com.holderzone.member.common.dto.growth.HsaExtraAwardRuleDTO>
     */
    List<HsaExtraAwardRuleDTO> listAllExtraAwardRuleByTask(@Param("collect")List<String> collect);
}
