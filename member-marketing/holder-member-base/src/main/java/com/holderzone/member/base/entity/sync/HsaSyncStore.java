package com.holderzone.member.base.entity.sync;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 同步门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="HsaSyncStore对象", description="同步门店")
public class HsaSyncStore extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 门店状态
     * @see com.holderzone.member.common.enums.sale.SaleStatusEnum#des
     */
    private String status;


    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店")
    private String storeName;

    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    @ApiModelProperty(value = "营业地址")
    private String address;

    @ApiModelProperty(value = "经纬度")
    private String addressPoint;

    @ApiModelProperty(value = "logo")
    private String storeLogo;

    @ApiModelProperty(value = "营业时间")
    private String time;

    @ApiModelProperty(value = "营业时间：周一")
    private Boolean isMonday;

    @ApiModelProperty(value = "营业时间：周二")
    private Boolean isTuesday;

    @ApiModelProperty(value = "营业时间：周三一")
    private Boolean isWednesday;

    @ApiModelProperty(value = "营业时间：周四")
    private Boolean isThursday;

    @ApiModelProperty(value = "营业时间：周五")
    private Boolean isFriday;

    @ApiModelProperty(value = "营业时间：周六一")
    private Boolean isSaturday;

    @ApiModelProperty(value = "营业时间：周日")
    private Boolean isSunday;


}
