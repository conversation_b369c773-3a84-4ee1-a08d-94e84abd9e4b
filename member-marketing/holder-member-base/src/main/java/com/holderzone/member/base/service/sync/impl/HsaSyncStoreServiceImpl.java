package com.holderzone.member.base.service.sync.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.SyncStoreAssembler;
import com.holderzone.member.base.entity.sync.HsaSyncStore;
import com.holderzone.member.base.mapper.sync.HsaSyncStoreMapper;
import com.holderzone.member.base.service.sync.IHsaSyncStoreService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.base.store.dto.SyncStoreQo;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 同步门店 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaSyncStoreServiceImpl extends HolderBaseServiceImpl<HsaSyncStoreMapper, HsaSyncStore> implements IHsaSyncStoreService {

    final HsaSyncStoreMapper syncStoreMapper;

    final ExternalSupport externalSupport;

    final RedissonClient redissonClient;

    final GuidGeneratorUtil guidGeneratorUtil;

    @Override

    public void save(SyncStoreDTO dto) {
        //主体+门店确定唯一
        String key = String.format(RedisKeyConstant.LOCK_SYNC_STORE, dto.getOperSubjectGuid(), dto.getId());
        final RLock lock = redissonClient.getLock(key);
        try {
            if (!lock.tryLock(10, TimeUnit.SECONDS)) {
                log.error("数据同步正忙，{}", JacksonUtils.writeValueAsString(dto));
                return;
            }
            saveStoreData(dto);
        } catch (Exception e) {
            log.error("门店同步错误:{}", e.getMessage());
            throw new MemberBaseException("门店同步错误");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 保存门店数据
     *
     * @param dto
     */
    private void saveStoreData(SyncStoreDTO dto) {
        final LambdaQueryWrapper<HsaSyncStore> wrapper = new LambdaQueryWrapper<HsaSyncStore>()
                .eq(HsaSyncStore::getOperSubjectGuid, dto.getOperSubjectGuid())
                .eq(HsaSyncStore::getStoreGuid, dto.getId());
        HsaSyncStore store = this.getOne(wrapper);
        if (Objects.isNull(store)) {
            //新增
            HsaSyncStore syncStore = SyncStoreAssembler.toHsaSyncStore(dto, new HsaSyncStore());
            final String guid = guidGeneratorUtil.getStringGuid(HsaSyncStore.class.getSimpleName());
            syncStore.setGuid(guid);
            syncStoreMapper.insertStore(syncStore);
            return;
        }
        if (dto.getDeleted() > 0) {
            //删除 todo 删除、禁用暂不同步其他业务场景（查询过滤）
            removeById(store);
            return;
        }
        //更新
        final HsaSyncStore hsaSyncStore = SyncStoreAssembler.toHsaSyncStore(dto, store);
        updateById(hsaSyncStore);
    }

    @Override
    public PageResult<SyncStoreDTO> page(SyncStoreQo qo) {
        qo.defaultOperSubjectGuid();
        if (qo.getSource() == BooleanEnum.FALSE.getCode()) {
            //先查crm接口
            return listCrmStore(qo);
        }
        qo.startPage();
        List<SyncStoreDTO> list = syncStoreMapper.list(qo);
        return PageUtil.pageResult(list);
    }

    /**
     * 直接调用crm接口
     *
     * @param qo
     * @return
     */
    private PageResult<SyncStoreDTO> listCrmStore(SyncStoreQo qo) {
        QueryStoreBasePage storeQuery = new QueryStoreBasePage();
        storeQuery.setOperatingSubjectId(qo.getOperSubjectGuid());
        storeQuery.setName(qo.getName());
        storeQuery.setPage(qo.getCurrentPage());
        storeQuery.setPageSize(qo.getPageSize());
        storeQuery.setStoreIds(qo.getStoreIds());
        storeQuery.setStatus(qo.getStatus());
        final List<StoreBaseInfo> dataList = externalSupport.storeServer(ThreadLocalCache.getSystem()).listStore(storeQuery);
        log.info("crm接口返回门店数据 get_members_store_list:{}", JacksonUtils.writeValueAsString(dataList));
        if (CollUtil.isEmpty(dataList)) {
            return PageUtil.emptyPageResult();
        }
        List<SyncStoreDTO> resultList = SyncStoreAssembler.toSyncStoreDTOList(dataList);
        return PageUtil.pageResult(resultList);
    }
}
