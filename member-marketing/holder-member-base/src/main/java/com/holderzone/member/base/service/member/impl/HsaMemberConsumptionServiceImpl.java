package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.dto.OrderTypeDTO;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberConsumptionPayWay;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionPayWayMapper;
import com.holderzone.member.base.mapper.member.HsaMemberFundingDetailMapper;
import com.holderzone.member.base.service.assembler.MerchantPayAssembler;
import com.holderzone.member.base.service.member.HsaMemberConsumptionService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.RequestBaseInfoDTO;
import com.holderzone.member.common.dto.card.RequestOrderInfoDTO;
import com.holderzone.member.common.dto.order.ReserveConsumptionDTO;
import com.holderzone.member.common.dto.terminal.SettlementBalanceDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.ConsumptionTypeEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.base.TypeItemVO;
import com.holderzone.member.common.vo.base.TypeVO;
import com.holderzone.member.common.vo.order.OrderConsumptionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会员消费信息
 * @date 2021/11/26
 */
@Service
@Slf4j
public class HsaMemberConsumptionServiceImpl implements HsaMemberConsumptionService {

    /**
     * 微信来源
     */
    public static final int WECHAT = 53;

    public static final String REMARK = "好搭档预定";

    private final HsaMemberConsumptionMapper consumptionMapper;

    private final HsaMemberFundingDetailMapper fundingDetailMapper;

    @Resource
    private ExternalSupport externalSupport;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaMemberConsumptionPayWayMapper consumptionPayWayMapper;

    public HsaMemberConsumptionServiceImpl(HsaMemberConsumptionMapper consumptionMapper, HsaMemberFundingDetailMapper fundingDetailMapper, GuidGeneratorUtil guidGeneratorUtil,
                                           HsaMemberConsumptionPayWayMapper consumptionPayWayMapper) {
        this.consumptionMapper = consumptionMapper;
        this.fundingDetailMapper = fundingDetailMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.consumptionPayWayMapper = consumptionPayWayMapper;
    }

    @Override
    public BigDecimal totalPeriodAmount(String memberInfoGuid, Integer totalPeriodType, String totalPeriod, LocalDateTime gmtCreate) {
        BigDecimal total = consumptionMapper.totalPeriodAmount(
                buildBeginTime(totalPeriodType, totalPeriod)
                , totalPeriodType
                , memberInfoGuid
                , ConsumptionTypeEnum.TYPE_CONSUMPTION.getCode()
                , gmtCreate);
        return ObjectUtil.isNull(total) ? BigDecimal.ZERO : total;
    }

    @Override
    public Integer totalPeriodCount(String memberInfoGuid, Integer totalPeriodType, String totalPeriod, LocalDateTime gmtCreate, BigDecimal consumptionIgnoreAmount) {
        Integer total = consumptionMapper.totalPeriodCount(buildBeginTime(totalPeriodType, totalPeriod), totalPeriodType, memberInfoGuid, gmtCreate, consumptionIgnoreAmount);
        return ObjectUtil.isNull(total) ? NumberConstant.NUMBER_0 : total;
    }

    @Override
    public BigDecimal totalPeriodRechargeAmount(String memberInfoGuid, Integer totalPeriodType, String totalPeriod, LocalDateTime gmtCreate) {
        BigDecimal total = consumptionMapper.totalPeriodAmount(
                buildBeginTime(totalPeriodType, totalPeriod)
                , totalPeriodType
                , memberInfoGuid
                , ConsumptionTypeEnum.TYPE_RECHARGE.getCode()
                , gmtCreate);
        return ObjectUtil.isNull(total) ? BigDecimal.ZERO : total;
    }

    private String buildBeginTime(Integer totalPeriodType, String totalPeriod) {
        LocalDateTime beginTime = LocalDateTime.now();
        LocalDate beginDate = beginTime.toLocalDate();
        if (Objects.nonNull(totalPeriodType)) {
            switch (Objects.requireNonNull(DataUnitEnum.Enum(totalPeriodType))) {
                case DAY:
                    beginTime = formatDayPeriod(beginDate, totalPeriod);
                    break;
                case WEEK:
                    beginTime = formatWeekPeriod(beginDate, totalPeriod);
                    break;
                case MONTH:
                    beginTime = formatMonthPeriod(beginDate, totalPeriod);
                    break;
                case YEAR:
                    beginTime = formatYearPeriod(beginDate, totalPeriod);
                    break;
//            case FOREVER:
//                break;
                default:
                    break;
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return formatter.format(beginTime);
    }


    private LocalDateTime formatDayPeriod(LocalDate beginDate, String totalPeriod) {
        LocalTime periodTime = LocalTime.parse(totalPeriod, DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime nowTime = LocalTime.now();
        long minutes = Duration.between(nowTime, periodTime).toMinutes();
        //当前时间还没到累计时间，取昨天为开始时间
        if (minutes > NumberConstant.NUMBER_0) {
            return LocalDateTime.of(beginDate.minusDays(NumberConstant.NUMBER_1), periodTime);
        } else {
            return LocalDateTime.of(beginDate, periodTime);
        }
    }

    private LocalDateTime formatWeekPeriod(LocalDate beginDate, String totalPeriod) {
        int nowWeek = beginDate.getDayOfWeek().getValue();
        //判断时间
        if (nowWeek >= Integer.parseInt(totalPeriod)) {
            return LocalDateTime.of(beginDate.minusDays(nowWeek - Integer.parseInt(totalPeriod)), LocalTime.now());
        } else {
            int periodDay = nowWeek + NumberConstant.NUMBER_7 - Integer.parseInt(totalPeriod);
            return LocalDateTime.of(beginDate.minusDays(periodDay), LocalTime.now());
        }
    }

    private LocalDateTime formatMonthPeriod(LocalDate beginDate, String totalPeriod) {
        int nowDay = beginDate.getDayOfMonth();
        //判断时间
        if (nowDay >= Integer.parseInt(totalPeriod)) {
            return LocalDateTime.of(beginDate.minusDays(nowDay - Integer.parseInt(totalPeriod)), LocalTime.now());
        } else {
            LocalDateTime periodDate = LocalDateTime.of(beginDate.getYear(), beginDate.getMonthValue(),
                    Integer.parseInt(totalPeriod), NumberConstant.NUMBER_0, NumberConstant.NUMBER_0);
            return periodDate.minusMonths(NumberConstant.NUMBER_1);
        }
    }

    private LocalDateTime formatYearPeriod(LocalDate beginDate, String totalPeriod) {
        LocalDate periodTime = LocalDate.parse(beginDate.getYear() + StringConstant.STR_BIAS_TWO + totalPeriod
                , DateTimeFormatter.ofPattern("yyyy-M-d"));
        long days = ChronoUnit.DAYS.between(periodTime, beginDate);
        //判断时间
        if (days >= NumberConstant.NUMBER_0) {
            return LocalDateTime.of(periodTime, LocalTime.now());
        } else {
            return LocalDateTime.of(periodTime.minusYears(NumberConstant.NUMBER_1), LocalTime.now());
        }
    }

    @Override
    public void saveReserve(ReserveConsumptionDTO consumptionDTO) {
        HsaMemberConsumption consumption = new HsaMemberConsumption();
        String consumptionGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        consumption.setGuid(consumptionGuid);
        consumption.setOperSubjectGuid(consumptionDTO.getOperSubjectGuid());
        consumption.setMemberInfoGuid(consumptionDTO.getMemberGuid());
        consumption.setStoreGuid(consumptionDTO.getMerchantGuid());
        consumption.setStoreName(consumptionDTO.getMerchantName());
        consumption.setOrderAmount(consumptionDTO.getDeposit());
        consumption.setOrderPaidAmount(consumptionDTO.getDeposit());
        consumption.setOrderDiscountAmount(BigDecimal.ZERO);
        consumption.setCardBalancePayAmount(BigDecimal.ZERO);
        consumption.setConsumptionType(ConsumptionTypeEnum.TYPE_CONSUMPTION.getCode());
        // 已支付的就是支付成功时间，不支付的就是下单时间
        LocalDateTime gmtCreate = ObjectUtils.isEmpty(consumptionDTO.getPayTime()) ?
                consumptionDTO.getGmtCreate() : consumptionDTO.getPayTime();
        if (!ObjectUtils.isEmpty(consumptionDTO.getCancelTime())) {
            gmtCreate = consumptionDTO.getCancelTime();
        }
        consumption.setGmtCreate(gmtCreate);
        consumption.setConsumptionTime(consumption.getGmtCreate());
        consumption.setOrderGuid(consumptionDTO.getReserveGuid());
        consumption.setBankTransactionId(consumptionDTO.getBankTransactionId());
        consumption.setOrderNumber(consumptionDTO.getOrderNo());
        consumption.setOrderTime(consumptionDTO.getGmtCreate());
        consumption.setOrderSource(WECHAT);
        consumption.setIsCancel(ObjectUtils.isEmpty(consumptionDTO.getCancelTime()) ? BooleanEnum.FALSE.getCode()
                : BooleanEnum.TRUE.getCode());
        consumption.setOperatorTelName(consumptionDTO.getMemberGuid());
        consumption.setRemark(REMARK);
        consumption.setOrderType(ConsumptionOrderTypeEnum.PARTNER.getCode());
        consumption.setRefundTime(consumptionDTO.getCancelTime());

        consumptionMapper.insert(consumption);

        if (!ObjectUtils.isEmpty(consumptionDTO.getPayWay())) {
            HsaMemberConsumptionPayWay consumptionPayWay = new HsaMemberConsumptionPayWay();
            consumptionPayWay.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionPayWay.class.getSimpleName()));
            consumptionPayWay.setPayWay(consumptionDTO.getPayWay());
            consumptionPayWay.setPayName(PayWayEnum.getPayName(consumptionDTO.getPayWay()));
            consumptionPayWay.setPayAmount(consumptionDTO.getDeposit());
            consumptionPayWay.setConsumptionGuid(consumptionGuid);
            consumptionPayWayMapper.insert(consumptionPayWay);
        }
    }

    @Override
    public OrderConsumptionVO queryOrderStatusByOrderNumber(String orderNumber) {
        log.info("查询订单状态，订单号：{}", orderNumber);
        HsaMemberConsumption hsaMemberConsumption = consumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, orderNumber));

        if (Objects.nonNull(hsaMemberConsumption)) {
            OrderConsumptionVO orderConsumptionVO = new OrderConsumptionVO();
            BeanUtils.copyProperties(hsaMemberConsumption, orderConsumptionVO);
            return orderConsumptionVO;
        }
        return null;
    }

    /**
     * 保存消费记录信息
     *
     * @param requestConfirmPay RequestConfirmPay
     * @param memberInfoCard    HsaMemberInfoCard
     */
    @Override
    public HsaMemberConsumption saveConsumptionRecord(RequestConfirmPayVO requestConfirmPay,
                                                      HsaMemberInfoCard memberInfoCard,
                                                      HeaderUserInfo headerUserInfo,
                                                      SettlementBalanceDTO settlementBalanceDTO,
                                                      HsaOperationMemberInfo hsaOperationMemberInfo) {
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(requestConfirmPay.getRequestOrderInfo().getOrderTime())) {
            now = requestConfirmPay.getRequestOrderInfo().getOrderTime();
        }
        RequestBaseInfoDTO requestBaseInfo = requestConfirmPay.getRequestBaseInfo();
        String memberConsumptionGuid = requestConfirmPay.getMemberConsumptionGuid();
        RequestOrderInfoDTO requestOrderInfo = requestConfirmPay.getRequestOrderInfo();
        HsaMemberConsumption memberConsumption = MerchantPayAssembler.getHsaMemberConsumption(
                requestConfirmPay,
                memberInfoCard,
                settlementBalanceDTO,
                hsaOperationMemberInfo,
                requestOrderInfo,
                requestBaseInfo,
                now);
        memberConsumption.setGuid(memberConsumptionGuid);
        memberConsumption.setRemark(externalSupport.baseServer(ThreadLocalCache.getSystem()).getBusinessName(requestOrderInfo.getOrderType())
                + "-" + requestOrderInfo.getOrderNumber());
        if (requestOrderInfo.getOrderSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()) {
            memberConsumption.setOperatorTelName(headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel());
        } else {
            memberConsumption.setOperatorTelName(hsaOperationMemberInfo.getUserName() + StringConstant.STR_BIAS + hsaOperationMemberInfo.getPhoneNum());
        }
        consumptionMapper.insert(memberConsumption);
        return memberConsumption;
    }

    @Override
    public void synConsumptionDiscount(String orderNumber, BigDecimal discountAmount) {
        log.info("同步优惠金额，订单号：{}，优惠金额：{}", orderNumber, discountAmount);
        HsaMemberConsumption hsaMemberConsumption = consumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, orderNumber)
                .eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        if (Objects.isNull(hsaMemberConsumption)) {
            return;
        }
        hsaMemberConsumption.setOrderDiscountAmount(hsaMemberConsumption.getOrderDiscountAmount().subtract(discountAmount));
        consumptionMapper.updateByGuid(hsaMemberConsumption);
    }

    @Override
    public TypeVO getType(OrderTypeDTO dto) {

        List<HsaMemberConsumption> consumptionList = consumptionMapper.selectList(
                new LambdaQueryWrapper<HsaMemberConsumption>()
                        .eq(HsaMemberConsumption::getMemberInfoGuid, dto.getMemberInfoGuid())
                        .eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaMemberConsumption::getEnterpriseGuid, ThreadLocalCache.getEnterpriseGuid())
                        .select(HsaMemberConsumption::getOrderType, HsaMemberConsumption::getOrderSource)
        );

        Set<Integer> orderTypeCodes = new TreeSet<>();
        Set<Integer> orderSourceCodes = new TreeSet<>();
        consumptionList.forEach(consumption -> {
            if (consumption.getOrderType() != null) orderTypeCodes.add(consumption.getOrderType());
            if (consumption.getOrderSource() != null) orderSourceCodes.add(consumption.getOrderSource());
        });

        Map<Integer, String> orderTypeMap = Arrays.stream(MarketConsumptionOrderTypeEnum.values())
                .collect(Collectors.toMap(
                        MarketConsumptionOrderTypeEnum::getCode,
                        MarketConsumptionOrderTypeEnum::getDes,
                        (a, b) -> a
                ));
        Map<Integer, String> orderSourceMap = Arrays.stream(SourceTypeEnum.values())
                .collect(Collectors.toMap(
                        SourceTypeEnum::getCode,
                        SourceTypeEnum::getDes,
                        (a, b) -> a
                ));

        List<TypeItemVO> orderTypeItems = orderTypeCodes.stream()
                .map(code -> new TypeItemVO(
                        code,
                        orderTypeMap.get(code)
                ))
                .collect(Collectors.toList());

        List<TypeItemVO> orderSourceItems = orderSourceCodes.stream()
                .map(code -> new TypeItemVO(
                        code,
                        orderSourceMap.get(code)
                ))
                .collect(Collectors.toList());

        return new TypeVO()
                .setOrderType(orderTypeItems)
                .setSourceType(orderSourceItems);
    }

}
