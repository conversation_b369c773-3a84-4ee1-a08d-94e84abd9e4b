package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @description: 亲属管理 实体类
 * <AUTHOR>
 */
@Data
public class HsaKinsfolkManagement implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 头像地址
     */
    private String iconUrl;

    /***
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 班级
     */
    private String classAndGrade;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 人脸识别（用户分组id）
     */
    private String groupId;

    /**
     * 人脸识别（用户id）
     */
    private String userId;

    /**
     * 人脸识别（请求标识码，随机数，唯一）
     */
    private String logId;

    /**
     * 人脸识别（图片的唯一表示）
     */
    private String faceToken;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 是否删除,0未删除,1已删除
     */
    @TableLogic//逻辑删除标识
    private Integer isDelete;

}
