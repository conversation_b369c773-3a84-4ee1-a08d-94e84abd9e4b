package com.holderzone.member.base.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.base.entity.member.HsaMemberInfoWxMp;
import com.holderzone.member.base.mapper.member.HsaMemberInfoWxMpMapper;
import com.holderzone.member.base.service.member.HsaMemberInfoWxMpService;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 会员微信公众号关联表 服务实现类
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HsaMemberInfoWxMpServiceImpl extends ServiceImpl<HsaMemberInfoWxMpMapper, HsaMemberInfoWxMp> implements HsaMemberInfoWxMpService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public void save(String appId, String unionId, String openId) {
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        if (StringUtils.isEmpty(unionId) && StringUtils.isEmpty(openId)) {
            return;
        }
        HsaMemberInfoWxMp memberInfoWxMp = new HsaMemberInfoWxMp();
        memberInfoWxMp.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoWxMp.class.getSimpleName()));
        memberInfoWxMp.setAppId(appId);
        memberInfoWxMp.setOpenId(openId);
        memberInfoWxMp.setUnionId(unionId);
        baseMapper.save(memberInfoWxMp);
    }

    @Override
    public String getOpenIdByAppIdAndUnionId(String appId, String unionId) {
        QueryWrapper<HsaMemberInfoWxMp> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaMemberInfoWxMp::getAppId, appId);
        qw.lambda().eq(HsaMemberInfoWxMp::getUnionId, unionId);
        qw.last("limit 1");
        HsaMemberInfoWxMp memberInfoWxMp = getOne(qw);
        if (Objects.isNull(memberInfoWxMp)) {
            return null;
        }
        return memberInfoWxMp.getOpenId();
    }

    @Override
    public List<HsaMemberInfoWxMp> getOpenIdByAppIdAndUnionIds(String appId, List<String> unionIds) {
        QueryWrapper<HsaMemberInfoWxMp> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaMemberInfoWxMp::getAppId, appId);
        qw.lambda().in(HsaMemberInfoWxMp::getUnionId, unionIds);
        return list(qw);
    }
}
