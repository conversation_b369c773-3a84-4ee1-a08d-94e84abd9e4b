package com.holderzone.member.base.service.gift.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.recharge.HsaRechargeGiftAmountRecord;
import com.holderzone.member.base.mapper.gift.HsaRechargeGiftAmountRecordMapper;
import com.holderzone.member.base.service.gift.HsaRechargeGiftAmountRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.gift.QueryGiftAmountRecordPage;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.excel.ExcelGiftAmountRecordVO;
import com.holderzone.member.common.vo.excel.GiftAmountRecordVO;
import com.holderzone.member.common.vo.gift.CalculateRechargeGiftRecordVO;
import com.holderzone.member.common.vo.gift.RechargeGiftAmountRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@Slf4j
public class HsaRechargeGiftAmountRecordServiceImpl
        extends HolderBaseServiceImpl<HsaRechargeGiftAmountRecordMapper, HsaRechargeGiftAmountRecord>
        implements HsaRechargeGiftAmountRecordService {

    private final HsaRechargeGiftAmountRecordMapper hsaRechargeGiftAmountRecordMapper;

    private final ExternalSupport externalSupport;

    public HsaRechargeGiftAmountRecordServiceImpl(HsaRechargeGiftAmountRecordMapper hsaRechargeGiftAmountRecordMapper, ExternalSupport externalSupport) {
        this.hsaRechargeGiftAmountRecordMapper = hsaRechargeGiftAmountRecordMapper;
        this.externalSupport = externalSupport;
    }

    @Override
    public PageResult queryRechargeGiftAmountRecordPage(QueryGiftAmountRecordPage query) {
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        List<RechargeGiftAmountRecordVO> recordList = getAmountRecordList(query);
        return PageUtil.getPageResult(new PageInfo<>(recordList));
    }

    @Override
    public GiftAmountRecordVO exportRechargeGiftAmountRecord(QueryGiftAmountRecordPage query) {
        query.setPageSize(20000);
        Integer num = hsaRechargeGiftAmountRecordMapper.queryGiftAmountRecordNum(query);
        if (num == 0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (num > 20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        GiftAmountRecordVO giftAmountRecordVO = new GiftAmountRecordVO();
        List<ExcelGiftAmountRecordVO> excelGiftAmountRecordVOList = Lists.newArrayList();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<RechargeGiftAmountRecordVO> rechargeGiftAmountRecordVOS = hsaRechargeGiftAmountRecordMapper.queryGiftAmountRecordPage(query);
        for (RechargeGiftAmountRecordVO rechargeGiftAmountRecordVO : rechargeGiftAmountRecordVOS) {
            ExcelGiftAmountRecordVO excelGiftAmountRecordVOS = new ExcelGiftAmountRecordVO();
            excelGiftAmountRecordVOS.setGmtCreate(df.format(rechargeGiftAmountRecordVO.getGmtCreate()))
                    .setOrderNumber(rechargeGiftAmountRecordVO.getOrderNumber())
                    .setRechargeStatus(rechargeGiftAmountRecordVO.getRechargeStatus() == 1 ? "充值成功" : "已退款")
                    .setMemberName(rechargeGiftAmountRecordVO.getMemberName())
                    .setMemberPhone(rechargeGiftAmountRecordVO.getMemberPhone())
                    .setCardName(rechargeGiftAmountRecordVO.getCardName())
                    .setCardNum(rechargeGiftAmountRecordVO.getCardNum())
                    .setChangeSource(externalSupport.baseServer(ThreadLocalCache.getSystem()).getSourceTypeEnum(rechargeGiftAmountRecordVO.getChangeSource()))

                    .setStoreName(rechargeGiftAmountRecordVO.getStoreName())
                    .setPayName(rechargeGiftAmountRecordVO.getPayName())
                    .setRechargeAmount(rechargeGiftAmountRecordVO.getRechargeAmount() + "")
                    .setGiftAmount(rechargeGiftAmountRecordVO.getGiftAmount() + "");

            excelGiftAmountRecordVOList.add(excelGiftAmountRecordVOS);
        }
        giftAmountRecordVO.setExcelGiftAmountRecordVOList(excelGiftAmountRecordVOList);
        giftAmountRecordVO.setName(query.getActivityName());
        return giftAmountRecordVO;
    }

    @Override
    public CalculateRechargeGiftRecordVO calculateRechargeAmountRecord(QueryGiftAmountRecordPage query) {
        VerifyUtil.notNull(query.getActivityGuid(), "活动guid不能为空");
        query.setRechargeStatus(1);
        final CalculateRechargeGiftRecordVO sumRechargeAmountVo = hsaRechargeGiftAmountRecordMapper.sumRechargeAmount(query);
        //top门店
        final String top1Store = hsaRechargeGiftAmountRecordMapper.getTop1Store(query);
        sumRechargeAmountVo.setStoreName(top1Store);
        //手机号去重
        final Integer countMemberPhone = Optional.ofNullable(hsaRechargeGiftAmountRecordMapper.countMemberPhone(query)).orElse(0);
        //白卡：卡号去重(之前白卡，后面合卡的单独算)
        final Integer countMemberCard = Optional.ofNullable(hsaRechargeGiftAmountRecordMapper.countMemberCard(query)).orElse(0);
        sumRechargeAmountVo.setRechargeMemberNum(countMemberPhone + countMemberCard);
        //退单
        query.setRechargeStatus(0);
        final Integer refundOrderNum = hsaRechargeGiftAmountRecordMapper.countRechargeOrder(query);
        sumRechargeAmountVo.setRefundOrderNum(refundOrderNum);

        sumRechargeAmountVo.calAverageAmount();
        return sumRechargeAmountVo;
    }

    private List<RechargeGiftAmountRecordVO> getAmountRecordList(QueryGiftAmountRecordPage query) {
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        return hsaRechargeGiftAmountRecordMapper.queryGiftAmountRecordPage(query);
    }

    @Override
    public void updateRechargeStatus(String operSubjectGuid, String memberFundingDetailGuid,
                                     Integer rechargeStatus) {
        hsaRechargeGiftAmountRecordMapper.updateRechargeStatus(operSubjectGuid, memberFundingDetailGuid, rechargeStatus);
    }

    @Override
    public List<String> getSuccessOrder(List<String> activityGuidList) {
        List<String> successOrder = Lists.newArrayList();
        List<HsaRechargeGiftAmountRecord> rechargeGiftAmountRecords = hsaRechargeGiftAmountRecordMapper.selectList(new LambdaQueryWrapper<HsaRechargeGiftAmountRecord>()
                .in(HsaRechargeGiftAmountRecord::getRechargeActivityGuid, activityGuidList)
                .eq(HsaRechargeGiftAmountRecord::getRechargeStatus, 1));

        if (CollUtil.isNotEmpty(rechargeGiftAmountRecords)) {
            successOrder = rechargeGiftAmountRecords.stream().map(HsaRechargeGiftAmountRecord::getMemberFundingDetailGuid).collect(Collectors.toList());
        }
        return successOrder;
    }
}
