package com.holderzone.member.base.entity.recharge;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员卡金额充值赠送统计记录
 *
 * <AUTHOR>
 * @since 2023-07049 16:45:11
 */
@ApiModel("会员卡金额充值赠送统计记录")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaRechargeGiftAmountRecord extends HsaBaseEntity implements Serializable {
    private static final long serialVersionUID = -57323975547772597L;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 对应明细记录Guid
     */
    private String memberFundingDetailGuid;

    /**
     * 活动guid
     */
    private String rechargeActivityGuid;

    /**
     * 活动name
     */
    private String rechargeActivityName;

    /**
     * 充值状态 0 退款  1 成功
     */
    private Integer rechargeStatus;

    /**
     * 卡名称
     */
    @ApiModelProperty("卡名称")
    private String cardName;

    /**
     * 实体卡号或卡号 账户编号
     */
    @ApiModelProperty("实体卡号或卡号")
    private String cardNum;

    /**
     * 卡guid
     */
    @ApiModelProperty("卡guid")
    private String cardGuid;

    /**
     * 会员持卡GUID
     */
    @ApiModelProperty("会员持卡GUID")
    private String memberInfoCardGuid;

    /**
     * 会员名称
     */
    @ApiModelProperty("会员名称")
    private String memberName;

    /**
     * memberPhone
     */
    @ApiModelProperty("memberPhone")
    private String memberPhone;

    /**
     * 门店GUID
     */
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 卡实充变动金额
     */
    @ApiModelProperty("卡实充变动金额")
    private BigDecimal rechargeAmount;

    /**
     * 卡赠送变动金额
     */
    @ApiModelProperty("卡赠送变动金额")
    private BigDecimal giftAmount;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 变动来源 0：管理后台 2：一体机 53：小程序
     */
    private Integer changeSource;

    /**
     * 支付方式
     */
    private String payName;
}