package com.holderzone.member.base.event.delayed;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.base.event.listener.RedisTaskHandlerListener;
import com.holderzone.member.common.enums.delayed.RedisDelayedEnum;

import com.holderzone.member.common.exception.MemberBaseException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;


import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class RedisDelayedFactory {

    private final Map<String, RedisDelayedEvent<String>> redisDelayedQueueMap = new HashMap<>();

    @Resource
    private RedisTaskHandlerListener redisTaskHandlerListener;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public RedisDelayedEvent<String> getDelayedQueue(RedisDelayedEnum delayedEnum) {
        String delayedDes = delayedEnum.getDes();
        log.info("获取延时类型：{}", delayedDes);
        if (ObjectUtil.isNull(RedisDelayedEnum.getByDes(delayedDes))) {
            throw new MemberBaseException("队列类型不存在，请添加类型！");
        }
        RedisDelayedEvent<String> redisDelayedEvent;
        redisDelayedEvent = redisDelayedQueueMap.get(delayedDes);
        if (CollUtil.isEmpty(redisDelayedQueueMap) || ObjectUtil.isNull(redisDelayedEvent)) {
            redisDelayedEvent = toDelayed(delayedEnum);
            redisDelayedQueueMap.put(delayedDes, redisDelayedEvent);
        }
        if (Objects.nonNull(redisDelayedEvent)) {
            redisDelayedEvent.sendRefresh();
        }
        return redisDelayedEvent;
    }

    private RedisDelayedEvent<String> toDelayed(RedisDelayedEnum delayedEnum) {
        switch (Objects.requireNonNull(delayedEnum)) {
            case GIFT_AMOUNT:
                return giftAmountDelayed(delayedEnum.getDes());
            case GIFT_TEST:
                return delayedTest(delayedEnum.getDes());
            default:
                log.info("类型不存在");
                break;
        }
        return null;
    }

    private RedisDelayedEvent<String> giftAmountDelayed(String delayedDes) {
        RedisDelayedEvent<String> redisDelayedEvent;
        redisDelayedEvent =
                new RedisDelayedEvent<>(delayedDes,
                        String.class,
                        redisTaskHandlerListener::sendFixedGiftAmount,
                        stringRedisTemplate);
        return redisDelayedEvent;
    }

    private RedisDelayedEvent<String> delayedTest(String delayedDes) {
        RedisDelayedEvent<String> redisDelayedEvent;
        redisDelayedEvent =
                new RedisDelayedEvent<>(delayedDes,
                        String.class,
                        redisTaskHandlerListener::sendTest,
                        stringRedisTemplate);
        return redisDelayedEvent;
    }
}