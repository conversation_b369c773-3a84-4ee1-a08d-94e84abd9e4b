package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Joiner;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberCardRule;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.card.HsaStoreCardRuleMapper;
import com.holderzone.member.base.mapper.member.TerMemberInfoMapper;
import com.holderzone.member.base.service.card.HsaElectronicCardService;
import com.holderzone.member.base.service.card.HsaMemberCardRuleService;
import com.holderzone.member.base.service.member.*;
import com.holderzone.member.base.transform.member.MemberInfoTransform;
import com.holderzone.member.base.util.ValidTimeUtils;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.TerOpenCardConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.constant.ValidTimeConstant;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.card.TerMemberCardDTO;
import com.holderzone.member.common.dto.card.TerOpenCardConditionDTO;
import com.holderzone.member.common.dto.card.TerOpenCardDetailDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.RegisterChannelEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.TerAbleCardDetailQO;
import com.holderzone.member.common.qo.card.TerLoginMemberCardQO;
import com.holderzone.member.common.qo.card.TerOpenCardQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.card.MemberCardStoreDataQO;
import com.holderzone.member.common.vo.card.TerAbleCardDetailVO;
import com.holderzone.member.common.vo.card.TerAbleCardVO;
import com.holderzone.member.common.vo.card.TerCardDetailVO;
import com.holderzone.member.common.vo.member.ConsumptionOrderTotalVO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import com.holderzone.member.common.vo.member.TerMemberDetailVO;
import com.holderzone.member.common.vo.member.TerMemberPropertyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 一体机终端会员操作
 * @date 2021/11/17
 */
@Slf4j
@Service
public class TerminalMemberInfoServiceImpl implements TerminalMemberInfoService {

    private final HsaOperationMemberInfoService operationMemberInfoService;

    private final HsaMemberLabelService labelService;

    private final TerMemberInfoMapper terMemberMapper;

    private final HsaLabelSettingService labelSettingService;

    private final StoreBaseService storeBaseService;

    private final HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    private final HsaMemberFundingDetailService fundingDetailService;

    private final HsaMemberCardRuleService memberCardRuleService;

    private final HsaElectronicCardService electronicCardService;

    private final HsaMemberInfoCardMapper memberInfoCardMapper;

    private final HsaCardBaseInfoMapper memberInfoCardBase;

    private final RequestGoalgoService hsaRequestGoalgoService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private SystemRoleHelper systemRoleHelper;


    public TerminalMemberInfoServiceImpl(HsaOperationMemberInfoService operationMemberInfoService,
                                         HsaMemberLabelService labelService, TerMemberInfoMapper terMemberMapper,
                                         HsaLabelSettingService labelSettingService,
                                         StoreBaseService storeBaseService,
                                         HsaStoreCardRuleMapper hsaStoreCardRuleMapper,
                                         HsaMemberFundingDetailService fundingDetailService,
                                         HsaMemberCardRuleService memberCardRuleService,
                                         HsaElectronicCardService electronicCardService,
                                         HsaMemberInfoCardMapper memberInfoCardMapper,
                                         HsaCardBaseInfoMapper memberInfoCardBase,
                                         RequestGoalgoService hsaRequestGoalgoService) {
        this.operationMemberInfoService = operationMemberInfoService;
        this.labelService = labelService;
        this.terMemberMapper = terMemberMapper;
        this.labelSettingService = labelSettingService;
        this.storeBaseService = storeBaseService;
        this.hsaStoreCardRuleMapper = hsaStoreCardRuleMapper;
        this.fundingDetailService = fundingDetailService;
        this.memberCardRuleService = memberCardRuleService;
        this.electronicCardService = electronicCardService;
        this.memberInfoCardMapper = memberInfoCardMapper;
        this.memberInfoCardBase = memberInfoCardBase;
        this.hsaRequestGoalgoService = hsaRequestGoalgoService;
    }

    @Override
    public String add(TerSaveMemberInfoQO saveMemberInfo) {
        SaveOperationMemberInfoQO memberInfoQO = MemberInfoTransform.INSTANCE.memberInfoTerQO2QO(saveMemberInfo);
        return operationMemberInfoService.saveOperationMemberInfo(memberInfoQO);
    }

    @Override
    public String login(TerLoginMemberCardQO qo) {

        //二维码扫码
        if (qo.getLoginNum().contains(QrcodeTypeEnum.MEMBER.getDes())) {
            log.info("二维码扫码，二维码key=======>{}", qo.getLoginNum());
            Object content = redisTemplate.opsForValue().get(qo.getLoginNum());
            if (null == content) {
                throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_INVALID,
                        ThreadLocalCache.getOperSubjectGuid()));
            }
            qo.setLoginNum(content.toString());
            log.info("二维码扫码，二维码卡号======>{}", qo.getLoginNum());
        }
        //先去查询会员信息
        HsaOperationMemberInfo memberInfo = operationMemberInfoService.getOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .and(wq ->
                        wq
                                .eq(HsaOperationMemberInfo::getMemberAccount, qo.getLoginNum())
                                .or()
                                .eq(HsaOperationMemberInfo::getPhoneNum, qo.getLoginNum())));
        //手机号 or 账号 = 直接登录
        if (ObjectUtil.isNotNull(memberInfo)) {
            if (memberInfo.getAccountState() == 1) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
            }
            return memberInfo.getGuid();
        }
        //卡号查询
        LambdaQueryWrapper<HsaMemberInfoCard> query = new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .and(wq -> wq
                        .eq(HsaMemberInfoCard::getElectronicCardNum, qo.getLoginNum())
                        .or()
                        .eq(HsaMemberInfoCard::getPhysicalCardNum, qo.getLoginNum()
            ));
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.selectOne(query);
        //校验会员和会员卡
        return judgeLoginInfo(hsaMemberInfoCard, qo.getLoginNum());
    }

    private String judgeLoginInfo(HsaMemberInfoCard hsaMemberInfoCard, String loginNum) {
        if (ObjectUtil.isNull(hsaMemberInfoCard)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        if (StringUtils.isEmpty(hsaMemberInfoCard.getMemberPhoneNum())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NOT_BOUND_MEMBER,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        if (StringUtils.isEmpty(hsaMemberInfoCard.getMemberInfoGuid())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_USER_NOT_REGISTERED);
        }
        final HsaCardBaseInfo cardBaseInfo =
                memberInfoCardBase.selectOne(new LambdaQueryWrapper<HsaCardBaseInfo>().eq(HsaCardBaseInfo::getGuid,
                        hsaMemberInfoCard.getCardGuid()));
        //卡种校验
        if (ObjectUtil.equal(cardBaseInfo.getCardStatus(), CardStatusEnum.FORBIDDEN.getCode())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (ObjectUtil.equal(cardBaseInfo.getCardStatus(), CardStatusEnum.FINISH.getCode())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
        }
        validateCardNum(hsaMemberInfoCard, loginNum);
        //先去查询会员信息
        HsaOperationMemberInfo memberInfo = operationMemberInfoService.getOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getGuid,hsaMemberInfoCard.getMemberInfoGuid()));
        if (ObjectUtil.isNotNull(memberInfo)) {
            if (memberInfo.getAccountState() == 1) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED,ThreadLocalCache.getOperSubjectGuid()));
            }
            return memberInfo.getGuid();
        }
        throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
    }

    private void validateCardNum(HsaMemberInfoCard hsaMemberInfoCard, String loginNum) {
        //电子卡
        if (ObjectUtil.equal(hsaMemberInfoCard.getElectronicCardNum(), loginNum)) {
            if (ObjectUtil.equal(hsaMemberInfoCard.getElectronicCardState(), EletronicCardStateEnum.ALREADY_FROZEN.getCode())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_DISABLED, ThreadLocalCache.getOperSubjectGuid()));            }
            if (ObjectUtil.equal(hsaMemberInfoCard.getElectronicCardState(), EletronicCardStateEnum.HAVE_EXPIRED.getCode())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
        //实体卡
        if (ObjectUtil.equal(hsaMemberInfoCard.getPhysicalCardNum(), loginNum)) {
            //需求如此  冻结依旧算正常
            if (ObjectUtil.equal(hsaMemberInfoCard.getPhysicalCardState(), PhysicalCardStateEnum.NOT_ACTIVATE.getCode())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_NOT_ACTIVATED, ThreadLocalCache.getOperSubjectGuid()));
            }
            if (ObjectUtil.equal(hsaMemberInfoCard.getPhysicalCardState(), PhysicalCardStateEnum.HAVE_EXPIRED.getCode())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
    }

    @Override
    public TerMemberDetailVO getDetail(String guid) {
        TerMemberDetailVO detail = MemberInfoTransform.INSTANCE.memberInfoVO2TerVO(operationMemberInfoService.getOperationMemberDetail(guid));
        List<MemberLabelVO> memberLabelList = labelService.listMemberLabelByMemberInfoGuid(guid);
        //所关联的所有标签信息
        detail.setMemberLabelList(memberLabelList);
        return detail;
    }

    @Override
    public boolean updateOperationMemberState(TerMemberStateQO request) {
        OperationMemberStateQO qo = new OperationMemberStateQO();
        qo.setAccountState(request.getAccountState());
        qo.setGuids(Collections.singletonList(request.getGuid()));
        return operationMemberInfoService.updateOperationMemberState(qo);
    }

    @Override
    public boolean updateByMemberGuid(UpdateOperationMemberInfoQO request) {
        return operationMemberInfoService.updateByMemberGuid(request);
    }

    @Override
    public TerMemberPropertyVO getMemberProperty(String guid) {
        HsaOperationMemberInfo memberInfo = operationMemberInfoService.queryByGuid(guid);
        if (ObjectUtil.isNull(memberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        TerMemberPropertyVO property = new TerMemberPropertyVO();
        property.setMemberAccountMoney(memberInfo.getMemberAccountMoney());
        property.setMemberGiftAccountMoney(memberInfo.getMemberGiftAccountMoney());
        property.setMemberSubsidyAmountMoney(memberInfo.getMemberSubsidyAmountMoney());
        property.setMemberAccountTotalMoney(property.getMemberAccountMoney()
                .add(property.getMemberGiftAccountMoney())
                .add(property.getMemberSubsidyAmountMoney()));
        property.setMemberIntegral(memberInfo.getMemberIntegral());

        //去查询会员的会员卡信息
        List<TerMemberCardDTO> cardList = terMemberMapper.listTerminalMemberCard(guid);
        //过滤出有效的会员卡 默认卡排第一位其它根据开卡时间排序
        List<TerMemberCardDTO> validCardList = cardList.stream().filter(obj ->
                (ObjectUtil.notEqual(obj.getElectronicCardState(), EletronicCardStateEnum.HAVE_EXPIRED.getCode())
                        && ObjectUtil.isNotNull(obj.getElectronicCardState()))
                        || (ObjectUtil.notEqual(obj.getPhysicalCardState(), PhysicalCardStateEnum.HAVE_EXPIRED.getCode())
                        && ObjectUtil.isNotNull(obj.getPhysicalCardState())))
                .sorted(Comparator.comparing(TerMemberCardDTO::getGmtCreate))
                .sorted(Comparator.comparing(TerMemberCardDTO::getDefaultCard).reversed())
                .collect(Collectors.toList());
        property.setValidCardList(validCardList);
        //过滤出无效的会员卡
        List<TerMemberCardDTO> invalidCardList = cardList.stream().filter(obj ->
                (ObjectUtil.equal(obj.getElectronicCardState(), EletronicCardStateEnum.HAVE_EXPIRED.getCode())
                        || ObjectUtil.isNull(obj.getElectronicCardState()))
                        && (ObjectUtil.equal(obj.getPhysicalCardState(), PhysicalCardStateEnum.HAVE_EXPIRED.getCode())
                        || ObjectUtil.isNull(obj.getPhysicalCardState())))
                .sorted(Comparator.comparing(TerMemberCardDTO::getCardValidityDate).reversed())
                .collect(Collectors.toList());
        property.setInvalidCardList(invalidCardList);
        return property;
    }

    @Override
    public List<TerAbleCardVO> listTerminalAbleCard(String guid) {
        List<TerAbleCardVO> ableCardList = terMemberMapper.listTerminalAbleCard(ThreadLocalCache.getOperSubjectGuid());
        if (CollectionUtil.isEmpty(ableCardList)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_CURRENTLY_UNAVAILABLE, ThreadLocalCache.getOperSubjectGuid()));

        }
        List<String> openedCardGuidList;
        if (StringUtils.isEmpty(guid)) {
            openedCardGuidList = new ArrayList<>();
        } else {
            openedCardGuidList = terMemberMapper.listTerminalOpenedCardGuid(guid);
        }
        ableCardList.forEach(obj -> {
            if (openedCardGuidList.contains(obj.getCardGuid())) {
                obj.setOpenState(OpenStateEnum.OPENED.getCode());
            } else {
                obj.setOpenState(OpenStateEnum.NOT_OPENED.getCode());
            }
        });
        ableCardList.sort(Comparator.comparing(TerAbleCardVO::getOpenState));
        return ableCardList;
    }

    @Override
    public TerAbleCardDetailVO getTerminalAbleCardDetail(TerAbleCardDetailQO qo) {
        TerOpenCardDetailDTO detailDTO = terMemberMapper.getTerAbleCardDetail(qo);
        //会员卡查询为空
        if (ObjectUtil.isNull(detailDTO)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, ThreadLocalCache.getOperSubjectGuid()));
        }
        //会员卡查询状态不为发送中
        if (ObjectUtil.notEqual(detailDTO.getSendStatus(), SendCardStateEnum.CARD_STATE_START.getCode())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, ThreadLocalCache.getOperSubjectGuid()));
        }
        //去构建返回数据
        return buildReturnData(qo.getGuid(), detailDTO);
    }

    @Override
    public String openElectronicCard(TerOpenCardQO qo) {
        TerAbleCardDetailQO detail = new TerAbleCardDetailQO();
        detail.setCardGuid(qo.getCardGuid());
        detail.setGuid(qo.getGuid());
        //只支持自主开通
        if (ObjectUtil.notEqual(qo.getOpenWay(), ElectronicCardOpenWayEnum.INDEPENDENT_OPEN.getCode())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_TYPE_ERROR, ThreadLocalCache.getOperSubjectGuid()));

        }
        //再去查询此卡的开通信息
        TerOpenCardDetailDTO detailDTO = terMemberMapper.getTerAbleCardDetail(detail);
        if (ObjectUtil.isNull(detailDTO)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (ObjectUtil.notEqual(qo.getOpenWay(), detailDTO.getOpenWay())
                || ObjectUtil.notEqual(qo.getSelfType(), detailDTO.getSelfType())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_INFO_UPDATED, ThreadLocalCache.getOperSubjectGuid()));
        }
        //去判断发卡状态和剩余数量
        if (ObjectUtil.notEqual(detailDTO.getSendStatus(), SendCardStateEnum.CARD_STATE_START)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (ObjectUtil.equal(detailDTO.getSendCountLimit(), BooleanEnum.TRUE.getCode())
                && detailDTO.getSurplusCount() <= 0) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, ThreadLocalCache.getOperSubjectGuid()));
        }
        HsaOperationMemberInfo memberInfo = operationMemberInfoService.queryByGuid(qo.getGuid());
        if (ObjectUtil.isNull(memberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        if (ObjectUtil.notEqual(memberInfo.getAccountState(), AccountStateEnum.NORMAL.getCode())) {
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_MEMBER_DISABLED);
        }
        //去判断开卡用户的范围
        judgeOpenScope(memberInfo, detailDTO);
        //去开通会员卡
        return payOpenCard(qo, memberInfo, detailDTO);
    }

    @Override
    public TerCardDetailVO getCardDetail(String ownGuid) {
        return null;
    }

    private TerAbleCardDetailVO buildReturnData(String memberInfoGuid, TerOpenCardDetailDTO detailDTO) {
        TerAbleCardDetailVO detailVO = new TerAbleCardDetailVO();
        //设置基础信息
        detailVO.setCardGuid(detailDTO.getCardGuid());
        detailVO.setCardImage(detailDTO.getCardImage());
        detailVO.setCardName(detailDTO.getCardName());
        detailVO.setCardValueMoney(detailDTO.getCardValueMoney());
        detailVO.setCardEmployExplain(detailDTO.getCardEmployExplain());
        detailVO.setSelfType(detailDTO.getSelfType());
        detailVO.setOpenWay(detailDTO.getOpenWay());
        //设置开卡时效和使用有效期
        setValidityTime(detailVO, detailDTO);
        //设置剩余数量
        detailVO.setSurplusCount(
                ObjectUtil.equal(detailDTO.getSendCountLimit(), BooleanEnum.FALSE.getCode()) ?
                        TerOpenCardConstant.UNLIMITED : String.valueOf(detailDTO.getSurplusCount()));
        //设置会员范围和需满足的条件
        setScope(detailVO, detailDTO);
        //设置适用范围信息
        setStoreScopeList(detailVO, detailDTO);
        detailVO.setOpenWay(detailDTO.getOpenWay());
        detailVO.setPayMoney(buildPayMoney(detailDTO));
        //设置开通状态
        detailVO.setOpenState(detailDTO.getOpenState());

        //如果有开卡条件
        if (ObjectUtil.equal(detailDTO.getOpenWay(), ElectronicCardOpenWayEnum.SATISFY_CONDITION_AUTO.getCode())) {
            //设置开卡条件信息
            setOpenCondition(memberInfoGuid, detailVO, detailDTO);
        }
        //设置立省金额
        if (detailVO.getCardValueMoney().compareTo(detailVO.getPayMoney()) > 0) {
            detailVO.setSpareMoney(detailVO.getCardValueMoney().subtract(detailVO.getPayMoney()));
        }
        return detailVO;
    }

    private BigDecimal buildPayMoney(TerOpenCardDetailDTO detailDTO) {
        BigDecimal payMoney = BigDecimal.ZERO;
        //自主开通设置金额
        if (ObjectUtil.equal(detailDTO.getOpenWay(), ElectronicCardOpenWayEnum.INDEPENDENT_OPEN.getCode())) {
            if (ObjectUtil.equal(detailDTO.getSelfType(), SelfTypeEnum.DIRECT_PAY.getCode())) {
                payMoney = detailDTO.getSelfPaymentMoney();
            }
            if (ObjectUtil.equal(detailDTO.getSelfType(), SelfTypeEnum.RECHARGE.getCode())) {
                payMoney = detailDTO.getSelfRechargeMoney();
            }
        }
        return payMoney;
    }

    private void setOpenCondition(String memberInfoGuid, TerAbleCardDetailVO detailVO, TerOpenCardDetailDTO detailDTO) {
        TerOpenCardConditionDTO condition = new TerOpenCardConditionDTO();
        ConsumptionOrderTotalVO consumptionOrderTotal = fundingDetailService.getConsumptionOrderTotal(memberInfoGuid);
        if (ObjectUtil.isNotNull(detailDTO.getConsumeSumMoney())) {
            condition.setConsumeSumMoney(detailDTO.getConsumeSumMoney());
            condition.setFinishConsumeSumMoney(consumptionOrderTotal.getAllPaidAmount());
        }
        if (ObjectUtil.isNotNull(detailDTO.getConsumeNum())) {
            condition.setConsumeNum(detailDTO.getConsumeNum());
            condition.setFinishConsumeNum(consumptionOrderTotal.getAllConsumptionCount());
        }
        condition.setHasConsumeProduct(ObjectUtil.isNull(detailDTO.getConsumeProduct())
                ? BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());

        if (ObjectUtil.equal(condition.getHasConsumeProduct(), BooleanEnum.TRUE.getCode())) {
            //TODO 系统还没有商品信息
            condition.setFinishConsumeProduct(BooleanEnum.FALSE.getCode());
        }
        detailVO.setCondition(condition);
        //未满足条件的开通
        detailVO.setOpenState(OpenStateEnum.UNSATISFIED_OPEN.getCode());
    }

    private void setStoreScopeList(TerAbleCardDetailVO detailVO, TerOpenCardDetailDTO detailDTO) {
        MemberCardStoreDataQO storeQO = new MemberCardStoreDataQO();
        storeQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        storeQO.setCurrentPage(NumberConstant.NUMBER_0);
        storeQO.setPageSize(NumberConstant.NUMBER_9999);
        storeQO.setCardGuid(detailDTO.getCardGuid());
        if (ObjectUtil.equal(detailDTO.getApplicableAllStore(), BooleanEnum.TRUE.getCode())) {
            //所有门店
            detailVO.setStoreList(storeBaseService.appletGetStoreInfo());
        } else {
            //部分门店
            detailVO.setStoreList(hsaStoreCardRuleMapper.findStoreCardRule(storeQO));
        }
    }

    private void setScope(TerAbleCardDetailVO detailVO, TerOpenCardDetailDTO detailDTO) {
        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.ALL_REGISTER.getCode())) {
            detailVO.setMemberScope(TerOpenCardConstant.MEMBER_ALL_REGISTER);
        }
        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.SATISFY_CONDITION.getCode())) {
            //满足所有条件
            if (ObjectUtil.equal(detailDTO.getScopeConditionType(), NumberConstant.NUMBER_0)) {
                detailVO.setMemberScope(TerOpenCardConstant.MEMBER_SATISFY_ALL_CONDITION);
            }
            //满足部分条件
            if (ObjectUtil.equal(detailDTO.getScopeConditionType(), NumberConstant.NUMBER_1)) {
                detailVO.setMemberScope(TerOpenCardConstant.MEMBER_SATISFY_ANY_CONDITION);
            }
            //设置条件多渠道
            if (StringUtils.isNotEmpty(detailDTO.getRegisterChannel())) {
                List<Integer> listRegisterChannel = JSONArray.parseArray(detailDTO.getRegisterChannel(), Integer.class);
                List<String> listRegisterChannelName = new ArrayList<>();
                listRegisterChannel.forEach(obj -> listRegisterChannelName.add(RegisterChannelEnum.getNameByCode(obj)));
                detailVO.setRegisterChannel(Joiner.on(TerOpenCardConstant.SEPARATOR_MARK).join(listRegisterChannelName));
            }
            //设置条件多标签
            if (StringUtils.isNotEmpty(detailDTO.getLabelGuid())) {
                List<String> listLabelGuid = JSONArray.parseArray(detailDTO.getLabelGuid(), String.class);
                List<HsaLabelSetting> labelList = labelSettingService.queryByGuids(listLabelGuid);
                List<String> labelNameList = labelList.stream().map(HsaLabelSetting::getLabelName).collect(Collectors.toList());
                detailVO.setMemberLabel(Joiner.on(TerOpenCardConstant.SEPARATOR_MARK).join(labelNameList));
            }
        }

        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.ASSIGN.getCode())) {
            detailVO.setMemberScope(TerOpenCardConstant.MEMBER_ASSIGN);
        }
    }

    private void setValidityTime(TerAbleCardDetailVO detailVO, TerOpenCardDetailDTO detailDTO) {
        detailVO.setPeriodOfValidity(ValidTimeConstant.PERMANENT_VALIDITY);
        detailVO.setUsefulLife(ValidTimeConstant.PERMANENT_VALIDITY);
        if (ObjectUtil.equal(detailDTO.getCardValidity(), CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode())) {
            detailVO.setPeriodOfValidity(String.format(
                    ValidTimeConstant.TIME_QUANTUM, LocalDate.now(), detailDTO.getCardValidityDate()));
            detailVO.setUsefulLife(String.format(
                    ValidTimeConstant.MAX_VALIDITY, detailDTO.getCardValidityDate()));
        }
        if (ObjectUtil.equal(detailDTO.getCardValidity(), CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode())) {
            detailVO.setPeriodOfValidity(String.format(
                    ValidTimeConstant.TIME_QUANTUM,
                    LocalDate.now(),
                    ValidTimeUtils.buildValidityDate(detailDTO.getValidityUnit(), detailDTO.getCardValidityTime())));
            detailVO.setUsefulLife(String.format(
                    ValidTimeConstant.AFTER_VALIDITY,
                    detailDTO.getCardValidityTime(),
                    ReturnPeriodTypeEnum.findMsgByCode(detailDTO.getValidityUnit())));
        }
    }

    private String payOpenCard(TerOpenCardQO qo, HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        BigDecimal payMoney = buildPayMoney(detailDTO);
        if (!BigDecimalUtil.equal(payMoney, qo.getPayMoney())) {
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_PAY_MONEY_ERROR);
        }
        if (BigDecimalUtil.equal(qo.getPayMoney(), BigDecimal.ZERO)) {
            //直接开通
            return directOpen(memberInfo, detailDTO);
        }
        if (ObjectUtil.equal(detailDTO.getSelfType(), SelfTypeEnum.RECHARGE)) {
            //去充值
        }
        if (ObjectUtil.equal(detailDTO.getSelfType(), SelfTypeEnum.DIRECT_PAY)) {
            //去支付
        }

        return null;
    }

    private String directOpen(HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        return electronicCardService.terminalOpenCard(buildMemberCardOpenData(memberInfo, detailDTO));
    }

    MemberCardOpenDTO buildMemberCardOpenData(HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        MemberCardOpenDTO openDTO = new MemberCardOpenDTO();
        openDTO.setCardGuid(detailDTO.getCardGuid());
        openDTO.setCardName(detailDTO.getCardName());
        openDTO.setSource(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
        openDTO.setMemberInfoGuid(memberInfo.getGuid());
        openDTO.setMemberPhoneNum(memberInfo.getPhoneNum());
        openDTO.setOpenWay(detailDTO.getSelfType());
        openDTO.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
        openDTO.setEnterpriseGuid(memberInfo.getEnterpriseGuid());
        return openDTO;
    }

    private void judgeOpenScope(HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.ALL_REGISTER.getCode())) {
            return;
        }
        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.SATISFY_CONDITION.getCode())) {
            //去查询会员的来源
            boolean satisfyRegisterChannel = isSatisfyRegisterChannel(memberInfo, detailDTO);
            boolean satisfyLabel = isSatisfyLabel(memberInfo, detailDTO);
            //所有条件
            if (ObjectUtil.equal(detailDTO.getScopeConditionType(), NumberConstant.NUMBER_0) && (!satisfyRegisterChannel || !satisfyLabel)) {
                    throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL);

            }
            //部分条件
            if (ObjectUtil.equal(detailDTO.getScopeConditionType(), NumberConstant.NUMBER_1) && (!satisfyRegisterChannel && !satisfyLabel)) {
                    throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL);

            }
        }
        if (ObjectUtil.equal(detailDTO.getOpenCardScopeType(), RegisterMemberTypeEnum.ASSIGN.getCode())) {
            HsaMemberCardRule memberCardRule = memberCardRuleService.getOne(new LambdaQueryWrapper<HsaMemberCardRule>()
                    .eq(HsaMemberCardRule::getOperationMemberInfoGuid, memberInfo.getGuid())
                    .eq(HsaMemberCardRule::getCardGuid, detailDTO.getCardGuid()));
            if (ObjectUtil.isNull(memberCardRule)) {
                throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL);
            }
        }
    }

    private boolean isSatisfyLabel(HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        if (StringUtils.isNotEmpty(detailDTO.getLabelGuid())) {
            List<MemberLabelVO> labelList = labelService.listMemberLabelByMemberInfoGuid(memberInfo.getGuid());
            AtomicBoolean hasLabel = new AtomicBoolean(false);
            labelList.forEach(obj -> {
                if (detailDTO.getLabelGuid().contains(obj.getLabelGuid())) {
                    hasLabel.set(true);
                }
            });
            return hasLabel.get();

        }
        return true;
    }

    private static boolean isSatisfyRegisterChannel(HsaOperationMemberInfo memberInfo, TerOpenCardDetailDTO detailDTO) {
        if (StringUtils.isNotEmpty(detailDTO.getRegisterChannel())) {
            Integer sourceType = memberInfo.getSourceType();
            return detailDTO.getRegisterChannel().contains(String.valueOf(sourceType));
        }
        return true;
    }
}
