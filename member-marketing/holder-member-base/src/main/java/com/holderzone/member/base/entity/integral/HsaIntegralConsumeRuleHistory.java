package com.holderzone.member.base.entity.integral;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 积分消耗规则历史
 *
 * <AUTHOR>
 * @since 2024-01-4
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class HsaIntegralConsumeRuleHistory extends HsaIntegralConsumeRule implements Serializable {

    private static final long serialVersionUID = -6347421404136628545L;
}
