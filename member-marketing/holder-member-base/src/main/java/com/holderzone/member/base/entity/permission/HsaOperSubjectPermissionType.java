package com.holderzone.member.base.entity.permission;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 运营主体权限类型表
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_oper_subject_permission_type")
public class HsaOperSubjectPermissionType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 岗位id或者角色id
     */
    private String positionGuid;

    /**
     * 1：会员管理
     * 2：营销中心
     */
    private Integer sourceType;

    /**
     * 0：岗位
     * 1:角色
     */
    private Integer isRole;

    /**
     * 判断运营主体选则是全部还是部分、
     * @see com.holderzone.member.common.enums.permission.SubjectPermissionTypeEnum
     */
    private Integer isAll;
}
