package com.holderzone.member.base.manage;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.CouponPackageAssembler;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseBaseVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseCountVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseVO;
import com.holderzone.member.common.support.StoreCommonSupport;
import com.holderzone.member.common.util.page.PageUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 优惠劵核销
 *
 * <AUTHOR>
 * @date 2023/11/21
 **/
@Slf4j
@Component
public class MemberCouponUseManage {

    @Resource
    private HsaMemberCouponLinkMapper memberCouponLinkMapper;

    @Resource
    private HsaMemberCouponUseMapper memberCouponUseMapper;

    @Resource
    private StoreCommonSupport storeCommonSupport;

    @Resource
    private IHsaMemberCouponLinkService memberCouponLinkService;


    /**
     * 核销明细查询
     *
     * @param qo 核销条件
     * @return 明细
     */
    public PageResult<CouponUseVO> pageDetail(CouponUseQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //分页参数
        qo.startPage();
        //查询 券包发放明细
        List<CouponUseVO> packageLinkList = memberCouponLinkMapper.listUseDetail(qo);
        if (CollUtil.isEmpty(packageLinkList)) {
            return PageUtil.emptyPageResult();
        }
        //查询规则
        final List<String> guids = packageLinkList.stream().map(CouponUseBaseVO::getMemberCouponLinkGuid).collect(Collectors.toList());
        final List<HsaMemberCouponLink> couponLinks = memberCouponLinkMapper.queryByGuids(guids);
        final Map<String, HsaMemberCouponLink> couponLinkMap = couponLinks.stream().collect(Collectors.toMap(HsaMemberCouponLink::getGuid, v -> v, (v1, v2) -> v2));
        for (CouponUseVO couponUseVO : packageLinkList) {
            HsaMemberCouponLink couponLink = couponLinkMap.get(couponUseVO.getMemberCouponLinkGuid());
            if (couponLink != null){
                couponUseVO.setCouponType(couponLink.getCouponType());
                couponUseVO.setCouponPackageType(couponLink.getCouponPackageType());
            }

            //规则
            Optional.ofNullable(couponLinkMap.get(couponUseVO.getMemberCouponLinkGuid()))
                    .ifPresent(hsaMemberCouponLink ->
                            couponUseVO.setRuleVO(CouponPackageAssembler.toCouponGiveRuleVO(hsaMemberCouponLink))
                    );
        }
        return PageUtil.pageResult(packageLinkList);
    }

    /**
     * 统计核销结果
     *
     * @param qo 核销条件
     * @return 核销结果
     */
    public CouponUseCountVO countCouponUseNum(CouponUseQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //核销统计
        CouponUseCountVO couponUseCountVO = memberCouponLinkMapper.countUse(qo);
        if (couponUseCountVO == null) {
            //无核销
            return new CouponUseCountVO();
        }
        //带动消费金额
        final BigDecimal sumOrderPaidAmount = memberCouponLinkMapper.sumOrderPaidAmount(qo);
        couponUseCountVO.setOrderPaidAmount(sumOrderPaidAmount);

        //top核销数门店
        final String storeGuid = memberCouponLinkMapper.queryTopStoreByUse(qo);
        if (StringUtil.isNotBlank(storeGuid)) {
            //门店必然存在，查询不到就是异常
            final List<SyncStoreDTO> storeList = storeCommonSupport.getAllStoreList(Collections.singletonList(storeGuid));
            log.info("核销明细查询:门店guid-{},crm门店返回：{}", storeGuid, JacksonUtils.writeValueAsString(storeList));
            if (CollUtil.isNotEmpty(storeList)) {
                final SyncStoreDTO storeDTO = storeList.get(0);
                //核销数量：top门店
                couponUseCountVO.setStoreGuid(storeDTO.getStoreGuid());
                couponUseCountVO.setStoreName(storeDTO.getStoreName());
            }
        }
        return couponUseCountVO;
    }

    /**
     * 标记使用
     *
     * @param qo 标记参数
     */
    public void markUse(CouponMarkUseQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        memberCouponLinkService.markUse(qo);
    }

    /**
     * 作废
     *
     * @param qo 作废参数
     */
    public void invalid(CouponMarkInvalidQO qo) {
        memberCouponLinkService.invalid(qo);
    }
}
