package com.holderzone.member.base.manage;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.response.AlipayCommerceEducateStudentIdentityVerifyResponse;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.base.manage.biz.CertifiedAliPayMemberBO;
import com.holderzone.member.base.service.member.HsaMemberCertificateStudentInfoService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.helper.AliPayRequestHelper;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.common.vo.certificate.CertifiedPupilActivityVO;
import com.holderzone.member.common.vo.certificate.CertifiedStudentVerifiedVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * 会员认证学生信息 业务层
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MemberCertificateStudentInfoManager {

    private final HsaMemberCertificateStudentInfoService memberCertificateStudentInfoService;

    private final AliPayRequestHelper aliPayRequestHelper;

    private final MemberMallToolFeign memberMallToolFeign;

    private final MemberMarketingFeign memberMarketingFeign;

    /**
     * 会员认证学生信息
     */
    public CertifiedPupilActivityVO certificate(CertifiedAliPayMemberBO biz) {
        // 校验
        verify(biz);
        // 业务处理
        return postHandle(biz);
    }

    /**
     * 校验
     */
    private void verify(CertifiedAliPayMemberBO biz) {
        // 参数校验
        verifyParams(biz);
        // 判断会员是否参与学生认证
        verifyIsCertificated(biz.getMemberUserId());
        // 判断是否需要进行单次核验
        verifyIdentity(biz);
    }

    /**
     * 参数校验
     */
    private void verifyParams(CertifiedAliPayMemberBO biz) {
        String memberInfoGuid = biz.getMemberInfoGuid();
        if (StringUtils.isEmpty(memberInfoGuid)) {
            throw new MemberBaseException("会员信息为空");
        }
    }

    /**
     * 查询会员是否已经参与学生认证
     */
    private void verifyIsCertificated(String userId) {
        if (Boolean.TRUE.equals(memberMarketingFeign.checkJoinStudentActivity(userId))) {
            throw new MemberBaseException("重复认证");
        }
    }


    /**
     * 服务端二次核验身份
     */
    private void verifyIdentity(CertifiedAliPayMemberBO biz) {
        if (!biz.isNeedSingleCheck()) {
            log.warn("服务端不需要二次核验身份");
            return;
        }
        HsaAliAppletInfoVO aliAppAuthInfo = memberMallToolFeign.getAliAppletInfoByOper(ThreadLocalCache.getOperSubjectGuid());
        log.info("支付宝授权信息返回:{}", JacksonUtils.writeValueAsString(aliAppAuthInfo));
        if (Objects.isNull(aliAppAuthInfo)) {
            throw new MemberBaseException("支付宝小程序登录授权为空");
        }
        try {
            // 通过appAuthCode获取用户信息（userId、accessToken）
            AlipayClient alipayClient = aliPayRequestHelper.getAlipayClient(aliAppAuthInfo.getAppId(),
                    aliAppAuthInfo.getApplyPrivateKey(), aliAppAuthInfo.getAliPublicKey());
            AlipaySystemOauthTokenResponse authTokenResponse = aliPayRequestHelper.oauthTokenRequest(alipayClient, biz.getAppAuthCode());
            // 通过accessToken、bizToken核验学生身份
            AlipayCommerceEducateStudentIdentityVerifyResponse identityVerifyResponse =
                    aliPayRequestHelper.identityVerifyRequest(alipayClient, biz.getBizToken(), authTokenResponse.getAccessToken());
            biz.joinStudentInfo(identityVerifyResponse);
        } catch (AlipayApiException e) {
            log.error("学生身份单次核验失败, e:{}", e.getMessage());
            throw new MemberBaseException("学生身份校验异常");
        }
    }

    private CertifiedPupilActivityVO postHandle(CertifiedAliPayMemberBO biz) {
        if (!biz.isCollegeOnlineTag()) {
            CertifiedPupilActivityVO result = new CertifiedPupilActivityVO();
            result.setIslandPupilCouponDTOList(Lists.newArrayList());
            return result;
        }
        // 保存认证学生信息
        memberCertificateStudentInfoService.saveInfo(biz.getMemberCertificateStudentInfo());
        // 认证通过 发优惠券
        return joinCertificateActivity(biz);
    }

    /**
     * 参与学生认证活动
     */
    private CertifiedPupilActivityVO joinCertificateActivity(CertifiedAliPayMemberBO biz) {
        CertifiedPupilActivityVO result = new CertifiedPupilActivityVO();
        result.setIslandPupilCouponDTOList(Lists.newArrayList());
        try {
            CertifiedStudentVerifiedVO certifiedStudentVerifiedVO = new CertifiedStudentVerifiedVO();
            certifiedStudentVerifiedVO.setCertifiedActivityGuid(biz.getActivityGuid());
            certifiedStudentVerifiedVO.setUserId(biz.getMemberUserId());
            log.info("支付宝学生认证发放优惠券入参:{}", JacksonUtils.writeValueAsString(certifiedStudentVerifiedVO));
            result = memberMarketingFeign.studentVerified(certifiedStudentVerifiedVO);
            log.info("支付宝学生认证发放优惠券信息:{}", JacksonUtils.writeValueAsString(result));
        } catch (Exception e) {
            log.error("支付宝学生认证发放优惠券失败, e:{}", e.getMessage());
        }
        return result;
    }
}
