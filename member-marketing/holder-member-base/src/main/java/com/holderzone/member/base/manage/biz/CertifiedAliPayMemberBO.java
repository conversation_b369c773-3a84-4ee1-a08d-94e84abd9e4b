package com.holderzone.member.base.manage.biz;

import com.alipay.api.response.AlipayCommerceEducateStudentIdentityVerifyResponse;
import com.holderzone.member.base.entity.member.HsaMemberCertificateStudentInfo;
import com.holderzone.member.common.enums.alipay.YesOrNoEnums;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

/**
 * 会员支付宝认证BO
 */
@Data
public class CertifiedAliPayMemberBO implements Serializable {

    private static final long serialVersionUID = -2530867934367796622L;

    /**
     * 认证活动guid
     */
    private String activityGuid;

    /**
     * 单次核验
     * 'Y'：需要，'N'：不需
     * 要（同 studentInfo.needSingleCheck 字段,兼容使⽤）
     */
    private String needSingleCheck;

    /**
     * 支付宝授权码
     */
    private String appAuthCode;

    /**
     * biz_token
     */
    private String bizToken;

    /**
     * 认证信息
     */
    private HsaMemberCertificateStudentInfo memberCertificateStudentInfo;

    public String getMemberInfoGuid() {
        return Optional.of(memberCertificateStudentInfo).orElse(new HsaMemberCertificateStudentInfo()).getMemberInfoGuid();
    }

    public String getMemberUserId() {
        return Optional.of(memberCertificateStudentInfo).orElse(new HsaMemberCertificateStudentInfo()).getUserId();
    }

    /**
     * 是否需要核验
     */
    public boolean isNeedSingleCheck() {
        return YesOrNoEnums.Y.name().equals(needSingleCheck);
    }

    /**
     * 是否学生
     */
    public boolean isCollegeOnlineTag() {
        return YesOrNoEnums.Y.getNumber().equals(memberCertificateStudentInfo.getCollegeOnlineTag());
    }

    /**
     * 加入学生信息
     */
    public void joinStudentInfo(AlipayCommerceEducateStudentIdentityVerifyResponse identityVerifyResponse) {
        if (Objects.isNull(identityVerifyResponse)) {
            return;
        }
        this.memberCertificateStudentInfo.setCollegeOnlineTag(identityVerifyResponse.getCollegeOnlineTag());
        this.memberCertificateStudentInfo.setExamineStatus(identityVerifyResponse.getExamineStatus());
        this.memberCertificateStudentInfo.setDegree(identityVerifyResponse.getDegree());
        this.memberCertificateStudentInfo.setEnrollDate(identityVerifyResponse.getEnrollDate());
        this.memberCertificateStudentInfo.setType(identityVerifyResponse.getType());
        this.memberCertificateStudentInfo.setMemberShipStatus(identityVerifyResponse.getMemberShipStatus());
        this.memberCertificateStudentInfo.setSchoolName(identityVerifyResponse.getSchoolName());
        this.memberCertificateStudentInfo.setSchoolId(identityVerifyResponse.getSchoolId());
    }

}
