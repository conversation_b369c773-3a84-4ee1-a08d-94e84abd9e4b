package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 结算单操作记录
 * @author: pan tao
 * @create: 2022-06-13 17:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaClearingStatementOperateRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 结算单编号
     */
    private String clearingStatementNumber;

    /**
     * 订单记录guid
     */
    private String orderRecordGuid;

    /**
     * 操作类型 0:订单退款 1:应收调整 2:移除订单
     * @see com.holderzone.member.common.enums.credit.OperateType
     */
    private Integer operateType;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单状态 0：已结账 1：已取消
     */
    private Integer orderStatus;

    /**
     * 订单实付金额
     */
    private BigDecimal orderPaidAmount;

    /**
     * 挂账金额
     */
    private BigDecimal creditAmount;

    /**
     * 挂账使用人
     */
    private String creditUsername;

    /**
     * 应收调整金额
     */
    private BigDecimal receivableAdjustAmount;

    /**
     * 应收调整类型 0：增加 1：减少
     */
    private Integer receivableAdjustType;

    /**
     * 调整备注
     */
    private String adjustRemark;

    /**
     * 操作人
     */
    private String operationUsername;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
