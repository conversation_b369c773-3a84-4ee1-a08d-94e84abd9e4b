package com.holderzone.member.base.service.system;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.system.HsaDict;
import com.holderzone.member.common.vo.system.DictVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据字典表 服务类
 * </p>
 */
public interface DictService extends IService<HsaDict> {

    Map<String, List<DictVO>> getDict(List<String> fields);

    List<DictVO> getOtherServiceDict();

    HsaDict getByField(String field);

}
