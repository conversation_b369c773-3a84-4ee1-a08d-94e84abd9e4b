package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.credit.AppletPaymentCreditDTO;
import com.holderzone.member.common.vo.credit.CreditUserAmountInfoVO;
import com.holderzone.member.common.vo.credit.CreditUserVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-02-09 18:10
 */
public interface HsaCreditUserMapper extends HolderBaseMapper<HsaCreditUser> {


    List<CreditUserVO> queryCreditUser(@Param("creditInfoGuid") String creditInfoGuid);

    List<AppletPaymentCreditDTO> queryAppletPaymentAccount(@Param("operSubjectGuid") String operSubjectGuid,
                                                           @Param("memberInfoGuid") String memberInfoGuid);

    void updateDefaultChooseByMemberGuid(@Param("memberGuid") String memberGuid);

    BigDecimal getUnlimitedUserCreditUpperLimit(@Param("creditInfoGuid") String creditInfoGuid);

    List<HsaCreditUser> queryHsaCreditUserByClearing(@Param("clearingStatementNumber") String clearingStatementNumber);

    /**
     * 获取当前用户挂账明细统计信息
     * @param memberInfoGuid 请求参数guid
     * @param creditInfoGuid 挂账账户guid
     * @return 操作结果
     */
    CreditUserAmountInfoVO getCreditUserAmountInfo(String memberInfoGuid, String creditInfoGuid);


}
