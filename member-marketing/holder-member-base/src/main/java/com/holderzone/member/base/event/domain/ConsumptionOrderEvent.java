package com.holderzone.member.base.event.domain;

import org.springframework.context.ApplicationEvent;

/**
 * 消费订单
 *
 * <AUTHOR>
 */

public class ConsumptionOrderEvent extends ApplicationEvent {

    private static final long serialVersionUID = -4805583245899993982L;
    /**
     * 事件类型
     */
    private ConsumptionOrderEventEnum eventType;

    /**
     * 事件内容
     */
    private String content;

    public ConsumptionOrderEvent(Object source, ConsumptionOrderEventEnum eventType, String content) {
        super(source);
        this.eventType = eventType;
        this.content = content;
    }

    public ConsumptionOrderEventEnum getEventType() {
        return eventType;
    }

    public String getContent() {
        return content;
    }

    public void setEventType(ConsumptionOrderEventEnum eventType) {
        this.eventType = eventType;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
