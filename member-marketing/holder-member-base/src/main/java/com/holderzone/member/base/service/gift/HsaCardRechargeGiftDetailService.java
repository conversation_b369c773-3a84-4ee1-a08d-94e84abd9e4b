package com.holderzone.member.base.service.gift;

import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.common.qo.card.TerCardRechargeGiftQO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;
import com.holderzone.member.common.vo.gift.RechargeSuccessGiftDetailVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * HsaCardRechargeGiftRuleService
 */
public interface HsaCardRechargeGiftDetailService extends IHolderBaseService<HsaCardRechargeGiftDetail> {

    /**
     * 处理充值赠送
     *
     * @param terCardRechargeGiftQO
     */
    RechargeOrderGiftSummaryVO dealRechargeGift(TerCardRechargeGiftQO terCardRechargeGiftQO);


    void fixedEffectiveGiftAmount();


    List<String> getActivityDetailsMemberLabels(String fundingDetailGuid);

    List<HsaCardRechargeGiftDetail> getNoRefreshDetails(String fundingDetailGuid);

    /**
     * 充值成功获取赠送明细记录
     *
     * @param orderNumber
     * @return
     */
    RechargeSuccessGiftDetailVO getRechargeSuccessGiftDetail(String orderNumber);


    /**
     * 获取活动订单数量
     *
     * @param activityGuidList activityGuidList
     * @return Map<String, Integer>
     */
    Map<String, Integer> getActivityOrderNum(List<String> activityGuidList);

    /**
     * 获取卡剩余冻结金额
     * @param cardQO cardQO
     * @return
     */
    BigDecimal getMemberCardFreezeAmount(CardFreezeBalanceAmountQO cardQO);

    /**
     * 批量获取卡剩余冻结金额
     */
    Map<String, BigDecimal> getMemberCardFreezeAmountMap(List<CardFreezeBalanceAmountQO> cardList);

    /**
     * 清空冻结金额
     */
    void clearMemberCardFreezeAmount(String memberInfoGuid);
}