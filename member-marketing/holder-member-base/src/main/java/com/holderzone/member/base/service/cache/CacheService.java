package com.holderzone.member.base.service.cache;


import com.holderzone.member.common.dto.user.HeaderUserInfo;

import java.util.List;

/**
 * 缓存服务
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface CacheService {

    /**
     * 缓存token
     *
     * @param appToken
     * @param openId
     */
    void createTokenForClient(Integer source, String appToken, String openId);

    void deleteToken(String openId);

    void setGradeCache(String key, String orderNum, Integer value);

    void setOrderCache(String orderKey, String value);

    String getCache(String key);

    /**
     * 缓存wx_member
     *
     * @param openId
     * @param userInfo
     */
    void createMember(String openId, HeaderUserInfo userInfo);

    HeaderUserInfo getTokenMember(String openId);

    /**
     * 缓存等级刷新标识
     *
     * @param cacheKey
     * @param operSubjectGuid
     */
    void createGradeRefresh(String cacheKey, String operSubjectGuid, String roleType);

    /**
     * 获取缓存等级刷新标识
     *
     * @param cacheKey
     */
    boolean getGradeRefresh(String cacheKey);

    /**
     * 根据Token的值清除指定source来源的token
     *
     * @param cacheKey
     */
    boolean cleanToken(String cacheKey, String operSubjectGuid);

    boolean cleanToken(String cacheKey, String operSubjectGuid, String roleType);

    boolean delete(String cacheKey);

    void updateInventoryNum(String key, String code);

    int getInventoryNum(String key, String code);

    Boolean setLock(String key);

    Boolean setCardLock(String key);

    void setOldGradeEquitiesList(String operSubjectGuid, String finalRoleType, List<String> gradeInfoList);

    List<String> getOldGradeEquitieList(String operSubjectGuid, String finalRoleType);
}
