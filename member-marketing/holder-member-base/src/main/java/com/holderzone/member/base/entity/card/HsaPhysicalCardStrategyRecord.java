package com.holderzone.member.base.entity.card;

import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.enums.credit.PayStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 实体卡押金支付记录
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaPhysicalCardStrategyRecord extends HsaBaseEntity {

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 建卡记录
     */
    private String makePhysicalCardRecordGuid;

    /**
     * 支付记录guid
     */
    private String payGuid;

    /**
     * 会员卡GUID
     */
    private String cardGuid;

    /**
     * 来源,0后台添加,2一体机注册
     */
    private Integer source;

    /**
     * 支付状态
     *
     * @see PayStatusEnum
     */
    private Integer payStatus;

    /**
     * 押金策略编号
     */
    private String depositStrategyCode;

    /**
     * 押金策略名称
     */
    private String depositStrategyName;

    /**
     * 押金金额
     */
    private BigDecimal depositAmount;

    /**
     * 退卡是否退押金
     */
    private Integer isRefundDeposit;

    /**
     * 可退金额
     */
    private BigDecimal refundAmount;

    /**
     * 操作人账户
     */
    private String operatorAccount;

    /**
     * 操作人名字
     */
    private String operatorName;
}
