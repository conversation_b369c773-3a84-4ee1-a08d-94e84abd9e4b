package com.holderzone.member.base.service.card.business;


import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.dto.card.RequestBaseInfoDTO;
import com.holderzone.member.common.qo.card.BindingPhysicalCardQO;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.TerLoginMemberCardQO;
import com.holderzone.member.common.vo.card.TerBaseLoginMemberCardVO;

public interface TerCardCheckBusinessService {

    void checkMemberCardLogin(HsaMemberInfoCard hsaMemberInfoCard, Integer state, TerLoginMemberCardQO terLoginMemberCardQO, Integer cardType);

    void checkMemberInfo(HsaOperationMemberInfo hsaOperationMemberInfo);

    String getCheckPhoneNum(TerLoginMemberCardQO terLoginMemberCardQO, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO, String loginNum);

    void checkMemberCard(RequestConfirmPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo, RequestBaseInfoDTO requestBaseInfo, HsaMemberInfoCard memberInfoCard);

    void checkCreditUserCondition(RequestConfirmPayVO request, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo, HsaOperationMemberInfo hsaOperationMemberInfo);

    void checkCard(HsaCardBaseInfo hsaCardBaseInfo, HsaMemberInfoCard hsaMemberInfoCard);

    void checkPassword(RequestConfirmPayVO requestConfirmPay, HsaMemberInfoCard memberInfoCard);

    void checkBindingAndMatching(HsaMemberInfoCard hsaMemberInfoCard, HsaPhysicalCard card, HsaPhysicalCard hsaPhysicalCard);

    void checkPhysicalCard(HsaPhysicalCard card, BindingPhysicalCardQO bindingPhysicalCardQO);
}
