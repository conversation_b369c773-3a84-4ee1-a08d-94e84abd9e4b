package com.holderzone.member.base.entity.member;

import com.holderzone.member.common.enums.member.PayWayEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 消费记录支付方式
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberConsumptionPayWay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付方式guid
     */
    private String guid;

    /**
     * 支付类型 ,现金 0，聚合 1，银行卡 2，卡余额 3，扫脸 4，自定义 5，自助小碗菜 14
     * @see PayWayEnum
     */
    private Integer payWay;

    /**
     * 支付名称
     */
    private String payName;

    /**
     * 支付码
     */
    private String payCode;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 消费记录guid
     */
    private String consumptionGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtUdpate;


}
