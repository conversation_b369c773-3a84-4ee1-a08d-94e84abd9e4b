package com.holderzone.member.base.entity.equities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权益信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaEquitiesInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 权益基础信息guid
     */
    private String guid;

    /**
     * 权益编号
     */
    private String equitiesNumber;

    /**
     * 权益所属的企业guid
     */
    private String enterpriseGuid;

    /**
     * 权益名称
     */
    private String equitiesName;

    /**
     * 权益图片
     */
    private String equitiesImg;

    /**
     * 权益描述
     */
    private String equitiesExplain;

    /**
     * 权益适用类型
     * 1:全部运营主体  2:部分运营主体
     * com.holderzone.member.common.enums.member.EquitiesSubjectTypeEnum
     */
    private Integer equitiesType;

    /**
     * 权益适用的运营主体
     * json字符串
     */
    private String operSubjectGuidList;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 是否开启 0：正常  1：禁用  2:草稿
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesStatusEnum
     */
    private Integer isStatus;

    /**
     * 权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesTypeEnum
     */
    private Integer type;

    /**
     * 是否删除,0未删除,1已删除
     */
    @TableLogic//逻辑删除标识
    @TableField(select = false)//查询的时候不显示
    private Integer isDelete;

    /**
     * 是否默认
     */
    private Integer isDefault;

}
