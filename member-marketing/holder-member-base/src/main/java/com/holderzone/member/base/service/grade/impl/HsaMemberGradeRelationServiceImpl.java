package com.holderzone.member.base.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.grade.HsaMemberGradeRelation;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeRelationMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.feign.PartnerPlatformFeign;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.*;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员和等级关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
@AllArgsConstructor
public class HsaMemberGradeRelationServiceImpl extends ServiceImpl<HsaMemberGradeRelationMapper, HsaMemberGradeRelation>
        implements IHsaMemberGradeRelationService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    private final HsaMemberGradeInfoMapper memberGradeInfoMapper;

    @Resource
    private HsaMemberGradeRelationMapper hsaMemberGradeRelationMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private PartnerPlatformFeign partnerPlatformFeign;

    @Override
    public void batchSaveMember(MemberGradeRelationDTO relationDTO) {
        HsaMemberGradeInfo memberGradeInfo = memberGradeInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getGuid, relationDTO.getMemberInfoGradeGuid())
                        .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        LocalDateTime payTime = LocalDateTime.now();
        List<HsaMemberGradeRelation> relationList = new ArrayList<>();
        relationDTO.getMemberGuidList().forEach(member -> {
            HsaMemberGradeRelation relation = new HsaMemberGradeRelation();
            relation.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRelation.class.getSimpleName()));
            relation.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            relation.setMemberInfoGuid(member);
            relation.setPayTime(payTime);
            relation.setExpireTime(DateUtil.getFirstExpireTime(relationDTO.getNum(), relationDTO.getUnit()));
            relation.setRoleType(relationDTO.getRoleType());
            relation.setMemberInfoGradeGuid(relationDTO.getMemberInfoGradeGuid());
            relation.setNum(relationDTO.getNum());
            relation.setUnit(relationDTO.getUnit());
            relation.setIsEnable(Boolean.TRUE);
            relation.setSourceType(relationDTO.getSourceType());
            relation.setTemporaryVipGrade(memberGradeInfo.getTemporaryVipGrade());
            relationList.add(relation);
        });
        this.saveBatch(relationList);
    }

    @Override
    public MemberGradeDTO getUserCurrentGrade(String memberGuid, Integer roleType) {
        MemberGradeDTO gradeVO = null;
        HsaMemberGradeRelation hsaMemberRelevanceGrade = getHsaMemberGradeRelation(memberGuid, roleType);
        if (Objects.nonNull(hsaMemberRelevanceGrade)) {
            gradeVO = new MemberGradeDTO();
            BeanUtils.copyProperties(hsaMemberRelevanceGrade, gradeVO);
        }
        return gradeVO;
    }

    @Override
    public List<MemberGradeCountVO> queryMemberGradeCount(List<String> memberGradeInfoGuids, List<String> memberGuidList) {
        return hsaMemberGradeRelationMapper.queryMemberGradeCount(memberGradeInfoGuids, memberGuidList);
    }

    private HsaMemberGradeRelation getHsaMemberGradeRelation(String memberGuid, Integer roleType) {
        HsaMemberGradeRelation hsaMemberRelevanceGrade = hsaMemberGradeRelationMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGuid, memberGuid)
                .eq(HsaMemberGradeRelation::getRoleType, roleType == 0 ? RoleTypeEnum.MEMBER : RoleTypeEnum.MERCHANT)
                .eq(HsaMemberGradeRelation::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode()));
        return hsaMemberRelevanceGrade;
    }

    @Override
    public List<MemberGradeDTO> getUserGradeAll(String memberGuid, Integer roleType) {
        List<MemberGradeDTO> memberRelevanceGradeVO = Lists.newArrayList();
        List<HsaMemberGradeRelation> hsaMemberRelevanceGradeList = hsaMemberGradeRelationMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGuid, memberGuid)
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberGradeRelation::getRoleType, roleType == 0 ? RoleTypeEnum.MEMBER : RoleTypeEnum.MERCHANT));
        if (CollectionUtils.isNotEmpty(hsaMemberRelevanceGradeList)) {
            for (HsaMemberGradeRelation hsaMemberGradeRelation : hsaMemberRelevanceGradeList) {
                MemberGradeDTO relevanceGrade = new MemberGradeDTO();
                BeanUtils.copyProperties(hsaMemberGradeRelation, relevanceGrade);
                memberRelevanceGradeVO.add(relevanceGrade);
            }
        }
        return memberRelevanceGradeVO;
    }

    @Override
    public boolean checkUserExistGrade(String memberGuid, String grade, Integer roleType) {
        HsaMemberGradeRelation hsaMemberGradeRelation = hsaMemberGradeRelationMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGuid, memberGuid)
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberGradeRelation::getRoleType, roleType == 0 ? RoleTypeEnum.MEMBER : RoleTypeEnum.MERCHANT)
                .eq(HsaMemberGradeRelation::getMemberInfoGradeGuid, grade));
        return Objects.nonNull(hsaMemberGradeRelation);
    }

    @Override
    public void saveUserGrade(LocalDateTime expireTime, SaveMemberGradeDTO saveMemberGradeDTO) {
        HsaMemberGradeRelation hsaMemberGradeRelation = getHsaMemberGradeRelation(saveMemberGradeDTO.getMemberInfoGuid(), saveMemberGradeDTO.getRoleType());
        if (Objects.nonNull(hsaMemberGradeRelation)) {
            hsaMemberGradeRelation.setIsEnable(false);
            hsaMemberGradeRelationMapper.updateById(hsaMemberGradeRelation);
        }

        HsaMemberGradeRelation relation = new HsaMemberGradeRelation();
        relation.setGuid(guidGeneratorUtil.getStringGuid(relation.getClass().getSimpleName()))
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setMemberInfoGuid(saveMemberGradeDTO.getMemberInfoGuid())
                .setNum(saveMemberGradeDTO.getNum())
                .setUnit(saveMemberGradeDTO.getUnit())
                .setMemberInfoGradeGuid(saveMemberGradeDTO.getMemberInfoGradeGuid())
                .setExpireTime(expireTime)
                .setPayTime(LocalDateTime.now())
                .setRoleType(saveMemberGradeDTO.getRoleType() == 0 ? RoleTypeEnum.MEMBER + "" : RoleTypeEnum.MERCHANT + "")
                .setIsDelete(false)
                .setIsEnable(true)
                .setSourceType(53)
                .setTemporaryVipGrade(saveMemberGradeDTO.getTemporaryVipGrade());
        this.save(relation);
    }

    private static LocalDateTime getExpireTime(LocalDateTime now, Integer num, Integer unit) {
        if (unit == 3) {
            now = now.plusMonths(num);
        } else {
            now = now.plusYears(num);
        }
        return now;
    }

    @Override
    public LocalDateTime updateExpiration(SaveMemberGradeDTO saveMemberGradeDTO) {
        HsaMemberGradeRelation hsaMemberGradeRelation = getHsaMemberGradeRelation(saveMemberGradeDTO.getMemberInfoGuid(), saveMemberGradeDTO.getRoleType());
        if (Objects.nonNull(hsaMemberGradeRelation)) {
            LocalDateTime expireTime = getExpireTime(hsaMemberGradeRelation.getExpireTime(), saveMemberGradeDTO.getNum(), saveMemberGradeDTO.getUnit());
            hsaMemberGradeRelation.setExpireTime(expireTime);
            hsaMemberGradeRelation.setPayTime(LocalDateTime.now());
            hsaMemberGradeRelation.setNum(saveMemberGradeDTO.getNum());
            hsaMemberGradeRelation.setUnit(saveMemberGradeDTO.getUnit());
            hsaMemberGradeRelationMapper.updateById(hsaMemberGradeRelation);
            return expireTime;
        }
        return null;
    }

    @Override
    public List<MemberNewGradeDTO> getNewCurrentGradeList(List<String> memberGuid) {
        List<MemberNewGradeDTO> memberNewGradeDTOList = Lists.newArrayList();
        memberGuid.forEach(in -> {
            MemberNewGradeDTO dto = new MemberNewGradeDTO();
            MemberGradeDTO memberNewGradeOne = getUserCurrentGrade(in, 0);
            MemberGradeDTO memberNewGradeTwo = getUserCurrentGrade(in, 1);
            String gradeNameOne = null;
            String gradeNameTwo = null;
            if (Objects.nonNull(memberNewGradeOne) && Objects.nonNull(memberNewGradeTwo)) {
                gradeNameOne = getGradeName(memberNewGradeOne);
                gradeNameTwo = getGradeName(memberNewGradeTwo);
            } else if (Objects.nonNull(memberNewGradeOne)) {
                gradeNameOne = getGradeName(memberNewGradeOne);
            } else if (Objects.nonNull(memberNewGradeTwo)) {
                gradeNameTwo = getGradeName(memberNewGradeTwo);
            }
            getAdd(in, dto, gradeNameOne, gradeNameTwo, memberNewGradeDTOList);
        });
        return memberNewGradeDTOList;
    }

    @Override
    public List<MemberNewGradeDTO> getNewCurrentGradeIconList(List<String> memberGuid) {
        List<MemberNewGradeDTO> newGradeDTOS = Lists.newArrayList();
        Map<String, String> roleMap = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberGuid))
                .stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, HsaOperationMemberInfo::getRoleType, (obj1, obj2) -> obj1));
        memberGuid.forEach(in -> {
            MemberNewGradeDTO dto = new MemberNewGradeDTO();
            dto.setMemberInfoGuid(in);
            MemberGradeDTO memberNewGradeOne = getUserCurrentGrade(in, 0);
            MemberGradeDTO memberNewGradeTwo = getUserCurrentGrade(in, 1);
            String memberGradeIcon;
            String merchantGradeIcon;
            String role = roleMap.get(in);
            String[] roles = role.split(StringConstant.MARK);
            ArrayList<String> roleList = new ArrayList<>(Arrays.asList(roles));
            if (Objects.nonNull(memberNewGradeOne) && roleList.contains(RoleTypeEnum.MEMBER + "")) {
                memberGradeIcon = getGradeIcon(memberNewGradeOne);
                dto.setMemberInfoGradeIcon(memberGradeIcon);
            }
            if (Objects.nonNull(memberNewGradeTwo) && roleList.contains(RoleTypeEnum.MERCHANT + "")) {
                merchantGradeIcon = getGradeIcon(memberNewGradeTwo);
                dto.setBusinessGradeIcon(merchantGradeIcon);
            }
            if (StringUtils.isNotBlank(dto.getMemberInfoGradeIcon()) || StringUtils.isNotBlank(dto.getBusinessGradeIcon())) {
                newGradeDTOS.add(dto);
            }
        });
        return newGradeDTOS;
    }

    @Override
    public List<String> getMerchantMember(String guid) {
        List<MerchantNewGradeDTO> merchantNewGradeDTOS = Lists.newArrayList();
        List<String> merPhone = null;
        List<HsaMemberGradeRelation> homeMemberGradeRelations = hsaMemberGradeRelationMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGradeGuid, guid)
                .eq(HsaMemberGradeRelation::getIsEnable, 1)
                .eq(HsaMemberGradeRelation::getIsDelete, 0));
        if (CollectionUtils.isEmpty(homeMemberGradeRelations)) {
            return merPhone;
        }
        List<String> memberGuid = homeMemberGradeRelations
                .stream()
                .map(HsaMemberGradeRelation::getMemberInfoGuid).collect(Collectors.toList());
        List<HsaOperationMemberInfo> homeMemberInfo = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .in(HsaOperationMemberInfo::getGuid, memberGuid));
        for (HsaOperationMemberInfo memberInfo : homeMemberInfo) {
            MerchantNewGradeDTO dto = new MerchantNewGradeDTO();
            dto.setMemberInfoGuid(memberInfo.getGuid())
                    .setPhoneNum(memberInfo.getPhoneNum());
            merchantNewGradeDTOS.add(dto);
        }

        List<String> phoneNum = merchantNewGradeDTOS.stream().map(MerchantNewGradeDTO::getPhoneNum).collect(Collectors.toList());
        return partnerPlatformFeign.getAvailablePhones(phoneNum);
    }

    @Override
    public Map<String, List<String>> queryMemberGradeIcon(List<String> memberGuidList) {
        List<HsaMemberGradeRelation> relationList = this.list(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .in(HsaMemberGradeRelation::getMemberInfoGuid, memberGuidList)
                .eq(HsaMemberGradeRelation::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaMemberGradeRelation::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyMap();
        }
        List<String> memberGradeGuidList = relationList.stream()
                .map(HsaMemberGradeRelation::getMemberInfoGradeGuid)
                .distinct()
                .collect(Collectors.toList());
        Map<String, List<HsaMemberGradeRelation>> relationMap = relationList.stream()
                .collect(Collectors.groupingBy(HsaMemberGradeRelation::getMemberInfoGuid));
        List<HsaMemberGradeInfo> gradeInfoList = memberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .in(HsaMemberGradeInfo::getGuid, memberGradeGuidList)
                .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (CollectionUtils.isEmpty(gradeInfoList)) {
            return Collections.emptyMap();
        }
        Map<String, String> gradeIconMap = gradeInfoList.stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, HsaMemberGradeInfo::getGradeIcon));
        Map<String, List<String>> map = new HashMap<>();
        memberGuidList.forEach(memberGuid -> {
            List<HsaMemberGradeRelation> gradeRelationList = relationMap.get(memberGuid);
            if (CollectionUtils.isNotEmpty(gradeRelationList)) {
                List<String> gradeIconList = new ArrayList<>();
                gradeRelationList.sort(Comparator.comparing(r -> RoleTypeEnum.name2Code(r.getRoleType())));
                gradeRelationList.forEach(relation -> {
                    String gradeIcon = gradeIconMap.get(relation.getMemberInfoGradeGuid());
                    gradeIconList.add(gradeIcon);
                });
                map.put(memberGuid, gradeIconList);
            }
        });
        return map;
    }

    private String getGradeName(MemberGradeDTO memberNewGradeOne) {
        HsaMemberGradeInfo memberGradeInfo = getHsaMemberGradeInfo(memberNewGradeOne);
        return Optional.ofNullable(memberGradeInfo).map(HsaMemberGradeInfo::getName).orElse(null);
    }

    private String getGradeIcon(MemberGradeDTO memberNewGradeOne) {
        HsaMemberGradeInfo memberGradeInfo = getHsaMemberGradeInfo(memberNewGradeOne);
        return Optional.ofNullable(memberGradeInfo).map(HsaMemberGradeInfo::getGradeIcon).orElse(null);
    }

    private void getAdd(String in, MemberNewGradeDTO dto, String gradeNameOne, String gradeNameTwo, List<MemberNewGradeDTO> memberNewGradeDTOS) {
        if (Objects.isNull(gradeNameOne) && Objects.isNull(gradeNameTwo)) {
            return;
        }

        dto.setMemberInfoGuid(in);
        dto.setMemberInfoGradeName(gradeNameOne);
        dto.setBusinessGradeName(gradeNameTwo);
        memberNewGradeDTOS.add(dto);
    }

    private HsaMemberGradeInfo getHsaMemberGradeInfo(MemberGradeDTO gradeDTO) {
        return memberGradeInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getGuid, gradeDTO.getMemberInfoGradeGuid())
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), 2)
                        .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
    }
}
