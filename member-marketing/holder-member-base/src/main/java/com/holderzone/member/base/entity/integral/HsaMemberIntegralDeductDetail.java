package com.holderzone.member.base.entity.integral;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员积分抵扣明细记录表
 *
 * <AUTHOR>
 * @since 2022-02-08 16:45:11
 */
@ApiModel("会员积分抵扣明细记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberIntegralDeductDetail implements Serializable {
    private static final long serialVersionUID = -57323975547772597L;

    /**
     * 会员等级会员价周期累计明细guid
     */
    @ApiModelProperty("会员等级会员价周期累计明细guid")
    private String guid;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderPrice;

    /**
     * 订单积分优惠金额
     */
    @ApiModelProperty("订单积分优惠金额")
    private BigDecimal discountedPrice;


    /**
     * 抵扣积分
     */
    @ApiModelProperty("抵扣积分")
    private Integer integralDeduct;


    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNumber;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 积分消耗规则guid
     */
    private String consumeRuleGuid;

    /**
     * 积分消耗规则id
     */
    private String consumeVersionId;


    /**
     * 周期时间
     */
    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
