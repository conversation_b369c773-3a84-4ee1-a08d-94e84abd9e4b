package com.holderzone.member.base.mapper.grade;


import com.holderzone.member.base.entity.grade.HsaMemberGradeChangeDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员等级变化明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface HsaMemberGradeChangeDetailMapper extends HolderBaseMapper<HsaMemberGradeChangeDetail> {
    /**
     * 批量更会员等级变更记录
     * @param request 修改参数
     */
    void batchUpdateGradeChange(@Param("request") List<HsaMemberGradeChangeDetail> request);
}
