package com.holderzone.member.base.manage;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.event.ConsumptionOrderPublisher;
import com.holderzone.member.base.event.domain.ConsumptionOrderEventEnum;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.module.base.purchase.dto.PurchaseOrderCancelDto;
import com.holderzone.member.common.module.base.purchase.dto.PurchaseOrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 消费订单操作
 *
 * <AUTHOR>
 * @date 2023/12/4
 * @since 1.8
 */
@Slf4j
@Component

public class ConsumptionOrderManage {

    @Resource
    private ConsumptionOrderPublisher publisher;

    public void create(PurchaseOrderDto dto) {
        //数据校验
        final boolean validate = dto.validate();
        if(validate){
            log.error("订单创建：无商品！{}",dto.getOrderNumber());
            return;
        }
        //创建订单暂未保存 HsaMemberConsumption 
        dto.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //todo 限购活动是前端传，还是再来查询一次：推送到业务
        //活动和商品是一对多
        publisher.publish(ConsumptionOrderEventEnum.PURCHASE_ORDER_CREATE, JacksonUtils.writeValueAsString(dto));
    }

    public void cancel(PurchaseOrderCancelDto dto) {

        dto.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        publisher.publish(ConsumptionOrderEventEnum.PURCHASE_ORDER_CANCEL, JacksonUtils.writeValueAsString(dto));
    }
}
