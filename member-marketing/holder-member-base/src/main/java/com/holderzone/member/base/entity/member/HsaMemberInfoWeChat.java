package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 运营主体会员微信表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberInfoWeChat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体会员微信GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 运营主体会员GUID
     */
    private String operationMemberInfoGuid;

    /**
     * 微信用户union_id
     */
    private String unionId;

    /**
     * 微信用户open_id
     */
    private String openId;

    /**
     * 支付宝userId
     */
    private String userId;

//    /**
//     * 操作人guid
//     */
//    private String operatorGuid;
//
//    /**
//     * 操作人名字
//     */
//    private String operatorName;

//    /**
//     * 是否在微信上登陆
//     */
//    private Boolean isLogin;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private String phoneNum;

}
