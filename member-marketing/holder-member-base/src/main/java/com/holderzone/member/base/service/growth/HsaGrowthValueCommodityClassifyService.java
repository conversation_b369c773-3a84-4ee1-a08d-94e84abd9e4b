package com.holderzone.member.base.service.growth;

import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityClassify;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.growth.AppointClassifyGoodsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-31
 * @description
 */
public interface HsaGrowthValueCommodityClassifyService extends IHolderBaseService<HsaGrowthValueCommodityClassify> {

    /**
     * 保存成长值任务指定商品分类
     * @param taskGuid 成长值任务编号
     * @param appointGoodsTypeList 指定商品分类列表
     */
    void saveGrowthValueTaskCommodityClassify(String taskGuid, List<AppointClassifyGoodsDTO> appointGoodsTypeList);

    /**
     * 根据任务guid查询指定商品分类列表
     * @param taskGuid 任务guid
     * @return 商品分类列表
     */
    List<AppointClassifyGoodsDTO> listCommodityClassifyByTask(String taskGuid);
}
