package com.holderzone.member.base.entity.equities;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.enums.equities.ApplyCommodityTypeEnum;
import com.holderzone.member.common.enums.equities.RelationRuleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权益规则
 * author: pan_tao
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class HsaEquitiesRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值 6：赠送积分 7：翻倍积分
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer type;

    /**
     * 商品折扣 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer goodsDiscountSet;


    /**
     * 权益时段 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equitiesTimeSet;

    /**
     * 权益时段限制类型 0：不限制 1：限制时段 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String equitiesTimeLimitedType;

    /**
     * 权益生效时段必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equitiesEffectiveDateRequired;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String limitedTimeType;

    /**
     * 适用门店设置 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyStoreSet;

    /**
     * 适用门店设置类型 0：全部门店 1：选择部分门店 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyStoreType;

    /**
     * 适用业务 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyBusinessSet;

    /**
     * 适用业务类型 0：全部业务 1：部分业务 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyBusinessType;

    /**
     * 业务类型 0：食堂预定 3：快速收款 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String businessType;

    /**
     * 适用终端 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyTerminalSet;

    /**
     * 适用终端类型 0：全部终端 1：部分终端 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.ApplyTerminalTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyTerminalType;

    /**
     * 部分终端类型 53：小程序 2：一体机 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String terminalType;

    /**
     * 适用商品 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyCommoditySet;

    /**
     * 适用商品必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyCommodityRequired;

    /**
     * -1：全部商品适用 0：适用商品 1：不适用商品
     *
     * @see ApplyCommodityTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyCommodityType;

    /**
     * 满减 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer enoughReduceSet;

    /**
     * 是否可设置多个满减 0：不能 1：能
     *
     * @see com.holderzone.member.common.enums.equities.MultipleEnoughReduceEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer multipleEnoughReduce;

    /**
     * 金额满足计算范围 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer amountSatisfyScopeSet;

    /**
     * 金额满足计算范围类型 0：全部计算 1：部分计算设置 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.AmountSatisfyScopeTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String amountSatisfyScopeType;

    /**
     * 部分计价类型 0：商品价格 1：桌台费 2：配送费 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.PartValuationTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String partValuationType;

    /**
     * 直减 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer directReduceSet;

    /**
     * 会员折扣（修改为折扣力度） 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer memberDiscountSet;

    /**
     * 单次优惠限制 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDiscountsLimitedSet;


    /**
     * 单次优惠限制类型 0：不限制 1：限制  多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String singleDiscountsLimitedType;


    /**
     * 累计最高优惠限制 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalMostLimitedTypeSet;

    /**
     * 0：不限 1：优惠金额限制  多个用逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String totalMostLimitedType;

    /**
     * 累计赠送成长值数量 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleGiveNumberSet;

    /**
     * 赠送成长值方式 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer giveWaySet;

    /**
     * 赠送成长值周期类型 0：日 1：周 2：月 3：年 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String periodType;

    /**
     * 翻倍倍数 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer doubleMultipleSet;

    /**
     * 单次翻倍获取成长值上限 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDoubleLimitedSet;

    /**
     * 单次翻倍获取成长值上限类型 0：不限 1：限制 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String singleDoubleLimitedType;

    /**
     * 限制次数 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer doubleCountSet;

    /**
     * 翻倍次数限制类型 0：不限次数 1：设置可翻倍次数 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String doubleCountType;

    /**
     * 累计翻倍获取成长值上限 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalDoubleLimitedSet;


    /**
     * 累计翻倍获取成长值上限类型 0：不限制 1：设置上限值 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String totalDoubleLimitedType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 权益信息guid
     */
    private String equitiesInfoGuid;

    /**
     * 单次优惠限制必填 0：非必填 1：必填
     */
    private Integer singleDiscountsRequired;

    /**
     * 翻倍次数必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer doubleCountRequired;

    /**
     * 单次翻倍获取成长值上线必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDoubleLimitedRequired;

    /**
     * 累计翻倍获取成长值上限必填  0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalDoubleLimitedRequired;


    //TODO 会员价
    /**
     * 周期优惠限制 0：设置 1：未设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodDiscountLimitedSet;

    /**
     * 周期优惠限制必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodDiscountRequired;

    /**
     * 周期优惠限制类型 -1：不限制 0：天 1：周 2：月 3：天
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String periodDiscountType;


    /**
     * 适用渠道 0：设置 1：未设置
     */
    private Integer applyChannelSet;

    /**
     * 适用渠道必填 0：非必填 1：必填
     */
    private Integer applyChannelRequired;

    /**
     * 适用渠道限制 0：全部 1：部分,多个，分割
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyChannelLimited;

    /**
     * 适用渠道类型
     */
    private String applyChannelType;

    /**
     * 累计最高优惠限制必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalMostLimitedTypeRequired;

    /**
     * 单次优惠限制必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDiscountsLimitedRequired;

    /**
     * 适用终端必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyTerminalRequired;

    /**
     * 适用业务必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyBusinessRequired;

    /**
     * 权益时段必填 0：非必填 1：必填
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equitiesTimeRequired;

    /**
     * 门店必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    @ApiModelProperty(value = "门店必填")
    private Integer storeRequired;

    /**
     * 共享互斥关系设置 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    @ApiModelProperty(value = "共享互斥关系设置")
    private Integer relationRuleSet;

    /**
     * 共享互斥关系 0-互斥 1-共享 多个用逗号隔开
     *
     * @see RelationRuleEnum
     */
    @ApiModelProperty(value = "共享互斥关系类型")
    private String relationRuleType;

    /**
     * 适用业务必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer relationRuleRequired;

}
