package com.holderzone.member.base.service.card;

import com.holderzone.member.common.dto.card.CreateSecretDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.member.MemberListExcelQO;
import com.holderzone.member.common.vo.excel.ExcelResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/31
 */
public interface MemberCardOperationService {

    /**
     * 功能描述：导入会员账户
     *
     * @param file      文件
     * @param phoneList excel选中得会员电话号码列表
     * @param cardGuid  会员卡guid
     * @return com.holderzone.member.common.vo.excel.ExcelResultVO
     * @date 2021/9/1
     */
    ExcelResultVO importMemberInfo(MultipartFile file, List<String> phoneList, String cardGuid);

    /**
     * 功能描述：分页查询未绑定此会员卡的实体卡的会员
     *
     * @param pageRequest 分页数据
     * @return PageResult
     * @date 2021/9/1
     */
    PageResult listMemberPage(MemberListExcelQO pageRequest);

    /**
     * 功能描述：生成卡密
     *
     * @param qo 传递参数
     * @return CreateSecretDTO 返回下载卡密地址
     * @date 2021/9/1
     */
    CreateSecretDTO createPhysicalCardSecret(CreatePhysicalCardSecretQO qo);

    int validateCreatePhysicalCard(CreatePhysicalCardSecretQO qo);

    CreateSecretDTO handlerCreatePhysicalCardSecret(CreatePhysicalCardSecretQO qo, int count);

    /**
     * 功能描述：冻结或者解冻会员卡
     *
     * @param qo 参数
     * @date 2021/9/6
     */
    void freezeOrThawMemberCard(FreezeOrThawQO qo);

    /**
     * 功能描述：绑定实体卡
     *
     * @param qo 实体卡信息
     * @date 2021/9/6
     */
    void bindPhysicalCard(BindPhysicalCardQO qo);

    /**
     * 会员卡冻结操作
     *
     */
    void memberCardFreezeOperation(CardFreezeOperationQO qo);

    /**
     * 功能描述：开通电子卡
     *
     * @param ownGuid 关联guid
     * @date 2021/9/6
     */
    void openElectronicCard(String ownGuid);

    /**
     * 功能描述：绑定账户
     *
     * @param qo 绑定参数
     * @date 2021/9/6
     */
    void bindMemberAccount(BindMemberAccountQO qo);

    /**
     * 功能描述：实体卡退卡
     *
     * @param qo 退卡参数
     * @date 2021/9/6
     */
    void cancelPhysicalCard(CancelPhysicalCardQO qo);
}
