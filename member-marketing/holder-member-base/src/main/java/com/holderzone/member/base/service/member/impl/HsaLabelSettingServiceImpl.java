package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.member.base.dto.QueueRelationLabelDTO;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.grade.HsaMemberGradeCard;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeCardMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDetailMapper;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.service.card.HsaElectronicCardService;
import com.holderzone.member.base.service.member.ChainEnterService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberLabelRecordService;
import com.holderzone.member.base.transform.card.CardInfoTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.label.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.EletronicCardStateEnum;
import com.holderzone.member.common.enums.exception.MemberLabelExceptionEnum;
import com.holderzone.member.common.enums.grade.GradeCardStatusEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.qo.permission.HsaDeleteOperSubjectLabelTypeQO;
import com.holderzone.member.common.qo.permission.HsaOperSubjectLabelQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.util.verify.SetOptUtils;
import com.holderzone.member.common.vo.member.*;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import cn.hutool.core.date.DatePattern;

/**
 * 会员标签service
 */
@Slf4j
@Service
public class HsaLabelSettingServiceImpl extends HolderBaseServiceImpl<HsaLabelSettingMapper, HsaLabelSetting> implements HsaLabelSettingService {

    /**
     * 会员标签设置mapper
     */
    private final HsaLabelSettingMapper hsaLabelMapper;

    private final HsaMemberLabelMapper hsaMemberLabelMapper;

    /**
     * 表服务实现类
     */
    private final HsaLabelSettingBaseInfoMapper hsaLabelSettingBaseInfoMapper;

    @Autowired
    @Lazy
    private HsaElectronicCardService hsaElectronicCardService;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private HsaIntegralDetailMapper hsaIntegralDetailMapper;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaMemberLabelRecordService hsaMemberLabelRecordService;

    private final HsaMemberLabelRecordMapper hsaMemberLabelRecordMapper;

    private final HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    /**
     * 英文逗号
     */
    private static final String COMMA = ",";

    private final HsaLabelSettingMapper hsaLabelSettingMapper;

    private final RedissonClient redissonClient;

    private final HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Autowired
    @Lazy
    private ChainEnterService chainEnterService;


    private static final ArrayBlockingQueue<QueueRelationLabelDTO> RELATION_LABEL_QUEUE = new ArrayBlockingQueue<>(2048);


    public HsaLabelSettingServiceImpl(HsaLabelSettingMapper hsaLabelMapper, HsaMemberLabelMapper hsaMemberLabelMapper,
                                      HsaLabelSettingBaseInfoMapper hsaLabelSettingBaseInfoMapper,
                                      @Lazy HsaElectronicCardService hsaElectronicCardService, Executor memberBaseThreadExecutor,
                                      HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper, GuidGeneratorUtil guidGeneratorUtil,
                                      @Lazy HsaMemberLabelRecordService hsaMemberLabelRecordService, HsaLabelSettingMapper hsaLabelSettingMapper,
                                      HsaMemberLabelRecordMapper hsaMemberLabelRecordMapper,
                                      RedissonClient redissonClient,
                                      HsaMemberConsumptionMapper hsaMemberConsumptionMapper,
                                      HsaMemberInfoCardMapper hsaMemberInfoCardMapper,
                                      HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper) {
        this.hsaLabelMapper = hsaLabelMapper;
        this.hsaMemberLabelMapper = hsaMemberLabelMapper;
        this.hsaLabelSettingBaseInfoMapper = hsaLabelSettingBaseInfoMapper;
        this.hsaElectronicCardService = hsaElectronicCardService;
        this.memberBaseThreadExecutor = memberBaseThreadExecutor;
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.hsaMemberLabelRecordService = hsaMemberLabelRecordService;
        this.hsaLabelSettingMapper = hsaLabelSettingMapper;
        this.hsaMemberLabelRecordMapper = hsaMemberLabelRecordMapper;
        this.redissonClient = redissonClient;
        this.hsaMemberConsumptionMapper = hsaMemberConsumptionMapper;
        this.hsaMemberInfoCardMapper = hsaMemberInfoCardMapper;
        this.hsaGrowthValueDetailMapper = hsaGrowthValueDetailMapper;
    }


    @Override
    public PageResult pageMemberLabel(MemberLabelListQO memberLabelListQO) {
        //设置运营主体
        memberLabelListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        PageHelper.startPage(memberLabelListQO.getCurrentPage(), memberLabelListQO.getPageSize());
        List<MemberLabelListVO> list = hsaLabelMapper.listMemberLabel(memberLabelListQO);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public List<LabelSiftVO> listMemberLabel() {
        MemberLabelListQO memberLabelListQO = new MemberLabelListQO();
        memberLabelListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        return toLabelSiftList(hsaLabelMapper.listMemberLabel(memberLabelListQO));
    }

    /**
     * 获取标签信息
     */
    @Override
    public List<LabelSiftVO> listLabel(List<String> labelGuidList) {
        List<LabelSiftVO> labelVOS = Lists.newArrayList();
        if (CollUtil.isEmpty(labelGuidList)) {
            return labelVOS;
        }
        List<HsaLabelSetting> hsaLabelSettings = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                .eq(HsaLabelSetting::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .in(HsaLabelSetting::getGuid, labelGuidList));
        hsaLabelSettings.forEach(e -> {
            LabelSiftVO labelSift = new LabelSiftVO();
            labelSift.setGuid(e.getGuid());
            labelSift.setLabelName(e.getLabelName());
            labelVOS.add(labelSift);
        });
        return labelVOS;
    }

    private List<LabelSiftVO> toLabelSiftList(List<MemberLabelListVO> memberLabelList) {
        if (CollUtil.isEmpty(memberLabelList)) {
            return Collections.emptyList();
        }
        List<LabelSiftVO> list = Lists.newArrayList();
        memberLabelList.forEach(e -> {
            LabelSiftVO labelSift = new LabelSiftVO();
            labelSift.setGuid(e.getGuid());
            labelSift.setLabelName(e.getLabelName());
            list.add(labelSift);
        });
        return list;
    }

    @Override
    public List<MemberLabelListVO> listLabelOperGuid(HsaOperSubjectLabelQO operatorLabelQO) {
        MemberLabelListQO memberLabelListQO = new MemberLabelListQO();
        memberLabelListQO.setOperSubjectGuid(operatorLabelQO.getOperSubjectGuid());
        return hsaLabelMapper.listMemberLabel(memberLabelListQO);
    }

    @Override
    public boolean operationLabel(OperationLabelQO operationLabelQO) {

        //获取操作类型枚举
        OperationLabelEnum operationLabelEnum = OperationLabelEnum.getEnumByCode(operationLabelQO.getType());
        switch (operationLabelEnum) {
            case FORBIDDEN:
            case START:
                HsaLabelSetting labelInfo = hsaLabelMapper.queryByGuid(operationLabelQO.getLabelGuid());
                if (Objects.isNull(labelInfo)) {
                    throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_NOT_LABEL);
                }
                labelInfo.setIsEnable(operationLabelEnum.getCode());
                labelInfo.setOperatorName(ThreadLocalCache.getOperatorName());
                labelInfo.setOperatorAccount(ThreadLocalCache.getHeaderUserInfo().getTel());
                boolean result = updateByGuid(labelInfo);
                //启用要刷标签
                if (operationLabelEnum.getCode() == OperationLabelEnum.START.getCode()) {
                    refreshLabel(null, Lists.newArrayList(labelInfo), BooleanEnum.TRUE.getCode(), null,
                            LabelTriggerTypeEnum.ALL.getCode());
                }
                return result;
            case DELETE:
                int count = hsaLabelMapper.delete(new LambdaQueryWrapper<HsaLabelSetting>().eq(HsaLabelSetting::getGuid,
                        operationLabelQO.getLabelGuid()));
                //同时删除关联
                hsaMemberLabelMapper.delete(new LambdaQueryWrapper<HsaMemberLabel>()
                        .eq(HsaMemberLabel::getLabelSettingGuid, operationLabelQO.getLabelGuid()));
                hsaLabelSettingBaseInfoMapper.delete(new LambdaQueryWrapper<HsaLabelSettingBaseInfo>()
                        .eq(HsaLabelSettingBaseInfo::getLabelSettingGuid, operationLabelQO.getLabelGuid()));
                // 将标签历史记录标记为已取关
                HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();

                HsaMemberLabelRecord updateRecord = new HsaMemberLabelRecord();
                updateRecord.setIsConnection(BooleanEnum.FALSE.getCode())
                           .setCancelConnectionTime(DateTimeUtils.now())
                           .setCancelConnectionOperator(StringUtil.isNotEmpty(userInfo.getUserName()) ?
                                   userInfo.getUserName() + StringConstant.STR_BIAS + userInfo.getTel() : null)
                           .setCancelConnectionType(BooleanEnum.FALSE.getCode())
                           .setGmtModified(DateTimeUtils.now());

                final LambdaUpdateWrapper<HsaMemberLabelRecord> updateWrapper = new UpdateWrapper<HsaMemberLabelRecord>()
                        .lambda()
                        .eq(HsaMemberLabelRecord::getLabelSettingGuid, operationLabelQO.getLabelGuid())
                        .eq(HsaMemberLabelRecord::getIsConnection, BooleanEnum.TRUE.getCode());
                
                hsaMemberLabelRecordMapper.update(updateRecord, updateWrapper);
                //同步删除标签权限
                memberMarketingFeign.deleteOperSubjectLabel(new HsaDeleteOperSubjectLabelTypeQO()
                        .setLabelGuid(operationLabelQO.getLabelGuid())
                        .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid()));
                return count > 0;
            default:
                throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_DATA_REQUEST);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addAutomaticLabel(RequestOperationLabel labelSave) {
        HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        HsaLabelSettingBaseInfo labelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        if (labelSave.getRequestLabelSetting() == null || labelSave.getRequestLabelSetting().getLabelName() == null) {
            throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_BAD_LABEL);
        }
        String labelValue = labelSave.getRequestLabelSetting().getLabelName();
        // 标签名称主体下唯一
        HsaLabelSetting labelName = this.getOne(new LambdaQueryWrapper<HsaLabelSetting>()
                .eq(HsaLabelSetting::getLabelName, labelValue)
                .eq(HsaLabelSetting::getOperSubjectGuid, ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid()));
        //不为空且查询到信息：更新操作
        if (StringUtils.isNotBlank(labelSave.getSettingGuid())) {
            hsaLabelSetting = this.getOne(new LambdaQueryWrapper<HsaLabelSetting>()
                    .eq(HsaLabelSetting::getGuid, labelSave.getSettingGuid()));
            if (hsaLabelSetting == null) {
                throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_NOT_LABEL);
            }
            if (!Objects.isNull(labelName) && labelName.getLabelName().equals(hsaLabelSetting.getLabelName()) &&
                    !labelName.getGuid().equals(hsaLabelSetting.getGuid())) {
                throw new MemberBaseException(MemberLabelExceptionEnum.DUPLICATE_LABEL);
            }
            HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
            hsaLabelSetting.setLabelName(labelValue)
                    .setLabelType(labelSave.getRequestLabelSetting().getLabelType())
                    .setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid())
                    .setConditionSet(labelSave.getRequestLabelSetting().getConditionSet())
                    .setOperatorName(userInfo.getUserName())
                    .setOperatorAccount(userInfo.getTel())
                    .setGmtModified(DateTimeUtils.now())
                    .setRemark(labelSave.getRequestLabelSetting().getRemark());
            boolean guid = this.updateByGuid(hsaLabelSetting);
            log.info("更新标签设置表:{},{}", guid, hsaLabelSetting);
            //手动标签直接返回
            if (hsaLabelSetting.getLabelType() == LabelTypeEnum.MANUAL.getCode()) {
                return hsaLabelSetting.getGuid();
            }
            labelSettingBaseInfo = hsaLabelSettingBaseInfoMapper.queryByGuid(labelSave.getSettingInfoGuid());
            if (labelSettingBaseInfo == null) {
                throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_NOT_LABEL);
            }
            toHsaLabelSettingBaseInfo(labelSettingBaseInfo, labelSave);
            boolean b = hsaLabelSettingBaseInfoMapper.updateByGuid(labelSettingBaseInfo);

            log.info("更新标签基本信息表：{},{}", b, labelSettingBaseInfo);
            refreshLabel(null, Lists.newArrayList(hsaLabelSetting), BooleanEnum.TRUE.getCode(), null,
                    LabelTriggerTypeEnum.ALL.getCode());
            return hsaLabelSetting.getGuid();
        }
        String operSubjectGuid = ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid();
        // 否則：保存操作
        addAutomaticLabel(labelSave, hsaLabelSetting, labelSettingBaseInfo, labelValue, labelName, operSubjectGuid);
        return hsaLabelSetting.getGuid();
    }

    private void addAutomaticLabel(RequestOperationLabel labelSave, HsaLabelSetting hsaLabelSetting,
                                   HsaLabelSettingBaseInfo labelSettingBaseInfo, String labelValue, HsaLabelSetting labelName,
                                   String operSubjectGuid) {
        HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        hsaLabelSetting.setGuid(ObjectUtil.objToString(guidGeneratorUtil.getGuid(HsaLabelSetting.class.getSimpleName())))
                .setLabelType(labelSave.getRequestLabelSetting().getLabelType())
                .setLabelName(labelValue)
                .setOperSubjectGuid(operSubjectGuid)
                .setOperatorGuid(userInfo.getUserGuid())
                .setOperatorName(userInfo.getUserName())
                .setOperatorAccount(userInfo.getTel())
                .setMemberNum(NumberConstant.NUMBER_0)
                .setConditionSet(labelSave.getRequestLabelSetting().getConditionSet())
                .setRemark(labelSave.getRequestLabelSetting().getRemark())
                .setIsEnable(EnableEnum.ENABLE.getCode());

        if (!Objects.isNull(labelName) && labelName.getLabelName().equals(hsaLabelSetting.getLabelName()) &&
                !labelName.getGuid().equals(hsaLabelSetting.getGuid())) {
            throw new MemberBaseException(MemberLabelExceptionEnum.DUPLICATE_LABEL);
        }
        boolean save = this.save(hsaLabelSetting);
        log.info("保存标签设置表：{},{}", save, hsaLabelSetting);
        //手动标签直接返回
        if (hsaLabelSetting.getLabelType() == LabelTypeEnum.MANUAL.getCode()) {
            return;
        }
        //封装HsaLabelSettingBaseInfo
        toHsaLabelSettingBaseInfo(labelSettingBaseInfo, labelSave);
        labelSettingBaseInfo.setLabelSettingGuid(hsaLabelSetting.getGuid())
                .setGuid(guidGeneratorUtil.getGuid(HsaLabelSettingBaseInfo.class.getSimpleName()).toString());
        hsaLabelSettingBaseInfoMapper.insert(labelSettingBaseInfo);
        log.info("保存标签基本信息表：{}", labelSettingBaseInfo);
        log.info("异步刷新标签...");
        refreshLabel(null, Lists.newArrayList(hsaLabelSetting), BooleanEnum.TRUE.getCode(), null,
                LabelTriggerTypeEnum.ALL.getCode());
    }

    /**
     * 封装HsaLabelSettingBaseInfo
     *
     * @param labelSettingBaseInfo 会员标签基础信息表
     * @param labelSave            标签操作请求
     */
    private void toHsaLabelSettingBaseInfo(HsaLabelSettingBaseInfo labelSettingBaseInfo, RequestOperationLabel labelSave) {
        BeanUtils.copyProperties(labelSave.getRequestBaseInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestRegisterInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestEquityCardInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestRechargeInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestConsumptionInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestGrowInfo(), labelSettingBaseInfo);
        BeanUtils.copyProperties(labelSave.getRequestIntegraInfo(), labelSettingBaseInfo);
        Object o = JSON.toJSON(labelSave.getRequestBaseInfo().getAgeRange());
        labelSettingBaseInfo.setAgeRange(o.toString());
        labelSettingBaseInfo.setSex(StringUtils.join(labelSave.getRequestBaseInfo().
                getSex(), COMMA));
        labelSettingBaseInfo.setWorkName(StringUtils.join(labelSave.getRequestBaseInfo().
                getWorkName(), COMMA));
        labelSettingBaseInfo.setCertificateType(StringUtils.join(labelSave.getRequestBaseInfo().
                getCertificateType(), COMMA));
        labelSettingBaseInfo.setDepartmentName(StringUtils.join(labelSave.getRequestBaseInfo().
                getDepartmentName(), COMMA));
        labelSettingBaseInfo.setJobTitle(StringUtils.join(labelSave.getRequestBaseInfo().
                getJobTitles(), COMMA));

        //----------------------------数组装换成字符串存储----------------------------
        labelSettingBaseInfo.setBirthdayRangeDateJson(StringUtils.join(labelSave.getRequestBaseInfo().
                getBirthdayRangeDateJson(), COMMA));
        labelSettingBaseInfo.setBirthdayRangeMonth(StringUtils.join(labelSave.getRequestBaseInfo().
                getBirthdayRangeMonth(), COMMA));
        labelSettingBaseInfo.setRegisterFixedTimeRangeJson(StringUtils.join(labelSave.getRequestRegisterInfo().
                getRegisterFixedTimeRangeJson(), COMMA));
        labelSettingBaseInfo.setRegisterSourceType(StringUtils.join(labelSave.getRequestRegisterInfo().
                getRegisterSourceType(), COMMA));
        labelSettingBaseInfo.setRegisterStores(StringUtils.join(labelSave.getRequestRegisterInfo().
                getRegisterStores(), COMMA));
        labelSettingBaseInfo.setIndustry(StringUtils.join(labelSave.getRequestRegisterInfo()
                .getIndustries(), COMMA));
        labelSettingBaseInfo.setLabelAreaJson(ObjectUtil.objToString(JSON.toJSON(labelSave.getRequestBaseInfo()
                .getLabelAreaJson())));
        labelSettingBaseInfo.setOwnedEquityCardJson(StringUtils.join(labelSave.getRequestEquityCardInfo().
                getOwnedEquityCard(), COMMA));
        labelSettingBaseInfo.setEquityCardFixedTimeRangeJson(StringUtils.join(labelSave.getRequestEquityCardInfo().
                getEquityCardFixedTimeRangeJson(), COMMA));
        labelSettingBaseInfo.setEquityCardSourceType(StringUtils.join(labelSave.getRequestEquityCardInfo().
                getEquityCardSourceType(), COMMA));
        //消费
        labelSettingBaseInfo.setConsumptionStoreJson(StringUtils.join(labelSave.getRequestConsumptionInfo().
                getConsumptionStoreJson(), COMMA));
        labelSettingBaseInfo.setConsumptionAmountFixedDateJson(StringUtils.join(labelSave.getRequestConsumptionInfo().
                getConsumptionAmountFixedDateJson(), COMMA));
        labelSettingBaseInfo.setConsumptionCountFixedDateJson(StringUtils.join(labelSave.getRequestConsumptionInfo().
                getConsumptionCountFixedDateJson(), COMMA));
        labelSettingBaseInfo.setConsumptionAvgFixedDateJson(StringUtils.join(labelSave.getRequestConsumptionInfo().
                getConsumptionAvgFixedDateJson(), COMMA));
        // 注册门店
        if (labelSave.getRequestRegisterInfo().getRegisterStores() != null) {
            labelSettingBaseInfo.setRegisterStores(JSON.toJSONString(labelSave.getRequestRegisterInfo().getRegisterStores()));
        } else {
            labelSettingBaseInfo.setRegisterStores(null);
        }

        // 消费门店
        if (labelSave.getRequestConsumptionInfo().getConsumptionStoreJson() != null) {
            labelSettingBaseInfo.setConsumptionStoreJson(JSON.toJSONString(labelSave.getRequestConsumptionInfo().getConsumptionStoreJson()));
        } else {
            labelSettingBaseInfo.setConsumptionStoreJson(null);
        }

        // 充值门店
        if (labelSave.getRequestRechargeInfo().getRechargeStore() != null) {
            labelSettingBaseInfo.setRechargeStore(JSON.toJSONString(labelSave.getRequestRechargeInfo().getRechargeStore()));
        } else {
            labelSettingBaseInfo.setRechargeStore(null);
        }

        //成长值
        labelSettingBaseInfo.setCardMemberLevelGuid(StringUtils.join(labelSave.getRequestGrowInfo().
                getCardMemberLevelGuid(), COMMA));
        labelSettingBaseInfo.setGrowValueFixedDateJson(StringUtils.join(labelSave.getRequestGrowInfo().
                getGrowValueFixedDateJson(), COMMA));
        // 付费等级
        labelSettingBaseInfo.setPaidMemberLevelGuid(StringUtils.join(labelSave.getRequestGrowInfo().getPaidMemberLevelGuid(), COMMA));

        //积分
        labelSettingBaseInfo.setGetIntegralFixedDateJson(StringUtils.join(labelSave.getRequestIntegraInfo().
                getGetIntegralFixedDateJson(), COMMA));
        labelSettingBaseInfo.setConsumptionIntegralFixedDateJson(StringUtils.join(labelSave.getRequestIntegraInfo().
                getConsumptionIntegralFixedDateJson(), COMMA));

        // 自定义资料项
        if (labelSave.getRequestBaseInfo().getCustomDataItems() != null) {
            labelSettingBaseInfo.setDataItemJson(JSON.toJSONString(labelSave.getRequestBaseInfo().getCustomDataItems()));
        } else {
            labelSettingBaseInfo.setDataItemJson(null);
        }
    }

    /**
     * 标签刷新-异步执行
     */
    /*@Override
    public void refreshLabel(List<String> memberGuid, List<HsaLabelSetting> hsaLabelSettings, int assignLabel,
                             Integer sourceType, Integer triggerType) {
        log.info("标签更新任务开始执行");
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("刷新标签headerUserInfo:------>{}", JSON.toJSONString(headerUserInfo));
        memberBaseThreadExecutor.execute(() -> {
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            //导入会员、新增会员指定标签，不判断条件，直接添加
            if (!CollectionUtils.isEmpty(hsaLabelSettings) && !CollectionUtils.isEmpty(memberGuid)) {
                saveHsaMemberLabel(memberGuid, hsaLabelSettings, headerUserInfo);
            }
            //指定标签，如新增标签
            if (assignLabel == BooleanEnum.TRUE.getCode()) {
                relationAssignLabel(hsaLabelSettings, headerUserInfo, memberGuid);
                return;
            }
            List<HsaLabelSetting> labelSettings = getHsaLabelSetting(hsaLabelSettings, triggerType, headerUserInfo);
            log.info("labelSettings====>{}", JSON.toJSON(labelSettings));
            for (HsaLabelSetting labelSetting : labelSettings) {
                try {
                    singleLabelRefresh(labelSetting, headerUserInfo, memberGuid);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    log.error("关联失败标签guid：{}", labelSetting.getGuid());
                }
            }
            //注册完成的会员开卡
            if (CollectionUtil.isNotEmpty(memberGuid) && Objects.nonNull(sourceType)) {
                hsaElectronicCardService.openCardByMemberLabel(memberGuid, sourceType);
            }
        });
    }*/


    /**
     * 标签刷新-异步执行
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshLabel(List<String> memberGuid, List<HsaLabelSetting> hsaLabelSettings, int assignLabel, Integer sourceType,
                             Integer triggerType) {
        log.info("标签更新任务开始执行");
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        memberBaseThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            //导入会员、新增会员指定标签，不判断条件，直接添加
            if (!CollectionUtils.isEmpty(hsaLabelSettings) && !CollectionUtils.isEmpty(memberGuid)) {
                saveHsaMemberLabel(memberGuid, hsaLabelSettings, headerUserInfo);
            }
            //指定标签，如新增标签
            if (assignLabel == BooleanEnum.TRUE.getCode()) {
                relationAssignLabel(hsaLabelSettings, headerUserInfo, memberGuid, triggerType);
                return;
            }
            List<HsaLabelSetting> labelSettings = getHsaLabelSetting(hsaLabelSettings, triggerType, headerUserInfo);
            log.info("labelSettings====>{}", JSON.toJSON(labelSettings));
            for (HsaLabelSetting labelSetting : labelSettings) {
                try {
                    RELATION_LABEL_QUEUE.add(new QueueRelationLabelDTO(labelSetting, headerUserInfo, memberGuid, sourceType, triggerType));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    log.error("关联失败标签guid：{}", labelSetting.getGuid());
                }
            }
        });
    }

    @Override
    public void feignRefreshLabel(List<String> memberGuid, List<String> hsaLabelSettings, int assignLabel, Integer sourceType, Integer triggerType) {
        List<HsaLabelSetting> hsaLabelSetting = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(hsaLabelSettings)) {
            hsaLabelSetting = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                    .in(HsaLabelSetting::getGuid, hsaLabelSettings));
        }
        log.info("远程标签刷新：memberGuid{}", JSONObject.toJSONString(memberGuid));
        this.refreshLabel(memberGuid, hsaLabelSetting, assignLabel, null, triggerType);
    }

    @PostConstruct
    public void refreshLabelQueue() {
        memberBaseThreadExecutor.execute(() -> {
            QueueRelationLabelDTO relationLabelDTO = null;
            while (true) {
                try {
                    relationLabelDTO = RELATION_LABEL_QUEUE.take();
                    if (Objects.isNull(relationLabelDTO)) {
                        continue;
                    }
                    Set<String> memberInfoGuids = singleLabelRefresh(relationLabelDTO.getLabelSetting(), relationLabelDTO.getHeaderUserInfo()
                            , relationLabelDTO.getMemberGuid(), relationLabelDTO.getTriggerType());
                    //注册完成的会员开卡
                    if (CollectionUtil.isNotEmpty(memberInfoGuids) && Objects.nonNull(relationLabelDTO.getSourceType())) {
                        hsaElectronicCardService.openCardByMemberLabel(new ArrayList<>(memberInfoGuids), relationLabelDTO.getSourceType());
                    }
                } catch (Exception e) {
                    log.error("关联标签失败=======>{}", relationLabelDTO.getLabelSetting().getGuid());
                }
            }
        });
    }

    private List<HsaLabelSetting> getHsaLabelSetting(List<HsaLabelSetting> hsaLabelSettings, Integer triggerType,
                                                     HeaderUserInfo headerUserInfo) {
        log.info("triggerType---->{},getOperSubjectGuid----->{}", triggerType, headerUserInfo.getOperSubjectGuid());
        //排除导入会员、信息会员指定的标签
        List<String> list = CollectionUtils.isEmpty(hsaLabelSettings) ? new ArrayList<>() : hsaLabelSettings.stream()
                .map(HsaLabelSetting::getGuid).collect(Collectors.toList());
        if (triggerType == LabelTriggerTypeEnum.CONSUMPTION_INFO.getCode() ||
                triggerType == LabelTriggerTypeEnum.RECHARGE_INFO.getCode() ||
                triggerType == LabelTriggerTypeEnum.INTEGRAL_INFO.getCode() ||
                triggerType == LabelTriggerTypeEnum.IDENTITY_INFO.getCode() ||
                triggerType == LabelTriggerTypeEnum.UPDATE_STORE.getCode() ||
                triggerType == LabelTriggerTypeEnum.REGISTER_INFO.getCode()) {
            return hsaLabelMapper.getHsaLabelSetting(headerUserInfo.getOperSubjectGuid(), triggerType, list);
        }
        // 查询所有标签(如新增会员)
        List<HsaLabelSetting> labelSettings = this.list(new LambdaQueryWrapper<HsaLabelSetting>()
                .eq(HsaLabelSetting::getLabelType, LabelTypeEnum.AUTOMATION.getCode())
                .eq(HsaLabelSetting::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                .notIn(!CollectionUtils.isEmpty(list), HsaLabelSetting::getGuid, list)
                .eq(HsaLabelSetting::getIsEnable, EnableEnum.ENABLE.getCode())
        );
        return labelSettings;
    }


    private void relationAssignLabel(List<HsaLabelSetting> hsaLabelSettings, HeaderUserInfo headerUserInfo,
                                     List<String> memberGuid, Integer triggerType) {
        for (HsaLabelSetting labelSetting : hsaLabelSettings) {
            try {
                if (labelSetting.getIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
                    continue;
                }
                singleLabelRefresh(labelSetting, headerUserInfo, memberGuid, triggerType);
            } catch (Exception e) {
                log.error("关联失败标签guid：{}", labelSetting.getGuid());
                log.error(e.getMessage(), e);
            }
        }
    }

    private void saveHsaMemberLabel(List<String> memberGuidList, List<HsaLabelSetting> hsaLabelSettings,
                                    HeaderUserInfo headerUserInfo) {
        LocalDateTime now = LocalDateTime.now();
        List<HsaMemberLabel> labels = new ArrayList<>();
        List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
        for (HsaLabelSetting hsaLabelSetting : hsaLabelSettings) {
            for (String memberGuid : memberGuidList) {
                HsaMemberLabel label = new HsaMemberLabel()
                        .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName()))
                        .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                        .setOperatorName(headerUserInfo.getUserName())
                        .setOperatorGuid(headerUserInfo.getUserGuid())
                        .setLabelSettingGuid(hsaLabelSetting.getGuid())
                        .setOperationMemberInfoGuid(memberGuid)
                        .setConnectionType(LabelConnectTypeEnum.MANUAL.getCode())
                        .setGmtModified(LocalDateTime.now())
                        .setGmtCreate(LocalDateTime.now());
                labels.add(label);
                //关联记录
                getMemberLabelRecord(hsaLabelSetting, memberLabelRecordDTOArrayList, now, label, headerUserInfo);
            }
        }
        if (!CollectionUtils.isEmpty(labels)) {
            hsaMemberLabelMapper.batchSave(labels);
            //保存关联记录
            memberLabelRecordDTOArrayList.forEach(in -> in.setConnectionType(LabelConnectTypeEnum.MANUAL.getCode()));
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
        }
    }

    /**
     * 单个标签刷新
     *
     * @param labelSetting   会员标签设置表
     * @param headerUserInfo 请求头用户信息
     * @param memberGuid     会员guid集合
     */
    private Set<String> singleLabelRefresh(HsaLabelSetting labelSetting, HeaderUserInfo headerUserInfo, List<String> memberGuid,
                                           Integer triggerType) {
        log.info(labelSetting.getGuid() + "->标签开始过滤：");
        Set<String> memberInfoGuids = Objects.isNull(memberGuid) ? new HashSet<>() : new HashSet<>(memberGuid);
        return refreshLabelHandle(memberInfoGuids, labelSetting, headerUserInfo, triggerType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Set<String> refreshLabelHandle(Set<String> memberInfoGuids, HsaLabelSetting labelSetting, HeaderUserInfo headerUserInfo,
                                          Integer triggerType) {
        HsaLabelSettingBaseInfo baseInfo = hsaLabelSettingBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaLabelSettingBaseInfo>()
                .eq(HsaLabelSettingBaseInfo::getLabelSettingGuid, labelSetting.getGuid()));
        RequestLabelQuery query = new RequestLabelQuery();
        if (!CollectionUtils.isEmpty(memberInfoGuids)) {
            query.setMemberGuid(memberInfoGuids);
        }
        // 设置query参数
        querySetting(query, labelSetting, baseInfo, headerUserInfo);
        // 获取标签条件设置： 0满足以下任一条件，1满足以下所有条件
        Integer conditionSet = labelSetting.getConditionSet();
        Set<String> members;
        //满足以下任一条件
        if (conditionSet == ConditionSetEnum.ONE_CONDITION.getCode()) {
            // 勾选基本信息
            members = queryConditionMembers(baseInfo, query);
            //新增会员，导入会员
            if (!CollectionUtils.isEmpty(memberInfoGuids)) {
                members = SetOptUtils.intersect(members, memberInfoGuids);
            }
        } else {
            members = getMemberGuids(baseInfo, query, memberInfoGuids);
        }
        log.info("标签{}-{}满足条件会员数量===================>memberSize:{}", labelSetting.getGuid(), labelSetting.getLabelName(), members.size());
        //注册时没有用户直接返回
        if (triggerType == LabelTriggerTypeEnum.REGISTER_INFO.getCode() && members.size() == 0) {
            return members;
        }
        // 保存会员标签
        saveLabelMember(labelSetting, members, headerUserInfo, memberInfoGuids, triggerType);
        return members;
    }

    /**
     * 获取满足条件的会员guid集合
     *
     * @param baseInfo 会员标签设置表
     * @param query    请求参数
     * @return 满足条件的会员guid集合
     */
    private Set<String> queryConditionMembers(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        Set<String> members = new HashSet<>();
        // 勾选基本信息
        if (Boolean.TRUE == baseInfo.getSetBaseInfo()) {
            members.addAll(baseFilter(baseInfo, query));
        }

        // 勾选注册信息
        if (Boolean.TRUE == baseInfo.getSetRegisterInfo()) {
            members.addAll(registerFilter(query));
        }

        //勾选身份信息
        if (Boolean.TRUE == baseInfo.getSetEquityCardInfo()) {
            members.addAll(equityCardFilter(query));
        }

        //勾选充值信息
        if (NumberConstant.NUMBER_1 == baseInfo.getSetRechargeInfo()) {
            members.addAll(rechargeInfoFilter(query, baseInfo));
        }

        //勾选消费信息
        if (Boolean.TRUE == baseInfo.getSetConsumptionInfo()) {
            members.addAll(consumptionFilter(query));
        }

        //成长值信息
        if (Boolean.TRUE == baseInfo.getSetGrowthInfo()) {
            members.addAll(growthValueFilter(query));
        }

        if (Boolean.TRUE == baseInfo.getSetIntegral()) {
            members.addAll(integralFilter(query));
        }
        return members;
    }

    /**
     * 基本信息过滤
     *
     * @param baseInfo 配置信息
     * @param query    查询参数
     * @return 满足条件的会员guid集合
     */
    @Override
    public Set<String> baseFilter(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        // 基本信息过滤的会员guid集合
        Set<String> baseFilterMemberIds = hsaOperationMemberInfoMapper.memberLabelBaseFilter(query)
                .stream()
                .map(HsaOperationMemberInfo::getGuid)
                .collect(Collectors.toSet());

        // 自定义资料项筛选
        if (StrUtil.isNotEmpty(baseInfo.getDataItemJson())) {
            List<CustomDataItemWithValues> configDataItems = JSONObject.parseArray(baseInfo.getDataItemJson(), CustomDataItemWithValues.class);
            if (CollUtil.isNotEmpty(configDataItems)) {
                configDataItems = configDataItems.stream()
                        .filter(customDataItemWithValues -> CollUtil.isNotEmpty(customDataItemWithValues.getValues()))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(configDataItems)) {
                    // 自定义资料项过滤的会员guid集合
                    Set<String> customFilterMemberIds = customDataItemFilter(query.getOperSubjectGuid(), configDataItems);

                    if (CollUtil.isEmpty(baseFilterMemberIds)) {
                        baseFilterMemberIds.addAll(customFilterMemberIds);
                    } else {
                        //交集
                        baseFilterMemberIds.retainAll(customFilterMemberIds);
                    }
                }
            }
        }
        return baseFilterMemberIds;
    }

    /**
     * 自定义资料项筛选
     *
     * @param operSubjectGuid 运营主体guid
     * @param configDataItems 配置的自定义资料项
     * @return 满足条件的会员guid集合
     */
    private Set<String> customDataItemFilter(String operSubjectGuid, List<CustomDataItemWithValues> configDataItems) {
        // 1. 查询所有data_item_json不为空的会员
        List<HsaOperationMemberInfo> memberInfoList = hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                        .isNotNull(HsaOperationMemberInfo::getDataItemJson)
                        .ne(HsaOperationMemberInfo::getDataItemJson, "")
        );

        if (CollUtil.isEmpty(memberInfoList)) {
            return new HashSet<>();
        }

        // 2. 筛选满足条件的会员
        return memberInfoList.stream()
                .filter(member -> {
                    try {
                        // 解析会员的data_item_json
                        List<DataItemSetVO> memberDataItems = JSONObject.parseArray(
                                member.getDataItemJson(),
                                DataItemSetVO.class
                        );

                        if (CollUtil.isEmpty(memberDataItems)) {
                            return false;
                        }

                        // 将会员的自定义资料项转换为Map，方便查找
                        Map<String, DataItemSetVO> memberDataItemMap = memberDataItems.stream()
                                .collect(Collectors.toMap(
                                        DataItemSetVO::getInfoName,
                                        item -> item,
                                        (v1, v2) -> v1
                                ));

                        // 检查是否满足所有筛选条件
                        return configDataItems.stream().allMatch(configDataItem -> {
                            DataItemSetVO memberItem = memberDataItemMap.get(configDataItem.getInfoName());

                            // 如果会员没有这个资料项，则不满足条件
                            if (memberItem == null) {
                                return false;
                            }

                            // 获取会员资料项的值
                            String memberValue = memberItem.getValue();
                            if (StrUtil.isEmpty(memberValue)) {
                                return false;
                            }

                            // 根据不同的资料项类型进行匹配
                            List<String> configDataItemValues = configDataItem.getValues();
                            String infoFormat = memberItem.getInfoFormat().get(0);
                            String infoCheck = memberItem.getInfoCheck();
                            return matchesDataItemValue(memberValue, configDataItemValues, infoFormat, infoCheck);
                        });
                    } catch (Exception e) {
                        log.error("解析会员自定义资料项失败，memberGuid: {}, error: {}", member.getGuid(), e);
                        return false;
                    }
                })
                .map(HsaOperationMemberInfo::getGuid)
                .collect(Collectors.toSet());
    }

    /**
     * 处理资料项值匹配
     */
    private boolean matchesDataItemValue(String memberValue, List<String> configValues, String infoFormat, String infoCheck) {
        if (StrUtil.isEmpty(memberValue)) {
            return false;
        }

        //  文本、数字、单选项
        if (DataItemFormatEnum.TEXT.getDesc().equals(infoFormat)
                || DataItemFormatEnum.NUMBER.getDesc().equals(infoFormat)
                || DataItemFormatEnum.RADIO_OPTION.getDesc().equals(infoFormat)) {
            return configValues.contains(memberValue);

        } else if (DataItemFormatEnum.MULTIPLE_OPTIONS.getDesc().equals(infoFormat)) {
            JSONArray multipleOption = JSONUtil.parseArray(memberValue);
            //判断会员多选项任意一个在配置的多选项中
            return multipleOption.stream()
                    .anyMatch(item -> configValues.contains(item.toString()));

        } else if (DataItemFormatEnum.DATE_ITEM_OPTION.getDesc().equals(infoFormat)) {
            String startDate = configValues.get(0);
            String endDate = configValues.get(1);
            return isDateInRange(memberValue, startDate, endDate, infoCheck);

        } else if (DataItemFormatEnum.PROVINCES_MUNICIPALITIES.getDesc().equals(infoFormat)) {
            JSONArray memberProvinceCityDistrict = JSONUtil.parseArray(memberValue);
            String memberProvinceCityDistrictStr = memberProvinceCityDistrict.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining("/"));
            //判断会员省市区任意一个在配置的省市区中
            return configValues.stream()
                    .anyMatch(configValue -> {
                        //比如 四川省,四川省/成都市，需要取最后一个
                        String[] configProvinceCityDistrictSplit = configValue.split(",");
                        String configProvinceCityDistrictStr = configProvinceCityDistrictSplit[configProvinceCityDistrictSplit.length - 1];
                        //商城叫：市辖区 ，会员叫：直辖市
                        configProvinceCityDistrictStr = configProvinceCityDistrictStr.replace("直辖市", "市辖区");
                        return memberProvinceCityDistrictStr.contains(configProvinceCityDistrictStr);
                    });

        } else {
            return true;
        }
    }

    /**
     * 处理日期比较
     */
    private boolean isDateInRange(String memberValue, String startDate, String endDate, String infoCheck) {
        DateTime memberDateTime;
        DateTime startDateTime;
        DateTime endDateTime;
        String currentYear = DateUtil.format(new Date(), "yyyy");
        
        switch (infoCheck) {
            case "year,month,day,time":
                memberDateTime = DateUtil.parse(memberValue, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                startDateTime = DateUtil.parse(startDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                endDateTime = DateUtil.parse(endDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                break;
            case "year,month,day":
                memberDateTime = DateUtil.parse(memberValue, DatePattern.NORM_DATE_PATTERN);
                startDateTime = DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                endDateTime = DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                break;
            case "month,day,time":
                // 对于月日时间格式，需要补充年份
                memberDateTime = DateUtil.parse(currentYear + "-" + memberValue, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                startDateTime = DateUtil.parse(startDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                endDateTime = DateUtil.parse(endDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                break;
            case "month,day":
                // 对于月日格式，需要补充年份
                memberDateTime = DateUtil.parse(currentYear + "-" + memberValue, DatePattern.NORM_DATE_PATTERN);
                startDateTime = DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                endDateTime = DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                break;
            case "year,month":
                memberDateTime = DateUtil.parse(memberValue, DatePattern.NORM_MONTH_PATTERN);
                startDateTime = DateUtil.beginOfMonth(DateUtil.parse(startDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                endDateTime = DateUtil.endOfMonth(DateUtil.parse(endDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
                break;
            default:
                log.warn("未知的日期格式: {}", infoCheck);
                return false;
        }
        
        // 比较日期范围
        return memberDateTime.isAfterOrEquals(startDateTime) && memberDateTime.isBeforeOrEquals(endDateTime);
    }

    private Set<String> getMemberGuids(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query, Set<String> memberInfoGuids) {
        Set<String> members = chainEnterService.executeQueryLabelMembers(query, baseInfo);
        if (!CollectionUtils.isEmpty(memberInfoGuids)) {
            members = SetOptUtils.intersect(members, memberInfoGuids);
        }
        return members;
    }


    @Override
    public Set<String> integralFilter(RequestLabelQuery query) {

        boolean isOk = false;
        Set<String> memberGuids = new HashSet<>();
        if (Objects.nonNull(query.getMaxIntegral()) || Objects.nonNull(query.getMinIntegral())) {
            memberGuids = hsaOperationMemberInfoMapper.queryIntegralMember(query);
            isOk = true;
            if (CollectionUtils.isEmpty(memberGuids)) {
                return memberGuids;
            }
        }
        if (Objects.nonNull(query.getGetIntegralNumType())) {
            if (isOk) {
                memberGuids = SetOptUtils.intersect(memberGuids, hsaIntegralDetailMapper.queryIntegralRelationMember(query, 0));
            } else {
                memberGuids = hsaIntegralDetailMapper.queryIntegralRelationMember(query, 0);
            }
            if (CollectionUtils.isEmpty(memberGuids)) {
                return memberGuids;
            }
        }
        if (Objects.nonNull(query.getConsumptionIntegralNumType())) {
            if (isOk) {
                memberGuids = SetOptUtils.intersect(memberGuids, hsaIntegralDetailMapper.queryIntegralRelationMember(query, 1));
            } else {
                memberGuids = hsaIntegralDetailMapper.queryIntegralRelationMember(query, 1);
            }
        }
        return memberGuids;
    }

    /**
     * 成长值过滤
     *
     * @param query sql请求参数
     * @return 查询结果
     */
    @Override
    public Set<String> growthValueFilter(RequestLabelQuery query) {
        Set<String> result = null;

        // 免费等级过滤
        if (Objects.nonNull(query.getCardMemberLevelGuid()) && query.getCardMemberLevelGuid().length > 0) {
            Set<String> memberIdByGrade = hsaGrowthValueDetailMapper.queryGuidByGrade(query);
            if (CollUtil.isEmpty(memberIdByGrade)) {
                return new HashSet<>();
            }
            result = new HashSet<>(memberIdByGrade);
        }

        // 成长值过滤
        if (query.getMinCurrentGrowValue() != null
                || query.getMaxCurrentGrowValue() != null
                || query.getGrowValueType() != null
                || query.getMinTotalGrowValue() != null
                || query.getMaxTotalGrowValue() != null) {
            Set<String> memberIdByGrowthValue = new HashSet<>(hsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query));
            if (CollUtil.isEmpty(memberIdByGrowthValue)) {
                return new HashSet<>();
            }
            result = intersectOrInit(result, memberIdByGrowthValue);
        }

        // 付费等级过滤
        if (CollUtil.isNotEmpty(query.getPaidMemberLevelGuid())
                || StrUtil.isNotEmpty(query.getPaidMemberStartDateFrom())
                || StrUtil.isNotEmpty(query.getPaidMemberStartDateTo())
                || query.getPaidMemberDurationMin() != null
                || query.getPaidMemberDurationMax() != null
                || query.getPaidMemberExpireRemainMin() != null
                || query.getPaidMemberExpireRemainMax() != null) {
            Set<String> memberIdByPaidGrade = paidGradeFilter(query.getOperSubjectGuid(), query);
            result = intersectOrInit(result, memberIdByPaidGrade);
        }

        return result == null ? new HashSet<>() : result;
    }

    /**
     * 交集或初始化
     */
    private Set<String> intersectOrInit(Set<String> base, Set<String> other) {
        if (base == null) {
            return new HashSet<>(other);
        } else {
            base.retainAll(other);
            return base;
        }
    }

    /**
     * 付费等级过滤
     *
     * @param operSubjectGuid 运营主体guid
     * @param query           请求参数
     * @return 满足条件的会员guid集合
     */
    private Set<String> paidGradeFilter(String operSubjectGuid, RequestLabelQuery query) {
        // 1. 先查出有付费等级的会员guid
        List<HsaOperationMemberInfo> paidMembers = getPaidMembersForPaidGradeFilter(operSubjectGuid);
        if (CollUtil.isEmpty(paidMembers)) {
            return Collections.emptySet();
        }
        // 2. 付费等级筛选
        paidMembers = filterPaidMemberLevelGuidsForPaidGradeFilter(paidMembers, query.getPaidMemberLevelGuid());
        if (CollUtil.isEmpty(paidMembers)) {
            return Collections.emptySet();
        }
        // 3. 成为付费会员时间点筛选
        paidMembers = filterPaidMemberStartDateForPaidGradeFilter(paidMembers, query.getPaidMemberStartDateFrom(), query.getPaidMemberStartDateTo());
        if (CollUtil.isEmpty(paidMembers)) {
            return Collections.emptySet();
        }
        // 4. 成为付费会员时长筛选
        paidMembers = filterPaidMemberDurationForPaidGradeFilter(paidMembers, query.getPaidMemberDurationMin(), query.getPaidMemberDurationMax(), query.getPaidMemberDurationUnit());
        if (CollUtil.isEmpty(paidMembers)) {
            return Collections.emptySet();
        }
        // 5. 距离会员过期时间筛选
        paidMembers = filterPaidMemberExpireRemainForPaidGradeFilter(paidMembers, query.getPaidMemberExpireRemainMin(), query.getPaidMemberExpireRemainMax(), query.getPaidMemberExpireRemainUnit());
        if (CollUtil.isEmpty(paidMembers)) {
            return Collections.emptySet();
        }
        // 6. 返回会员guid集合
        return paidMembers.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toSet());
    }

    /**
     * 1. 先查出有付费等级的会员guid
     */
    private List<HsaOperationMemberInfo> getPaidMembersForPaidGradeFilter(String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                        .isNotNull(HsaOperationMemberInfo::getMemberPaidGradeInfoGuid)
                        .ne(HsaOperationMemberInfo::getMemberPaidGradeInfoGuid, "")
                        .isNotNull(HsaOperationMemberInfo::getExpireTime)
                        .gt(HsaOperationMemberInfo::getExpireTime, LocalDateTime.now())
        );
    }

    /**
     * 2. 付费等级筛选
     */
    private List<HsaOperationMemberInfo> filterPaidMemberLevelGuidsForPaidGradeFilter(List<HsaOperationMemberInfo> paidMembers, List<String> paidMemberLevelGuids) {
        if (CollUtil.isNotEmpty(paidMemberLevelGuids)) {
            paidMembers = paidMembers.stream()
                    .filter(member -> paidMemberLevelGuids.contains(member.getMemberPaidGradeInfoGuid()))
                    .collect(Collectors.toList());
        }
        return paidMembers;
    }

    /**
     * 3. 成为付费会员时间点筛选
     */
    private List<HsaOperationMemberInfo> filterPaidMemberStartDateForPaidGradeFilter(List<HsaOperationMemberInfo> paidMembers, String startDateFrom, String startDateTo) {
        LocalDateTime paidMemberStartDateFrom = StrUtil.isNotEmpty(startDateFrom) ? LocalDate.parse(startDateFrom).atStartOfDay() : null;
        LocalDateTime paidMemberStartDateTo = StrUtil.isNotEmpty(startDateTo) ? LocalDate.parse(startDateTo).atTime(23, 59, 59) : null;
        return paidMembers.stream()
                .filter(member -> {
                    LocalDateTime paidMemberTime = member.getUpgradeTime();
                    if (paidMemberTime == null) return false;
                    boolean afterFrom = paidMemberStartDateFrom == null || !paidMemberTime.isBefore(paidMemberStartDateFrom);
                    boolean beforeTo = paidMemberStartDateTo == null || !paidMemberTime.isAfter(paidMemberStartDateTo);
                    return afterFrom && beforeTo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 4. 成为付费会员时长筛选
     */
    private List<HsaOperationMemberInfo> filterPaidMemberDurationForPaidGradeFilter(List<HsaOperationMemberInfo> paidMembers, Integer min, Integer max, DataUnitEnum unit) {
        if (min == null && max == null) return paidMembers;
        LocalDateTime now = LocalDateTime.now();
        return paidMembers.stream()
                .filter(member -> {
                    LocalDateTime beginPaidGradeMemberDate = member.getUpgradeTime();
                    if (beginPaidGradeMemberDate == null) return false;
                    long diff;
                    if (DataUnitEnum.YEAR.equals(unit)) {
                        diff = ChronoUnit.YEARS.between(beginPaidGradeMemberDate, now);
                    } else if (DataUnitEnum.MONTH.equals(unit)) {
                        diff = ChronoUnit.MONTHS.between(beginPaidGradeMemberDate, now);
                    } else {
                        diff = ChronoUnit.DAYS.between(beginPaidGradeMemberDate, now);
                    }
                    boolean gtMin = min == null || diff >= min;
                    boolean ltMax = max == null || diff <= max;
                    return gtMin && ltMax;
                })
                .collect(Collectors.toList());
    }

    /**
     * 5. 距离会员过期时间筛选
     */
    private List<HsaOperationMemberInfo> filterPaidMemberExpireRemainForPaidGradeFilter(List<HsaOperationMemberInfo> paidMembers, Integer min, Integer max, DataUnitEnum unit) {
        if (min == null && max == null) return paidMembers;
        LocalDateTime now = LocalDateTime.now();
        return paidMembers.stream()
                .filter(member -> {
                    LocalDateTime paidMemberExpireDate = member.getExpireTime();
                    if (paidMemberExpireDate == null) return false;
                    long diff;
                    if (DataUnitEnum.YEAR.equals(unit)) {
                        diff = ChronoUnit.YEARS.between(now, paidMemberExpireDate);
                    } else if (DataUnitEnum.MONTH.equals(unit)) {
                        diff = ChronoUnit.MONTHS.between(now, paidMemberExpireDate);
                    } else {
                        diff = ChronoUnit.DAYS.between(now, paidMemberExpireDate);
                    }
                    boolean gtMin = min == null || diff >= min;
                    boolean ltMax = max == null || diff <= max;
                    return gtMin && ltMax;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Set<String> consumptionFilter(RequestLabelQuery query) {

        Set<String> collect = new HashSet<>();
        List<MemberConsumptionInfoVO> consumptionInfoVOS = hsaMemberConsumptionMapper.findRelationLabelMemberGuid(query);
        if (CollectionUtils.isEmpty(consumptionInfoVOS)) {
            return collect;
        }
        Map<String, List<MemberConsumptionInfoVO>> consumptionInfoMap = consumptionInfoVOS.stream().collect(Collectors.
                groupingBy(MemberConsumptionInfoVO::getMemberInfoGuid));
        Set<Map.Entry<String, List<MemberConsumptionInfoVO>>> entrySet = consumptionInfoMap.entrySet();
        for (Map.Entry<String, List<MemberConsumptionInfoVO>> entry : entrySet) {
            String memberGuid = entry.getKey();
            List<MemberConsumptionInfoVO> memberConsumptionInfoVOS = consumptionInfoMap.get(memberGuid);
            if (meetTheConditions(memberConsumptionInfoVOS, query)) {
                collect.add(memberGuid);
            }
        }
        return collect;
    }


    private boolean meetTheConditions(List<MemberConsumptionInfoVO> memberConsumptionInfoVOS, RequestLabelQuery query) {
        //累计消费金额类型
        if (Objects.nonNull(query.getConsumptionAmountType()) && (checkMaxConsumptionAmount(memberConsumptionInfoVOS, query))) {
            return false;

        }
        //累计消费均价类型
        if (Objects.nonNull(query.getConsumptionAvgType()) && (checkMaxConsumptionAvg(memberConsumptionInfoVOS, query))) {
            return false;
        }
        //累计消费次数类型
        return !Objects.nonNull(query.getConsumptionCountType()) || (!checkConsumptionCount(memberConsumptionInfoVOS, query));

    }

    private static boolean checkConsumptionCount(List<MemberConsumptionInfoVO> memberConsumptionInfoVOS, RequestLabelQuery query) {
        //消费次数
        int consumptionCount = memberConsumptionInfoVOS.size();
        //累计消费次数小于消费次数最小值
        if (Objects.nonNull(query.getMinConsumptionCount()) && (consumptionCount < query.getMinConsumptionCount())) {
                return true;

        }
        //累计消费次数大于消费次数最大值
        return Objects.nonNull(query.getMaxConsumptionCount()) && (consumptionCount > query.getMaxConsumptionCount());
    }

    private static boolean checkMaxConsumptionAvg(List<MemberConsumptionInfoVO> memberConsumptionInfoVOS, RequestLabelQuery query) {
        double doubleValue = memberConsumptionInfoVOS.stream().map(r -> BigDecimalUtil.nonNullValue(r.getOrderPaidAmount()))
                .mapToDouble(BigDecimal::doubleValue).average().getAsDouble();
        BigDecimal avgValue = BigDecimal.valueOf(doubleValue);
        //累计消费均价小于消费均价最小值
        if (Objects.nonNull(query.getMinConsumptionAvg())) {
            if (BigDecimalUtil.lessThan(avgValue, query.getMinConsumptionAvg())) {
                return true;
            }
        }
        if (Objects.nonNull(query.getMaxConsumptionAvg())) {
            //累计消费均价大于消费均价最大值
            return BigDecimalUtil.greaterThan(avgValue, query.getMaxConsumptionAvg());
        }
        return false;
    }

    private static boolean checkMaxConsumptionAmount(List<MemberConsumptionInfoVO> memberConsumptionInfoVOS, RequestLabelQuery query) {
        BigDecimal totalAmount = memberConsumptionInfoVOS.stream().map(r -> BigDecimalUtil.nonNullValue(r.getOrderPaidAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (Objects.nonNull(query.getMinConsumptionAmount())) {
            //累计消费金额小于累计消费最小值
            if (BigDecimalUtil.lessThan(totalAmount, query.getMinConsumptionAmount())) {
                return true;
            }
        }
        if (Objects.nonNull(query.getMaxConsumptionAmount())) {
            //累计消费金额大于累计消费最大值
            return BigDecimalUtil.greaterThan(totalAmount, query.getMaxConsumptionAmount());
        }
        return false;
    }

    /**
     * 注册信息查询
     *
     * @param query sql请求参数
     * @return 查询结果
     */
    private Set<String> registerFilter(RequestLabelQuery query) {

        return hsaOperationMemberInfoMapper.memberLabelRegisterFilter(query).stream().map(HsaOperationMemberInfo::getGuid).
                collect(Collectors.toSet());
    }

    /**
     * 会员卡信息查询
     *
     * @param query sql请求参数
     * @return 查询结果
     */
    private Set<String> equityCardFilter(RequestLabelQuery query) {

        return hsaOperationMemberInfoMapper.memberLabelEquityCardFilter(query).stream().map(HsaOperationMemberInfo::
                getGuid).collect(Collectors.toSet());
    }

    /**
     * 会员标签充值信息过滤
     *
     * @param query    请求筛选的条件参数
     * @param baseInfo 会员标签基础信息
     * @return 满足条件的会员guid
     */
    @Override
    public Set<String> rechargeInfoFilter(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        Set<String> collect = new HashSet<>();
        if (BooleanEnum.FALSE.getCode() == baseInfo.getSetRechargeInfo()) {
            log.info("充值信息未勾选，直接返回空集合");
            return collect;
        }
        
        // 根据labelSettingGuid获取标签配置信息，判断条件设置
        HsaLabelSetting labelSetting = hsaLabelSettingMapper.selectOne(
                new LambdaQueryWrapper<HsaLabelSetting>()
                        .eq(HsaLabelSetting::getGuid, baseInfo.getLabelSettingGuid())
        );
        if (labelSetting == null) {
            log.warn("未找到标签配置信息，labelSettingGuid: {}", baseInfo.getLabelSettingGuid());
            return collect;
        }
        
        // 获取条件设置：0满足以下任一条件，1满足以下所有条件
        Integer conditionSet = labelSetting.getConditionSet();
        boolean isAllConditionsRequired = ConditionSetEnum.ALL_CONDITION.getCode() == conditionSet;
        log.info("开始充值信息过滤 - 条件设置: {}, 是否需要满足所有条件: {}", conditionSet, isAllConditionsRequired);
        
        //用户拥有的会员卡和充值信息
        List<LabelRechargeInfo> labelRechargeInfos = new ArrayList<>();
        if (CollUtil.isEmpty(query.getMemberGuid())) {
            // query.getOperSubjectGuid() 前面有set值
            Set<String> guids = new HashSet<>(hsaOperationMemberInfoMapper
                    .findAllGuidByOperSubjectGuid(query.getOperSubjectGuid(), null));
            if (guids.isEmpty()) {
                log.info("运营主体下没有找到会员，返回空集合");
                return collect;
            }
            query.setMemberGuid(guids);
        }
        
        //当前用户拥有的会员卡
        List<HsaMemberInfoCard> hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(
                new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, query.getMemberGuid())
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, query.getOperSubjectGuid())
                        .and(i -> i.ne(HsaMemberInfoCard::getElectronicCardState, EletronicCardStateEnum.HAVE_EXPIRED.getCode())
                                .or()
                                .ne(HsaMemberInfoCard::getPhysicalCardState, EletronicCardStateEnum.HAVE_EXPIRED.getCode()))
                        .isNotNull(HsaMemberInfoCard::getMemberInfoGuid));
        
        //判断充值信息条件是否选中了（充值门店or累计充值金额or累计充值次数or平均充值金额）条件
        Boolean isRecharge = isRecharge(baseInfo, query);
        
        if (Boolean.TRUE.equals(isRecharge)) {
            //用户会员卡充值消费信息
            labelRechargeInfos = hsaOperationMemberInfoMapper.memberLabelRechargeInfoFilter(query);
        }
        if (CollUtil.isEmpty(hsaMemberInfoCardList)) {
            log.info("没有找到有效的会员卡，返回空集合");
            return collect;
        }
        //有会员卡但是没充值消费信息场景
        if (CollUtil.isEmpty(labelRechargeInfos)) {
            labelRechargeInfos = CardInfoTransform.INSTANCE.memberCardTOLabel(hsaMemberInfoCardList);
        }
        
        log.info("充值信息过滤统计 - 查询会员数: {}, 有效会员卡数: {}, 充值记录数: {}, 是否选中充值条件: {}", 
                query.getMemberGuid().size(), hsaMemberInfoCardList.size(), labelRechargeInfos.size(), isRecharge);
        
        // 根据会员,分组（主要用于统计会员的充值金额、充值次数、平均充值金额是否满足条件）
        Map<String, List<LabelRechargeInfo>> memberRechargeMap = labelRechargeInfos.stream()
                .collect(Collectors.groupingBy(LabelRechargeInfo::getMemberInfoGuid));
        // 根据当前会员拥有的会员卡，分组（主要用于统计会员实时的充值、赠送金额范围是否满足条件）
        Map<String, List<HsaMemberInfoCard>> memberCardMap = hsaMemberInfoCardList.stream()
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid));

        // 处理会员卡过滤
        ProcessStatistics statistics = processMemberCardFiltering(
                memberCardMap, memberRechargeMap, query, baseInfo, isRecharge, isAllConditionsRequired, collect);
        
        log.info("充值信息过滤完成 - 处理会员数: {}, 通过筛选: {}, 最终结果集大小: {}", 
                statistics.getProcessedCount(), statistics.getPassedCount(), collect.size());
        return collect;
    }

    /**
     * 处理会员卡过滤逻辑
     *
     * @param memberCardMap 会员卡映射
     * @param memberRechargeMap 会员充值记录映射
     * @param query 查询条件
     * @param baseInfo 标签基础信息
     * @param isRecharge 是否选中充值条件
     * @param isAllConditionsRequired 是否需要满足所有条件
     * @param collect 结果集合（直接操作原集合）
     * @return 处理统计结果
     */
    private ProcessStatistics processMemberCardFiltering(
            Map<String, List<HsaMemberInfoCard>> memberCardMap,
            Map<String, List<LabelRechargeInfo>> memberRechargeMap,
            RequestLabelQuery query,
            HsaLabelSettingBaseInfo baseInfo,
            Boolean isRecharge,
            boolean isAllConditionsRequired,
            Set<String> collect) {
        
        // 统计变量
        int processedCount = 0;
        int passedCount = 0;
        
        for (Map.Entry<String, List<HsaMemberInfoCard>> entry : memberCardMap.entrySet()) {
            String memberGuid = entry.getKey();
            List<HsaMemberInfoCard> rechargeInfoList = entry.getValue();
            processedCount++;
            
            // 检查实时余额条件
            boolean realTimeFilterResult = realTimeAmountFilter(query, rechargeInfoList);
            boolean realTimePass = !realTimeFilterResult; // 实时余额是否通过
            
            // 检查历史充值条件
            boolean historyPass;
            if (Boolean.TRUE.equals(isRecharge)) {
                if (memberRechargeMap.containsKey(memberGuid)) {
                    List<LabelRechargeInfo> labelRechargeInfoList = memberRechargeMap.get(memberGuid);
                    boolean historyFilterResult = historyAmountFilter(baseInfo, labelRechargeInfoList);
                    historyPass = !historyFilterResult; // 历史充值是否通过
                } else {
                    historyPass = false;
                }
            } else {
                historyPass = true;
            }
            
            // 根据标签的conditionSet配置判断条件逻辑
            boolean shouldAddMember= isShouldAddMember(isAllConditionsRequired, realTimePass, historyPass);

            if (shouldAddMember) {
                collect.add(memberGuid);
                passedCount++;
            }
            
            // 只在调试模式下或者处理失败时打印详细日志
            if (log.isDebugEnabled() || !shouldAddMember) {
                log.debug("会员 {} 处理结果 - 实时余额通过: {}, 历史充值通过: {}, 最终结果: {}", 
                    memberGuid, realTimePass, historyPass, shouldAddMember);
            }
        }
        
        return new ProcessStatistics(processedCount, passedCount);
    }

    private static boolean isShouldAddMember (boolean isAllConditionsRequired, boolean realTimePass, boolean historyPass) {
        boolean shouldAddMember;
        if (isAllConditionsRequired) {
            // 需要满足所有条件（实时余额 AND 历史充值）
            shouldAddMember = realTimePass && historyPass;
        } else {
            // 满足任意一个条件即可（实时余额 OR 历史充值）
            shouldAddMember = realTimePass || historyPass;
        }
        return shouldAddMember;
    }

    /**
     * 处理统计结果类
     */
    private static class ProcessStatistics {
        private final int processedCount;
        private final int passedCount;
        
        public ProcessStatistics(int processedCount, int passedCount) {
            this.processedCount = processedCount;
            this.passedCount = passedCount;
        }

        public int getProcessedCount() {
            return processedCount;
        }

        public int getPassedCount() {
            return passedCount;
        }
    }

    /**
     * 历史充值数据过滤
     *
     * @param baseInfo              会员标签基础信息
     * @param labelRechargeInfoList 当前会员卡历史充值信息
     * @return 是否满足条件
     */
    private boolean historyAmountFilter(HsaLabelSettingBaseInfo baseInfo, List<LabelRechargeInfo> labelRechargeInfoList) {
        //筛选充值消费时间
        List<LabelRechargeInfo> labelRechargeList = labelRechargeInfoList.stream()
                .filter(x -> Objects.nonNull(x) && Objects.nonNull(x.getConsumptionTime())).collect(Collectors.toList());
        
        // 如果没有有效的充值消费记录，但有会员卡信息，则基于会员卡当前余额来判断
        if (CollUtil.isEmpty(labelRechargeList)) {
            return handleEmptyRechargeRecords(baseInfo, labelRechargeInfoList);
        }

        // 有有效充值记录的情况下，按原有逻辑处理
        // 累计充值金额条件过滤
        Boolean rechargeAmount = rechargeAmountFilter(baseInfo, labelRechargeList);
        if (Boolean.FALSE.equals(rechargeAmount)) {
            return true;
        }
        // 累计充值次数条件过滤
        Boolean cumulativeNum = cumulativeNumFilter(baseInfo, labelRechargeList);
        if (Boolean.FALSE.equals(cumulativeNum)) {
            return true;
        }
        // 平均充值金额条件过滤
        Boolean averageType = averageTypeFilter(baseInfo, labelRechargeList);
        
        // 只在调试模式下打印详细信息
        if (log.isDebugEnabled()) {
            log.debug("历史充值数据过滤详情 - baseInfoGuid: {}, 充值记录数: {}, 累计充值金额: {}, 累计充值次数: {}, 平均充值金额: {}", 
                    baseInfo.getGuid(), labelRechargeList.size(), rechargeAmount, cumulativeNum, averageType);
        }
        
        return Boolean.FALSE.equals(averageType);
    }

    /**
     * 处理没有有效充值消费记录的情况
     * 基于会员卡当前余额来判断是否满足历史充值条件
     *
     * @param baseInfo 标签条件信息
     * @param labelRechargeInfoList 充值信息集合
     * @return 是否过滤掉（true=过滤掉, false=保留）
     */
    private boolean handleEmptyRechargeRecords(HsaLabelSettingBaseInfo baseInfo, List<LabelRechargeInfo> labelRechargeInfoList) {
        // 检查是否设置了累计充值金额条件
        Integer cumulativeType = baseInfo.getCumulativeRechargeType();
        BigDecimal minAmount = baseInfo.getMinCumulativeRechargeAmount();
        BigDecimal maxAmount = baseInfo.getMaxCumulativeRechargeAmount();
        
        boolean hasCumulativeAmountCondition = Objects.nonNull(cumulativeType) && 
                (Objects.nonNull(minAmount) || Objects.nonNull(maxAmount));
        
        if (hasCumulativeAmountCondition && CollUtil.isNotEmpty(labelRechargeInfoList)) {
            // 计算会员卡总余额（充值余额 + 赠送余额）
            BigDecimal totalCardAmount = labelRechargeInfoList.stream()
                    .map(r -> {
                        BigDecimal cardAmount = BigDecimalUtil.nonNullValue(r.getCardAmount());
                        BigDecimal giftAmount = BigDecimalUtil.nonNullValue(r.getCardGiftAmount());
                        return cardAmount.add(giftAmount);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 基于总余额判断是否满足累计充值金额条件
            boolean amountConditionMet = testNumerical(minAmount, maxAmount, totalCardAmount);
            
            // 只在调试模式下打印详细信息
            if (log.isDebugEnabled()) {
                log.debug("基于会员卡余额判断累计充值条件 - 总余额: {}, 条件范围: {}-{}, 结果: {}", 
                        totalCardAmount, minAmount, maxAmount, amountConditionMet);
            }
            
            return !amountConditionMet; // true=过滤掉, false=保留
        } else {
            // 未设置累计充值金额条件或无会员卡信息，默认不过滤
            return false;
        }
    }

    /**
     * 实时余额（实时充值余额、实时赠送余额）条件过滤
     *
     * @param query            请求参数
     * @param rechargeInfoList 会员卡信息集合
     * @return 是否满足条件
     */
    private boolean realTimeAmountFilter(RequestLabelQuery query, List<HsaMemberInfoCard> rechargeInfoList) {
        //充值金额范围
        BigDecimal minRechargeAmount = query.getMinRechargeAmount();
        BigDecimal maxRechargeAmount = query.getMaxRechargeAmount();
        //赠送金额范围
        BigDecimal minGiftAmount = query.getMinGiftAmount();
        BigDecimal maxGiftAmount = query.getMaxGiftAmount();
        
        // 判断是否设置了充值金额条件
        boolean hasRechargeCondition = Objects.nonNull(minRechargeAmount) || Objects.nonNull(maxRechargeAmount);
        // 判断是否设置了赠送金额条件
        boolean hasGiftCondition = Objects.nonNull(minGiftAmount) || Objects.nonNull(maxGiftAmount);
        
        // 如果两个条件都没有设置，则不过滤
        if (!hasRechargeCondition && !hasGiftCondition) {
            return false;
        }
        
        boolean rechargeAmountMatch = false;
        boolean giftAmountMatch = false;
        
        // 实充值金额条件判断
        if (hasRechargeCondition) {
            BigDecimal cardAmount = rechargeInfoList.stream()
                    .map(r -> BigDecimalUtil.nonNullValue(r.getCardAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            rechargeAmountMatch = Boolean.TRUE.equals(testNumerical(minRechargeAmount, maxRechargeAmount, cardAmount));
        }
        
        // 赠送金额条件判断
        if (hasGiftCondition) {
            BigDecimal giftAmount = rechargeInfoList.stream()
                    .map(r -> BigDecimalUtil.nonNullValue(r.getGiftAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            giftAmountMatch = Boolean.TRUE.equals(testNumerical(minGiftAmount, maxGiftAmount, giftAmount));
        }
        
        // 只要满足其中任意一个条件就不过滤（返回false）
        // 如果只设置了充值条件，则只看充值条件
        // 如果只设置了赠送条件，则只看赠送条件
        // 如果两个条件都设置了，则满足任意一个即可
        if (hasRechargeCondition && hasGiftCondition) {
            // 两个条件都设置了，满足任意一个即可
            return !(rechargeAmountMatch || giftAmountMatch);
        } else if (hasRechargeCondition) {
            // 只设置了充值条件
            return !rechargeAmountMatch;
        } else {
            // 只设置了赠送条件
            return !giftAmountMatch;
        }
    }

    /**
     * 判断充值信息是否选中了（累计充值金额、累计充值次数、平均充值金额）
     *
     * @param baseInfo 标签条件信息
     * @return true or false
     */
    private Boolean isRecharge(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        //充值门店
        boolean checkedStore = isCheckedStore(baseInfo);
        //是否选中平均充值金额
        boolean checkedAvg = isCheckedAvg(baseInfo, query);
        //是否选中累计充值次数
        boolean checkedCount = isCheckedCount(baseInfo, query);
        //是否选中累计充值金额
        boolean checkedCumulative = isCheckedCumulative(baseInfo, query);
        //如果有一个充值条件选中了，那么为true,否则为false（主要用于判断是否需要查询会员充值记录）
        log.info("充值标签信息选中=======>：充值门店:{},是否选中平均充值金额:{},是否选中累计充值次数:{},是否选中累计充值金额:{}", checkedStore, checkedAvg, checkedCount, checkedCumulative);
        return checkedAvg || checkedCount || checkedCumulative || checkedStore;
    }

    private RechargeParamDTO timeTypeHandler(String rechargeTime, Integer timeType) {
        RechargeParamDTO rechargeParam = new RechargeParamDTO();
        //累计周期
        switch (Objects.requireNonNull(LabelConsumptionTypeEnum.getEnumByCode(timeType))) {
            case RECENTLY:
                rechargeParam.setDayNum(Integer.valueOf(rechargeTime));
                break;
            case FIXED_TIME:
                String[] fixedTime = rechargeTime.split(StringConstant.COMMA);
                rechargeParam.setFixedTime(fixedTime);
                break;
            default:
                break;
        }
        return rechargeParam;
    }

    /**
     * 是否选中充值门店条件
     *
     * @param baseInfo 标签条件信息
     * @return true or false
     */
    private boolean isCheckedStore(HsaLabelSettingBaseInfo baseInfo) {
        //充值门店
        String rechargeStore = baseInfo.getRechargeStore();
        return !StringUtils.isEmpty(rechargeStore);
    }

    /**
     * 是否选中平均充值金额
     *
     * @param baseInfo 标签条件信息
     * @return true or false
     */
    private boolean isCheckedAvg(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        //平均充值金额时间类型
        Integer averageType = baseInfo.getAverageRechargeType();
        //最小平均充值金额
        BigDecimal minAvgAmount = baseInfo.getMinAverageRechargeAmount();
        //最大平均充值金额
        BigDecimal maxAvgAmount = baseInfo.getMaxAverageRechargeAmount();
        //如果都为空说明:未选中平均充值金额条件
        if (Objects.isNull(averageType) && Objects.isNull(minAvgAmount) && Objects.isNull(maxAvgAmount)) {
            return false;
        }
        boolean checkedAvg = !Objects.isNull(averageType) && (!Objects.isNull(minAvgAmount) || !Objects.isNull(maxAvgAmount));
        //选中了平均充值金额过滤条件
        if (checkedAvg) {
            String averageTime = baseInfo.getAverageRechargeTime();
            RechargeParamDTO rechargeParam = timeTypeHandler(averageTime, averageType);
            query.setAverageRechargeType(averageType);
            query.setAverageAmountDay(rechargeParam.getDayNum());
            query.setAverageRechargeFixedTime(rechargeParam.getFixedTime());
        }
        return checkedAvg;
    }

    /**
     * 是否选中累计充值次数
     *
     * @param baseInfo 标签条件信息
     * @return true or false
     */
    private boolean isCheckedCount(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        //累计充值次数时间类型
        Integer cumulativeNum = baseInfo.getCumulativeRechargeNumType();
        //最小累计充值次数
        Integer minNum = baseInfo.getMinCumulativeRechargeNum();
        //最大累计充值次数
        Integer maxNum = baseInfo.getMaxCumulativeRechargeNum();
        //如果都为空说明:未选中累计充值次数条件
        if (Objects.isNull(cumulativeNum) && Objects.isNull(minNum) && Objects.isNull(maxNum)) {
            return false;
        }
        boolean checkedCount = !Objects.isNull(cumulativeNum) && (!Objects.isNull(minNum) || !Objects.isNull(maxNum));
        //选中了累计充值次数过滤条件
        if (checkedCount) {
            String cumulativeNumTime = baseInfo.getCumulativeRechargeNumTime();
            RechargeParamDTO cumulativeNumParam = timeTypeHandler(cumulativeNumTime, cumulativeNum);
            query.setCumulativeRechargeNumType(cumulativeNum);
            query.setRechargeNumDay(cumulativeNumParam.getDayNum());
            query.setRechargeNumFixedTime(cumulativeNumParam.getFixedTime());
        }
        return checkedCount;
    }

    /**
     * 是否选中累计充值金额
     *
     * @param baseInfo 标签条件信息
     * @return true or false
     */
    private boolean isCheckedCumulative(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        //累计充值金额时间类型
        Integer cumulativeType = baseInfo.getCumulativeRechargeType();
        //最小累计充值金额
        BigDecimal minAmount = baseInfo.getMinCumulativeRechargeAmount();
        //最大累计充值金额
        BigDecimal maxAmount = baseInfo.getMaxCumulativeRechargeAmount();
        //如果都为空说明:未选中累计充值金额条件
        if (Objects.isNull(cumulativeType) && Objects.isNull(minAmount) && Objects.isNull(maxAmount)) {
            return false;
        }
        boolean checkedCumulative = !Objects.isNull(cumulativeType) && (!Objects.isNull(minAmount) || !Objects.isNull(maxAmount));
        //选中了累计充值金额过滤条件
        if (checkedCumulative) {
            String cumulativeAmountTime = baseInfo.getCumulativeRechargeTime();
            RechargeParamDTO cumulativeTypeParam = timeTypeHandler(cumulativeAmountTime, cumulativeType);
            query.setCumulativeRechargeType(cumulativeType);
            query.setRechargeAmountDay(cumulativeTypeParam.getDayNum());
            query.setRechargeAmountFixedTime(cumulativeTypeParam.getFixedTime());
        }
        return checkedCumulative;
    }

    /**
     * 区间条件过滤
     * 1：最小值和最大值同时存在：value值属于这个范围为 true;
     * 2: 只有最小值存在：value大于等于最小值为 true;
     * 3: 只有最大值存在：value小于等于最大值为 true;
     *
     * @param minScope 最小值
     * @param maxScope 最大值
     * @param value    比较值
     * @return 是否满足条件（true or false）
     */
    private Boolean testNumerical(BigDecimal minScope, BigDecimal maxScope, BigDecimal value) {
        if (Objects.isNull(minScope) && Objects.isNull(maxScope)) {
            return true;

        }
        //最小值和最大值同时存在：value值属于这个范围为 true
        if (Objects.nonNull(minScope) && Objects.nonNull(maxScope)) {
            return value.compareTo(minScope) >= 0 && value.compareTo(maxScope) <= 0;
        }
        //只有最小值存在：value大于等于最小值为 true
        if (Objects.nonNull(minScope)) {
            return value.compareTo(minScope) >= 0;
        }
        //只有最大值存在：value小于等于最大值为 true
        return value.compareTo(maxScope) <= 0;
    }

    /**
     * 封装累计充值金额的过滤参数
     *
     * @param baseInfo         标签条件信息
     * @param rechargeInfoList 充值信息集合
     * @return 是否满足过滤条件
     */
    private Boolean rechargeAmountFilter(HsaLabelSettingBaseInfo baseInfo, List<LabelRechargeInfo> rechargeInfoList) {
        //累计充值金额时间类型
        Integer cumulativeType = baseInfo.getCumulativeRechargeType();
        //最小累计充值金额
        BigDecimal minAmount = baseInfo.getMinCumulativeRechargeAmount();
        //最大累计充值金额
        BigDecimal maxAmount = baseInfo.getMaxCumulativeRechargeAmount();
        log.info("累计充值条件参数 - 时间类型: {}, 最小金额: {}, 最大金额: {}",
                cumulativeType, minAmount, maxAmount);
        //如果都为空说明:未选中累计充值金额条件
        if (Objects.isNull(cumulativeType) && Objects.isNull(minAmount) && Objects.isNull(maxAmount)) {
            log.info("所有充值金额条件参数均为空，视为未设置该条件，直接返回通过");
            return true;
        }
        if (Objects.isNull(cumulativeType) || (Objects.isNull(minAmount) && Objects.isNull(maxAmount))) {
            log.warn("充值金额条件参数不完整 - 时间类型为空或金额范围为空，视为条件不成立，返回通过");
            return true;
        }
        return isConditions(minAmount, maxAmount, rechargeInfoList, LabelRechargeTypeEnum.RECHARGE_AMOUNT.getCode());
    }

    /**
     * 封装累计次数的过滤参数
     *
     * @param baseInfo         标签条件信息
     * @param rechargeInfoList 充值信息集合
     * @return 是否满足过滤条件
     */
    private Boolean cumulativeNumFilter(HsaLabelSettingBaseInfo baseInfo, List<LabelRechargeInfo> rechargeInfoList) {
        //累计充值次数时间类型
        Integer cumulativeNum = baseInfo.getCumulativeRechargeNumType();
        //最小累计充值次数
        Integer minNum = baseInfo.getMinCumulativeRechargeNum();
        //最大累计充值次数
        Integer maxNum = baseInfo.getMaxCumulativeRechargeNum();
        //累计充值金额次数时间
        String cumulativeRechargeNumTime = baseInfo.getCumulativeRechargeNumTime();
        log.info("累计次数的过滤参数: 累计充值次数时间类型={}, 累计充值次数={}, 最大累计充值次数={}, cumulativeRechargeNumTime={}", cumulativeNum, minNum, maxNum, cumulativeRechargeNumTime);
        //如果都为空说明:未选中累计充值次数条件
        if (Objects.isNull(cumulativeNum) && Objects.isNull(minNum) && Objects.isNull(maxNum)) {
            return true;
        }
        if (Objects.isNull(cumulativeNum) || (Objects.isNull(minNum) && Objects.isNull(maxNum))) {
            return true;
        }
        BigDecimal minNumVo = Objects.isNull(minNum) ? null : BigDecimal.valueOf(minNum);
        BigDecimal maxNumVo = Objects.isNull(maxNum) ? null : BigDecimal.valueOf(maxNum);
        return isConditions(minNumVo, maxNumVo, rechargeInfoList, LabelRechargeTypeEnum.RECHARGE_NUM.getCode());
    }

    /**
     * 封装平均累计金额的过滤参数
     *
     * @param baseInfo         标签条件信息
     * @param rechargeInfoList 充值信息集合
     * @return 是否满足过滤条件
     */
    private Boolean averageTypeFilter(HsaLabelSettingBaseInfo baseInfo, List<LabelRechargeInfo> rechargeInfoList) {
        //平均充值金额时间类型
        Integer averageType = baseInfo.getAverageRechargeType();
        //最小平均充值金额
        BigDecimal minAvgAmount = baseInfo.getMinAverageRechargeAmount();
        //最大平均充值金额
        BigDecimal maxAvgAmount = baseInfo.getMaxAverageRechargeAmount();
        log.info("平均累计金额的过滤参数: 平均充值金额时间类型={}, 最小平均充值金额={}, 最大平均充值金额={}", averageType, minAvgAmount, maxAvgAmount);
        //如果都为空说明:未选中平均充值金额条件
        if (Objects.isNull(averageType) && Objects.isNull(minAvgAmount) && Objects.isNull(maxAvgAmount)) {
            return true;
        }
        if (Objects.isNull(averageType) || (Objects.isNull(minAvgAmount) && Objects.isNull(maxAvgAmount))) {
            return true;
        }
        return isConditions(minAvgAmount, maxAvgAmount, rechargeInfoList, LabelRechargeTypeEnum.AVG_RECHARGE_AMOUNT.getCode());
    }

    /**
     * 是否满足条件
     *
     * @param minScope           最小范围
     * @param maxScope           最大范围
     * @param labelRechargeInfos 充值信息集合
     * @param type               type(1:累计充值金额 2：累计充值次数 3：平均充值金额)
     * @return 是否满足条件
     */
    private Boolean isConditions(BigDecimal minScope, BigDecimal maxScope, List<LabelRechargeInfo> labelRechargeInfos, int type) {
        if (CollUtil.isEmpty(labelRechargeInfos)) {
            return false;
        }
        //判断scopeValue是否在 minScope 和maxScope范围内
        BigDecimal scopeValue = BigDecimal.ZERO;
        //累计充值金额
        if (LabelRechargeTypeEnum.RECHARGE_AMOUNT.getCode() == type) {
            //实际充值余额
            BigDecimal amount = labelRechargeInfos.stream()
                    .map(r -> BigDecimalUtil.nonNullValue(r.getOrderPaidAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //充值赠送余额
            BigDecimal cardGiftAmount = labelRechargeInfos.stream()
                    .map(r -> BigDecimalUtil.nonNullValue(r.getCardGiftAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            scopeValue = amount.add(cardGiftAmount);
            //累计充值次数
        } else if (LabelRechargeTypeEnum.RECHARGE_NUM.getCode() == type) {
            long count = labelRechargeInfos.stream().map(LabelRechargeInfo::getOrderPaidAmount)
                    .filter(Objects::nonNull).count();
            scopeValue = BigDecimal.valueOf(count);
            //平均充值金额
        } else if (LabelRechargeTypeEnum.AVG_RECHARGE_AMOUNT.getCode() == type) {
            double average = labelRechargeInfos.stream()
                    .map(r -> BigDecimalUtil.nonNullValue(r.getOrderPaidAmount()))
                    .mapToDouble(BigDecimal::doubleValue).average().orElse(0D);
            scopeValue = BigDecimal.valueOf(average);
        }
        return testNumerical(minScope, maxScope, scopeValue);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveLabelMember(HsaLabelSetting label, Set<String> members, HeaderUserInfo headerUserInfo, Set<String> memberInfoGuids,
                                Integer triggerType) {
        RLock lock = redissonClient.getLock("SAVE_LABEL_MEMBER");
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_10, NumberConstant.NUMBER_10, TimeUnit.SECONDS)) {
                return;
            }
            List<HsaMemberLabel> memberLabels = new ArrayList<>();
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
            List<String> removeids = new ArrayList<>();
            List<String> addids = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            // 比较当前标签顾客是否在存在以及是否新增
            // 对传入顾客先判断是否在标签里,如果在,判断过滤后是否还在,如果不在了就删除此顾客
            // 如果不在标签里,过滤后还在,那么则追加
            List<LabelRelationTypeDTO> labels = hsaMemberLabelMapper.queryConnectionType(label.getGuid(),
                    headerUserInfo.getOperSubjectGuid(), memberInfoGuids);
            //标签下会员guid对应关联类型map
            Map<String, Integer> map = labels.stream().collect(Collectors.toMap(LabelRelationTypeDTO::getMemberInfoGuid,
                    LabelRelationTypeDTO::getConnectionType, (entity1, entity2) -> entity1));
            Map<String, CancelConnectionTypeDTO> connectionTypeMap;
            connectionTypeMap = getStringCancelConnectionTypeDTOMap(label, members, triggerType);
            forCheckMembers(members, map, connectionTypeMap, addids);
            //不是指定会员，需要移除不满足添加的关联
            addRemoveids(memberInfoGuids, map, removeids);
            if (!CollectionUtils.isEmpty(removeids)) {
                //取关记录
                getCancelMemberLabelList(label, memberLabelRecordDTOArrayList, removeids, headerUserInfo);
                hsaMemberLabelMapper.delete(new LambdaQueryWrapper<HsaMemberLabel>()
                        .eq(HsaMemberLabel::getLabelSettingGuid, label.getGuid())
                        .in(HsaMemberLabel::getOperationMemberInfoGuid, removeids));
                log.info("删除的标签个数{}", removeids.size());
            }
            dealMemberLabelRecord(label, headerUserInfo, addids, now, memberLabels, memberLabelRecordDTOArrayList);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info("标签关联失败，失败标签，{}", label.getLabelName());
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private static void addRemoveids(Set<String> memberInfoGuids, Map<String, Integer> map, List<String> removeids) {
        Set<Map.Entry<String, Integer>> entrySet = map.entrySet();
        for (Map.Entry<String, Integer> entry : entrySet) {
            String key = entry.getKey();
            if (map.get(key) == LabelConnectTypeEnum.AUTOMATION.getCode()) {
                //如果是指定会员,只移除指定会员
                if (!CollectionUtils.isEmpty(memberInfoGuids)) {
                    if (memberInfoGuids.contains(key)) {
                        removeids.add(key);
                    }
                } else {
                    removeids.add(key);
                }
            }
        }
    }

    private void dealMemberLabelRecord(HsaLabelSetting label, HeaderUserInfo headerUserInfo, List<String> addids, LocalDateTime now, List<HsaMemberLabel> memberLabels, List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList) {
        if (!CollectionUtils.isEmpty(addids)) {
            for (String memberInfoGuid : addids) {
                HsaMemberLabel hsmMemberLabel = new HsaMemberLabel()
                        .setOperSubjectGuid(label.getOperSubjectGuid())
                        .setLabelSettingGuid(ObjectUtil.objToString(label.getGuid()))
                        .setGuid(guidGeneratorUtil.getGuid(HsaMemberLabel.class.getSimpleName()).toString())
                        .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                        .setOperationMemberInfoGuid(memberInfoGuid)
                        .setGmtCreate(now)
                        .setGmtModified(now);
                memberLabels.add(hsmMemberLabel);
                //关联记录
                getMemberLabelRecord(label, memberLabelRecordDTOArrayList, now, hsmMemberLabel, headerUserInfo);
            }
            if (!CollectionUtils.isEmpty(memberLabels)) {
                log.info("标签:{},追加的人数为:{}", label.getLabelName(), addids.size());
                hsaMemberLabelMapper.batchSave(memberLabels);
                //保存关联记录
                hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
            }
        }
    }

    private static void forCheckMembers(Set<String> members, Map<String, Integer> map, Map<String, CancelConnectionTypeDTO> connectionTypeMap, List<String> addids) {
        for (String member : members) {
            if (Objects.isNull(map.get(member))) {
                //人工取关的 不添加
                if (Objects.nonNull(connectionTypeMap.get(member)) && Objects.nonNull(connectionTypeMap.get(member).
                        getCancelConnectionType()) && connectionTypeMap.get(member).getCancelConnectionType() ==
                        LabelCancelConnectTypeEnum.MANUAL.getCode()) {
                    continue;
                }
                addids.add(member);
            }
            map.remove(member);
        }
    }

    private Map<String, CancelConnectionTypeDTO> getStringCancelConnectionTypeDTOMap(HsaLabelSetting label, Set<String> members, Integer triggerType) {
        Map<String, CancelConnectionTypeDTO> connectionTypeMap;
        if (triggerType == LabelTriggerTypeEnum.REGISTER_INFO.getCode() || CollectionUtils.isEmpty(members)) {
            connectionTypeMap = new HashMap<>();
        } else if (members.size() == 1) {
            CancelConnectionTypeDTO cancelConnectionTypeDTO = hsaMemberLabelRecordMapper.findNewestCancelConnectionType(label.getGuid(),
                    members.iterator().next());
            connectionTypeMap = new HashMap<>();
            if (!Objects.isNull(cancelConnectionTypeDTO)) {
                connectionTypeMap.put(cancelConnectionTypeDTO.getMemberInfoGuid(), cancelConnectionTypeDTO);
            }
        } else {
            connectionTypeMap = hsaMemberLabelRecordMapper.findLastCancelConnectionType(label.getGuid()).stream().collect(Collectors.
                    toMap(CancelConnectionTypeDTO::getMemberInfoGuid, Function.identity(),
                            (entity1, entity2) -> entity1));
        }
        return connectionTypeMap;
    }

    private void getCancelMemberLabelList(HsaLabelSetting label, List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList, List<String> removeids, HeaderUserInfo headerUserInfo) {
        List<HsaMemberLabel> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .eq(HsaMemberLabel::getLabelSettingGuid, label.getGuid())
                .in(HsaMemberLabel::getOperationMemberInfoGuid, removeids));
        if (CollectionUtil.isNotEmpty(hsaMemberLabelList)) {
            hsaMemberLabelList.forEach(in -> {
                MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
                labelRecordQO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                        .setLabelSettingGuid(in.getLabelSettingGuid())
                        .setMemberInfoGuid(in.getOperationMemberInfoGuid())
                        .setIsConnection(ConnectionLabelTypeEnum.MANUAL.getCode())
                        .setMemberLabelGuid(in.getGuid())
                        .setCancelConnectionTime(LocalDateTime.now())
                        .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                        .setCancelConnectionType(LabelCancelConnectTypeEnum.AUTOMATION.getCode());
                memberLabelRecordDTOArrayList.add(labelRecordQO);
            });
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
        }
    }

    private void getMemberLabelRecord(HsaLabelSetting label, List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList, LocalDateTime now, HsaMemberLabel hsmMemberLabel, HeaderUserInfo headerUserInfo) {
        MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
        labelRecordQO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode())
                .setMemberLabelGuid(hsmMemberLabel.getGuid())
                .setConnectionTime(now)
                .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                .setMemberInfoGuid(hsmMemberLabel.getOperationMemberInfoGuid())
                .setLabelSettingGuid(hsmMemberLabel.getLabelSettingGuid())
                .setLabelType(label.getLabelType())
                .setLabelName(label.getLabelName());
        memberLabelRecordDTOArrayList.add(labelRecordQO);
    }

    /**
     * 动态sql条件设置
     *
     * @param query        查询条件
     * @param labelSetting 会员标签设置
     * @param baseInfo     会员标签基础信息
     */
    private void querySetting(RequestLabelQuery query, HsaLabelSetting labelSetting, HsaLabelSettingBaseInfo baseInfo,
                              HeaderUserInfo headerUserInfo) {
        BeanUtils.copyProperties(baseInfo, query);
        BeanUtils.copyProperties(labelSetting, query, "gmtCreate", "gmtModified");
        query.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        //基础
        setData(query, baseInfo);
        //消费
        setConsumption(query, baseInfo);
        //充值
        setRecharge(query, baseInfo);
        //成长值
        setMemberLevel(query, baseInfo);
        //积分
        setIntegral(query, baseInfo);
        appendRequestParam(query, baseInfo);
        setLabelAreaJson(baseInfo, query);
    }

    private static void setIntegral(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        query.setGetIntegralFixedDateJson(StringUtils.isEmpty(baseInfo.getGetIntegralFixedDateJson()) ? null :
                baseInfo.getGetIntegralFixedDateJson().split(COMMA));
        query.setConsumptionIntegralFixedDateJson(StringUtils.isEmpty(baseInfo.getConsumptionIntegralFixedDateJson()) ? null :
                baseInfo.getConsumptionIntegralFixedDateJson().split(COMMA));
    }

    private static void setMemberLevel(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        query.setCardMemberLevelGuid(StringUtils.isEmpty(baseInfo.getCardMemberLevelGuid()) ? null :
                baseInfo.getCardMemberLevelGuid().split(COMMA));
        query.setGrowValueFixedDateJson(StringUtils.isEmpty(baseInfo.getGrowValueFixedDateJson()) ? null :
                baseInfo.getGrowValueFixedDateJson().split(COMMA));
        query.setPaidMemberLevelGuid(StringUtils.isEmpty(baseInfo.getPaidMemberLevelGuid()) ? null :
                Arrays.asList(baseInfo.getPaidMemberLevelGuid().split(COMMA)));
    }

    private static void setConsumption(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        query.setConsumptionAmountFixedDateJson(StringUtils.isEmpty(baseInfo.getConsumptionAmountFixedDateJson()) ? null :
                baseInfo.getConsumptionAmountFixedDateJson().split(COMMA));
        query.setConsumptionCountFixedDateJson(StringUtils.isEmpty(baseInfo.getConsumptionCountFixedDateJson()) ? null :
                baseInfo.getConsumptionCountFixedDateJson().split(COMMA));
        query.setConsumptionAvgFixedDateJson(StringUtils.isEmpty(baseInfo.getConsumptionAvgFixedDateJson()) ? null :
                baseInfo.getConsumptionAvgFixedDateJson().split(COMMA));

        //消费门店
        if (StrUtil.isNotEmpty(baseInfo.getConsumptionStoreJson())) {
            query.setConsumptionStoreJson(JSON.parseArray(baseInfo.getConsumptionStoreJson(), StoreSimpleInfo.class));
        } else {
            query.setConsumptionStoreJson(null);
        }
    }

    private static void setRecharge(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        //充值门店
        if (StrUtil.isNotEmpty(baseInfo.getRechargeStore())) {
            query.setRechargeStores(JSON.parseArray(baseInfo.getRechargeStore(), StoreSimpleInfo.class));
        } else {
            query.setRechargeStores(null);
        }
    }

    private static void setData(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        query.setBirthdayRangeDateJson(StringUtils.isEmpty(baseInfo.getBirthdayRangeDateJson()) ? null :
                baseInfo.getBirthdayRangeDateJson().split(COMMA));
        query.setBirthdayRangeMonth(StringUtils.isEmpty(baseInfo.getBirthdayRangeMonth()) ? null :
                baseInfo.getBirthdayRangeMonth().split(COMMA));
        query.setCertificateType(StringUtils.isEmpty(baseInfo.getCertificateType()) ? null :
                baseInfo.getCertificateType().split(COMMA));
        query.setCardSourceType(StringUtils.isEmpty(baseInfo.getCardSourceType()) ? null :
                baseInfo.getCardSourceType().split(COMMA));
        //注册门店
        if (StrUtil.isNotEmpty(baseInfo.getRegisterStores())) {
            query.setRegisterStores(JSON.parseArray(baseInfo.getRegisterStores(), StoreSimpleInfo.class));
        } else {
            query.setRegisterStores(null);
        }

        query.setCardMemberLevelGuid(StringUtils.isEmpty(baseInfo.getCardMemberLevelGuid()) ? null :
                baseInfo.getCardMemberLevelGuid().split(COMMA));
        query.setWorkName(StringUtils.isEmpty(baseInfo.getWorkName()) ? null : baseInfo.getWorkName().split(COMMA));
        query.setDepartmentName(StringUtils.isEmpty(baseInfo.getDepartmentName()) ? null :
                baseInfo.getDepartmentName().split(COMMA));
        query.setSex(StringUtils.isEmpty(baseInfo.getSex()) ? null : baseInfo.getSex().split(COMMA));
        query.setLabelAges(JSON.parseObject(baseInfo.getAgeRange(), AgeRange.class));
        query.setRegisterFixedTimeRangeJson(StringUtils.isEmpty(baseInfo.getRegisterFixedTimeRangeJson()) ? null :
                baseInfo.getRegisterFixedTimeRangeJson().split(COMMA));
        query.setRegisterSourceType(StringUtils.isEmpty(baseInfo.getRegisterSourceType()) ? null :
                baseInfo.getRegisterSourceType().split(COMMA));
        query.setOwnedEquityCardJson(StringUtils.isEmpty(baseInfo.getOwnedEquityCardJson()) ? null :
                baseInfo.getOwnedEquityCardJson().split(COMMA));
        query.setEquityCardFixedTimeRangeJson(StringUtils.isEmpty(baseInfo.getEquityCardFixedTimeRangeJson()) ? null :
                baseInfo.getEquityCardFixedTimeRangeJson().split(COMMA));
        query.setEquityCardSourceType(StringUtils.isEmpty(baseInfo.getEquityCardSourceType()) ? null :
                baseInfo.getEquityCardSourceType().split(COMMA));
    }

    private void appendRequestParam(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        query.setIndustries(StringUtils.isEmpty(baseInfo.getIndustry()) ? null :
                baseInfo.getIndustry().split(COMMA));
        query.setJobTitles(StringUtils.isEmpty(baseInfo.getJobTitle()) ? null :
                baseInfo.getJobTitle().split(COMMA));
    }

    private void setLabelAreaJson(HsaLabelSettingBaseInfo baseInfo, RequestLabelQuery query) {
        if (StringUtils.isEmpty(baseInfo.getLabelAreaJson())) {
            return;
        }
        List<LabelAreaJson> labelAreaJson = null;
        try {
            labelAreaJson = JSONObject.parseArray(baseInfo.getLabelAreaJson(), LabelAreaJson.class);
        } catch (Exception e) {
            log.info("labelAreaJson==============>" + baseInfo.getLabelAreaJson());
        }
        if (CollectionUtils.isEmpty(labelAreaJson)) {
            return;
        }
        for (LabelAreaJson json : labelAreaJson) {
            String allName = "";
            if (!StringUtils.isEmpty(json.getProvinceName())) {
                allName += json.getProvinceName();
            }
            if (!StringUtils.isEmpty(json.getCityName())) {
                allName += COMMA + json.getCityName();
            }
            if (!StringUtils.isEmpty(json.getAreaName())) {
                allName += COMMA + json.getAreaName();
            }
            json.setAllName(allName);
        }
        query.setLabelAreaJson(labelAreaJson);
    }

    @Override
    public RequestOperationLabel getLabelDetails(String guid) {
        HsaLabelSetting labelSetting = this.getOne(new LambdaQueryWrapper<HsaLabelSetting>()
                        .eq(HsaLabelSetting::getGuid, guid)
                /* .eq(HsaLabelSetting::getLabelType,labelType) */);
        if (Objects.isNull(labelSetting)) {
            throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_NOT_LABEL);
        }

        HsaLabelSettingBaseInfo settingBaseInfo = hsaLabelSettingBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaLabelSettingBaseInfo>()
                .eq(HsaLabelSettingBaseInfo::getLabelSettingGuid, labelSetting.getGuid()));
        RequestOperationLabel detail = new RequestOperationLabel();
        if (Objects.isNull(settingBaseInfo)) {
            RequestLabelSetting requestLabelSetting = new RequestLabelSetting();
            BeanUtils.copyProperties(labelSetting, requestLabelSetting);
            detail.setRequestLabelSetting(requestLabelSetting);
            detail.setSettingGuid(labelSetting.getGuid());
            return detail;
        }

        RequestBaseInfo requestBaseInfo = new RequestBaseInfo();
        RequestLabelSetting requestLabelSetting = new RequestLabelSetting();
        RequestEquityCardInfo requestEquityCardInfo = new RequestEquityCardInfo();
        RequestRegisterInfo requestRegisterInfo = new RequestRegisterInfo();
        RequestRechargeInfo requestRechargeInfo = new RequestRechargeInfo();
        RequestGrowInfo requestGrowInfo = new RequestGrowInfo();
        RequestIntegralInfo requestIntegraInfo = new RequestIntegralInfo();
        RequestConsumptionInfo requestConsumptionInfo = new RequestConsumptionInfo();
        BeanUtils.copyProperties(settingBaseInfo, requestBaseInfo);
        BeanUtils.copyProperties(settingBaseInfo, requestEquityCardInfo);
        BeanUtils.copyProperties(settingBaseInfo, requestRegisterInfo);
        BeanUtils.copyProperties(labelSetting, requestLabelSetting);
        BeanUtils.copyProperties(settingBaseInfo, requestRechargeInfo);
        BeanUtils.copyProperties(settingBaseInfo, requestGrowInfo);
        BeanUtils.copyProperties(settingBaseInfo, requestConsumptionInfo);
        BeanUtils.copyProperties(settingBaseInfo, requestIntegraInfo);
        //---------------------基础信息--------------------
        setBaseInfo(requestBaseInfo, settingBaseInfo);

        //---------------------注册信息--------------------
        setRegisterInfo(requestRegisterInfo, settingBaseInfo);

        //---------------------身份信息--------------------
        setOwned(requestEquityCardInfo, settingBaseInfo);

        //---------------------消费--------------------
        setConsumption(requestConsumptionInfo, settingBaseInfo);

        //---------------------充值--------------------
        setRecharge(requestRechargeInfo, settingBaseInfo);

        //---------------------成长值--------------------
        setMemberLevel(requestGrowInfo, settingBaseInfo);

        //积分
        setIntegral(requestIntegraInfo, settingBaseInfo);

        appendRequestParam(requestBaseInfo, settingBaseInfo);

        // 年龄范围单独处理 转换解析
        AgeRange ageRange = JSON.parseObject(settingBaseInfo.getAgeRange(), AgeRange.class);
        requestBaseInfo.setAgeRange(ageRange);
        detail.setSettingGuid(labelSetting.getGuid());
        detail.setSettingInfoGuid(settingBaseInfo.getGuid());
        detail.setRequestBaseInfo(requestBaseInfo);
        detail.setRequestRegisterInfo(requestRegisterInfo);
        detail.setRequestLabelSetting(requestLabelSetting);
        detail.setRequestEquityCardInfo(requestEquityCardInfo);
        detail.setRequestRechargeInfo(requestRechargeInfo);
        detail.setRequestConsumptionInfo(requestConsumptionInfo);
        detail.setRequestGrowInfo(requestGrowInfo);
        detail.setRequestIntegraInfo(requestIntegraInfo);
        return detail;
    }

    private static void setIntegral(RequestIntegralInfo requestIntegraInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestIntegraInfo.setGetIntegralFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getGetIntegralFixedDateJson()) ? null :
                settingBaseInfo.getGetIntegralFixedDateJson().split(COMMA));
        requestIntegraInfo.setConsumptionIntegralFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getConsumptionIntegralFixedDateJson())
                ? null : settingBaseInfo.getConsumptionIntegralFixedDateJson().split(COMMA));
    }

    private static void setMemberLevel(RequestGrowInfo requestGrowInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestGrowInfo.setCardMemberLevelGuid(StringUtils.isEmpty(settingBaseInfo.getCardMemberLevelGuid()) ? null :
                settingBaseInfo.getCardMemberLevelGuid().split(COMMA));
        requestGrowInfo.setGrowValueFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getGrowValueFixedDateJson()) ? null :
                settingBaseInfo.getGrowValueFixedDateJson().split(COMMA));
        // 新增成长信息字段
        requestGrowInfo.setPaidMemberLevelGuid(StringUtils.isEmpty(settingBaseInfo.getPaidMemberLevelGuid()) ? null :
                Arrays.asList(settingBaseInfo.getPaidMemberLevelGuid().split(COMMA)));
    }

    private static void setConsumption(RequestConsumptionInfo requestConsumptionInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestConsumptionInfo.setConsumptionAmountFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getConsumptionAmountFixedDateJson()) ? null :
                settingBaseInfo.getConsumptionAmountFixedDateJson().split(COMMA));
        requestConsumptionInfo.setConsumptionCountFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getConsumptionCountFixedDateJson()) ? null :
                settingBaseInfo.getConsumptionCountFixedDateJson().split(COMMA));
        requestConsumptionInfo.setConsumptionAvgFixedDateJson(StringUtils.isEmpty(settingBaseInfo.getConsumptionAvgFixedDateJson()) ? null :
                settingBaseInfo.getConsumptionAvgFixedDateJson().split(COMMA));
        //消费门店
        if (StrUtil.isNotEmpty(settingBaseInfo.getConsumptionStoreJson())){
            requestConsumptionInfo.setConsumptionStoreJson(JSONObject.parseArray(settingBaseInfo.getConsumptionStoreJson(), StoreSimpleInfo.class));
        }
    }

    private static void setRecharge(RequestRechargeInfo requestRechargeInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        //充值门店
        if (StrUtil.isNotEmpty(settingBaseInfo.getRechargeStore())){
            requestRechargeInfo.setRechargeStore(JSONObject.parseArray(settingBaseInfo.getRechargeStore(), StoreSimpleInfo.class));
        }
    }

    private static void setOwned(RequestEquityCardInfo requestEquityCardInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestEquityCardInfo.setOwnedEquityCard(StringUtils.isEmpty(settingBaseInfo.getOwnedEquityCardJson()) ? null :
                settingBaseInfo.getOwnedEquityCardJson().split(COMMA));
        requestEquityCardInfo.setEquityCardFixedTimeRangeJson(StringUtils.isEmpty(settingBaseInfo.getEquityCardFixedTimeRangeJson()) ? null :
                settingBaseInfo.getEquityCardFixedTimeRangeJson().split(COMMA));
        requestEquityCardInfo.setEquityCardSourceType(StringUtils.isEmpty(settingBaseInfo.getEquityCardSourceType()) ? null :
                settingBaseInfo.getEquityCardSourceType().split(COMMA));
    }

    private static void setRegisterInfo(RequestRegisterInfo requestRegisterInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestRegisterInfo.setRegisterFixedTimeRangeJson(StringUtils.isEmpty(settingBaseInfo.getRegisterFixedTimeRangeJson()) ? null :
                settingBaseInfo.getRegisterFixedTimeRangeJson().split(COMMA));
        requestRegisterInfo.setRegisterSourceType(StringUtils.isEmpty(settingBaseInfo.getRegisterSourceType()) ? null :
                settingBaseInfo.getRegisterSourceType().split(COMMA));
        requestRegisterInfo.setIndustries(StringUtils.isEmpty(settingBaseInfo.getIndustry()) ? null :
                settingBaseInfo.getIndustry().split(COMMA));
        if (StrUtil.isNotEmpty(settingBaseInfo.getRegisterStores())){
            requestRegisterInfo.setRegisterStores(JSONObject.parseArray(settingBaseInfo.getRegisterStores(), StoreSimpleInfo.class));
        }
    }

    private static void setBaseInfo(RequestBaseInfo requestBaseInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestBaseInfo.setSex(StringUtils.isEmpty(settingBaseInfo.getSex()) ? null : settingBaseInfo.getSex().split(COMMA));
        requestBaseInfo.setBirthdayRangeDateJson(StringUtils.isEmpty(settingBaseInfo.getBirthdayRangeDateJson()) ? null :
                settingBaseInfo.getBirthdayRangeDateJson().split(COMMA));
        requestBaseInfo.setBirthdayRangeMonth(StringUtils.isEmpty(settingBaseInfo.getBirthdayRangeMonth()) ? null :
                settingBaseInfo.getBirthdayRangeMonth().split(COMMA));
        if (!StringUtils.isEmpty(settingBaseInfo.getLabelAreaJson())) {
            requestBaseInfo.setLabelAreaJson(JSONObject.parseArray(settingBaseInfo.getLabelAreaJson(), LabelAreaJson.class));
        }
        requestBaseInfo.setWorkName(StringUtils.isEmpty(settingBaseInfo.getWorkName()) ? null :
                settingBaseInfo.getWorkName().split(COMMA));
        requestBaseInfo.setDepartmentName(StringUtils.isEmpty(settingBaseInfo.getDepartmentName()) ? null :
                settingBaseInfo.getDepartmentName().split(COMMA));
        requestBaseInfo.setCertificateType(StringUtils.isEmpty(settingBaseInfo.getCertificateType()) ? null :
                settingBaseInfo.getCertificateType().split(COMMA));
        if (StrUtil.isNotEmpty(settingBaseInfo.getDataItemJson())){
            requestBaseInfo.setCustomDataItems(JSONObject.parseArray(settingBaseInfo.getDataItemJson(), CustomDataItemWithValues.class));
        }
    }

    private void appendRequestParam(RequestBaseInfo requestBaseInfo, HsaLabelSettingBaseInfo settingBaseInfo) {
        requestBaseInfo.setJobTitles(StringUtils.isEmpty(settingBaseInfo.getJobTitle()) ? null :
                settingBaseInfo.getJobTitle().split(COMMA));
    }

    @Override
    public int countLabelMember(List<String> labelGuid) {
        Set<String> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                        .in(HsaMemberLabel::getLabelSettingGuid, labelGuid))
                .stream()
                .map(HsaMemberLabel::getOperationMemberInfoGuid)
                .collect(Collectors.toSet());
        return CollectionUtil.isNotEmpty(hsaMemberLabelList) ? hsaMemberLabelList.size() : 0;
    }

    @Override
    public List<HsaLabelSetting> getLabelSettingList(List<String> guids, String operSubjectGuid) {
        return hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                .eq(!StringUtils.isEmpty(operSubjectGuid), HsaLabelSetting::getOperSubjectGuid, operSubjectGuid)
                .in(HsaLabelSetting::getGuid, guids));
    }

    @Override
    public List<String> getLabelGuidByMember(String memberGuid) {
        List<HsaMemberLabel> hsaMemberLabels = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .eq(HsaMemberLabel::getOperationMemberInfoGuid, memberGuid));
        if (hsaMemberLabels.isEmpty()) {
            return Collections.emptyList();
        }
        return hsaMemberLabels.stream().map(HsaMemberLabel::getLabelSettingGuid).collect(Collectors.toList());
    }
}
