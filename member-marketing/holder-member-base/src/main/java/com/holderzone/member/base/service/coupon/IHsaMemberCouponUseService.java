package com.holderzone.member.base.service.coupon;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠券使用记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
public interface IHsaMemberCouponUseService extends IHolderBaseService<HsaMemberCouponUse> {

    /**
     * 锁定优惠券
     *
     * @param lockedDiscount 锁定优惠
     * @param memberCoupons  券
     */
    void lockedCoupon(SettlementOrderLockDTO lockedDiscount, List<HsaMemberCouponLink> memberCoupons);

    /**
     * 根据订单号查询
     *
     * @param operSubjectGuid 主体
     * @param orderNumber     订单号
     * @return
     */
    List<HsaMemberCouponUse> listByOrderNumber(String operSubjectGuid, String orderNumber);

    /**
     * 支付后更新
     *
     * @param operSubjectGuid 主体
     * @param orderNumber     订单
     * @param payTime         支付时间
     */
    void afterPayCoupon(String operSubjectGuid, String orderNumber, LocalDateTime payTime);

    /**
     * 锁定优惠券
     *
     * @param qo 参数
     */
    void markUse(CouponMarkUseQO qo);
}
