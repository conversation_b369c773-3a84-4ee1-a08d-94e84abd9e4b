package com.holderzone.member.base.event;



import com.holderzone.member.common.dto.event.GrowthValueChangeEvent;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @description 成长值流程事件定义
 * @date 2021/11/23 11:48
 */
@EnableBinding(BinderChannel.class)
public class GrowthValueEvent {

    @Resource
    private BinderChannel binderChannel;

    /**
     * 触发事件
     *
     * @param request request model
     */
    public boolean send(GrowthValueChangeEvent request) {
        Message<GrowthValueChangeEvent> build = MessageBuilder.withPayload(request).build();
        return binderChannel.outputChangeGrowthValue().send(build);
    }
}
