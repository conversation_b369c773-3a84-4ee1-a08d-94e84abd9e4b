package com.holderzone.member.base.mapper.integral;

import com.holderzone.member.base.entity.integral.HsaIntegralExtraAwardRule;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.integral.HsaIntegralExtraAwardRuleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: pantao
 */
public interface HsaIntegralExtraAwardRuleMapper extends HolderBaseMapper<HsaIntegralExtraAwardRule> {


    /**
     * 功能描述：根据任务guid查询额外奖励规则
     * @date 2021/11/24
     * @param taskGuid 任务guid
     * @return com.holderzone.member.common.dto.integral.HsaIntegralExtraAwardRuleDTO
     */
    List<HsaIntegralExtraAwardRuleDTO> listExtraAwardRule(@Param("taskGuid") String taskGuid);

    /**
     * 功能描述：查询额外奖励规则
     * @date 2021/11/24
     * @return java.util.List<com.holderzone.member.common.dto.integral.HsaIntegralExtraAwardRuleDTO>
     */
    List<HsaIntegralExtraAwardRuleDTO> listAllExtraAwardRule();

    /**
     * 功能描述：查询额外奖励规则根据任务id列表
     * @date 2021/11/24
     * @return java.util.List<com.holderzone.member.common.dto.integral.HsaIntegralExtraAwardRuleDTO>
     */
    List<HsaIntegralExtraAwardRuleDTO> listAllExtraAwardRuleByTask(@Param("collect")List<String> collect);
}
