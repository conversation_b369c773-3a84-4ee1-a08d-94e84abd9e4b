package com.holderzone.member.base.service.coupon;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;

import java.util.List;

/**
 * <p>
 * 会员券包发放 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface IHsaMemberCouponPackageLinkService extends IHolderBaseService<HsaMemberCouponPackageLink> {

    Boolean couponPackageReissue(List<String> reissueGuid);
}
