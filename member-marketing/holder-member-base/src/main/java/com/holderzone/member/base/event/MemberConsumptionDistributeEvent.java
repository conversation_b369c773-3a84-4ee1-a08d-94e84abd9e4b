package com.holderzone.member.base.event;

import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;

import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;

/**
 * 会员消费分销记录事件
 * <AUTHOR>
 * @version 1.0, 2025/6/4
 */
@EnableBinding (BinderChannel.class)
public class MemberConsumptionDistributeEvent {

	@Resource
	private BinderChannel binderChannel;

	/**
	 * 触发事件
	 *
	 * @param request request model
	 */
	public boolean send(SendMemberConsumptionDistributeEvent request) {
		Message<SendMemberConsumptionDistributeEvent> build = MessageBuilder.withPayload(request).build();
		return binderChannel.outputMemberConsumptionDistribute().send(build);
	}
}
