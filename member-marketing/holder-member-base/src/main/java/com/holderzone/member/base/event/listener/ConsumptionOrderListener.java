package com.holderzone.member.base.event.listener;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.event.domain.ConsumptionOrderEvent;
import com.holderzone.member.base.service.purchase.IHsaPurchaseOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 消费订单监听
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class ConsumptionOrderListener {

    private IHsaPurchaseOrderService purchaseService;

    @EventListener
    @Async
    public void onApplicationEvent(ConsumptionOrderEvent orderEvent) {
        log.info("base收到订单事件：{}", JacksonUtils.writeValueAsString(orderEvent));
        if (orderEvent == null || orderEvent.getEventType() == null || orderEvent.getContent() == null) {
            return;
        }
        switch (orderEvent.getEventType()) {
            // 订单创建
            case PURCHASE_ORDER_CREATE:
                purchaseService.saveByOrderCreateEvent(orderEvent.getContent());
                break;
            // 订单取消
            case PURCHASE_ORDER_CANCEL:
                purchaseService.updateByOrderCancelEvent(orderEvent.getContent());
                break;
            // 订单状态变更
            case PURCHASE_ORDER_STATE:
                purchaseService.updateByOrderStateEvent(orderEvent.getContent());
                break;
            default:
                log.error("未找到对应事件");
        }
    }
}
