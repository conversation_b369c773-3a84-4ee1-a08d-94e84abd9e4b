package com.holderzone.member.base.util;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.holderzone.member.base.entity.integral.HsaIntegralConsumeRule;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.integral.CalculateIntegralDeductQO;
import com.holderzone.member.common.qo.integral.IntegralGeneralRulesQO;
import com.holderzone.member.common.qo.integral.IntegralTaskQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
public class IntegralTaskCheckUtil {

    private IntegralTaskCheckUtil() {
    }

    /**
     * 积分注册参数校验
     *
     * @param integralTaskQO 积分任务请求参数
     */
    public static void baseParamCheck(IntegralTaskQO integralTaskQO) {
        Integer integralValue = integralTaskQO.getIntegralValue();
        if (Objects.isNull(integralValue)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_VALUE_NOT_FOUND);
        }
        Integer taskAction = integralTaskQO.getTaskAction();
        String[] sourceTypeJson = integralTaskQO.getSourceTypeJson();
        if (TaskActionEnum.REGISTER.getCode() == taskAction && (Objects.isNull(sourceTypeJson) || sourceTypeJson.length < 1)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_REGISTER_NOT_FOUND);
        }
        String[] personalDetailsTypeJson = integralTaskQO.getPersonalDetailsTypeJson();
        if (TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode() == taskAction && (Objects.isNull(personalDetailsTypeJson) || personalDetailsTypeJson.length < 1)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_INFO_TYPE_NOT_FOUND);
        }
    }


    /**
     * 积分消费参数校验
     *
     * @param integralTaskQO 积分任务请求参数
     */
    public static void consumptionParamCheck(IntegralTaskQO integralTaskQO) {
        BigDecimal amount = integralTaskQO.getAmount();
        if (Objects.isNull(amount)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_PERFECT_PARAM_AMOUNT_ERROR);
        }
        Integer integralValue = integralTaskQO.getIntegralValue();
        if (Objects.isNull(integralValue)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_VALUE_NOT_FOUND);
        }
        String[] applyBusinessJson = integralTaskQO.getApplyBusinessJson();
        if (Objects.isNull(applyBusinessJson) || applyBusinessJson.length < 1) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_APPLY_BUSINESS_ERROR);
        }
        Integer getCountType = integralTaskQO.getGetCountType();
        if (Objects.isNull(getCountType)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_RECHARGE_NUM_TYPE_ERROR);
        }
        if (GetCountTypeEnum.UN_LIMITED.getCode() == getCountType) {
            integralTaskQO.setLimitedNumber(null);
            integralTaskQO.setPeriodLimitedType(null);
        }
        if (GetCountTypeEnum.FIXED_LIMITED.getCode() == getCountType) {
            integralTaskQO.setPeriodLimitedType(null);
        }
    }

    public static void consumptionGoodsParamCheck(IntegralTaskQO integralTaskQO) {
        if (Objects.equals(ChooseGoodsTypeEnum.CONSUMPTION_APPOINT_GOODS.getCode(), integralTaskQO.getChooseGoodsType())
                && CollectionUtils.isEmpty(integralTaskQO.getGrowthCommodityBaseQOList())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_NO_COMMODITY_ERROR);
        }
    }

    public static void totalConsumptionParamCheck(IntegralTaskQO integralTaskQO) {
        if (StringUtils.isEmpty(integralTaskQO.getTotalPeriod())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_TOTAL_PERIOD_ERROR);
        }
    }

    /**
     * 积分充值参数校验
     *
     * @param integralTaskQO 积分任务请求参数
     */
    public static void rechargeParamCheck(IntegralTaskQO integralTaskQO) {
        BigDecimal amount = integralTaskQO.getAmount();
        if (Objects.isNull(amount)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_PERFECT_PARAM_AMOUNT_ERROR);
        }
        Integer integralValue = integralTaskQO.getIntegralValue();
        if (Objects.isNull(integralValue)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_VALUE_NOT_FOUND);
        }
        Integer getCountType = integralTaskQO.getGetCountType();
        if (Objects.isNull(getCountType)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_RECHARGE_NUM_TYPE_ERROR);
        }
        if (GetCountTypeEnum.UN_LIMITED.getCode() == getCountType) {
            integralTaskQO.setLimitedNumber(null);
            integralTaskQO.setPeriodLimitedType(null);
        }
        if (GetCountTypeEnum.FIXED_LIMITED.getCode() == getCountType) {
            integralTaskQO.setPeriodLimitedType(null);
        }

    }

    /**
     * 积分创建有效期参数校验
     *
     * @param integralTaskQO 积分任务请求参数
     */
    public static void timeParamCheck(IntegralTaskQO integralTaskQO) {
        Integer taskValidityType = integralTaskQO.getTaskValidityType();
        if (TaskValidityTypeEnum.PERMANENT_VALIDITY.getCode() == taskValidityType) {
            integralTaskQO.setStartFixedTaskValidityDate(null);
            integralTaskQO.setEndFixedTaskValidityDate(null);
        }

        Integer integralValidityType = integralTaskQO.getIntegralValidityType();
        if (GrowthValueValidityTypeEnum.DYNAMIC_VALIDITY.getCode() != integralValidityType) {
            integralTaskQO.setDynamicValidityType(null);
            integralTaskQO.setDynamicValidityNumber(null);
        }
        if (GrowthValueValidityTypeEnum.FIXED_VALIDITY.getCode() != integralValidityType) {
            integralTaskQO.setFixedIntegralValidityDate(null);
        }
    }

    /**
     * 参数校验
     * @param integralGeneralRulesQO 积分通用规则请求参数
     */
    public static void paramsValidation(IntegralGeneralRulesQO integralGeneralRulesQO){
        Integer isOverdueReminder = integralGeneralRulesQO.getIsOverdueReminder();
        if (BooleanEnum.TRUE.getCode() == isOverdueReminder && Objects.isNull(integralGeneralRulesQO.getOverdueDayNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_OVERDUE_DAY_NUM);
        }
        if (BooleanEnum.TRUE.getCode() == isOverdueReminder && CollUtil.isEmpty(integralGeneralRulesQO.getRemindTheWays())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_OVERDUE_WAYS_ERROR);
        }
        Integer isIntegralProtect = integralGeneralRulesQO.getIsIntegralProtect();
        if (BooleanEnum.TRUE.getCode() == isIntegralProtect && Objects.isNull(integralGeneralRulesQO.getIntegralProtectDayNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_PROTECT_DAY_NUM);
        }
        if (Objects.nonNull(integralGeneralRulesQO.getIsOverdueReminder()) &&
                integralGeneralRulesQO.getIsOverdueReminder() == BooleanEnum.FALSE.getCode()) {
            integralGeneralRulesQO.setRemindTheWays(null);
            integralGeneralRulesQO.setOverdueDayNum(null);
        }
        if (Objects.nonNull(integralGeneralRulesQO.getIsIntegralProtect()) &&
                integralGeneralRulesQO.getIsIntegralProtect() == BooleanEnum.FALSE.getCode()) {
            integralGeneralRulesQO.setIntegralProtectDayNum(null);
        }
    }

    /**
     * 校验整合
     *
     * @param qo qo
     * @return boolean
     */
    public static HsaIntegralConsumeRule getCheckHsaGradeEquities(CalculateIntegralDeductQO qo,
                                                            HsaIntegralConsumeRule hsaIntegralConsumeRule) {
        if (Objects.isNull(hsaIntegralConsumeRule) || hsaIntegralConsumeRule.getIsForNow() == BooleanEnum.FALSE.getCode()) {
            log.info("积分抵现不存在或未开启");
            return null;
        }
        if (isCheckApplyBusiness(hsaIntegralConsumeRule.getApplyBusinessJson(), qo.getBusiness())) {
            log.info("业务不适用：{}", qo.getBusiness());
            return null;
        }

        if (isCheckApplyBusiness(hsaIntegralConsumeRule.getTerminal(), qo.getTerminal())) {
            log.info("终端不适用：{}", qo.getTerminal());
            return null;
        }

        return hsaIntegralConsumeRule;
    }

    /**
     * 校验
     *
     * @param businessJson businessJson
     * @return boolean
     */
    private static boolean isCheckApplyBusiness(String businessJson, String business) {
        List<String> list = StringBaseHandlerUtil.StringConvertList(businessJson);
        return !list.contains(business);
    }
}
