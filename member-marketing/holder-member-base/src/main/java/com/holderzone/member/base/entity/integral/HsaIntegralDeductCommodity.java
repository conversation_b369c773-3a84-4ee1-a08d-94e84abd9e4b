package com.holderzone.member.base.entity.integral;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员积分抵现适用商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaIntegralDeductCommodity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 积分抵现guid
     */
    private String integralConsumeRuleGuid;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 单品1 固定2 可选3
     *
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    private Integer comboType;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 是否删除,0未删除,1已删除 2：删除但未生效
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 0 会员商城  1 门店业务 2 零售云POS
     */
    private Integer businessType;

    /**
     * 版本id
     */
    private String versionId;

}
