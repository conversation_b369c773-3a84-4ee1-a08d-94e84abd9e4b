package com.holderzone.member.base.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.base.entity.member.HsaMemberImportRecord;
import com.holderzone.member.base.mapper.member.HsaMemberImportRecordMapper;
import com.holderzone.member.base.service.member.HsaMemberImportRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.vo.member.MemberImportRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 会员导入记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Slf4j
@Service
public class HsaMemberImportRecordServiceImpl extends HolderBaseServiceImpl<HsaMemberImportRecordMapper, HsaMemberImportRecord> implements HsaMemberImportRecordService {

    @Resource
    private HsaMemberImportRecordMapper hsaMemberImportRecordMapper;

    @Override
    public com.holderzone.framework.util.Page<MemberImportRecordVO> getListMemberImportRecord(Long pageIndex, Long pageSize) {
        com.holderzone.framework.util.Page<MemberImportRecordVO> page = new com.holderzone.framework.util.Page<>();
        IPage<HsaMemberImportRecord> hsaMemberImportRecordIPage = hsaMemberImportRecordMapper.selectPage(new Page<>(pageIndex, pageSize), new LambdaQueryWrapper<HsaMemberImportRecord>()
                .eq(HsaMemberImportRecord::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .orderByDesc(HsaMemberImportRecord::getGmtCreate));
        if (!CollectionUtils.isEmpty(hsaMemberImportRecordIPage.getRecords())) {
            List<MemberImportRecordVO> memberImportRecordVOS = new ArrayList<>();
            hsaMemberImportRecordIPage.getRecords().forEach(in -> {
                MemberImportRecordVO memberImportRecordVO = new MemberImportRecordVO();
                BeanUtils.copyProperties(in, memberImportRecordVO);
                memberImportRecordVO.setGuid(in.getGuid() + "");
                memberImportRecordVO.setOperatorGuid(in.getOperatorGuid())
                        .setOperatorName(in.getOperatorName());
                memberImportRecordVOS.add(memberImportRecordVO);
            });
            page.setData(memberImportRecordVOS);
            page.setTotalCount(hsaMemberImportRecordIPage.getTotal());
            page.setCurrentPage(hsaMemberImportRecordIPage.getCurrent());
            page.setPageSize(hsaMemberImportRecordIPage.getSize());
        }
        return page;
    }

    @Override
    public Boolean updateDownloadNum(String guid) {
        HsaMemberImportRecord record = queryByGuid(guid);
        record.setDownloadNum(record.getDownloadNum() + 1);
        return updateByGuid(record);
    }
}
