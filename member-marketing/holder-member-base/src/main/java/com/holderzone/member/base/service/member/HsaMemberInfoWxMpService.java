package com.holderzone.member.base.service.member;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.member.HsaMemberInfoWxMp;

import java.util.List;

/**
 * <p>
 * 会员微信公众号关联表
 * </p>
 */
public interface HsaMemberInfoWxMpService extends IService<HsaMemberInfoWxMp> {

    void save(String appId, String unionId, String openId);

    String getOpenIdByAppIdAndUnionId(String appId, String unionId);

    List<HsaMemberInfoWxMp> getOpenIdByAppIdAndUnionIds(String appId, List<String> unionIds);

}
