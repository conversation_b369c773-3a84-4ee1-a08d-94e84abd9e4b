package com.holderzone.member.base.service.coupon.assembler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.ali.AlipayPassInstanceBatchAddResponseDTO;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.MemberCouponLinkDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedOrderInfoDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MemberCouponAssembler {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    private MemberCouponAssembler() {

    }

    public static HsaMemberCouponPackageLink updateMemberCouponPackageLink(HsaMemberCouponPackageLink hsaMemberCouponPackageLink,
                                                                           LocalDateTime now,
                                                                           Integer result,
                                                                           String reason) {
        hsaMemberCouponPackageLink.setReason(reason);
        hsaMemberCouponPackageLink.setState(result);
        hsaMemberCouponPackageLink.setGmtModified(now);
        return hsaMemberCouponPackageLink;
    }


    public static HsaMemberCouponUse toHsaMemberCouponUse(CouponMarkUseQO qo, String guid) {
        HsaMemberCouponUse hsaMemberCouponUse = new HsaMemberCouponUse();
        hsaMemberCouponUse.setGuid(guid);
        hsaMemberCouponUse.setOperSubjectGuid(qo.getOperSubjectGuid());
        hsaMemberCouponUse.setOrderNumber(qo.getOrderNumber());
        //优惠券guid
        hsaMemberCouponUse.setMemberCouponLinkGuid(qo.getCouponGuid());
        LocalDateTime now = LocalDateTime.now();
        //下单
        hsaMemberCouponUse.setLockTime(now);
        hsaMemberCouponUse.setPayTime(now);
        hsaMemberCouponUse.setStoreGuid(qo.getStoreGuid());
        hsaMemberCouponUse.setStoreName(qo.getStoreName());
        final HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        //来源
        hsaMemberCouponUse.setSource(userInfo.getSource());
        //操作人
        hsaMemberCouponUse.setOperatorAccountName(userInfo.getOperatorAccountName());
        //优惠金额 = 券面额
        hsaMemberCouponUse.setDiscountAmount(qo.getDiscountAmount());

        MemberCouponLinkDTO memberCouponLinkDTO = qo.getMemberCouponLinkDTO();
        hsaMemberCouponUse.setCouponName(memberCouponLinkDTO.getCouponName());
        hsaMemberCouponUse.setCouponCode(memberCouponLinkDTO.getCouponCode());
        hsaMemberCouponUse.setMemberPhone(memberCouponLinkDTO.getMemberPhone());
        hsaMemberCouponUse.setUserName(memberCouponLinkDTO.getUserName());
        hsaMemberCouponUse.setMemberGuid(memberCouponLinkDTO.getMemberGuid());
        hsaMemberCouponUse.setCode(memberCouponLinkDTO.getCode());
        hsaMemberCouponUse.setCouponPackageCode(memberCouponLinkDTO.getCouponPackageCode());
        hsaMemberCouponUse.setCouponPackageName(memberCouponLinkDTO.getCouponPackageName());
        return hsaMemberCouponUse;
    }

    public static void addRecordList(SettlementLockedOrderInfoDTO orderInfo,
                                     List<String> memberCouponGuids,
                                     Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap,
                                     List<String> guids,
                                     List<HsaMemberCouponUse> recordList,
                                     List<HsaMemberCouponLink> memberCoupons) {
        Map<String, HsaMemberCouponLink> hsaMemberCouponLinkMap = memberCoupons.stream()
                .collect(Collectors.toMap(HsaMemberCouponLink::getGuid, Function.identity(), (key1, key2) -> key1));

        for (int i = 0; i < memberCouponGuids.size(); i++) {
            final String memberCouponLinkGuid = memberCouponGuids.get(i);
            final String guid = guids.get(i);
            HsaMemberCouponUse hsaMemberCouponUse = new HsaMemberCouponUse();
            hsaMemberCouponUse.setOperSubjectGuid(orderInfo.getOperSubjectGuid());
            hsaMemberCouponUse.setOrderNumber(orderInfo.getOrderNumber());
            //优惠券guid
            hsaMemberCouponUse.setMemberCouponLinkGuid(memberCouponLinkGuid);
            //下单
            hsaMemberCouponUse.setLockTime(orderInfo.getOrderTime());
            hsaMemberCouponUse.setStoreGuid(orderInfo.getStoreGuid());
            hsaMemberCouponUse.setStoreName(orderInfo.getStoreName());
            hsaMemberCouponUse.setGuid(guid);
            //来源
            hsaMemberCouponUse.setSource(orderInfo.getSource());
            //优惠金额
            Optional.ofNullable(checkDiscountMap.get(memberCouponLinkGuid))
                    .ifPresent(req -> hsaMemberCouponUse.setDiscountAmount(req.getDiscountAmount()));
            //实付
            hsaMemberCouponUse.setOrderPaidAmount(orderInfo.getOrderPaidAmount());
            //操作人
            hsaMemberCouponUse.setOperatorAccountName(orderInfo.getOperatorAccountName());

            if (hsaMemberCouponLinkMap.containsKey(memberCouponLinkGuid)) {
                HsaMemberCouponLink hsaMemberCouponLink = hsaMemberCouponLinkMap.get(memberCouponLinkGuid);
                hsaMemberCouponUse.setCouponName(hsaMemberCouponLink.getCouponName());
                hsaMemberCouponUse.setCouponCode(hsaMemberCouponLink.getCouponCode());
                hsaMemberCouponUse.setMemberPhone(hsaMemberCouponLink.getMemberPhone());
                hsaMemberCouponUse.setUserName(hsaMemberCouponLink.getUserName());
                hsaMemberCouponUse.setMemberGuid(hsaMemberCouponLink.getMemberGuid());
                hsaMemberCouponUse.setCode(hsaMemberCouponLink.getCode());
                hsaMemberCouponUse.setCouponPackageCode(hsaMemberCouponLink.getCouponPackageCode());
                hsaMemberCouponUse.setCouponPackageName(hsaMemberCouponLink.getCouponPackageName());
            }
            recordList.add(hsaMemberCouponUse);
        }
    }

    public List<HsaMemberCouponLink> newMemberCouponLink(EditCouponPackageActivityVO activityVO,
                                                         MemberPhoneDTO memberPhoneDTO,
                                                         LocalDateTime now,
                                                         Map<String, String> codeMap,
                                                         Set<String> hsaMemberLabelSet) {

        List<HsaMemberCouponLink> hsaMemberCouponLinkList = Lists.newArrayList();
        for (ResponseCouponDTO responseCouponDTO : activityVO.getCouponGuidList()) {
            for (int i = 0; i < responseCouponDTO.getNum(); i++) {
                HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
                hsaMemberCouponLink.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCouponLink.class.getSimpleName()));
                hsaMemberCouponLink.setOperSubjectGuid(activityVO.getOperSubjectGuid());
                hsaMemberCouponLink.setCouponName(responseCouponDTO.getCouponName())
                        .setUserName(memberPhoneDTO.getUserName())
                        .setMemberGuid(memberPhoneDTO.getGuid())
                        .setMemberPhone(memberPhoneDTO.getPhoneNum())
                        .setSource(SourceTypeEnum.ADD_BACKGROUND.getCode())
                        .setReachTime(now)
                        .setCouponPackageType(CouponPackageTypeEnum.COUPON_REDEEM_ACTIVITY.getCode())
                        .setCouponPackageCode(activityVO.getActivityCode())
                        .setCouponPackageName(activityVO.getActivityName())
                        .setCode(createCouponCode(codeMap, hsaMemberCouponLink.getGuid()))
                        .setCouponCode(responseCouponDTO.getCouponCode())
                        .setCouponType(responseCouponDTO.getCouponType())
                        .setThresholdType(responseCouponDTO.getThresholdType())
                        .setThresholdAmount(responseCouponDTO.getThresholdAmount())
                        .setDiscountAmount(responseCouponDTO.getDiscountAmount())
                        .setRemark(responseCouponDTO.getRemark())
                        .setApplyDateLimited(responseCouponDTO.getApplyDateLimited())
                        .setApplyTimeLimitedType(responseCouponDTO.getApplyTimeLimitedType())
                        .setApplyTimeLimitedJson(responseCouponDTO.getApplyTimeLimitedJson())
                        .setApplyBusiness(responseCouponDTO.getApplyBusiness())
                        .setApplyTerminalJson(JSON.toJSONString(responseCouponDTO.getApplyTerminalList()))
                        .setApplicableAllStore(responseCouponDTO.getApplicableAllStore())
                        .setApplyCommodity(responseCouponDTO.getApplyCommodity())
                        .setDiscountAmountLimit(responseCouponDTO.getDiscountAmountLimit())
                        .setSingleOrderUsedLimit(responseCouponDTO.getSingleOrderUsedLimit())
                        .setExchangeLimit(responseCouponDTO.getExchangeLimit())
                        .setExchangeTimes(responseCouponDTO.getExchangeTimes());


                //todo 时间判断
                checkEffectiveTime(now, responseCouponDTO, hsaMemberCouponLink);

                if (CollUtil.isNotEmpty(responseCouponDTO.getApplyLabelGuidList())) {
                    hsaMemberCouponLink.setApplyLabelGuidJson(JSON.toJSONString(responseCouponDTO.getApplyLabelGuidList()));

                    hsaMemberLabelSet.addAll(responseCouponDTO.getApplyLabelGuidList());
                }

                if (CollUtil.isNotEmpty(responseCouponDTO.getApplyBusinessList())) {
                    hsaMemberCouponLink.setApplyBusinessJson(JSON.toJSONString(responseCouponDTO.getApplyBusinessList()));
                }

                //门店
                if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponStoreQOList())) {
                    hsaMemberCouponLink.setApplicableAllStoreJson(JSON.toJSONString(responseCouponDTO.getRequestCouponStoreQOList()));
                }

                //商品
                if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponCommodityQOList())) {
                    hsaMemberCouponLink.setApplyCommodityJson(JSON.toJSONString(responseCouponDTO.getRequestCouponCommodityQOList()));
                }
                hsaMemberCouponLinkList.add(hsaMemberCouponLink);
            }


        }

        return hsaMemberCouponLinkList;
    }

    private static void checkEffectiveTime(LocalDateTime now, ResponseCouponDTO responseCouponDTO, HsaMemberCouponLink hsaMemberCouponLink) {
        if (responseCouponDTO.getEffectiveType() == 0) {
            hsaMemberCouponLink.setCouponEffectiveStartTime(responseCouponDTO.getCouponEffectiveStartTime());
            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        } else if (responseCouponDTO.getEffectiveType() == 1) {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            if (responseCouponDTO.getEffectiveUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusHours(responseCouponDTO.getEffectiveValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusDays(responseCouponDTO.getEffectiveValue()));
            }

        } else {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        }

        //判断是否过期
        if (hsaMemberCouponLink.getCouponEffectiveEndTime().isAfter(now)) {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.UN_EXPIRE.getCode());
        } else {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.EXPIRE.getCode());
        }
    }


    /**
     * 生成编码
     *
     * @return String
     */
    public String createCouponCode(Map<String, String> codeMap, String guid) {
        String code = guid.substring(7, 19);
        if (codeMap.containsKey(code)) {
            code = NumberUtil.buildNumToStr(12);
        }
        codeMap.put(code, code);
        return code;
    }


}
