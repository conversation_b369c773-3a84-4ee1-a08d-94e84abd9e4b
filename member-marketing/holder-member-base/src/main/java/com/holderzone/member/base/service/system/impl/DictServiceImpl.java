package com.holderzone.member.base.service.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.system.HsaDict;
import com.holderzone.member.base.mapper.system.DictMapper;
import com.holderzone.member.base.service.system.DictService;
import com.holderzone.member.common.enums.DictEnum;
import com.holderzone.member.common.vo.system.DictVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 数据字典表 服务实现类
 * </p>
 */
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, HsaDict> implements DictService {

    @Override
    public Map<String, List<DictVO>> getDict(List<String> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Maps.newHashMap();
        }
        QueryWrapper<HsaDict> qw = new QueryWrapper<>();
        qw.lambda().in(HsaDict::getField, fields);
        List<HsaDict> list = list(qw);
        Map<String, List<DictVO>> resultMap = Maps.newHashMap();
        list.forEach(e -> resultMap.put(e.getField(), JacksonUtils.toObjectList(DictVO.class, e.getOptions())));
        return resultMap;
    }

    @Override
    public List<DictVO> getOtherServiceDict() {
        HsaDict dict = getByField(DictEnum.OTHER_SERVICE.getField());
        if (Objects.isNull(dict)) {
            return Lists.newArrayList();
        }
        return JacksonUtils.toObjectList(DictVO.class, dict.getOptions());
    }

    @Override
    public HsaDict getByField(String field) {
        QueryWrapper<HsaDict> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaDict::getField, field);
        return getOne(qw);
    }
}
