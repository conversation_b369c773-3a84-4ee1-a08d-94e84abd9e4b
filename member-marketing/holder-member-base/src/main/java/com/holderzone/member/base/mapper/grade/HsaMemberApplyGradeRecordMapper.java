package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaMemberApplyGradeRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.grade.GradeUpgradeApplyPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @program: member-marketing
 * @description: ${description}
 * @author: rw
 */
public interface HsaMemberApplyGradeRecordMapper extends HolderBaseMapper<HsaMemberApplyGradeRecord> {

    List<HsaMemberApplyGradeRecord> getMemberApplyGradeRecordPage(@Param("request") GradeUpgradeApplyPageVO gradVO);

    Integer getRecordCount(@Param("request") GradeUpgradeApplyPageVO gradVO);
}
