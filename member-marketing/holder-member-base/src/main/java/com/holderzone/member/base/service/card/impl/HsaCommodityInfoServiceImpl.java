package com.holderzone.member.base.service.card.impl;

import com.holderzone.member.base.entity.commodity.HsaCommodityInfo;
import com.holderzone.member.base.mapper.commodity.HsaCommodityInfoMapper;
import com.holderzone.member.base.service.commodity.HsaCommodityInfoService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-07-11 11:20
 */
@Slf4j
@Service
public class HsaCommodityInfoServiceImpl extends HolderBaseServiceImpl<HsaCommodityInfoMapper, HsaCommodityInfo>
        implements HsaCommodityInfoService {

}
