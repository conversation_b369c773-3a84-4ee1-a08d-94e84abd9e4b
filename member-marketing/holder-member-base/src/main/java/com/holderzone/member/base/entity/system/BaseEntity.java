package com.holderzone.member.base.entity.system;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @version 1.0
 * @description: 实体公共类
 * @date 2021/9/2 17:38
 */
@Data
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
