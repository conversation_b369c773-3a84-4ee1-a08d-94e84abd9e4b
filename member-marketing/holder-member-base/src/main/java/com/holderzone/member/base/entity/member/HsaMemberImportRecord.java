package com.holderzone.member.base.entity.member;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员导入记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberImportRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 表单名称
     */
    private String excelName;

    /**
     * 导入时间
     */
    private LocalDateTime importTime;

    /**
     * 导入成功数
     */
    private Integer importSucceedNum;

    /**
     * 导入失败数
     */
    private Integer importErrorNum;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人名字
     */
    private String operatorName;

    /**
     * 下载次数
     */
    private Integer downloadNum;

    private String excelAddress;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
