package com.holderzone.member.base.service.gift.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.member.HsaLabelSettingMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.gift.RechargeGiftActivityService;
import com.holderzone.member.base.transform.member.MemberInfoTransform;
import com.holderzone.member.base.transform.member.SubsidyLabelTransform;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.qo.gift.ActivityDetailQO;
import com.holderzone.member.common.vo.card.OperationMemberInfoVO;
import com.holderzone.member.common.vo.card.SubsidyLabelVO;
import com.holderzone.member.common.vo.gift.ActivityDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RechargeGiftActivityServiceImpl implements RechargeGiftActivityService {

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaLabelSettingMapper hsaLabelSettingMapper;

    public RechargeGiftActivityServiceImpl(HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper, HsaLabelSettingMapper hsaLabelSettingMapper) {
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.hsaLabelSettingMapper = hsaLabelSettingMapper;
    }

    @Override
    public ActivityDetailVO getRechargeGiftActivityBase(ActivityDetailQO activityDetailQO) {
        ActivityDetailVO activityDetailVO = new ActivityDetailVO();
        String memberInfoGuidJson = activityDetailQO.getMemberInfoGuidJson();
        if (StringUtils.isNotBlank(memberInfoGuidJson)) {
            List<String> memberInfoGuids = JSON.parseArray(memberInfoGuidJson, String.class);
            if (CollUtil.isNotEmpty(memberInfoGuids)) {
                List<HsaOperationMemberInfo> hsaOperationMemberInfos = hsaOperationMemberInfoMapper.selectList(
                        new LambdaQueryWrapper<HsaOperationMemberInfo>()
                                .in(HsaOperationMemberInfo::getGuid, memberInfoGuids));
                List<OperationMemberInfoVO> operationMemberInfoVOS = MemberInfoTransform.INSTANCE.memberInfosTOVO(hsaOperationMemberInfos);
                activityDetailVO.setMemberInfoGuidList(operationMemberInfoVOS);
            }
        }

        activityDetailVO.setLabelList(getLabelList(activityDetailQO.getLabelGuidJson()));
        activityDetailVO.setMarkingLabelList(getLabelList(activityDetailQO.getMarkingLabelGuidJson()));

        return activityDetailVO;
    }

    /**
     * 获取标签信息
     *
     */
    private List<SubsidyLabelVO> getLabelList(String labelJson) {
        List<SubsidyLabelVO> labelVOS = Lists.newArrayList();
        if (StringUtil.isNotEmpty(labelJson)) {
            List<String> labelGuidList = JSON.parseArray(labelJson, String.class);
            if (!CollUtil.isNotEmpty(labelGuidList)) {
                return labelVOS;
            }
            List<HsaLabelSetting> hsaLabelSettings = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                    .eq(HsaLabelSetting::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .in(HsaLabelSetting::getGuid, labelGuidList));
            labelVOS = SubsidyLabelTransform.INSTANCE.subsidyLabelTOVO(hsaLabelSettings);
        }
        return labelVOS;
    }
}
