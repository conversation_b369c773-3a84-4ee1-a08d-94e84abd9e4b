package com.holderzone.member.base.service.credit.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.base.entity.credit.HsaClearingStatement;
import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.base.entity.credit.HsaCreditOrderRecord;
import com.holderzone.member.base.entity.equities.HsaApplyDictionaries;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.credit.HasCancellationOrderMapper;
import com.holderzone.member.base.mapper.credit.HsaClearingStatementMapper;
import com.holderzone.member.base.mapper.credit.HsaCreditInfoMapper;
import com.holderzone.member.base.mapper.credit.HsaCreditOrderRecordMapper;
import com.holderzone.member.base.mapper.equities.HsaApplyDictionariesMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.credit.HsaCreditOrderRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.credit.ClearingStatusEnum;
import com.holderzone.member.common.enums.credit.PayStatusEnum;
import com.holderzone.member.common.enums.equities.ApplyModuleEnum;
import com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.credit.ClearingOrderQO;
import com.holderzone.member.common.qo.credit.CreditOrderRecordQO;
import com.holderzone.member.common.qo.credit.CreditRecordListQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.credit.*;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 挂账订单记录impl
 *
 * <AUTHOR>
 */
@Service
public class HsaCreditOrderRecordServiceImpl extends HolderBaseServiceImpl<HsaCreditOrderRecordMapper, HsaCreditOrderRecord>
        implements HsaCreditOrderRecordService {

    @Resource
    private HsaCreditInfoMapper hsaCreditInfoMapper;

    @Resource
    private HasCancellationOrderMapper hasCancellationOrderMapper;

    @Resource
    private HsaClearingStatementMapper hsaClearingStatementMapper;

    private final HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaApplyDictionariesMapper hsaApplyDictionariesMapper;

    public HsaCreditOrderRecordServiceImpl(HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper,
                                           HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper,
                                           HsaApplyDictionariesMapper hsaApplyDictionariesMapper) {
        this.hsaCreditOrderRecordMapper = hsaCreditOrderRecordMapper;
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.hsaApplyDictionariesMapper = hsaApplyDictionariesMapper;
    }


    @Override
    public PageResult queryListDetail(CreditRecordListQO request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<CreditRecordListVO> creditRecordList = hsaCreditOrderRecordMapper.queryListDetail(request);
        if (CollUtil.isEmpty(creditRecordList)) {
            return new PageResult();
        }
        return PageUtil.getPageResult(new PageInfo<>(creditRecordList));
    }

    @Override
    public PageResult queryCreditOrderRecords(CreditOrderRecordQO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<CreditOrderRecordVO> list;
        if (!StringUtils.isEmpty(request.getClearingStatementNumber())) {
            HsaClearingStatement hsaClearingStatement = hsaClearingStatementMapper
                    .selectOne(new LambdaQueryWrapper<HsaClearingStatement>()
                            .eq(HsaClearingStatement::getClearingStatementNumber, request.getClearingStatementNumber()));
            if (hsaClearingStatement.getPayStatus() == PayStatusEnum.CANCELLATION.getCode()) {
                list = hasCancellationOrderMapper.queryCreditOrderRecords(request);
                return PageUtil.getPageResult(new PageInfo<>(list));
            }
        }
        list = hsaCreditOrderRecordMapper.queryCreditOrderRecords(request);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public CreditOrderRecordTotalVO queryCreditOrderRecordTotal(String creditInfoGuid) {
        CreditOrderRecordTotalVO creditOrderRecordTotalVO = hsaCreditOrderRecordMapper.
                queryCreditOrderRecordTotal(creditInfoGuid, null);
        CreditOrderRecordTotalVO unPayeeRecord = hsaCreditOrderRecordMapper.queryCreditOrderRecordTotal(creditInfoGuid,
                ClearingStatusEnum.ALREADY_CLEARING.getCode());
        creditOrderRecordTotalVO.setUnPayeeAmount(unPayeeRecord.getTotalCreditAmount());
        creditOrderRecordTotalVO.setUnPayeeNumber(unPayeeRecord.getTotalCreditNumber());
        return creditOrderRecordTotalVO;
    }

    @Override
    public void exportListDetail(CreditRecordListQO request, HttpServletResponse response) throws IOException {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<CreditRecordListVO> creditRecordList = hsaCreditOrderRecordMapper.queryListDetail(request);
        if (creditRecordList.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        String phone = hsaOperationMemberInfoMapper.queryMemberPhone(request.getMemberInfoGuid());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDateTime = LocalDateTime.now().format(formatter);
        String fileName = phone + " 挂账明细" + formatDateTime;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());

        EasyExcel.write(response.getOutputStream(), ExportCreditRecordVO.class)
                .sheet("sheet1")
                .doWrite(toExcelDetailVOList(creditRecordList));
    }

    @Override
    public PageResult getClearingOrderList(ClearingOrderQO clearingOrderQO) {
        PageMethod.startPage(clearingOrderQO.getCurrentPage(),clearingOrderQO.getPageSize());
        List<ClearingStatementOrderVO> clearingOrderList = hsaCreditOrderRecordMapper
                .getClearingOrderList(clearingOrderQO.getCreditInfoGuid(), clearingOrderQO.getClearingStatementNumber());
        if (CollUtil.isEmpty(clearingOrderList)) {
            return new PageResult();
        }
        List<String> memberInfoGuids = clearingOrderList.stream()
                .map(ClearingStatementOrderVO::getMemberInfoGuid).distinct().collect(Collectors.toList());
        Map<String, HsaOperationMemberInfo> memberInfoMap = hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberInfoGuids))
                .stream().collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (obj, obj1) -> obj));

        for (ClearingStatementOrderVO statementOrderVO : clearingOrderList) {
            String memberInfoGuid = statementOrderVO.getMemberInfoGuid();
            if (!memberInfoMap.containsKey(memberInfoGuid)) {
                continue;
            }
            HsaOperationMemberInfo memberInfo = memberInfoMap.get(memberInfoGuid);
            statementOrderVO.setHeadImgUrl(memberInfo.getHeadImgUrl());
            statementOrderVO.setOperatorTelName(memberInfo.getNickName() + "-" + memberInfo.getPhoneNum());
        }
        return PageUtil.getPageResult(new PageInfo<>(clearingOrderList));
    }

    /**
     * 封装挂账订单记录列表
     *
     * @param creditRecordList 挂账订单记录列表Vo
     * @return 操作结果
     */
    private List<ExportCreditRecordVO> toExcelDetailVOList(List<CreditRecordListVO> creditRecordList) {
        List<ExportCreditRecordVO> exportCreditRecordList = Lists.newArrayList();
        if (CollUtil.isEmpty(creditRecordList)) {
            return exportCreditRecordList;
        }
        Map<String, String> applyBusinessMap = hsaApplyDictionariesMapper.selectList(
                new LambdaQueryWrapper<HsaApplyDictionaries>()
                        .eq(HsaApplyDictionaries::getModule, ApplyModuleEnum.APPLY_BUSINESS.getType()))
                .stream().collect(Collectors.toMap(HsaApplyDictionaries::getType, HsaApplyDictionaries::getTypeName, (obj, obj1) -> obj));
        for (CreditRecordListVO creditRecordListVO : creditRecordList) {
            ExportCreditRecordVO exportCreditRecord = paramConvert(creditRecordListVO,applyBusinessMap);
            exportCreditRecordList.add(exportCreditRecord);
        }
        return exportCreditRecordList;
    }

    /**
     * 参数转换
     *
     * @param creditRecordVO 挂账订单记录列表Vo
     * @return 操作结果
     */
    private ExportCreditRecordVO paramConvert(CreditRecordListVO creditRecordVO,Map<String, String> applyBusinessMap) {

        ExportCreditRecordVO exportCreditRecordListVO = new ExportCreditRecordVO();
        exportCreditRecordListVO.setCreditNumber(creditRecordVO.getCreditNumber());
        exportCreditRecordListVO.setCreditAccountName(creditRecordVO.getCreditAccountName());
        exportCreditRecordListVO.setGmtCreate(DateUtil.formatLocalDateTime(creditRecordVO.getGmtCreate()
                , DateUtil.PATTERN_DATETIME));
        exportCreditRecordListVO.setOrderNumber(creditRecordVO.getOrderNumber());

        String orderType = applyBusinessMap.get(String.valueOf(creditRecordVO.getOrderType()));
        exportCreditRecordListVO.setOrderType(orderType);
        exportCreditRecordListVO.setOrderStatus("-");
        if (Objects.nonNull(creditRecordVO.getOrderStatus())) {
            String orderStatus = creditRecordVO.getOrderStatus() == BooleanEnum.TRUE.getCode() ? "已取消" : "已结账";
            exportCreditRecordListVO.setOrderStatus(orderStatus);
        }
        exportCreditRecordListVO.setSaleStore(creditRecordVO.getSaleStore());
        exportCreditRecordListVO.setOrderPaidAmount(creditRecordVO.getOrderPaidAmount());
        exportCreditRecordListVO.setCreditAmount(creditRecordVO.getCreditAmount());
        String clearingStatus = ClearingStatusEnum.getDesByCode(creditRecordVO.getClearingStatus());
        exportCreditRecordListVO.setClearingStatus(clearingStatus);
        exportCreditRecordListVO.setClearingStatementNumber("-");
        if (!StringUtils.isEmpty(creditRecordVO.getClearingStatementNumber())) {
            exportCreditRecordListVO.setClearingStatementNumber(creditRecordVO.getClearingStatementNumber());
        }
        return exportCreditRecordListVO;
    }

    @SneakyThrows
    @Override
    public void exportCreditOrderRecord(CreditOrderRecordQO request, HttpServletResponse response) {
        List<CreditOrderRecordVO> list = hsaCreditOrderRecordMapper.queryCreditOrderRecords(request);
        if (CollUtil.isEmpty(list)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (list.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        HsaCreditInfo hsaCreditInfo = hsaCreditInfoMapper.queryByGuid(request.getCreditInfoGuid());
        String fileName = hsaCreditInfo.getCreditAccountName() + "挂账订单记录" + DateUtil.
                formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_SECOND);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExportCreditOrderRecordVO.class)
                .sheet("sheet1")
                .doWrite(toExportCreditOrderRecordVO(list));
    }

    /**
     * 封装导出挂账订单记录
     *
     * @param list 导出挂账订单记录记录
     * @return 操作结果
     */
    private List<ExportCreditOrderRecordVO> toExportCreditOrderRecordVO(List<CreditOrderRecordVO> list) {
        List<ExportCreditOrderRecordVO> exportCreditOrderRecordVOS = Lists.newArrayList();
        int index = 1;
        for (CreditOrderRecordVO creditOrderRecordVO : list) {
            ExportCreditOrderRecordVO exportCreditOrderRecordVO = new ExportCreditOrderRecordVO();
            exportCreditOrderRecordVO.setIndex(index);
            exportCreditOrderRecordVO.setGmtCreate(DateUtil.formatLocalDateTime(creditOrderRecordVO.getCreateTime()
                    , DateUtil.PATTERN_DATETIME));
            exportCreditOrderRecordVO.setOrderNumber(creditOrderRecordVO.getOrderNumber());
            exportCreditOrderRecordVO.setOrderType(ConsumptionOrderTypeEnum.getDesByCode(creditOrderRecordVO.getOrderType()));
            String orderStatus = creditOrderRecordVO.getOrderStatus() == BooleanEnum.TRUE.getCode() ? "已取消" : "已结账";
            exportCreditOrderRecordVO.setOrderStatus(orderStatus);
            exportCreditOrderRecordVO.setSaleStore(creditOrderRecordVO.getSaleStore());
            exportCreditOrderRecordVO.setOrderPaidAmount(creditOrderRecordVO.getOrderPaidAmount());
            exportCreditOrderRecordVO.setCreditAmount(creditOrderRecordVO.getCreditAmount());
            exportCreditOrderRecordVO.setCreditUsername(creditOrderRecordVO.getCreditUsername());
            exportCreditOrderRecordVO.setClearingStatus(ClearingStatusEnum.getDesByCode(creditOrderRecordVO.getClearingStatus()));
            exportCreditOrderRecordVO.setClearingStatementNumber(creditOrderRecordVO.getClearingStatementNumber());
            exportCreditOrderRecordVOS.add(exportCreditOrderRecordVO);
            index++;
        }
        return exportCreditOrderRecordVOS;
    }

    @Override
    @SneakyThrows
    public void exportClearingStatementDetail(CreditOrderRecordQO request, HttpServletResponse response) {
        List<CreditOrderRecordVO> list = hsaCreditOrderRecordMapper.queryCreditOrderRecords(request);
        if (CollUtil.isEmpty(list)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (list.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        //结算单号
        String clearingStatementNumber = list.get(0).getClearingStatementNumber();
        String fileName = "结算单" + clearingStatementNumber + "明细";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExportClearingStatementDetailVO.class)
                .sheet("sheet1")
                .doWrite(toExportClearingStatementDetailVO(list));
    }

    /**
     * 封装导出结算单明细vo
     *
     * @param list 导出结算单明细集合
     * @return 操作集合
     */
    private List<ExportClearingStatementDetailVO> toExportClearingStatementDetailVO(List<CreditOrderRecordVO> list) {
        List<ExportClearingStatementDetailVO> exportClearingStatementDetailVOList = Lists.newArrayList();
        int index = 1;
        for (CreditOrderRecordVO creditOrderRecordVO : list) {
            ExportClearingStatementDetailVO exportCreditOrderRecordVO = new ExportClearingStatementDetailVO();
            exportCreditOrderRecordVO.setIndex(index);
            exportCreditOrderRecordVO.setGmtCreate(DateUtil.formatLocalDateTime(creditOrderRecordVO.getCreateTime()
                    , DateUtil.PATTERN_DATETIME));
            exportCreditOrderRecordVO.setOrderNumber(creditOrderRecordVO.getOrderNumber());
            exportCreditOrderRecordVO.setOrderType(ConsumptionOrderTypeEnum.getDesByCode(creditOrderRecordVO.getOrderType()));
            exportCreditOrderRecordVO.setSaleStore(creditOrderRecordVO.getSaleStore());
            exportCreditOrderRecordVO.setOrderPaidAmount(creditOrderRecordVO.getOrderPaidAmount());
            exportCreditOrderRecordVO.setCreditAmount(creditOrderRecordVO.getCreditAmount());
            exportCreditOrderRecordVO.setCreditUsername(creditOrderRecordVO.getCreditUsername());
            exportClearingStatementDetailVOList.add(exportCreditOrderRecordVO);
            index++;
        }
        return exportClearingStatementDetailVOList;
    }

}
