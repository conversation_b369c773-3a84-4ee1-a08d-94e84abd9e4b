package com.holderzone.member.base.service.member;

import com.holderzone.member.base.entity.member.HsaMemberEducation;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.business.EducationalDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface HsaMemberEducationService extends IHolderBaseService<HsaMemberEducation> {
    /**
     * 编辑会员教育经历
     *
     * @param educationalDTO 编辑参数
     */
    void edit(EducationalDTO educationalDTO);

    /**
     * 批量保存或编辑
     *
     * @param educationalDTOList 保存或编辑参数
     */
    void batchHandleEducational(List<EducationalDTO> educationalDTOList, String memberGuid);

    void deleteByMemberGuid(String memberGuid);

    /**
     * 删除会员教育经历
     *
     * @param guid 教育经历guid
     */
    void deleteByGuid(String guid);

    /**
     * 添加会员教育经历
     *
     * @param educationalDTO 添加参数
     */
    void add(EducationalDTO educationalDTO);

    /**
     * 获取会员教育经历列表
     *
     * @param memberInfoGuid  会员guid
     * @param operSubjectGuid 主体guid
     * @return 教育经历
     */
    List<EducationalDTO> listByMemberGuid(String memberInfoGuid, String operSubjectGuid);

    /**
     * 根据学校查询会员guid
     * @param excludeMemberGuid 需要排除的会员guid
     * @param operSubjectGuid 主体guid
     * @param selfSchoolList 学校列表
     * @param memberGuidSet 包含的会员
     * @return 会员guid列表
     */
    Set<String> listMemberGuidBySchoolList(String excludeMemberGuid, String operSubjectGuid, Set<String> selfSchoolList,List<String> memberGuidSet);
}
