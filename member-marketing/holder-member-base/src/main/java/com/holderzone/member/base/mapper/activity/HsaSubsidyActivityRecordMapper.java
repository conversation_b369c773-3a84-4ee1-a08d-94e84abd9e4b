package com.holderzone.member.base.mapper.activity;


import com.holderzone.member.base.entity.activity.HsaSubsidyActivityRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.RequestSubsidyRecordQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员食堂卡补贴活动
 * @date 2021/10/26 9:42
 */
public interface HsaSubsidyActivityRecordMapper extends HolderBaseMapper<HsaSubsidyActivityRecord> {

    List<HsaSubsidyActivityRecord> subsidyRecordList(@Param("request") RequestSubsidyRecordQO request);

    void batchUpdateName(@Param("request") String request, @Param("name") String name);

    void batchUpdateDestroy(@Param("subsidyGuids") List<String> subsidyGuids,@Param("status") Integer status);

}
