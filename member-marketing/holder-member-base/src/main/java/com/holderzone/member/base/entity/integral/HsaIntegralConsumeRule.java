package com.holderzone.member.base.entity.integral;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.member.common.enums.equities.ApplyCommodityTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 积分消耗规则
 *
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class HsaIntegralConsumeRule implements Serializable {

    private static final long serialVersionUID = 1L;

    
    private Long id;
    
    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 积分抵现规则（0：默认关闭，1：开启）
     */
    private Integer isForNow;

    /**
     * 每多少积分 弃用
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer integralNum;

    /**
     * 抵扣金额 弃用
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal forNowMoney;

    /**
     * 积分抵现规则
     */
    private String integralNowMoneyJson;

    /**
     * 单笔订单金额不足 不能使用积分
     */
    private BigDecimal singleOrderMoney;

    /**
     * 单笔使用上限类型 0 不限制 1 上限金额 2 上限比例
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleOrderType;

    /**
     * 单笔使用上限
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal singleOrderValue;

    /**
     * 周期使用上限类型是否限制 0 限制 1 不限制
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer isLimit;

    /**
     * 周期使用上限类型  0：天 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodDiscountType;

    /**
     * 周期使用上限
     */
    private BigDecimal periodDiscountLimited;

    /**
     * 适用业务json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyBusinessJson;

    /**
     * 终端
     */
    @ApiModelProperty("终端")
    private String terminal;

    /**
     * 适用项目 10 订单商品金额 20 桌台费  30 配送费 40 运费 100 其他费用
     */
    @ApiModelProperty("适用项目")
    private String serviceableItem;

    /**
     * -1：全部商品适用 0：适用商品 1：不适用商品
     *
     * @see ApplyCommodityTypeEnum
     */
    private Integer applyGoodsType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 版本id
     */
    private String versionId;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;
    
}
