package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaCreditOrderRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.credit.CreditAmountDTO;
import com.holderzone.member.common.qo.credit.CreditRecordListQO;
import com.holderzone.member.common.vo.credit.ClearingStatementOrderVO;
import com.holderzone.member.common.vo.credit.CreditOrderRecordTotalVO;
import com.holderzone.member.common.vo.credit.CreditRecordListVO;
import com.holderzone.member.common.qo.credit.CreditInfoListQO;
import com.holderzone.member.common.qo.credit.CreditOrderRecordQO;
import com.holderzone.member.common.vo.credit.CreditOrderRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 挂账订单记录 mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HsaCreditOrderRecordMapper extends HolderBaseMapper<HsaCreditOrderRecord> {

    List<CreditOrderRecordVO> queryCreditOrderRecords(@Param("request") CreditOrderRecordQO request);

    BigDecimal getUserCreditOrderAmount(@Param("creditInfoGuid") String creditInfoGuid, @Param("memberGuid") List<String> memberGuid);

    BigDecimal queryRefundAmount(@Param("clearingStatementNumber") String clearingStatementNumber,
                                 @Param("recordGuids") List<String> recordGuids);

    /**
     * 查询挂账明细筛选列表
     *
     * @param request 筛选条件参数
     * @return 查询数据
     */
    List<CreditRecordListVO> queryListDetail(@Param("request") CreditRecordListQO request);

    void batchUpdateClearingStatementNumber(@Param("clearingStatementNumber") String clearingStatementNumber,
                                            @Param("recordIds") List<Long> recordIds);

    void updateClearingStatusAndClearingStatementNumber(@Param("clearingStatus") Integer clearingStatus,
                                      @Param("clearingStatementNumber") String clearingStatementNumber);

    void updateClearingStatus(@Param("clearingStatus") Integer clearingStatus,
                              @Param("clearingStatementNumber") String clearingStatementNumber);


    List<CreditAmountDTO> queryCreditAmount(@Param("clearingStatementNumber") String clearingStatementNumber);

    CreditOrderRecordTotalVO queryCreditOrderRecordTotal(@Param("creditInfoGuid") String creditInfoGuid,
                                                               @Param("clearingStatus") Integer clearingStatus);

    BigDecimal queryCreditUserOrderRecordTotal(@Param("creditUserGuid") String creditUserGuid);

    /**
     * 获取挂账结算订单列表
     * @param creditInfoGuid 挂账账户guid
     * @param clearingStatementNumber 结账单编号
     * @return 操作结果
     */
    List<ClearingStatementOrderVO> getClearingOrderList(@Param("creditInfoGuid") String creditInfoGuid,
                                                        @Param("clearingStatementNumber") String clearingStatementNumber);
}
