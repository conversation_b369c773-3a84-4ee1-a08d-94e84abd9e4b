package com.holderzone.member.base.entity.equities;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 权益初始化
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="HsaEquitiesInit对象", description="权益初始化")
public class HsaEquitiesInit extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "0 未初始化，1已初始化")
    private Integer isInit;


}
