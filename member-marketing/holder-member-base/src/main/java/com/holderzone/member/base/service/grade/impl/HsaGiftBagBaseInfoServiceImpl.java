package com.holderzone.member.base.service.grade.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.grade.HsaGiftBagBaseInfo;
import com.holderzone.member.base.mapper.grade.HsaGiftBagBaseInfoMapper;
import com.holderzone.member.base.service.grade.HsaGiftBagBaseInfoService;
import com.holderzone.member.base.transform.grade.GiftBagTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.grade.GiftBagEquitiesRuleEnum;
import com.holderzone.member.common.enums.grade.GiftBagEquitiesTypeEnum;
import com.holderzone.member.common.enums.grade.GradeMessageInfoEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.GiftBagBaseInfoQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.GiftBagBaseInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaGiftBagBaseInfoServiceImpl extends HolderBaseServiceImpl<HsaGiftBagBaseInfoMapper, HsaGiftBagBaseInfo> implements HsaGiftBagBaseInfoService {

    private final HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    public HsaGiftBagBaseInfoServiceImpl(HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper,
                                         GuidGeneratorUtil guidGeneratorUtil) {
        this.hsaGiftBagBaseInfoMapper = hsaGiftBagBaseInfoMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
    }

    @Override
    public void addGiftBag(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        List<HsaGiftBagBaseInfo> hsaGiftBagBaseInfos = hsaGiftBagBaseInfoMapper.selectList(
                new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                        .in(HsaGiftBagBaseInfo::getOperSubjectGuid, operSubjectGuids));
        if (CollUtil.isEmpty(hsaGiftBagBaseInfos)) {
            batchSaveGiftBag(operSubjectGuids);
        } else {
            //未初始化的运营主体集合
            List<String> operSubjectGuidList = hsaGiftBagBaseInfos.stream()
                    .map(HsaGiftBagBaseInfo::getOperSubjectGuid)
                    .filter(x -> !operSubjectGuids.contains(x)).distinct().collect(Collectors.toList());
            batchSaveGiftBag(operSubjectGuidList);
        }
    }


    /**
     * 批量保存升级礼包基础信息
     *
     * @param operSubjectGuids 运营主体集合
     */
    private void batchSaveGiftBag(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        operSubjectGuids = operSubjectGuids.stream().distinct().collect(Collectors.toList());
        List<HsaGiftBagBaseInfo> hsaGiftBagBaseInfoList = new ArrayList<>();
        for (String operSubjectGuid : operSubjectGuids) {
            HsaGiftBagBaseInfo hsaGiftBagBaseInfo = initializationSet(operSubjectGuid);
            hsaGiftBagBaseInfoList.add(hsaGiftBagBaseInfo);
        }
        this.saveBatch(hsaGiftBagBaseInfoList);
    }

    /**
     * 初始化升级礼包信息
     *
     * @return 初始化信息
     */
    private HsaGiftBagBaseInfo initializationSet(String operSubjectGuid) {
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        String guid = guidGeneratorUtil.getStringGuid(HsaGiftBagBaseInfo.class.getName());
        LocalDateTime now = LocalDateTime.now();
        hsaGiftBagBaseInfo.setGuid(guid);
        hsaGiftBagBaseInfo.setOperSubjectGuid(operSubjectGuid);
        hsaGiftBagBaseInfo.setEquitiesType(GiftBagEquitiesTypeEnum.UPGRADE_GIFT_BAG.getCode());
        hsaGiftBagBaseInfo.setEquitiesName(GradeMessageInfoEnum.GIFT_BAG_NAME.getDes());
        hsaGiftBagBaseInfo.setEquitiesDescribe(GradeMessageInfoEnum.GIFT_BAG_DESC.getDes());
        hsaGiftBagBaseInfo.setEquitiesRule(GiftBagEquitiesRuleEnum.GIVE_AWAY_REAL_TIME.getCode());
        hsaGiftBagBaseInfo.setOperatorName(StringConstant.ADMIN);
        hsaGiftBagBaseInfo.setGmtModified(now);
        hsaGiftBagBaseInfo.setGmtCreate(now);
        return hsaGiftBagBaseInfo;
    }

    @Override
    public boolean updateGiftBagInfo(GiftBagBaseInfoQO giftBagBaseInfo) {
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                        .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, giftBagBaseInfo.getOperSubjectGuid())
                        .eq(HsaGiftBagBaseInfo::getGuid, giftBagBaseInfo.getGuid()));
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.RESOURCE_UPDATE_ERROR);
        }
        HsaGiftBagBaseInfo updateGiftBagBaseInfo = GiftBagTransform.INSTANCE.giftBagQOToEntity(giftBagBaseInfo);
        updateGiftBagBaseInfo.setEquitiesIcon(JSON.toJSONString(giftBagBaseInfo.getEquitiesIcons()));
        updateGiftBagBaseInfo.setGmtModified(LocalDateTime.now());
        updateGiftBagBaseInfo.setOperatorName(ThreadLocalCache.getUserName());
        return this.updateByGuid(updateGiftBagBaseInfo);
    }

    @Override
    public GiftBagBaseInfoVO getGiftBagBaseInfo(String operSubjectGuid) {
        GiftBagBaseInfoVO giftBagBaseInfoVO = null;
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaGiftBagBaseInfo>().eq(HsaGiftBagBaseInfo::getOperSubjectGuid, operSubjectGuid));
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            log.error("运营主体：{} 未查询到升级礼包信息，请查看是否初始化成功", operSubjectGuid);
            throw new MemberBaseException(MemberAccountExceptionEnum.RESOURCE_NOT_EXIST);
        }
        giftBagBaseInfoVO = GiftBagTransform.INSTANCE.giftBagToVO(hsaGiftBagBaseInfo);
        giftBagBaseInfoVO.setEquitiesIcons(JSON.parseArray(hsaGiftBagBaseInfo.getEquitiesIcon(), String.class));
        return giftBagBaseInfoVO;
    }
}
