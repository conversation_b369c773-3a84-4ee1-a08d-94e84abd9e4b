package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.base.HsaBaseEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员消费分销记录表
 * <AUTHOR>
 * @version 1.0, 2025/6/4
 */
@Data
@EqualsAndHashCode (callSuper = false)
@Accessors (chain = true)
@TableName ("hsa_member_consumption_distribute")
public class HsaMemberConsumptionDistribute extends HsaBaseEntity implements Serializable {
    private static final long serialVersionUID = 3111070538580028382L;

    /**
     * 会员消费记录GUID
     */
    private String consumptionGuid;

    /**
     * 订单编号（外部订单编号）
     */
    private String orderNumber;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 操作人员账号名字/手机号
     */
    private String operatorTelName;

    /**
     * 分销记录类型-1:未退款（订单完成）1:整单退款，0:部分退款
     * @see com.holderzone.member.common.enums.member.DistributeTypeEnum
     */
    private Integer distributeRecordType;

    /**
     * 是否已经完成分销通知(0:未完成，1:完成，默认未完成)
     */
    private Integer isComplete;

    
}