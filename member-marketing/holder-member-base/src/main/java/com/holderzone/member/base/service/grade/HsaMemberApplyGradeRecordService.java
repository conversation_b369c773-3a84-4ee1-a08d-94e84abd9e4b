package com.holderzone.member.base.service.grade;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaMemberApplyGradeRecord;
import com.holderzone.member.common.dto.excel.MemberApplyGradeRecordExcelVO;
import com.holderzone.member.common.vo.grade.GradeUpgradeApplyPageDTO;
import com.holderzone.member.common.vo.grade.GradeUpgradeApplyPageVO;
import com.holderzone.member.common.vo.grade.GradeUpgradeApplyVO;
import com.holderzone.member.common.vo.grade.UpdateGradeApplyVO;

import java.util.List;


public interface HsaMemberApplyGradeRecordService extends IService<HsaMemberApplyGradeRecord> {

    /**
     * 申请开通等级
     *
     * @param gradeUpgradeApplyVO
     * @return
     */
    boolean memberGradeUpgradeApply(GradeUpgradeApplyVO gradeUpgradeApplyVO);

    /**
     * 分页
     *
     * @param gradeUpgradeApplyPageVO
     * @return
     */
    GradeUpgradeApplyPageDTO getGradeUpgradeApplyPage(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO);

    /**
     * 导出列表
     * @param gradeUpgradeApplyPageVO
     * @return
     */
    List<MemberApplyGradeRecordExcelVO> queryExportRecord(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO);

    /**
     * 审核状态修改
     *
     * @param updateGradeAp
     */
    boolean updateGradeUpgradeApply(UpdateGradeApplyVO updateGradeAp);


    /**
     * 待审核数量
     * @param gradeUpgradeApplyPageVO
     * @return
     */
    int getRecordCount(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO);
}
