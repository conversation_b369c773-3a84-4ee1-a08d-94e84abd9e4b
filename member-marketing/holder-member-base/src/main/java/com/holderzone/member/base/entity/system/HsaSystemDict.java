package com.holderzone.member.base.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统数据字典表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("hsa_system_dict")
@Accessors(chain = true)
public class HsaSystemDict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    /**
     * 系统类型
     *
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    @TableField("`system`")
    private Integer system;

    /**
     * 字段
     * 一个字段对应一个业务范围
     */
    private String field;

    /**
     * 字段名称
     */
    private String name;

    /**
     * 键
     */
    @TableField("`key`")
    private String key;

    /**
     * 值
     */
    private String value;

}
