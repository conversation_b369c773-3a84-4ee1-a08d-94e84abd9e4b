package com.holderzone.member.base.event;

import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.constant.StringConstant;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

public interface BinderChannel {

    /**
     * 发送消息通道定义
     *
     * @return MessageChannel
     */
    @Output(StringConstant.OUTPUT_SEND_MESSAGE)
    MessageChannel outputSendMessage();

    /**
     * 开通电子卡通道
     *
     * @return
     */
    @Output(StringConstant.OUTPUT_SEND_OPEN_ELECTRONIC_CARD)
    MessageChannel outputSendOpenElectronicCard();

    /**
     * 成长值流水记通道记录
     *
     * @return MessageChannel
     */
    @Output(StringConstant.OUTPUT_CHANGE_GROWTH_VALUE)
    MessageChannel outputChangeGrowthValue();

    /**
     * 会员等级变更
     *
     * @return MessageChannel
     */
    @Output(StringConstant.OUTPUT_MEMBER_GRADE_CHANGE)
    MessageChannel outputMemberGradeChange();

    /**
     * 会员消费分销记录
     *
     * @return MessageChannel
     */
    @Output(BinderConstant.OUTPUT_MEMBER_CONSUMPTION_DISTRIBUTE)
    MessageChannel outputMemberConsumptionDistribute();

}
