package com.holderzone.member.base.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.SecureUtil;
import com.google.common.collect.Lists;
import com.holderzone.member.base.dto.MemberInfoImportDTO;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaMemberCertificateInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.constant.ExcelImportConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.excel.HsmMemberUploadExcel;
import com.holderzone.member.common.dto.member.CertificateInfo;
import com.holderzone.member.common.enums.member.CertificateTypeEnum;
import com.holderzone.member.common.enums.member.EnableEnum;
import com.holderzone.member.common.enums.member.SexEnum;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.base.CountryVO;
import com.holderzone.member.common.vo.base.JobVO;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.*;

public class ExcelMemberUtil {

    private ExcelMemberUtil() {

    }

    /**
     * 英文分号 ;
     */
    private static final String SEMICOLON = ";";

    private static final String INITIAL_PASSWORD = "123456";

    // 会员或顾客初始化密码
    private static final String PAY_PASSWORD = SecureUtil.md5(INITIAL_PASSWORD).toUpperCase();

    private static final Integer ACCOUNT_LENGTH = 12;

    private static final Integer PASSWORD_LENGTH = 6;

    private static final String REG_NUM = "^[-+]?\\d*$";

    private static final String REG = "^(([1-9]\\d*)|(0))(\\.(\\d){0,2})?$";

    private static final String REGEX = "^[\\w-_.+]*[\\w-_.]@([\\w]+\\.)+\\w+\\w$";

    private static final String ID_CARD = "(^[0-9Xx]{15}$)|(^[0-9Xx]{18}$)";

    private static final String ARMYMAN = "^[\\u4E00-\\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$";

    // 定义中国护照号码的正则表达式
    private static final String CHINA_PASSPORT_REGEX = "^[GDESHWABCKLMNOPQRUTVXYZ]\\d{8}$";

    String idRegex = "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\\d{3}([0-9Xx])$";


    private static final String MACAU_PASS = "^([A-Z]\\d{6,10}(\\(\\w{1}\\))?)$";

    //会员最大充值金额
    private static final BigDecimal MAX_USER_AMOUNT = BigDecimal.valueOf(999999.99);

    private static final String INITIAL_GROWTH_VALUE_FORMAT_ERROR = "【初始成长值】格式错误";

    private static final String INITIAL_INTEGRAL_VALUE_FORMAT_ERROR = "【初始积分】格式错误";

    private static final String PREPAID_SUBSIDY_BALANCE_FORMAT_ERROR = "【预存补贴余额】填写错误";

    private static final String PREPAID_GIFT_BALANCE_FORMAT_ERROR = "【预存赠送余额】填写错误";

    private static final String PREPAID_CHARGE_BALANCE_FORMAT_ERROR = "【预存实充余额】填写错误";

    public static void memberExcelCheck(MemberInfoImportDTO importDTO, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        //区号校验
        checkExcelCountryCode(importDTO.getHsmMemberUploadExcel(), stringBuilder);

        //手机号校验
        checkExcelPhoneNum(stringBuilder, importDTO.getMemberPhoneMap(), importDTO.getHsmMemberUploadExcel(), importDTO.getCheckIphoneNumMap());

        //账户密码校验
        checkExcelPassword(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //值为1时是男性，值为2时是女性，值为0时是未知
        checkExcelSex(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //姓名
        checkExcelUserName(importDTO.getHsmMemberUploadExcel(), stringBuilder);

        //生日
        checkExcelBirthday(importDTO.getHsmMemberUploadExcel(), stringBuilder);

        //预存金额
        checkExcelMemberAccountMoney(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //预存赠送金额
        checkExcelMemberGiftAccountMoney(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //预存预存补贴余额
        checkExcelMemberSubsidyAmount(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //密码
        checkExcelPayPassword(importDTO.getHsmMemberUploadExcel(), hsaOperationMemberInfo, stringBuilder);

        //积分
        checkExcelMemberIntegral(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);

        //成长值
        checkExcelGrowthValue(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo);
    }

    private static void checkExcelGrowthValue(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        String growthValue = hsmMemberUploadExcel.getGrowthValue();
        if (StringUtils.hasText(growthValue)) {
            if (growthValue.trim().length() > 6 || !hsmMemberUploadExcel.getGrowthValue().matches(REG_NUM)) {
                stringBuilder.append(INITIAL_GROWTH_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
            } else {
                int integral = Integer.parseInt(growthValue);
                if (integral > NUMBER_999999) {
                    stringBuilder.append(INITIAL_GROWTH_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                if (integral < 0) {
                    stringBuilder.append(INITIAL_GROWTH_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                hsaOperationMemberInfo.setMemberGrowthValue(Integer.parseInt(growthValue));
            }
        } else {
            hsaOperationMemberInfo.setMemberGrowthValue(0);
        }
    }

    public static void checkAccount(HsmMemberUploadExcel hsmMemberUploadExcel,
                                    StringBuilder stringBuilder,
                                    Map<String, String> checkAccountNumMap,
                                    HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (!hsmMemberUploadExcel.getAccountId().matches(REG)
                || hsmMemberUploadExcel.getAccountId().length() != ACCOUNT_LENGTH) {
            stringBuilder.append("【账户ID】填写错误").append(StringConstant.STR_LINE_FEED);
        } else if (checkAccountNumMap.containsKey(hsmMemberUploadExcel.getAccountId())) {
            stringBuilder.append("【账户ID】已存在").append(StringConstant.STR_LINE_FEED);
        } else {
            hsaOperationMemberInfo.setMemberAccount(hsmMemberUploadExcel.getAccountId());
            checkAccountNumMap.put(hsmMemberUploadExcel.getAccountId(), hsmMemberUploadExcel.getAccountId());
        }
    }

    private static void checkExcelMemberIntegral(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        String integrals = hsmMemberUploadExcel.getIntegral();
        if (StringUtils.hasText(integrals)) {
            if (integrals.trim().length() > 6 || !hsmMemberUploadExcel.getIntegral().matches(REG_NUM)) {
                stringBuilder.append(INITIAL_INTEGRAL_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
            } else {
                int integral = Integer.parseInt(integrals);
                if (integral > NUMBER_999999) {
                    stringBuilder.append(INITIAL_INTEGRAL_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                if (integral < 0) {
                    stringBuilder.append(INITIAL_INTEGRAL_VALUE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                hsaOperationMemberInfo.setMemberIntegral(Integer.parseInt(integrals));
            }

        } else {
            hsaOperationMemberInfo.setMemberIntegral(0);
        }
    }

    private static void checkExcelPassword(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getAccountPassword())) {
            if (!hsmMemberUploadExcel.getAccountPassword().matches(REG)
                    || hsmMemberUploadExcel.getAccountPassword().length() != PASSWORD_LENGTH) {
                stringBuilder.append("【账户密码】格式错误").append(StringConstant.STR_LINE_FEED);
            } else {
                hsaOperationMemberInfo.setPassword(hsmMemberUploadExcel.getAccountPassword());
            }
        } else {
            hsaOperationMemberInfo.setPassword(INITIAL_PASSWORD);
        }

        hsaOperationMemberInfo.setPassword(SecureUtil.md5(String.valueOf(hsaOperationMemberInfo.getPassword())).toUpperCase());
    }


    private static void checkExcelCountryCode(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getPhoneCountryCode())) {
            if (!NumberConstant.AREA_CODE.contains(hsmMemberUploadExcel.getPhoneCountryCode())) {
                stringBuilder.append("【区号】错误").append(StringConstant.STR_LINE_FEED);
            }
        } else {
            hsmMemberUploadExcel.setPhoneCountryCode(StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE);
        }
    }

    private static void checkExcelSex(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        String sex = hsmMemberUploadExcel.getSex();
        if (StringUtils.hasText(sex)) {
            SexEnum sexEnum = SexEnum.asEnum(sex);
            if (Objects.isNull(sexEnum)) {
                stringBuilder.append("【性别】填写错误").append(StringConstant.STR_LINE_FEED);
            } else {
                hsaOperationMemberInfo.setSex(sexEnum.getCode());
            }
        } else {
            hsaOperationMemberInfo.setSex(SexEnum.SEX_WEIZHI.getCode());
        }
    }

    private static void checkPhoneNum(HsmMemberUploadExcel hsmMemberUploadExcel, Map<String, String> checkIphoneNumMap, StringBuilder stringBuilder) {
        if (CollUtil.isNotEmpty(checkIphoneNumMap) && checkIphoneNumMap.containsKey(hsmMemberUploadExcel.getPhoneNum())
                && (hsmMemberUploadExcel.getPhoneCountryCode().equals(checkIphoneNumMap.get(hsmMemberUploadExcel.getPhoneNum())))) {
            stringBuilder.append("【手机号】用户已存在" + "\r\n");

        }
    }

    private static void checkExcelPayPassword(HsmMemberUploadExcel hsmMemberUploadExcel, HsaOperationMemberInfo hsaOperationMemberInfo, StringBuilder stringBuilder) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getPayPassword())) {
            Pattern pattern = Pattern.compile("\\d{6}");
            boolean matches = pattern.matcher(hsmMemberUploadExcel.getPayPassword()).matches();
            if (matches) {
                hsaOperationMemberInfo.setPayPassword(SecureUtil.md5(hsmMemberUploadExcel.getPayPassword()).toUpperCase());
            } else {
                stringBuilder.append("【账户密码】格式错误").append(StringConstant.STR_LINE_FEED);
            }
        } else {
            hsaOperationMemberInfo.setPayPassword(PAY_PASSWORD);
        }
    }

    private static void checkExcelMemberSubsidyAmount(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getMemberSubsidyAmount())) {
            if (!hsmMemberUploadExcel.getMemberSubsidyAmount().matches(REG)) {
                stringBuilder.append(PREPAID_SUBSIDY_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
            } else {
                BigDecimal amount = new BigDecimal(hsmMemberUploadExcel.getMemberSubsidyAmount());
                if (amount.compareTo(MAX_USER_AMOUNT) > BigDecimal.ZERO.intValue()) {
                    stringBuilder.append(PREPAID_SUBSIDY_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                if (amount.doubleValue() < 0) {
                    stringBuilder.append(PREPAID_SUBSIDY_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
            }
        } else {
            hsaOperationMemberInfo.setMemberSubsidyAmountMoney(new BigDecimal(0));
        }
    }

    private static void checkExcelMemberGiftAccountMoney(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getMemberGiftAccountMoney())) {
            if (!hsmMemberUploadExcel.getMemberGiftAccountMoney().matches(REG)) {
                stringBuilder.append(PREPAID_GIFT_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
            } else {
                BigDecimal amount = new BigDecimal(hsmMemberUploadExcel.getMemberGiftAccountMoney());
                if (amount.compareTo(MAX_USER_AMOUNT) > BigDecimal.ZERO.intValue()) {
                    stringBuilder.append(PREPAID_GIFT_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                if (amount.doubleValue() < 0) {
                    stringBuilder.append(PREPAID_GIFT_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
            }
        } else {
            hsaOperationMemberInfo.setMemberGiftAccountMoney(new BigDecimal(0));
        }
    }

    private static void checkExcelMemberAccountMoney(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getMemberAccountMoney())) {
            if (!hsmMemberUploadExcel.getMemberAccountMoney().matches(REG)) {
                stringBuilder.append(PREPAID_CHARGE_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
            } else {
                BigDecimal amount = new BigDecimal(hsmMemberUploadExcel.getMemberAccountMoney());
                if (amount.compareTo(MAX_USER_AMOUNT) > BigDecimal.ZERO.intValue()) {
                    stringBuilder.append(PREPAID_CHARGE_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
                if (amount.doubleValue() < 0) {
                    stringBuilder.append(PREPAID_CHARGE_BALANCE_FORMAT_ERROR).append(StringConstant.STR_LINE_FEED);
                }
            }
        } else {
            hsaOperationMemberInfo.setMemberAccountMoney(new BigDecimal(0));
        }
    }

    private static void checkExcelBirthday(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder) {
        String s = hsmMemberUploadExcel.getBirthday();
        if (StringUtils.hasText(s)) {
            try {
                LocalDate parse = LocalDate.parse(s, DateTimeFormatter.ofPattern("y.M.d"));
                if (LocalDate.now().isBefore(parse)) {
                    stringBuilder.append("【生日】填写错误").append(StringConstant.STR_LINE_FEED);
                }
            } catch (Exception e) {
                stringBuilder.append("【生日】填写错误").append(StringConstant.STR_LINE_FEED);
            }
        }
    }


    private static void checkExcelUserName(HsmMemberUploadExcel hsmMemberUploadExcel, StringBuilder stringBuilder) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getUserName()) &&
                (hsmMemberUploadExcel.getUserName().length() > NumberConstant.NUMBER_6)) {
            stringBuilder.append("【会员姓名】格式错误")
                    .append(StringConstant.STR_LINE_FEED);

        }
    }

    private static void checkExcelPhoneNum(StringBuilder stringBuilder, Map<String, String> memberPhoneMap, HsmMemberUploadExcel hsmMemberUploadExcel, Map<String, String> checkIphoneNumMap) {
        String phoneNum = hsmMemberUploadExcel.getPhoneNum();
        if (StringUtils.hasText(phoneNum)) {
            if (!NumberUtil.isPhoneNum11(phoneNum)) {
                stringBuilder.append("【手机号】格式错误").append(StringConstant.STR_LINE_FEED);
            }

            if (CollUtil.isNotEmpty(memberPhoneMap) && memberPhoneMap.containsKey(hsmMemberUploadExcel.getPhoneNum())) {
                String code = memberPhoneMap.get(hsmMemberUploadExcel.getPhoneNum());
                if (hsmMemberUploadExcel.getPhoneCountryCode().equals(code)) {
                    stringBuilder.append("【手机号】用户已存在").append(StringConstant.STR_LINE_FEED);
                } else {
                    checkPhoneNum(hsmMemberUploadExcel, checkIphoneNumMap, stringBuilder);
                }
            } else {
                checkPhoneNum(hsmMemberUploadExcel, checkIphoneNumMap, stringBuilder);
            }
            checkIphoneNumMap.put(hsmMemberUploadExcel.getPhoneNum(), hsmMemberUploadExcel.getPhoneCountryCode());
        } else {
            stringBuilder.append("【手机号】格式错误").append(StringConstant.STR_LINE_FEED);
        }
    }

    public static void verifyMemberCertificate(HsmMemberUploadExcel hsmMemberUploadExcel,
                                        StringBuilder stringBuilder,
                                        Map<String, HsaMemberCertificateInfo> operationMemberCertificateNumMap,
                                        Map<String, CertificateInfo> certificateInfoMap) {
        String certificateType = hsmMemberUploadExcel.getCertificateType();
        String certificateNum = hsmMemberUploadExcel.getCertificateNum();
        if (StringUtils.isEmpty(certificateType) && StringUtils.isEmpty(certificateNum)) {
            return;
        }
        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.asEnumOrNull(hsmMemberUploadExcel.getCertificateType());
        if (Objects.isNull(certificateTypeEnum)) {
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_TYPE).append(StringConstant.STR_LINE_FEED);
            ;
            VerifyUtil.isTure(StringUtils.hasText(certificateNum))
                    .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED));
            return;
        }
        if (!StringUtils.hasText(certificateNum)) {
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED);
            return;
        }
        VerifyUtil.isTureOrFalse(CollUtil.isNotEmpty(operationMemberCertificateNumMap) && operationMemberCertificateNumMap.containsKey(hsmMemberUploadExcel.getCertificateNum()))
                .trueOrFalseHandle(() -> {
                    HsaMemberCertificateInfo certificateInfo = operationMemberCertificateNumMap.get(hsmMemberUploadExcel.getCertificateNum());
                    if (certificateTypeEnum.getCode() == certificateInfo.getCertificateType()) {
                        stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_EXIST).append(StringConstant.STR_LINE_FEED);
                    } else {
                        checkCertificateInfoMap(hsmMemberUploadExcel, certificateInfoMap, stringBuilder, certificateTypeEnum);
                    }
                }, () -> checkCertificateInfoMap(hsmMemberUploadExcel, certificateInfoMap, stringBuilder, certificateTypeEnum));
        CertificateInfo certificateInfo = new CertificateInfo();
        certificateInfo.setCertificateType(certificateTypeEnum.getCode());
        certificateInfo.setCertificateNum(hsmMemberUploadExcel.getCertificateNum());
        VerifyUtil.isTure(certificateTypeEnum.equals(CertificateTypeEnum.CERTIFICATE_OTHER))
                .trueHandle(() -> certificateInfo.setCertificateName(hsmMemberUploadExcel.getCertificateType()));
        certificateInfoMap.put(hsmMemberUploadExcel.getCertificateNum(), certificateInfo);
        switch (certificateTypeEnum) {
            case CERTIFICATE_IDCARD:
                VerifyUtil.isTure(!certificateNum.matches(ID_CARD))
                        .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED));
                break;
            case CERTIFICATE_MILITARYID:
                VerifyUtil.isTure(!certificateNum.matches(ARMYMAN))
                        .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED));
                break;
            case CERTIFICATE_PASSPORT:
                VerifyUtil.isTure(!isValidChinaPassport(certificateNum))
                        .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED));
                break;
            case CERTIFICATE_HONGKONGMACAUANDTAIWAN_PASS:
                VerifyUtil.isTure(!certificateNum.matches(MACAU_PASS))
                        .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED));
                break;
            default:
                //自定义证件类型
                VerifyUtil.isTureOrFalse(certificateNum.length() > NUMBER_20)
                        .trueOrFalseHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD).append(StringConstant.STR_LINE_FEED),
                                () -> hsmMemberUploadExcel.setCertificateName(hsmMemberUploadExcel.getCertificateType()));
        }
    }


    /**
     * 校验中国护照号码是否有效
     *
     * @param passportNumber 要校验的护照号码
     * @return 如果护照号码有效返回true，否则返回false
     */
    public static boolean isValidChinaPassport(String passportNumber) {
        if (passportNumber == null || passportNumber.isEmpty()) {
            return false;
        }
        return passportNumber.matches(CHINA_PASSPORT_REGEX);
    }

    private static void checkCertificateInfoMap(HsmMemberUploadExcel hsmMemberUploadExcel, Map<String, CertificateInfo> certificateInfoMap, StringBuilder stringBuilder, CertificateTypeEnum certificateTypeEnum) {
        if (CollUtil.isNotEmpty(certificateInfoMap) && certificateInfoMap.containsKey(hsmMemberUploadExcel.getCertificateNum())) {
            CertificateInfo certificateInfo = certificateInfoMap.get(hsmMemberUploadExcel.getCertificateNum());
            if (certificateTypeEnum.getCode() == CertificateTypeEnum.CERTIFICATE_OTHER.getCode()) {
                if (certificateInfo.getCertificateName().equals(hsmMemberUploadExcel.getCertificateType())) {
                    stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_EXIST + "\r\n");
                }
            } else {
                if (certificateInfo.getCertificateType().equals(certificateTypeEnum.getCode())) {
                    stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_EXIST + "\r\n");
                }
            }
        }
    }

    public static void checkExcelIndustrySector(HsmMemberUploadExcel hsmMemberUploadExcel,
                                                StringBuilder stringBuilder,
                                                HsaOperationMemberInfo hsaOperationMemberInfo,
                                                List<JobVO> jobList,
                                                List<CountryVO> countryList) {
        //电子邮件
        if (StringUtils.hasText(hsmMemberUploadExcel.getEmail()) &&
                (!hsmMemberUploadExcel.getEmail().matches(REGEX) || hsmMemberUploadExcel.getEmail().length() > NUMBER_20)) {
            stringBuilder.append("【电子邮箱】填写错误").append(StringConstant.STR_LINE_FEED);

        }
        //公司
        if (StringUtils.hasText(hsmMemberUploadExcel.getWorkName()) &&
                (hsmMemberUploadExcel.getWorkName().length() > NUMBER_20)) {
            stringBuilder.append("【所属公司】填写错误").append(StringConstant.STR_LINE_FEED);

        }
        VerifyUtil.isTure(StringUtils.hasText(hsmMemberUploadExcel.getCompanyName()))
                .trueHandle(() -> VerifyUtil.isTure(hsmMemberUploadExcel.getCompanyName().length() > NUMBER_20)
                        .trueHandle(() -> stringBuilder.append("【公司名称】填写错误").append(StringConstant.STR_LINE_FEED)));
        VerifyUtil.isTure(StringUtils.hasText(hsmMemberUploadExcel.getCompanyAddress()))
                .trueHandle(() -> VerifyUtil.isTure(hsmMemberUploadExcel.getCompanyAddress().length() > ExcelImportConstant.MEMBER_COMPANY_ADDRESS_LENGTH)
                        .trueHandle(() -> stringBuilder.append("【公司地址】填写错误").append(StringConstant.STR_LINE_FEED)));
        // 所在行业
        VerifyUtil.isTure(StringUtils.hasText(hsmMemberUploadExcel.getIndustry()))
                .trueHandle(() -> VerifyUtil.isTure(!verifyMemberIndustry(hsmMemberUploadExcel, hsaOperationMemberInfo, jobList))
                        .trueHandle(() -> stringBuilder.append("【所在行业】填写错误").append(StringConstant.STR_LINE_FEED)));
        //部门
        if (StringUtils.hasText(hsmMemberUploadExcel.getDepartmentName()) &&
                (hsmMemberUploadExcel.getDepartmentName().length() > NUMBER_20)) {
            stringBuilder.append("【所属部门】填写错误").append(StringConstant.STR_LINE_FEED);

        }
        //部门
        if (StringUtils.hasText(hsmMemberUploadExcel.getMemberNum())
                && hsmMemberUploadExcel.getMemberNum().length() > NUMBER_20) {
            stringBuilder.append("【编号】填写错误").append(StringConstant.STR_LINE_FEED);
        }
        // 职位名称
        VerifyUtil.isTure(StringUtils.hasText(hsmMemberUploadExcel.getJobTitle()))
                .trueHandle(() -> VerifyUtil.isTure(hsmMemberUploadExcel.getJobTitle().length() > ExcelImportConstant.MEMBER_JOB_TITLE_LENGTH)
                        .trueHandle(() -> stringBuilder.append("【职位名称】填写错误").append(StringConstant.STR_LINE_FEED)));

        // 省市区
        verifyMemberCountry(hsmMemberUploadExcel, hsaOperationMemberInfo, countryList, stringBuilder);

        //地址
        if (StringUtils.hasText(hsmMemberUploadExcel.getContactAddress()) &&
                (hsmMemberUploadExcel.getContactAddress().length() > NUMBER_40)) {
            stringBuilder.append("【详细地址】填写错误").append(StringConstant.STR_LINE_FEED);

        }
    }


    private static void verifyMemberCountry(HsmMemberUploadExcel hsmMemberUploadExcel, HsaOperationMemberInfo hsaOperationMemberInfo,
                                     List<CountryVO> countryList, StringBuilder stringBuilder) {
        // 不是必填
        String provinceName = hsmMemberUploadExcel.getProvinceName();
        String cityName = hsmMemberUploadExcel.getCityName();
        String areaName = hsmMemberUploadExcel.getAreaName();
        if (StringUtils.isEmpty(provinceName) && StringUtils.isEmpty(cityName) && StringUtils.isEmpty(areaName)) {
            return;
        }
        AtomicBoolean hasValue = new AtomicBoolean(true);
        VerifyUtil.isTure(StringUtils.isEmpty(provinceName)).trueHandle(() -> {
            hasValue.set(false);
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_PROVINCE + "\r\n");
        });
        VerifyUtil.isTure(StringUtils.isEmpty(cityName)).trueHandle(() -> {
            hasValue.set(false);
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_CITY + "\r\n");
        });
        VerifyUtil.isTure(StringUtils.isEmpty(areaName)).trueHandle(() -> {
            hasValue.set(false);
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_AREA + "\r\n");
        });
        if (!hasValue.get()) {
            return;
        }
        Map<String, CountryVO> provinceMap = countryList.stream().collect(Collectors.toMap(CountryVO::getName, Function.identity(), (key1, key2) -> key1));
        CountryVO province = provinceMap.get(provinceName);
        if (Objects.isNull(province)) {
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_PROVINCE + "\r\n");
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_CITY + "\r\n");
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_AREA + "\r\n");
            return;
        }
        Map<String, CountryVO> cityMap = province.getChildren().stream().collect(Collectors.toMap(CountryVO::getName, Function.identity(), (key1, key2) -> key1));
        CountryVO city = cityMap.get(cityName);
        if (Objects.isNull(city)) {
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_CITY + "\r\n");
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_AREA + "\r\n");
            return;
        }
        Map<String, CountryVO> areaMap = city.getChildren().stream().collect(Collectors.toMap(CountryVO::getName, Function.identity(), (key1, key2) -> key1));
        CountryVO area = areaMap.get(areaName);
        if (Objects.isNull(area)) {
            stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_AREA + "\r\n");
            return;
        }
        // 填写正确
        hsaOperationMemberInfo.setProvinceCode(province.getCode());
        hsaOperationMemberInfo.setCityCode(city.getCode());
        hsaOperationMemberInfo.setAreaCode(area.getCode());
    }


    private static boolean verifyMemberIndustry(HsmMemberUploadExcel hsmMemberUploadExcel, HsaOperationMemberInfo hsaOperationMemberInfo,
                                         List<JobVO> jobList) {
        String industry = hsmMemberUploadExcel.getIndustry();
        // 1级分类-2级分类
        if (!industry.contains("-")) {
            return false;
        }
        String[] industrySplit = industry.split("-");
        if (industrySplit.length != 2) {
            return false;
        }
        String firstName = industrySplit[0];
        String secondName = industrySplit[1];
        // check firstName
        for (JobVO firstJob : jobList) {
            if (firstJob.getName().equals(firstName)) {
                JobVO secondJob = firstJob.getChildren().stream().filter(e -> e.getName().equals(secondName)).findFirst().orElse(null);
                if (Objects.nonNull(secondJob)) {
                    hsaOperationMemberInfo.setIndustry(industry);
                    hsaOperationMemberInfo.setIndustryCodes(StringBaseHandlerUtil
                            .arrayConvert(Lists.newArrayList(String.valueOf(firstJob.getId()), String.valueOf(secondJob.getId()))));
                }

            }
        }
        return !StringUtils.isEmpty(hsaOperationMemberInfo.getIndustryCodes());
    }

    public static void checkExcelMemberLabel(HsmMemberUploadExcel hsmMemberUploadExcel,
                                             Map<String, HsaLabelSetting> labelMap,
                                             StringBuilder stringBuilder,
                                             Set<String> labelGuidSet) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getMemberLabels())) {
            String[] labelNames = hsmMemberUploadExcel.getMemberLabels().split(SEMICOLON);
            for (String labelName : labelNames) {
                HsaLabelSetting hsaLabelSetting = labelMap.get(labelName);
                if (Objects.isNull(hsaLabelSetting)) {
                    stringBuilder.append("【会员标签】").append(labelName).append("不存在").append(StringConstant.STR_LINE_FEED);
                } else if (hsaLabelSetting.getIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
                    stringBuilder.append("【会员标签】 ").append(labelName).append("已禁用").append(StringConstant.STR_LINE_FEED);
                } else {
                    labelGuidSet.add(hsaLabelSetting.getGuid());
                }
            }
        }
    }
}
