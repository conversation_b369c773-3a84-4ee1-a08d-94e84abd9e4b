package com.holderzone.member.base.entity.system;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
public class HsaInitSubjectData implements Serializable {

    private static final long serialVersionUID = 8707646243382390264L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 系统标识
     */
    private int system;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 已初始化
     */
    private int initialized;
}
