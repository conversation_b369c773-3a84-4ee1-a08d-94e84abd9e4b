package com.holderzone.member.base.entity.setting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员账户设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HsaAccountSetting对象", description = "会员账户设置表")
public class HsaAccountSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    /**
     * @see com.holderzone.member.common.enums.member.AccountLoginTypeEnum
     */
    @ApiModelProperty(value = "登陆类型：1登录+注册 2仅登录 3账号/手机号+密码登录")
    private Integer loginType;

    /**
     * @see com.holderzone.member.common.enums.member.MmberModeEnum
     */
    @ApiModelProperty(value = "0 弱登录模式  1强登录模式")
    private Integer memberMode;

    public HsaAccountSetting() {
    }

    public HsaAccountSetting(String operSubjectGuid) {
        this.operSubjectGuid = operSubjectGuid;
    }

    public static List<HsaAccountSetting> batchInit(List<String> operSubjectGuidList) {
        List<HsaAccountSetting> accountSettingList = Lists.newArrayList();
        operSubjectGuidList.forEach(e -> accountSettingList.add(new HsaAccountSetting(e)));
        return accountSettingList;
    }
}
