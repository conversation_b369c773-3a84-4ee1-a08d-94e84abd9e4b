package com.holderzone.member.base.service.grade.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.holderzone.member.base.entity.grade.HsaMemberGradePurchaseHistory;
import com.holderzone.member.base.mapper.grade.HsaMemberGradePurchaseHistoryMapper;
import com.holderzone.member.base.service.grade.HsaMemberGradePurchaseHistoryService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.grade.EffectiveDurationTypeEnum;
import com.holderzone.member.common.enums.grade.GradeHisChangeTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.sequence.SequenceGenerator;
import com.holderzone.member.common.vo.grade.MemberGradePurchaseHistoryExportVO;
import com.holderzone.member.common.vo.grade.MemberGradePurchaseHistorySingleExportVO;
import com.holderzone.member.common.vo.grade.MemberGradePurchaseHistoryVO;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_PATTERN;
import static com.holderzone.member.common.enums.member.MemberAccountExceptionEnum.MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST;

/**
 * 会员等级购买流水服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class HsaMemberGradePurchaseHistoryServiceImpl extends HolderBaseServiceImpl<HsaMemberGradePurchaseHistoryMapper, HsaMemberGradePurchaseHistory>
        implements HsaMemberGradePurchaseHistoryService {

    @Resource
    private HsaMemberGradePurchaseHistoryMapper hsaMemberGradePurchaseHistoryMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public List<MemberGradePurchaseHistoryVO> list(MemberGradePurchaseHistoryQO qo) {
        // 设置运营主体GUID
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        // 设置查询类型 单个会员进来需要查询过期，付费记录不需要
        if (qo.getMemberInfoGuid() == null) {
            qo.setTypes(GradeHisChangeTypeEnum.getNotIncludedUserTypes());
        }
        List<HsaMemberGradePurchaseHistory> list = hsaMemberGradePurchaseHistoryMapper.selectByCondition(qo);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }else{
            List<MemberGradePurchaseHistoryVO> returList = list.stream().map(this::convertToVO).collect(Collectors.toList());
            // 生成序号
            SequenceGenerator.generateSequence(returList, (item, sn) -> item.setSn(String.valueOf(sn)));
            return returList;
        }
    }

    @Override
    public MemberGradePurchaseHistoryVO getByGuid(String guid) {
        HsaMemberGradePurchaseHistory history = hsaMemberGradePurchaseHistoryMapper.queryByGuid(guid);
        if (history == null) {
            throw new MemberBaseException(MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST);
        }
        return convertToVO(history);
    }

    @Override
    public MemberGradePurchaseHistoryVO getByOrderNo(String orderNo) {
        HsaMemberGradePurchaseHistory history = hsaMemberGradePurchaseHistoryMapper.selectByOrderNo(orderNo);
        if (history == null) {
            throw new MemberBaseException(MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST);
        }
        return convertToVO(history);
    }

    @Override
    public List<MemberGradePurchaseHistoryVO> listByMember(String memberInfoGuid) {
        Collection<HsaMemberGradePurchaseHistory> histories = hsaMemberGradePurchaseHistoryMapper.selectByMemberInfoGuid(memberInfoGuid);
        if (CollectionUtils.isEmpty(histories)) {
            return Collections.emptyList();
        }
        return histories.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(MemberGradePurchaseHistoryQO qo) {
        HsaMemberGradePurchaseHistory history = new HsaMemberGradePurchaseHistory();
        BeanUtils.copyProperties(qo, history);
        history.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePurchaseHistory.class.getSimpleName()));
        history.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        history.setVersion(0);
        history.setIsDelete(0L);
        hsaMemberGradePurchaseHistoryMapper.insert(history);

        return history.getGuid();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MemberGradePurchaseHistoryQO qo) {
        HsaMemberGradePurchaseHistory history = hsaMemberGradePurchaseHistoryMapper.queryByGuid(qo.getGuid());
        if (history == null) {
            throw new MemberBaseException(MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST);
        }
        BeanUtils.copyProperties(qo, history);
        hsaMemberGradePurchaseHistoryMapper.updateByGuid(history);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String guid) {
        HsaMemberGradePurchaseHistory history = hsaMemberGradePurchaseHistoryMapper.queryByGuid(guid);
        if (history == null) {
            throw new MemberBaseException(MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST);
        }
        hsaMemberGradePurchaseHistoryMapper.removeByGuid(guid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> guids) {
        if (CollectionUtils.isEmpty(guids)) {
            return;
        }
        hsaMemberGradePurchaseHistoryMapper.removeByGuids(guids);
    }

    @Override
    public PageResult<MemberGradePurchaseHistoryVO> pageRecord (MemberGradePurchaseHistoryQO qo) {
        // 执行查询
        List<MemberGradePurchaseHistoryVO> list = this.list(qo);

        // 手动分页
        int total = list.size();
        int pages = (total + qo.getPageSize() - 1) / qo.getPageSize();

        // 检查页码是否超出范围
        if (qo.getCurrentPage() > pages) {
            // 如果超出范围，返回空数据
            PageResult<MemberGradePurchaseHistoryVO> pageResult = new PageResult<>();
            pageResult.setCurrent(qo.getCurrentPage());
            pageResult.setSize(qo.getPageSize());
            pageResult.setTotal(total);
            pageResult.setPages(pages);
            pageResult.setRecords(Collections.emptyList());
            return pageResult;
        }

        int fromIndex = (qo.getCurrentPage() - 1) * qo.getPageSize();
        int toIndex = Math.min(fromIndex + qo.getPageSize(), total);

        // 获取当前页的数据
        List<MemberGradePurchaseHistoryVO> pageList = list.subList(fromIndex, toIndex);

        // 创建分页结果
        PageResult<MemberGradePurchaseHistoryVO> pageResult = new PageResult<>();
        pageResult.setCurrent(qo.getCurrentPage());
        pageResult.setSize(qo.getPageSize());
        pageResult.setTotal(total);
        pageResult.setPages(pages);
        pageResult.setRecords(pageList);

        return pageResult;
    }

    @Override
    public void export (MemberGradePurchaseHistoryQO qo, HttpServletResponse response) {
        // 获取数据
        PageResult<MemberGradePurchaseHistoryVO> pageResult = this.pageRecord(qo);

        List<MemberGradePurchaseHistoryExportVO> exportList = new ArrayList<>();
        List<MemberGradePurchaseHistorySingleExportVO> singleExportList = new ArrayList<>();
        boolean isExportUserData = qo.getIsExportUserData() !=null && qo.getIsExportUserData();
        // 导出用户的数据
        if (isExportUserData){
            singleExportList = pageResult.getRecords().stream()
                                 .map(this::convertToSingleExportVO)
                                 .collect(Collectors.toList());
        }else{
            // 导出全部的会员等级购买记录
            exportList = pageResult.getRecords().stream()
                                 .map(this::convertToExportVO)
                                 .collect(Collectors.toList());
        }


        try {
            String currentDateTime = DateUtil.format(DateUtil.date(), PURE_DATETIME_PATTERN);
            String fileName;
            if (isExportUserData) {
                fileName = "付费会员变动记录_" + qo.getMemberInfoGuid() + "_" + currentDateTime + ".xlsx";
            } else {
                fileName = "会员等级购买记录_" + currentDateTime + ".xlsx";
            }
            fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replace("\\+", "%20");


            response.setHeader("Content-Disposition",
                    "attachment; filename*=utf-8''" + fileNameEncode);

            EasyExcel.write(response.getOutputStream(), isExportUserData ? MemberGradePurchaseHistorySingleExportVO.class : MemberGradePurchaseHistoryExportVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(getExcelExportStyle())
                    .sheet("会员等级购买记录")
                    .doWrite(isExportUserData ? singleExportList : exportList);
        } catch (IOException e) {
            log.error("导出会员等级购买记录失败", e);
            throw new MemberBaseException("导出失败");
        }
    }

    private MemberGradePurchaseHistoryVO convertToVO(HsaMemberGradePurchaseHistory history) {
        MemberGradePurchaseHistoryVO vo = new MemberGradePurchaseHistoryVO();
        BeanUtils.copyProperties(history, vo);

        return vo;
    }

    /**
     * 转换为导出VO
     */
    private MemberGradePurchaseHistoryExportVO convertToExportVO(MemberGradePurchaseHistoryVO vo) {
        MemberGradePurchaseHistoryExportVO exportVO = new MemberGradePurchaseHistoryExportVO();
        exportVO.setNo(vo.getNo());
        exportVO.setPayTime(vo.getGmtCreate() != null ?
                                    vo.getGmtCreate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        exportVO.setValidityPeriod(EffectiveDurationTypeEnum.fromValue(vo.getEffectiveDurationType()).getMsg());
        exportVO.setArrivalTime(vo.getExpireTime() != null ?
                                        vo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        exportVO.setGradeName(vo.getMemberInfoGradeName());
        exportVO.setMemberName(vo.getMemberInfoName());
        exportVO.setMemberPhone(vo.getUserPhone());
        exportVO.setAmount(vo.getPayAmount());
        exportVO.setType(GradeHisChangeTypeEnum.getDescByValue(vo.getType()));
        exportVO.setRemark(vo.getRemark());
        return exportVO;
    }

    private MemberGradePurchaseHistorySingleExportVO convertToSingleExportVO(MemberGradePurchaseHistoryVO vo) {
        MemberGradePurchaseHistorySingleExportVO exportVO = new MemberGradePurchaseHistorySingleExportVO();
        exportVO.setSn(vo.getSn() == null ? "" : vo.getSn());
        exportVO.setModifyDateTime(vo.getGmtModified() != null
                                           ? vo.getGmtModified().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                           : "");
        exportVO.setType(vo.getType() == null ? "" : GradeHisChangeTypeEnum.getDescByValue(vo.getType()));
        exportVO.setEffectiveDurationType(vo.getEffectiveDurationType() == null ? 0 : vo.getEffectiveDurationType());
        exportVO.setValidityPeriod(vo.getEffectiveDurationType() == null
                                           ? ""
                                           : EffectiveDurationTypeEnum.fromValue(vo.getEffectiveDurationType()).getMsg());
        exportVO.setChangedGrades(vo.getMemberInfoGradeName() == null ? "无等级" : vo.getMemberInfoGradeName());
        exportVO.setSource(vo.getSource() == null ? "" : SourceTypeEnum.getDesByCode(vo.getSource()));
        exportVO.setStoreName(vo.getStoreName() == null ? "" : vo.getStoreName());
        exportVO.setRecordDesc(vo.getRecordDesc() == null ? "" : vo.getRecordDesc());
        exportVO.setOperator(vo.getOperatorName() == null ? "" : vo.getOperatorName());
        return exportVO;
    }

    /**
     * 获取Excel导出样式策略
     * @return HorizontalCellStyleStrategy
     */
    private HorizontalCellStyleStrategy getExcelExportStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = getWriteCellStyle();

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置居中对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置字体
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short)11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 返回头和内容的样式策略
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    private static WriteCellStyle getWriteCellStyle () {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为灰色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 设置居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short)12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        return headWriteCellStyle;
    }
}




