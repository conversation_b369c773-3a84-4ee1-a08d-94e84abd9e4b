package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.base.entity.card.HsaPhysicalCardStrategyRecord;
import com.holderzone.member.base.mapper.card.HsaPhysicalCardStrategyRecordMapper;
import com.holderzone.member.base.service.card.HsaPhysicalCardCreateRecordService;
import com.holderzone.member.base.service.card.HsaPhysicalCardStrategyRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.credit.PayStatusEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.PhysicalStrategyPayQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 实体卡押金支付记录
 * @date 2021/9/1
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaPhysicalCardStrategyRecordServiceImpl extends HolderBaseServiceImpl<HsaPhysicalCardStrategyRecordMapper,
        HsaPhysicalCardStrategyRecord> implements HsaPhysicalCardStrategyRecordService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public String saveRecord(HsaPhysicalCardStrategyRecord strategyRecord) {
        if (ObjectUtil.isNull(strategyRecord)) {
            return null;
        }
        if (ObjectUtil.isNull(strategyRecord.getGuid())) {
            strategyRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaPhysicalCardCreateRecordService.class.getSimpleName()));
        }
        strategyRecord.setSource(ThreadLocalCache.getSource());
        strategyRecord.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        strategyRecord.setOperatorName(ThreadLocalCache.getHeaderUserInfo().getUserName());
        strategyRecord.setOperatorAccount(ThreadLocalCache.getHeaderUserInfo().getTel());
        this.save(strategyRecord);
        return strategyRecord.getGuid();
    }

    @Override
    public boolean savePayStatus(PhysicalStrategyPayQO qo){
        final HsaPhysicalCardStrategyRecord record = queryByGuid(qo.getOrderNumber());
        if(record != null){
            record.setPayStatus(PayStatusEnum.APY.getCode());
            record.setPayGuid(qo.getPayGuid());
            return updateByGuid(record);
        }
        throw new MemberBaseException("押金记录不存在！");
    }

}
