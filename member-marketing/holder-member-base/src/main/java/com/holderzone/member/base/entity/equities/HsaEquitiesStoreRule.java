package com.holderzone.member.base.entity.equities;


import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: 权益门店
 * @author: rw
 * @create: 2022-03-11 10:49
 */
@Data
@Accessors(chain = true)
public class HsaEquitiesStoreRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * storeGuid
     */
    private String storeGuid;

    /**
     * storeName
     */
    private String storeName;

    /**
     * 门店编号
     */
    private String storeNumber;

    /**
     * 营业时间
     */
    private String time;

    /**
     * 营业地址
     */
    private String address;

    /**
     * 经纬度
     */
    private String addressPoint;

    /**
     * 权益guid
     */
    private String equitiesGuid;

    /**
     * 权益id
     */
    private String equitiesVersionId;

    /**
     * 是否删除,0未删除,1已删除 2：删除但未生效
     */
    private Integer isDelete;

    /**
     * 是否有效 0:无效 1:有效
     */
    private Integer effective;

    private String storeLogo;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
