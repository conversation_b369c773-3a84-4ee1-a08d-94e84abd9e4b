package com.holderzone.member.base.service.growth.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityClassify;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueCommodityClassifyMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueCommodityClassifyService;
import com.holderzone.member.base.service.growth.converter.GrowthValueCommodityClassifyConverter;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.dto.growth.AppointClassifyGoodsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-31
 * @description 指定商品分类服务实现
 */
@Service
@Slf4j
public class HsaGrowthValueCommodityClassifyServiceImpl extends HolderBaseServiceImpl<HsaGrowthValueCommodityClassifyMapper,HsaGrowthValueCommodityClassify> implements HsaGrowthValueCommodityClassifyService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveGrowthValueTaskCommodityClassify(String taskGuid, List<AppointClassifyGoodsDTO> appointGoodsTypeList){
        if(CollectionUtil.isEmpty(appointGoodsTypeList)){
            return;
        }
        //根据成长值任务查询是否存在规则
        List<HsaGrowthValueCommodityClassify> growthValueCommodityClassifyList = list(new LambdaQueryWrapper<HsaGrowthValueCommodityClassify>().eq(HsaGrowthValueCommodityClassify::getTaskGuid, taskGuid));
        //不可编辑直接返回
        if(CollectionUtil.isNotEmpty(growthValueCommodityClassifyList)){
            return;
        }
        //保存商品分类信息
        List<HsaGrowthValueCommodityClassify> commodityClassifyList = GrowthValueCommodityClassifyConverter.fromTaskAndGrowthValueCommodityList(taskGuid, appointGoodsTypeList);
        saveBatch(commodityClassifyList);
    }

    @Override
    public List<AppointClassifyGoodsDTO> listCommodityClassifyByTask(String taskGuid) {
        List<HsaGrowthValueCommodityClassify> growthValueCommodityClassifyList = list(new LambdaQueryWrapper<HsaGrowthValueCommodityClassify>().eq(HsaGrowthValueCommodityClassify::getTaskGuid, taskGuid));
        if(CollectionUtil.isEmpty(growthValueCommodityClassifyList)){
            return Collections.emptyList();
        }
        return GrowthValueCommodityClassifyConverter.toGrowthValueCommodityList(growthValueCommodityClassifyList);
    }
}
