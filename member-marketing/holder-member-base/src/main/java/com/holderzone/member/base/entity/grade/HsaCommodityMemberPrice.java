package com.holderzone.member.base.entity.grade;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 商品会员价关联实体
 * @author: pan tao
 * @create: 2022-07-07 11:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCommodityMemberPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 销售渠道
     */
    private String channel;

    /**
     * 商品来源类型 store:门店 stall:档口
     *
     * @see com.holderzone.member.common.enums.grade.CommoditySourceEnum
     */
    private String commoditySource;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 折扣类型 0：折扣 1：减价 2：指定价格
     *
     * @see com.holderzone.member.common.enums.grade.DiscountTypeEnum
     */
    private int discountType;

    /**
     * 折扣值
     */
    private BigDecimal discountValue;

    /**
     * 限制类型 0:不限制 1:限制
     */
    private int limitedType;

    /**
     * 限制数量
     */
    private int limitedNumber;

    /**
     * 0：未删除 1：已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;
}

