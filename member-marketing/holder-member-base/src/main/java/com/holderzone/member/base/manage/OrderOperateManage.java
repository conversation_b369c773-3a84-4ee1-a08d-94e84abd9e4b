package com.holderzone.member.base.manage;

import com.holderzone.member.base.support.MemberRightsSupport;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.base.dto.SettlementUnLockedDiscountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单操作
 *
 * <AUTHOR>
 * @date 2023/12/15
 * @since 1.8
 */
@Slf4j
@Component
public class OrderOperateManage {

    @Resource
    private MemberRightsSupport memberRightsSupport;

    /**
     * 订单取消
     *
     * @param dto 取消参数
     * @return 是否成功
     */
    public boolean cancel(SettlementUnLockedDiscountDTO dto) {
        //释放优惠权益
        memberRightsSupport.unlock(dto);

        return true;
    }

    /**
     * 批量订单取消
     *
     * @param dtoLst 批量订单
     */
    public void cancel(List<SettlementUnLockedDiscountDTO> dtoLst) {
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        for (SettlementUnLockedDiscountDTO dto : dtoLst) {
            if(StringUtils.isEmpty(dto.getOperSubjectGuid())){
                dto.setOperSubjectGuid(operSubjectGuid);
            }
            //释放优惠权益
            memberRightsSupport.unlock(dto);
        }
    }
}
