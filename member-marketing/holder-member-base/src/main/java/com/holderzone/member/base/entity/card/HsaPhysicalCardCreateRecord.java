package com.holderzone.member.base.entity.card;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 实体卡开发记录实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaPhysicalCardCreateRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 实体卡开发记录GUID
     */
    private String guid;

    /**
     * 会员卡GUID
     */
    private String cardGuid;

    /**
     * 来源,0后台添加,2一体机注册
     */
    private Integer source;

    /**
     * 是否已经绑定会员：  0 否 1 是
     */
    private Integer memberBindingState;

    /**
     * 生成数量
     */
    private Integer createCount;

    /**
     * 生成结果 0生成失败 1生成成功 2制卡成功 3制卡失败
     */
    private Integer createResult;

    /**
     * 操作人账户
     */
    private String operatorAccount;

    /**
     * 操作人名字
     */
    private String operatorName;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
