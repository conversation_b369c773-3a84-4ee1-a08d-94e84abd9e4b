package com.holderzone.member.base.service.member.impl;

import com.holderzone.member.base.config.ChainConfig;
import com.holderzone.member.base.entity.member.HsaLabelSettingBaseInfo;
import com.holderzone.member.base.service.member.ChainEnterService;
import com.holderzone.member.base.service.member.chain.label.Responsibility;
import com.holderzone.member.base.service.member.chain.label.SchedulingChain;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 责任入口service实现
 */
@Service
public class ChainEnterServiceImpl implements ChainEnterService {

    @Autowired
    private ChainConfig chainConfig;

    @Override
    public Set<String> executeQueryLabelMembers(RequestLabelQuery query, HsaLabelSettingBaseInfo baseInfo) {
        // 获取容器中所有负责支付的实例
        List<Responsibility> responsibilities = chainConfig.getResponsibilities();
        List<Responsibility> sortedResponsibilities = responsibilities.stream()
                .sorted(Comparator.comparing(Responsibility::getCode)).collect(Collectors.toList());
        // 责任链调度类
        final SchedulingChain schedulingChain = new SchedulingChain(sortedResponsibilities, 0, query, baseInfo);
        // 传递支付责任
        return schedulingChain.proceedQuery(query);
    }
}
