package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.member.HsaDepositStrategy;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.card.ECardCompareExcelDTO;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface HsaElectronicCardMapper extends HolderBaseMapper<HsaElectronicCard> {


    List<String> listMemberGuidByRange(@Param("cardGuid") String cardGuid, @Param("collect")List<String> collect);

    List<ECardCompareExcelDTO> listAllECard(@Param("operSubjectGuid") String operSubjectGuid);

    void freezeOrThawCard(@Param("guidList") List<String> guidList,@Param("status")Integer status);

    MemberCardOpenDTO findCanOpenCardByMemberCardGuid(@Param("ownGuid")String ownGuid);

    HsaDepositStrategy findCardDepositStrategyByCardGuid(@Param("cardGuid") String cardGuid);

    void batchUpdatePhone(@Param("memberInfoGuid") String memberInfoGuid,@Param("phone") String phone);

    HsaElectronicCard getByCardNum(@Param("cardNum") String cardNum, @Param("operSubjectGuid") String operSubjectGuid);
}
