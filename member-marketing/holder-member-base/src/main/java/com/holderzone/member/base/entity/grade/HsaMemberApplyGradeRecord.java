package com.holderzone.member.base.entity.grade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 等级开通审查表
 * @author: rw
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberApplyGradeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 审核状态
     * @see com.holderzone.member.common.enums.member.GradeApplyStateEnum
     */
    @ApiModelProperty(value = "审核状态0 待审核 1 审核通过 2 驳回")
    private Integer applyState;

    /**
     * 0 会员等级 1 商家等级
     */
    @ApiModelProperty(value = "0 会员等级 1 商家等级")
    private Integer roleType;

    /**
     * 当前等级guid
     */
    @ApiModelProperty(value = "当前等级guid")
    private String currentGradeGuid;

    /**
     * 当前等级名称
     */
    @ApiModelProperty(value = "当前等级名称")
    private String currentGradeName;

    /**
     * 申请等级guid
     */
    @ApiModelProperty(value = "申请等级guid")
    private String applyGradeGuid;

    /**
     * 申请等级名称
     */
    @ApiModelProperty(value = "申请等级名称")
    private String applyGradeName;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;


    /**
     * 会员name
     */
    @ApiModelProperty(value = "会员name")
    private String memberName;


    /**
     * 会员phone
     */
    @ApiModelProperty(value = "会员phone")
    private String memberPhone;

    /**
     * 运营主体
     */
    @ApiModelProperty(value = "运营主体")
    private String operSubjectGuid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;


    /**
     * 有效期数量
     */
    @ApiModelProperty(value = "有效期数量")
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    @ApiModelProperty(value = "有效期单位：3月 4年")
    private Integer unit;

}
