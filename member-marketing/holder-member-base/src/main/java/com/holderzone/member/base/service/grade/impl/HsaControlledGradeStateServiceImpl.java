package com.holderzone.member.base.service.grade.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.grade.HsaControlledGradeState;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.event.MemberGradeChangeEvent;
import com.holderzone.member.base.mapper.grade.HsaControlledGradeStateMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.support.SettlementMemberSupport;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.EnableEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.ControlledStateQO;
import com.holderzone.member.common.qo.grade.UpdateStateRequest;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.ControlledGradeStateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaControlledGradeStateServiceImpl extends HolderBaseServiceImpl<HsaControlledGradeStateMapper, HsaControlledGradeState> implements HsaControlledGradeStateService {

    private final HsaControlledGradeStateMapper hsaControlledGradeStateMapper;

    @Resource
    private MemberGradeChangeEvent memberGradeChangeEvent;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    SettlementMemberSupport settlementMemberSupport;

    public HsaControlledGradeStateServiceImpl(HsaControlledGradeStateMapper hsaControlledGradeStateMapper,
                                              GuidGeneratorUtil guidGeneratorUtil) {
        this.hsaControlledGradeStateMapper = hsaControlledGradeStateMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
    }


    @Override
    public boolean addOrUpdate(ControlledStateQO controlledState) {
        HsaControlledGradeState hsaControlledState = new HsaControlledGradeState();
        String guid = controlledState.getGuid();
        LocalDateTime now = LocalDateTime.now();
        //判断当前操作是新增还是更新
        if (StringUtils.isEmpty(guid)) {
            guid = guidGeneratorUtil.getStringGuid(HsaControlledGradeState.class.getSimpleName());
            BeanUtils.copyProperties(controlledState, hsaControlledState);
            hsaControlledState.setGuid(guid);
            hsaControlledState.setOperatorName(StringConstant.ADMIN);
            hsaControlledState.setGmtCreate(now);
            hsaControlledState.setGmtModified(now);
            return this.save(hsaControlledState);
        } else {
            HsaControlledGradeState controlledStateInfo = hsaControlledGradeStateMapper.queryByGuid(guid);
            verifyResource(controlledStateInfo);
            hsaControlledState.setGmtModified(now);
            BeanUtils.copyProperties(controlledState, controlledStateInfo);
            return this.updateByGuid(controlledStateInfo);
        }
    }

    @Override
    public void addGradeState(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        List<HsaControlledGradeState> hsaControlledStates = hsaControlledGradeStateMapper.selectList(
                new LambdaQueryWrapper<HsaControlledGradeState>()
                        //默认
                        .eq(HsaControlledGradeState::getRoleType, RoleTypeEnum.MEMBER.name())
                        .in(HsaControlledGradeState::getOperSubjectGuid, operSubjectGuids)
                        .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())
        );

        if (CollUtil.isEmpty(hsaControlledStates)) {
            saveBatchGrade(operSubjectGuids);
        } else {
            List<String> operSubjectGuidList = hsaControlledStates.stream()
                    .map(HsaControlledGradeState::getOperSubjectGuid)
                    .filter(x -> !operSubjectGuids.contains(x)).distinct().collect(Collectors.toList());
            saveBatchGrade(operSubjectGuidList);
        }
    }

    /**
     * 批量保存会员等级初始化信息
     *
     * @param operSubjectGuids 运营主体
     */
    private void saveBatchGrade(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        operSubjectGuids = operSubjectGuids.stream().distinct().collect(Collectors.toList());
        List<HsaControlledGradeState> hsaControlledStateList = new ArrayList<>();
        for (String operSubjectGuid : operSubjectGuids) {
            HsaControlledGradeState hsaControlledState = initializeGrade(operSubjectGuid);
            hsaControlledStateList.add(hsaControlledState);
        }
        this.saveBatch(hsaControlledStateList);
    }

    /**
     * 通过运营主体初始化会员等级状态
     * 每一个运营主体对应一套会员等级的开启或禁用状态
     *
     * @param operSubjectGuid 运营主体
     * @return 封装的等级信息
     */
    private HsaControlledGradeState initializeGrade(String operSubjectGuid) {
        HsaControlledGradeState hsaControlledState = new HsaControlledGradeState();
        LocalDateTime now = LocalDateTime.now();
        String guid = guidGeneratorUtil.getStringGuid(HsaControlledGradeState.class.getSimpleName());
        hsaControlledState.setGuid(guid);
        hsaControlledState.setOperSubjectGuid(operSubjectGuid);
        hsaControlledState.setState(EnableEnum.ENABLE.getCode());
        hsaControlledState.setOperatorName(StringConstant.ADMIN);
        hsaControlledState.setGmtCreate(now);
        hsaControlledState.setGmtModified(now);
        //新增
        hsaControlledState.setRoleType(RoleTypeEnum.MEMBER.name());
        hsaControlledState.setName(RoleTypeEnum.MEMBER.getGrade());
        return hsaControlledState;
    }

    @Override
    public boolean updateState(UpdateStateRequest request) {
        final LambdaQueryWrapper<HsaControlledGradeState> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isEmpty(request.getGuid())) {
            wrapper.eq(HsaControlledGradeState::getRoleType, RoleTypeEnum.MEMBER.name());
            wrapper.eq(HsaControlledGradeState::getOperSubjectGuid, request.getOperSubjectGuid());
        } else {
            wrapper.eq(HsaControlledGradeState::getGuid, request.getGuid());
        }
        HsaControlledGradeState hsaControlledState = hsaControlledGradeStateMapper.selectOne(wrapper);
        verifyResource(hsaControlledState);
        hsaControlledState.setState(request.getState());
        hsaControlledState.setOperatorName(ThreadLocalCache.getUserName());
        hsaControlledState.setGmtModified(LocalDateTime.now());
        boolean isUpdate = this.updateByGuid(hsaControlledState);
        //推送到结算台
        settlementMemberSupport.sendGradeMemberDiscount(hsaControlledState.getOperSubjectGuid(), hsaControlledState.getRoleType(), hsaControlledState.getState());
        if (hsaControlledState.getState() == EnableEnum.ENABLE.getCode()) {
            SendMemberGradeChangeEvent event = getSendMemberGradeChangeEvent(hsaControlledState);
            return memberGradeChangeEvent.send(event);
        } else {
            return isUpdate;
        }
    }

    @Override
    public List<String> getEnableRoleType(String operSubjectGuid) {
        List<HsaControlledGradeState> gradeStateList = hsaControlledGradeStateMapper.selectList(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaControlledGradeState::getState, EnableEnum.ENABLE.getCode())
                .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        return gradeStateList.stream().map(HsaControlledGradeState::getRoleType).collect(Collectors.toList());
    }

    @Override
    public boolean isNotEnableByRole(String operSubjectGuid, String roleType) {
        HsaControlledGradeState hsaControlledGradeState = hsaControlledGradeStateMapper.selectOne(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaControlledGradeState::getRoleType, roleType)
                .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())

        );
        return hsaControlledGradeState.getState() == EnableEnum.NOT_ENABLE.getCode();
    }

    @Override
    public boolean isNotEnable(String operSubjectGuid) {
        return isNotEnableByRole(operSubjectGuid, RoleTypeEnum.MEMBER.name());
    }

    @Override
    public boolean isNotEnable() {
        return isNotEnable(ThreadLocalCache.getOperSubjectGuid());
    }

    private SendMemberGradeChangeEvent getSendMemberGradeChangeEvent(HsaControlledGradeState hsaControlledState) {
        SendMemberGradeChangeEvent event = new SendMemberGradeChangeEvent();
        event.setOperSubjectGuid(hsaControlledState.getOperSubjectGuid());
        event.setMemberGuidList(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, hsaControlledState.getOperSubjectGuid())).stream()
                .map(HsaOperationMemberInfo::getGuid)
                .collect(Collectors.toList()));
        event.setSourceType(SourceTypeEnum.ADD_BACKGROUND.getCode());
        event.setIsRefresh(BooleanEnum.FALSE.getCode());
        event.setRoleType(hsaControlledState.getRoleType());
        log.info("等级变更消息体：{}", event);
        return event;
    }

    @Override
    public ControlledGradeStateVO getInfo(String operSubjectGuid) {
        HsaControlledGradeState hsaControlledState = hsaControlledGradeStateMapper.selectOne(
                new LambdaQueryWrapper<HsaControlledGradeState>()
                        .eq(HsaControlledGradeState::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaControlledGradeState::getRoleType, RoleTypeEnum.MEMBER.name())
        );
        if (Objects.isNull(hsaControlledState)) {
            log.error("运营主体：{} 未查询到会员等级启动状态，请查看是否初始化成功", operSubjectGuid);
            throw new MemberBaseException(MemberAccountExceptionEnum.RESOURCE_NOT_EXIST);
        }
        ControlledGradeStateVO controlledStateVO = new ControlledGradeStateVO();
        BeanUtils.copyProperties(hsaControlledState, controlledStateVO);
        return controlledStateVO;
    }

    private void verifyResource(HsaControlledGradeState hsaControlledState) {
        if (Objects.isNull(hsaControlledState)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.RESOURCE_NOT_EXIST);
        }
    }
}
