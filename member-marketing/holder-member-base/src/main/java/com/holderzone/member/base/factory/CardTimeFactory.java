package com.holderzone.member.base.factory;

import com.holderzone.member.base.service.card.CardValidityType;
import com.holderzone.member.base.service.card.impl.CardCollectionDay;
import com.holderzone.member.base.service.card.impl.CardFixedTime;
import com.holderzone.member.base.service.card.impl.CardPermanent;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;

public class  CardTimeFactory {
     public static final int VALIDITY_PERIOD_TYPE_ALWAYS = 0 ; //永久有效
     public static final int VALIDITY_PERIOD_TYPE_DAY = 1 ; //领取之日起
     public static final int VALIDITY_PERIOD_TYPE_YEAR = 2 ; //固定时期

     public CardValidityType timeHandler(int phoneType) {
          switch (phoneType){
               case VALIDITY_PERIOD_TYPE_ALWAYS:  //永久有效
                    return new CardPermanent();
               case VALIDITY_PERIOD_TYPE_DAY:     //领取之日起
                    return new CardCollectionDay();
               case VALIDITY_PERIOD_TYPE_YEAR:    //固定时间
                    return new CardFixedTime();
               default:
                    throw new MemberBaseException(MemberAccountExceptionEnum.CARD_TIME_TYPE_DOES_NOT_EXIST);
          }
     }
}
