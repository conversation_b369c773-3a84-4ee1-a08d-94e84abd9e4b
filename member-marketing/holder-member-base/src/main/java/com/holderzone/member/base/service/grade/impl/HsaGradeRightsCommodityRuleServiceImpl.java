package com.holderzone.member.base.service.grade.impl;

import com.holderzone.member.base.entity.grade.HsaGradeRightsCommodityRule;
import com.holderzone.member.base.mapper.grade.HsaGradeRightsCommodityRuleMapper;
import com.holderzone.member.base.service.grade.HsaGradeRightsCommodityRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * @program: member-marketing
 * @author: rw
 * @create: 2022-01-18 18:22
 */
@Slf4j
@Service
public class HsaGradeRightsCommodityRuleServiceImpl extends HolderBaseServiceImpl<HsaGradeRightsCommodityRuleMapper,
        HsaGradeRightsCommodityRule> implements HsaGradeRightsCommodityRuleService {

}
