package com.holderzone.member.base.service.credit.impl;

import com.holderzone.member.base.entity.credit.HasCancellationOrder;
import com.holderzone.member.base.mapper.credit.HasCancellationOrderMapper;
import com.holderzone.member.base.service.credit.HasCancellationOrderService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-21 09:27
 */
@Slf4j
@Service
public class HasCancellationOrderServiceImpl extends HolderBaseServiceImpl<HasCancellationOrderMapper, HasCancellationOrder>
        implements HasCancellationOrderService {


}
