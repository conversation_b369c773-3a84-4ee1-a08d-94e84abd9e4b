package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.card.HsaPhysicalCardMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.card.HsaPhysicalCardService;
import com.holderzone.member.base.transform.card.HsaPhysicalCardTransform;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.FileConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.BindMemberAccountDTO;
import com.holderzone.member.common.dto.card.HsaPhysicalCardDTO;
import com.holderzone.member.common.dto.card.MemberBindPhysicalCardSecretDTO;
import com.holderzone.member.common.dto.card.MemberBindPhysicalQO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.BindPhysicalCardQO;
import com.holderzone.member.common.qo.card.FreezeOrThawQO;
import com.holderzone.member.common.qo.card.MemberCardExcelQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/9/1
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaPhysicalCardServiceImpl extends HolderBaseServiceImpl<HsaPhysicalCardMapper, HsaPhysicalCard> implements HsaPhysicalCardService {

    private final HsaPhysicalCardMapper physicalCardMapper;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    private final SystemRoleHelper systemRoleHelper;

    @Override
    public void saveBind(List<HsaPhysicalCardDTO> cardInfo2DTOs) {
        if (CollUtil.isEmpty(cardInfo2DTOs)) {
            return;
        }
        this.saveBatch(HsaPhysicalCardTransform.INSTANCE.dTO2Entity(cardInfo2DTOs));
    }

    @Override
    public void uploadCardSecret(String recordGuid) {
        //查询所有此类型卡
        List<HsaPhysicalCard> list = physicalCardMapper
                .selectList(new LambdaQueryWrapper<HsaPhysicalCard>().eq(HsaPhysicalCard::getMakePhysicalCardRecordGuid, recordGuid));
        //cardGuid：用于查询当前会员卡名称
        String cardGuid = list.stream().filter(ObjectUtil::isNotNull).map(HsaPhysicalCard::getCardGuid).findFirst().orElse(null);
        //未绑定的数据
        List<HsaPhysicalCard> notBindList = list.stream()
                .filter(obj -> ObjectUtil.notEqual(BooleanEnum.TRUE.getCode(), obj.getMemberBindingState())).collect(Collectors.toList());
        List<MemberBindPhysicalCardSecretDTO> notBindDTOList = HsaPhysicalCardTransform.INSTANCE.entities2ExcelDTO(notBindList);
        //已绑定的数据
        List<String> bindCardNumList = list.stream()
                .filter(obj -> ObjectUtil.equal(BooleanEnum.TRUE.getCode(), obj.getMemberBindingState()))
                .map(HsaPhysicalCard::getCardNum)
                .collect(Collectors.toList());
        //去查询已绑定会员的数据
        //(需求修改-已绑定会员数据和未绑定会员数据融合一起)
        List<MemberBindPhysicalCardSecretDTO> bindList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bindCardNumList)) {
            bindList = physicalCardMapper.listBindMemberByCardGuidAndCardNum(recordGuid, bindCardNumList);
        }
        bindList.addAll(notBindDTOList);
        //设置导出的卡的卡名称信息
        setCardName(bindList, cardGuid);
        //去构建需要导出的excel
        toExport(bindList);
    }

    @Override
    public PageResult getUploadCardSecret(MemberCardExcelQO memberCardExcelQO) {
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(HsaCardBaseInfo::getOperSubjectGuid, memberCardExcelQO.getOperSubjectGuid())
                .eq(HsaCardBaseInfo::getCardName, memberCardExcelQO.getCardName()));
        //查询所有此类型卡
        List<HsaPhysicalCard> list = physicalCardMapper
                .selectList(new LambdaQueryWrapper<HsaPhysicalCard>().eq(HsaPhysicalCard::getCardGuid, hsaCardBaseInfo.getGuid()));
        if (CollUtil.isEmpty(list))
            PageUtil.getPageResult(new PageInfo<>(list));
        //cardGuid：用于查询当前会员卡名称
        String cardGuid = list.stream().filter(ObjectUtil::isNotNull).map(HsaPhysicalCard::getCardGuid).findFirst().orElse(null);
        PageHelper.startPage(memberCardExcelQO.getCurrentPage(), memberCardExcelQO.getPageSize());
        List<MemberBindPhysicalCardSecretDTO> cardSecretDTOS = physicalCardMapper.listBindMemberByCardGuidPage(cardGuid);
        if (CollUtil.isEmpty(cardSecretDTOS))
            PageUtil.getPageResult(new PageInfo<>(cardSecretDTOS));

        List<MemberBindPhysicalQO> memberBindPhysicalQOS = Lists.newArrayList();
        List<String> cardNum = cardSecretDTOS.stream().filter(in -> StringUtils.isNotBlank(in.getPhoneNum())).map(MemberBindPhysicalCardSecretDTO::getCardNum).collect(Collectors.toList());
        List<String> phoneNum = cardSecretDTOS.stream().filter(in -> StringUtils.isNotBlank(in.getPhoneNum())).map(MemberBindPhysicalCardSecretDTO::getPhoneNum).collect(Collectors.toList());
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = new HashMap<>();
        if (CollUtil.isNotEmpty(cardNum)) {
            hsaMemberInfoCardMap = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getOperSubjectGuid, memberCardExcelQO.getOperSubjectGuid())
                    .in(HsaMemberInfoCard::getPhysicalCardNum, cardNum))
                    .stream()
                    .collect(Collectors.toMap(HsaMemberInfoCard::getPhysicalCardNum, Function.identity(), (entity1, entity2) -> entity1));
        }
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(phoneNum)) {
            hsaOperationMemberInfoMap = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, memberCardExcelQO.getOperSubjectGuid())
                    .in(HsaOperationMemberInfo::getPhoneNum, phoneNum))
                    .stream()
                    .collect(Collectors.toMap(HsaOperationMemberInfo::getPhoneNum, Function.identity(), (entity1, entity2) -> entity1));
        }

        for (MemberBindPhysicalCardSecretDTO cardSecretDTO : cardSecretDTOS) {
            MemberBindPhysicalQO memberBindPhysicalQO = new MemberBindPhysicalQO();
            BeanUtils.copyProperties(cardSecretDTO, memberBindPhysicalQO);
            if (CollUtil.isNotEmpty(hsaMemberInfoCardMap) && hsaMemberInfoCardMap.containsKey(cardSecretDTO.getCardNum())) {
                HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMap.get(cardSecretDTO.getCardNum());
                memberBindPhysicalQO.setBalance(hsaMemberInfoCard.getCardAmount().add(hsaMemberInfoCard.getGiftAmount()).add(hsaMemberInfoCard.getSubsidyAmount()));
                memberBindPhysicalQO.setCardStatus(1);
            }

            if (CollUtil.isNotEmpty(hsaOperationMemberInfoMap) && hsaOperationMemberInfoMap.containsKey(cardSecretDTO.getPhoneNum())) {
                HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(cardSecretDTO.getPhoneNum());
                memberBindPhysicalQO.setUserName(hsaOperationMemberInfo.getUserName());
                memberBindPhysicalQO.setPhoneNum(hsaOperationMemberInfo.getPhoneNum());
            }
            memberBindPhysicalQO.setCardName(hsaCardBaseInfo.getCardName());
            memberBindPhysicalQO.setExpiryDate("永久有效");
            memberBindPhysicalQOS.add(memberBindPhysicalQO);
        }
        return PageUtil.getPageResult(new PageInfo<>(memberBindPhysicalQOS));
    }

    /**
     * 设置导出的卡的卡名称信息
     *
     * @param bindList 绑定卡信息
     */
    private void setCardName(List<MemberBindPhysicalCardSecretDTO> bindList, String cardGuid) {
        if (ObjectUtil.isNull(bindList) || bindList.isEmpty()) {
            return;
        }
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .eq(HsaCardBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .in(HsaCardBaseInfo::getGuid, cardGuid));
        String cardName = Optional.ofNullable(hsaCardBaseInfo).map(HsaCardBaseInfo::getCardName).orElse("");
        for (MemberBindPhysicalCardSecretDTO memberBindPhysicalCardSecretDTO : bindList) {
            memberBindPhysicalCardSecretDTO.setCardName(cardName);
            String phone = memberBindPhysicalCardSecretDTO.getPhoneNum();
            String phoneCountryCode = memberBindPhysicalCardSecretDTO.getPhoneCountryCode();
            //给导出的手机号  +区号
            String phoneNum = StringBaseHandlerUtil.phoneCountryCodeHandler(phoneCountryCode, phone);
            memberBindPhysicalCardSecretDTO.setPhoneNum(phoneNum);
        }
    }

    @Override
    public void freezeOrThawCard(FreezeOrThawQO qo) {
        log.info("【冻结或者解冻实体卡】，参数：{}", qo);
        physicalCardMapper.freezeOrThawCard(qo.getOwnGuidList(), qo.getStatus());
    }

    @Override
    public void inactiveCardFreezeOrThawCard(String guid, Integer status) {
        physicalCardMapper.inactiveCardFreezeOrThawCard(guid, status);
    }

    @Override
    public String bindCard(BindPhysicalCardQO qo) {
        HsaPhysicalCard physicalCard = this.getOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getCardNum, qo.getCardNum())
                .eq(HsaPhysicalCard::getCardBindingNum, qo.getCardBindingNum()));
        if (ObjectUtil.isNull(physicalCard)) {
            throw new MemberBaseException(CardOperationExceptionEnum.PHYSICAL_CARD_NULL);
        }
        if (ObjectUtil.equal(BooleanEnum.TRUE.getCode(), physicalCard.getMemberBindingState())) {
            throw new MemberBaseException(CardOperationExceptionEnum.PHYSICAL_CARD_HAS_BIND);
        }
        if (ObjectUtil.notEqual(qo.getCardGuid(), physicalCard.getCardGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.PHYSICAL_CARD_NOT_BELONG_MEMBER_CARD,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        physicalCardMapper.updateMemberBind(qo.getOwnGuid(), physicalCard.getGuid());
        return physicalCard.getGuid();

    }

    @Override
    public void bindAccount(BindMemberAccountDTO memberAccountDTO) {
        physicalCardMapper.updateBindAccount(memberAccountDTO);
    }

    @Override
    public Integer findCardStatus(String guid) {
        return physicalCardMapper.findCardStatus(guid);
    }

    private void toExport(List<MemberBindPhysicalCardSecretDTO> bindList) {
        try {
            HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
            assert response != null;
            response.setContentType("application/octet-stream;charset=utf-8");
            String fileName = FileConstant.UPLOAD_PHYSICAL_CARD_SECRET_NAME + DateUtil.formatLocalDateTime(LocalDateTime.now()
                    , DateUtil.DATE_PATTERN) + ExcelTypeEnum.XLSX.getValue();
            response.setHeader("content-disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();

            WriteSheet bindWriteSheet = EasyExcelFactory.writerSheet(1, FileConstant.DEFAULT_SHEET).head(MemberBindPhysicalCardSecretDTO.class).build();
            excelWriter.write(bindList, bindWriteSheet);

            excelWriter.finish();
        } catch (Exception e) {
            log.warn(CardOperationExceptionEnum.EXPORT_PHYSICAL_CARD_SECRET_FAIL.getDes());
        }
    }

    @Override
    public Integer getCardNumCountLike(String currentDate, String operSubjectGuid) {
        return physicalCardMapper.getCardNumCountLike(currentDate, operSubjectGuid);
    }
}
