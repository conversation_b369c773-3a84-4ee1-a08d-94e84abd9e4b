package com.holderzone.member.base.event;

import com.holderzone.member.base.event.domain.ConsumptionOrderEvent;
import com.holderzone.member.base.event.domain.ConsumptionOrderEventEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ConsumptionOrderPublisher {

    private final ApplicationContext applicationContext;

    public ConsumptionOrderPublisher(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public void publish(ConsumptionOrderEventEnum type, String content) {
        applicationContext.publishEvent(new ConsumptionOrderEvent(this, type, content));
    }
}
