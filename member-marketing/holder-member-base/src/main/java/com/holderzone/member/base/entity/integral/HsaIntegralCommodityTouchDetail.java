package com.holderzone.member.base.entity.integral;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单积分任务商品触发次数、数量累计表
 */
@Data
@Accessors(chain = true)
@TableName("hsa_integral_commodity_touch_detail")
public class HsaIntegralCommodityTouchDetail implements Serializable {

    private static final long serialVersionUID = 8391262387191210803L;

    /**
     * guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 积分任务guid
     */
    private String integralTaskGuid;

    /**
     * 指定商品赠送成长值类型 0：购买次数 1:购买数量 2：购买周期
     *
     * @see com.holderzone.member.common.enums.growth.BuyTypeEnum
     */
    private Integer buyType;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 已完成数量  单位：购买次数 次 ，购买数量 件 ，购买周期 天/周
     */
    private Integer number;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单消费时间
     */
    private LocalDateTime orderTime;

    /**
     * 此记录是否已经触发过 1 是 0 否
     */
    private Integer isDelete;

}
