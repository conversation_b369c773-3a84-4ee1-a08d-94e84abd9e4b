package com.holderzone.member.base.entity.integral;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 会员积分抵现适用商品历史
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaIntegralDeductCommodityHistory extends HsaIntegralDeductCommodity implements Serializable {

    

}
