package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.member.HsaKinsfolkManagement;
import com.holderzone.member.base.mapper.member.HsaKinsfolkManagementMapper;
import com.holderzone.member.base.service.base.HsaApplyDictionariesService;
import com.holderzone.member.base.service.member.HsaKinsfolkManagementService;
import com.holderzone.member.base.util.BaiDuYunUtil;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.BaiduFaceEntryDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.member.HsaKinsfolkManagementQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.card.KinsfolkManagementVO;
import com.holderzone.member.common.vo.member.HsaKinsfolkManagementVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;


/**
 * @description: 亲属管理 serviceImpl
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaKinsfolkManagementServiceImpl extends HolderBaseServiceImpl<HsaKinsfolkManagementMapper,HsaKinsfolkManagement>
        implements HsaKinsfolkManagementService {

    @Resource
    private HsaKinsfolkManagementMapper hsaKinsfolkManagementMapper;

    @Resource
    private HsaApplyDictionariesService hsaApplyDictionariesService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Resource
    private BaiDuYunUtil baiDuYunUtil;


    @Override
    public List<HsaKinsfolkManagementVO> getListByMemberGuid(String memberInfoGuid) {
        List<HsaKinsfolkManagement> hsaKinsfolkManagements = hsaKinsfolkManagementMapper.selectList(
                new LambdaQueryWrapper<HsaKinsfolkManagement>()
                        .eq(HsaKinsfolkManagement::getMemberInfoGuid, memberInfoGuid)
                        .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaKinsfolkManagement::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (CollUtil.isEmpty(hsaKinsfolkManagements)) {
            return null;
        }
        List<HsaKinsfolkManagementVO> kinsfolkManagementVOList = new ArrayList<>();
        for (HsaKinsfolkManagement hsaKinsfolkManagement : hsaKinsfolkManagements) {
            HsaKinsfolkManagementVO hsaKinsfolkManagementVO = new HsaKinsfolkManagementVO();
            BeanUtils.copyProperties(hsaKinsfolkManagement,hsaKinsfolkManagementVO);

            String faceToken = hsaKinsfolkManagement.getFaceToken();
            String logId = hsaKinsfolkManagement.getLogId();
            String userId = hsaKinsfolkManagement.getUserId();
            hsaKinsfolkManagementVO.setIsFaceEntry(Boolean.FALSE);
            if (StringUtils.isNotBlank(faceToken) && StringUtils.isNotBlank(logId) && StringUtils.isNotBlank(userId)) {
                hsaKinsfolkManagementVO.setIsFaceEntry(Boolean.TRUE);
            }
            kinsfolkManagementVOList.add(hsaKinsfolkManagementVO);
        }
        return kinsfolkManagementVOList;
    }

    @Override
    public KinsfolkManagementVO getByFaceUserId(String userId){
        final HsaKinsfolkManagement hsaKinsfolkManagement = hsaKinsfolkManagementMapper.selectOne(
                new LambdaQueryWrapper<HsaKinsfolkManagement>()
                        .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaKinsfolkManagement::getUserId, userId));
        if(Objects.isNull(hsaKinsfolkManagement)){
            return null;
        }
        KinsfolkManagementVO kinsfolkManagementVO = new KinsfolkManagementVO();
        BeanUtils.copyProperties(hsaKinsfolkManagement, kinsfolkManagementVO);
        return kinsfolkManagementVO;
    }

    @Override
    public List<KinsfolkManagementVO> listByPhone(String phoneNum){
        if (StringUtils.isBlank(phoneNum)) {
            return Collections.emptyList();
        }
        final List<HsaKinsfolkManagement> managementList = hsaKinsfolkManagementMapper.selectList(
                new LambdaQueryWrapper<HsaKinsfolkManagement>()
                        .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaKinsfolkManagement::getPhoneNum, phoneNum));
        if(CollUtil.isEmpty(managementList)){
            return Collections.emptyList();
        }
        List<KinsfolkManagementVO> managementVOList = new ArrayList<>(managementList.size());
        managementList.forEach(m -> {
            KinsfolkManagementVO kinsfolkManagementVO = new KinsfolkManagementVO();
            BeanUtils.copyProperties(m, kinsfolkManagementVO);
            managementVOList.add(kinsfolkManagementVO);
        });
        return managementVOList;
    }

    @Override
    public Boolean deleteKinsfolkByGuid(String guid) {
        HsaKinsfolkManagement hsaKinsfolkManagement = hsaKinsfolkManagementMapper.selectOne(
                new LambdaQueryWrapper<HsaKinsfolkManagement>()
                        .eq(HsaKinsfolkManagement::getGuid,guid)
                        .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaKinsfolkManagement::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (Objects.isNull(hsaKinsfolkManagement)) {
            return Boolean.FALSE;
        }
        boolean result = hsaKinsfolkManagementMapper.deleteById(hsaKinsfolkManagement) > 0;

        String faceToken = hsaKinsfolkManagement.getFaceToken();
        if (StringUtils.isNotBlank(faceToken)) {
            // 删除百度云 ---人脸识别库中的当前用户人脸信息
            memberBaseThreadExecutor.execute(()-> faceToDelete(hsaKinsfolkManagement));
        }
        return result;
    }

    /**
     * 删除人脸识别库中的人脸信息
     * @param kinsfolkInfo 亲属信息对象
     */
    private void faceToDelete(HsaKinsfolkManagement kinsfolkInfo) {
        String logId = kinsfolkInfo.getLogId();
        String userId = kinsfolkInfo.getUserId();
        if (StringUtils.isEmpty(logId) || StringUtils.isEmpty(userId)) {
            log.error("faceToDelete方法中,核心参数为空 logId：{} ,userId:{}",logId,userId);
            return ;
        }
       baiDuYunUtil.faceToDelete(kinsfolkInfo.getLogId(),kinsfolkInfo.getUserId(),kinsfolkInfo.getGroupId(),kinsfolkInfo.getFaceToken());
    }

    @Override
    public Boolean saveOrUpdateInfo(HsaKinsfolkManagementQO kinsfolkManagementQO) {
        paramVerify(kinsfolkManagementQO);
        String guid = kinsfolkManagementQO.getGuid();
        if (StringUtils.isNotBlank(guid)) {
            HsaKinsfolkManagement hsaKinsfolkManagement = hsaKinsfolkManagementMapper.selectOne(
                    new LambdaQueryWrapper<HsaKinsfolkManagement>()
                            .eq(HsaKinsfolkManagement::getGuid,guid)
                            .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaKinsfolkManagement::getIsDelete, BooleanEnum.FALSE.getCode()));
            if (Objects.isNull(hsaKinsfolkManagement)) {
                return Boolean.FALSE;
            }
            BeanUtils.copyProperties(kinsfolkManagementQO,hsaKinsfolkManagement);
            hsaKinsfolkManagement.setGmtModified(LocalDateTime.now());
            return this.updateByGuid(hsaKinsfolkManagement);
        } else {
            HsaKinsfolkManagement hsaKinsfolkManagement = new HsaKinsfolkManagement();
            BeanUtils.copyProperties(kinsfolkManagementQO,hsaKinsfolkManagement);
            hsaKinsfolkManagement.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            hsaKinsfolkManagement.setGuid(guidGeneratorUtil.getStringGuid(HsaKinsfolkManagement.class.getSimpleName()));
            hsaKinsfolkManagement.setIsDelete(BooleanEnum.FALSE.getCode());
            if (StringUtils.isNotBlank(kinsfolkManagementQO.getImage()) && StringUtils.isNotBlank(kinsfolkManagementQO.getUserId())) {
                faceEntry(kinsfolkManagementQO, hsaKinsfolkManagement);
            }
            return this.save(hsaKinsfolkManagement);
        }
    }

    private void faceEntry(HsaKinsfolkManagementQO kinsfolkManagementQO, HsaKinsfolkManagement hsaKinsfolkManagement) {
        BaiduFaceEntryDTO baiduFaceEntryDTO = baiDuYunUtil.faceEntry(kinsfolkManagementQO.getUserId(), kinsfolkManagementQO.getGroupId(), kinsfolkManagementQO.getImage());
        if (Objects.isNull(baiduFaceEntryDTO)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FACE_ENTRY_ERROR);
        }
        Map<String, Object> resultMap = baiduFaceEntryDTO.getResult();
        if (Objects.isNull(resultMap) || !resultMap.containsKey(StringConstant.FACE_TOKEN)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FACE_ENTRY_ERROR);
        }

        hsaKinsfolkManagement.setLogId(baiduFaceEntryDTO.getLogId());
        hsaKinsfolkManagement.setFaceToken(resultMap.get(StringConstant.FACE_TOKEN).toString());
    }

    public void paramVerify(HsaKinsfolkManagementQO request) {
        if (StringUtils.isEmpty(request.getGuid())) {
            // 最多新增20个亲属
            Integer count = hsaKinsfolkManagementMapper.selectCount(
                    new LambdaQueryWrapper<HsaKinsfolkManagement>()
                            .eq(HsaKinsfolkManagement::getMemberInfoGuid, request.getMemberInfoGuid())
                            .eq(HsaKinsfolkManagement::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaKinsfolkManagement::getIsDelete, BooleanEnum.FALSE.getCode()));
            if (Objects.nonNull(count) && count >= NumberConstant.NUMBER_20) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_KINSFOLK_COUNT_ERROR);
            }
        }
    }

}
