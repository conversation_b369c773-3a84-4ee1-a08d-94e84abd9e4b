package com.holderzone.member.base.entity.integral;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023/6/2 下午6:36
 * @description 消费指定分类记录表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName(value = "hsa_integral_commodity_classify")
public class HsaIntegralCommodityClassify implements Serializable {

    private static final long serialVersionUID = -8535015742973398001L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String storeName;

    private String taskGuid;

    private String strategyName;

    private String storeGuid;

    private String strategyId;

    private String goodsCategory;

    private Integer businessType;

}
