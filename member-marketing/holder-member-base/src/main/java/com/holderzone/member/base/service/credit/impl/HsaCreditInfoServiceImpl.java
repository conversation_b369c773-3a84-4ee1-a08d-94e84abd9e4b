package com.holderzone.member.base.service.credit.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.credit.*;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.base.mapper.credit.*;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.assembler.MerchantPayAssembler;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.base.service.credit.HsaCreditUserService;
import com.holderzone.member.base.service.member.MemberExcelService;
import com.holderzone.member.base.service.system.HsaStoreRuleInfoService;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.StoreTypeEnum;
import com.holderzone.member.common.enums.credit.SettlePeriodEnum;
import com.holderzone.member.common.enums.credit.WalletChangeTypeEnum;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.RequestMemberCardPayVO;
import com.holderzone.member.common.qo.credit.*;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.credit.*;
import com.holderzone.member.common.vo.excel.ExcelResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;
import static com.holderzone.member.common.constant.NumberConstant.NUMBER_1;

/**
 * @program: member-marketing
 * @description: 挂账server
 * @author: pan tao
 * @create: 2022-02-09 11:44
 */
@Slf4j
@Service
public class HsaCreditInfoServiceImpl extends HolderBaseServiceImpl<HsaCreditInfoMapper, HsaCreditInfo> implements
        HsaCreditInfoService {

    /**
     * guid 生成工具类
     */
    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Autowired
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    @Autowired
    @Lazy
    private HsaStoreRuleInfoService hsaStoreRuleInfoService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private HsaCreditUserMapper hsaCreditUserMapper;

    @Resource
    private HsaCreditFundingDetailMapper hsaCreditFundingDetailMapper;

    @Resource
    private HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper;

    @Autowired
    @Lazy
    private HsaCreditUserService hsaCreditUserService;

    @Resource
    private HsaCreditInfoMapper hsaCreditInfoMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Autowired
    @Lazy
    private HsaCreditInfoService hsaCreditInfoService;

    private static final String G = "G";

    /**
     * 过期
     */
    private static final int OVERDUE = 2;

    @Autowired
    @Lazy
    private MemberExcelService memberExcelService;

    @Resource
    private HsaCreditWalletLogMapper hsaCreditWalletLogMapper;

    @Autowired
    @Lazy
    private HsaCardInfoService hsaCardInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "SAVE_OR_UPDATE_CREDIT", leaseTime = 10, tryLock = true)
    public boolean saveOrUpdateCredit(CreditInfoQO creditInfoQO) {
        if (!NumberUtil.isPhoneNum11(creditInfoQO.getLinkmanPhone())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.PHONE_ERROR);
        }
        // 用户挂账金额校验
        creditAmountVerify(creditInfoQO);
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaCreditInfo hsaCreditInfo;
        //新增
        if (StringUtils.isEmpty(creditInfoQO.getGuid())) {
            checkName(creditInfoQO);
            hsaCreditInfo = new HsaCreditInfo();
            hsaCreditInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditInfo.class.getSimpleName()));
            hsaCreditInfo.setCreditNumber(G + guidGeneratorUtil.getCode(hsaCreditInfo.getGuid()));
            hsaCreditInfo.setGmtCreate(LocalDateTime.now());
            hsaCreditInfo.setIsDelete(BooleanEnum.FALSE.getCode());
            hsaCreditInfo.setIsEnable(EnableEnum.ENABLE.getCode());
            hsaCreditInfo.setStoredAmount(creditInfoQO.getStoredAmount());
            hsaCreditInfo.setCreditWallet(creditInfoQO.getStoredAmount());
            //预存余额不为0
            if (!BigDecimalUtil.equelZero(BigDecimalUtil.nonNullValue(creditInfoQO.getStoredAmount()))) {
                HsaCreditWalletLog hsaCreditWalletLog = toHsaCreditWalletLog(creditInfoQO, headerUserInfo, hsaCreditInfo.getGuid());
                hsaCreditWalletLogMapper.insert(hsaCreditWalletLog);
            }
        } else {
            hsaCreditInfo = this.queryByGuid(creditInfoQO.getGuid());
            if (!hsaCreditInfo.getCreditAccountName().equals(creditInfoQO.getCreditAccountName())) {
                checkName(creditInfoQO);
            }
            //删除之前所有适用门店
            hsaStoreRuleInfoMapper.delete(new LambdaQueryWrapper<HsaStoreRuleInfo>().
                    eq(HsaStoreRuleInfo::getTypeGuid, creditInfoQO.getGuid()));
        }
        setHsaCreditInfo(creditInfoQO, hsaCreditInfo, headerUserInfo);
        //添加适用门店
        addApplyStore(creditInfoQO.getCreditApplyStoreList(), hsaCreditInfo.getGuid(), hsaCreditInfo.getOperSubjectGuid());
        //更新的挂账使用人
        editCreditUser(creditInfoQO.getCreditUserList(), hsaCreditInfo.getGuid());
        this.saveOrUpdate(hsaCreditInfo);
        return true;
    }

    private static void setHsaCreditInfo(CreditInfoQO creditInfoQO, HsaCreditInfo hsaCreditInfo, HeaderUserInfo headerUserInfo) {
        hsaCreditInfo.setCreditAccountName(creditInfoQO.getCreditAccountName());
        hsaCreditInfo.setCreditType(creditInfoQO.getCreditType());
        hsaCreditInfo.setCompanyName(creditInfoQO.getCompanyName());
        hsaCreditInfo.setAreaCode(creditInfoQO.getAreaCode());
        hsaCreditInfo.setLinkmanPhone(creditInfoQO.getLinkmanPhone());
        hsaCreditInfo.setCreditProve(JSONObject.toJSONString(creditInfoQO.getCreditProve()));
        hsaCreditInfo.setCreditLimitedSet(creditInfoQO.getCreditLimitedSet());
        hsaCreditInfo.setCreditLimitedAmount(creditInfoQO.getCreditLimitedAmount());
        hsaCreditInfo.setApplicableAllStore(creditInfoQO.getApplicableAllStore());
        hsaCreditInfo.setAccountValidity(creditInfoQO.getAccountValidity());
        hsaCreditInfo.setAccountValidityDate(creditInfoQO.getAccountValidityDate());
        hsaCreditInfo.setSettlePeriod(creditInfoQO.getSettlePeriod());
        hsaCreditInfo.setSettlePeriodType(creditInfoQO.getSettlePeriodType());
        hsaCreditInfo.setSettlePeriodDate(creditInfoQO.getSettlePeriodDate());
        hsaCreditInfo.setGmtModified(LocalDateTime.now());
        hsaCreditInfo.setOperatorName(headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel());
        hsaCreditInfo.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        hsaCreditInfo.setReceiverCompanyName(headerUserInfo.getEnterpriseName());
    }

    private HsaCreditWalletLog toHsaCreditWalletLog(CreditInfoQO creditInfoQO, HeaderUserInfo headerUserInfo,
                                                    String creditInfoGuid) {
        HsaCreditWalletLog hsaCreditWalletLog = new HsaCreditWalletLog();
        hsaCreditWalletLog.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditWalletLog.class.getSimpleName()));
        hsaCreditWalletLog.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        hsaCreditWalletLog.setCreditInfoGuid(creditInfoGuid);
        hsaCreditWalletLog.setChangeType(WalletChangeTypeEnum.DEPOSIT_BALANCE.getCode());
        hsaCreditWalletLog.setChangeAmount(creditInfoQO.getStoredAmount());
        hsaCreditWalletLog.setCreditWalletBalance(creditInfoQO.getStoredAmount());
        hsaCreditWalletLog.setChangeSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaCreditWalletLog.setOperatorName(headerUserInfo.getUserName() + StringConstant.STR_BIAS +
                headerUserInfo.getTel());
        hsaCreditWalletLog.setChangeAmountType(NumberConstant.NUMBER_0);
        hsaCreditWalletLog.setGmtCreate(LocalDateTime.now());
        hsaCreditWalletLog.setGmtModified(LocalDateTime.now());
        return hsaCreditWalletLog;
    }

    /**
     * 用户挂账金额校验
     * 1.用户挂账金额总和不能大于挂账上限金额
     *
     * @param creditInfoQO 请求参数
     */
    private void creditAmountVerify(CreditInfoQO creditInfoQO) {
        List<CreditUserQO> creditUserList = creditInfoQO.getCreditUserList();
        if (CollUtil.isEmpty(creditUserList)) {
            return;
        }
        Integer creditLimitedSet = creditInfoQO.getCreditLimitedSet();
        if (Objects.nonNull(creditLimitedSet) && BooleanEnum.FALSE.getCode() == creditLimitedSet) {
            return;
        }
        // creditLimitedAmount：挂账上限金额
        BigDecimal creditLimitedAmount = BigDecimalUtil.nonNullValue(creditInfoQO.getCreditLimitedAmount());
        // sum：用户挂账金额总和
        BigDecimal sum = creditUserList.stream()
                .map(CreditUserQO::getSinglePersonUpperLimit)
                .filter(BigDecimalUtil::equelZero)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (BigDecimalUtil.lessThan(sum, creditLimitedAmount)) {
            return;
        }
        // 用户挂账金额总和不能大于挂账上限金额
        throw new MemberBaseException(MemberAccountExceptionEnum.AMOUNT_OF_OVERFLOW_ERROR);
    }

    /**
     * 校验挂账账户名称
     *
     * @param creditInfoQO 新增挂账请求qo
     */
    private void checkName(CreditInfoQO creditInfoQO) {

        Integer count = hsaCreditInfoMapper.selectCount(new LambdaQueryWrapper<HsaCreditInfo>()
                .eq(HsaCreditInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaCreditInfo::getCreditAccountName, creditInfoQO.getCreditAccountName()));

        if (count > NumberConstant.NUMBER_0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CREDIT_ACCOUNT_NAME_REPETITION);
        }
    }

    /**
     * 更新的挂账使用人
     *
     * @param creditUserList 挂账使用人
     * @param creditInfoGuid 挂账guid
     */
    private void editCreditUser(List<CreditUserQO> creditUserList, String creditInfoGuid) {

        if (CollectionUtil.isEmpty(creditUserList)) {
            hsaCreditUserMapper.delete(new LambdaQueryWrapper<HsaCreditUser>()
                    .eq(HsaCreditUser::getCreditInfoGuid, creditInfoGuid));
            return;
        }
        Map<String, HsaCreditUser> map = hsaCreditUserMapper.selectList(new LambdaQueryWrapper<HsaCreditUser>()
                        .eq(HsaCreditUser::getCreditInfoGuid, creditInfoGuid))
                .stream()
                .collect(Collectors.toMap(HsaCreditUser::getMemberInfoGuid, Function.identity(), (obj, obj1) -> obj));
        //需要新增的人
        List<HsaCreditUser> addHsaCreditUsers = new ArrayList<>();
        //需要更新的人
        List<HsaCreditUser> editHsaCreditUsers = new ArrayList<>();
        for (CreditUserQO creditUser : creditUserList) {
            HsaCreditUser hsaCreditUser = map.get(creditUser.getMemberGuid());
            //新增的
            if (Objects.isNull(hsaCreditUser)) {
                addHsaCreditUsers.add(addCreditUser(creditUser, ThreadLocalCache.getOperSubjectGuid(), creditInfoGuid));
                //编辑
            } else {
                hsaCreditUser.setSingleCountUpperLimit(creditUser.getSingleCountUpperLimit());
                hsaCreditUser.setSinglePersonUpperLimit(creditUser.getSinglePersonUpperLimit());
                editHsaCreditUsers.add(hsaCreditUser);
            }
            //移除存在的，剩余的就是删除的
            map.remove(creditUser.getMemberGuid());
        }
        if (CollectionUtil.isNotEmpty(addHsaCreditUsers)) {
            hsaCreditUserService.saveBatch(addHsaCreditUsers);
        }
        if (CollectionUtil.isNotEmpty(editHsaCreditUsers)) {
            hsaCreditUserService.updateBatchById(editHsaCreditUsers);
        }
        //需要删除的
        if (!CollectionUtil.isEmpty(map.keySet())) {
            hsaCreditUserMapper.delete(new LambdaQueryWrapper<HsaCreditUser>()
                    .eq(HsaCreditUser::getCreditInfoGuid, creditInfoGuid)
                    .in(HsaCreditUser::getMemberInfoGuid, map.keySet()));
        }

    }

    /**
     * 新增挂账使用人
     *
     * @param creditUser      挂账使用人
     * @param operSubjectGuid 运营主体guid
     * @param creditInfoGuid  挂账guid
     * @return 操作结果
     */
    private HsaCreditUser addCreditUser(CreditUserQO creditUser, String operSubjectGuid, String creditInfoGuid) {

        HsaCreditUser hsaCreditUser = new HsaCreditUser();
        hsaCreditUser.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditUser.class.getSimpleName()));
        hsaCreditUser.setOperSubjectGuid(operSubjectGuid);
        hsaCreditUser.setCreditInfoGuid(creditInfoGuid);
        hsaCreditUser.setMemberInfoGuid(creditUser.getMemberGuid());
        hsaCreditUser.setSinglePersonUpperLimit(creditUser.getSinglePersonUpperLimit());
        hsaCreditUser.setSingleCountUpperLimit(creditUser.getSingleCountUpperLimit());
        hsaCreditUser.setIsEnable(EnableEnum.ENABLE.getCode());
        hsaCreditUser.setGmtCreate(LocalDateTime.now());
        hsaCreditUser.setGmtModified(LocalDateTime.now());
        return hsaCreditUser;
    }

    /**
     * 添加适用门店
     *
     * @param creditApplyStoreList 门店请求qo
     * @param creditInfoGuid       挂账guid
     * @param operSubjectGuid      运营主体guid
     */
    private void addApplyStore(List<CreditApplyStoreQO> creditApplyStoreList, String creditInfoGuid, String operSubjectGuid) {

        if (CollectionUtil.isEmpty(creditApplyStoreList)) {
            return;
        }
        LocalDateTime time = LocalDateTime.now();
        List<HsaStoreRuleInfo> hsaCreditApplyStores = new ArrayList<>();
        for (CreditApplyStoreQO creditApplyStore : creditApplyStoreList) {
            hsaCreditApplyStores.add(toHsaCreditApplyStore(creditApplyStore, creditInfoGuid, operSubjectGuid, time));
            if (CollectionUtil.isEmpty(creditApplyStore.getStoreBoothCardRuleQOList())) {
                continue;
            }
            //门店下档口数据
            for (CreditApplyStoreQO storeBooth : creditApplyStore.getStoreBoothCardRuleQOList()) {
                HsaStoreRuleInfo applyBooth = toHsaCreditApplyStore(storeBooth, creditInfoGuid,
                        operSubjectGuid, time);
                applyBooth.setParentGuid(creditApplyStore.getStoreGuid());
                hsaCreditApplyStores.add(applyBooth);
            }
        }
        hsaStoreRuleInfoService.saveBatch(hsaCreditApplyStores);
    }

    /**
     * 封装适用门店
     *
     * @param creditApplyStore 门店请求qo
     * @param creditInfoGuid   挂账guid
     * @param operSubjectGuid  运营主体guid
     * @param time             当前时间
     * @return 操作结果
     */
    private HsaStoreRuleInfo toHsaCreditApplyStore(CreditApplyStoreQO creditApplyStore, String creditInfoGuid,
                                                   String operSubjectGuid, LocalDateTime time) {
        HsaStoreRuleInfo hsaCreditApplyStore = new HsaStoreRuleInfo();
        hsaCreditApplyStore.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreRuleInfo.class.getSimpleName()));
        hsaCreditApplyStore.setOperSubjectGuid(operSubjectGuid);
        hsaCreditApplyStore.setStoreGuid(creditApplyStore.getStoreGuid());
        hsaCreditApplyStore.setStoreName(creditApplyStore.getStoreName());
        hsaCreditApplyStore.setStoreNumber(creditApplyStore.getStoreNumber());
        hsaCreditApplyStore.setTime(creditApplyStore.getTime());
        hsaCreditApplyStore.setAddress(creditApplyStore.getAddress());
        hsaCreditApplyStore.setAddressPoint(creditApplyStore.getAddressPoint());
        hsaCreditApplyStore.setType(StoreTypeEnum.CREDIT.getCode());
        hsaCreditApplyStore.setTypeGuid(creditInfoGuid);
        hsaCreditApplyStore.setGmtCreate(time);
        hsaCreditApplyStore.setGmtModified(time);
        return hsaCreditApplyStore;
    }

    @Override
    public CreditInfoDetailVO queryCreditDetail(String creditGuid) {
        if (StringUtils.isEmpty(creditGuid)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        HsaCreditInfo hsaCreditInfo = this.queryByGuid(creditGuid);
        if (Objects.isNull(hsaCreditInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CREDIT_ACCOUNT_NOT_EXIST);
        }
        CreditInfoDetailVO creditInfoVO = getCreditInfoDetailVO(creditGuid, hsaCreditInfo);

        return creditInfoVO;
    }

    private CreditInfoDetailVO getCreditInfoDetailVO(String creditGuid, HsaCreditInfo hsaCreditInfo) {
        CreditInfoDetailVO creditInfoVO = new CreditInfoDetailVO();
        creditInfoVO.setGuid(hsaCreditInfo.getGuid());
        creditInfoVO.setCreditNumber(hsaCreditInfo.getCreditNumber());
        creditInfoVO.setOperSubjectGuid(hsaCreditInfo.getOperSubjectGuid());
        creditInfoVO.setCreditAccountName(hsaCreditInfo.getCreditAccountName());
        creditInfoVO.setCreditType(hsaCreditInfo.getCreditType());
        creditInfoVO.setCompanyName(hsaCreditInfo.getCompanyName());
        creditInfoVO.setAreaCode(hsaCreditInfo.getAreaCode());
        creditInfoVO.setLinkmanPhone(hsaCreditInfo.getLinkmanPhone());
        creditInfoVO.setCreditProve(JSON.parseArray(hsaCreditInfo.getCreditProve(), String.class));
        creditInfoVO.setStoredAmount(hsaCreditInfo.getStoredAmount());
        creditInfoVO.setCreditWallet(hsaCreditInfo.getCreditWallet());
        creditInfoVO.setCreditLimitedSet(hsaCreditInfo.getCreditLimitedSet());
        creditInfoVO.setCreditLimitedAmount(hsaCreditInfo.getCreditLimitedAmount());
        creditInfoVO.setApplicableAllStore(hsaCreditInfo.getApplicableAllStore());
        creditInfoVO.setAccountValidity(hsaCreditInfo.getAccountValidity());
        creditInfoVO.setAccountValidityDate(hsaCreditInfo.getAccountValidityDate());
        creditInfoVO.setSettlePeriod(hsaCreditInfo.getSettlePeriod());
        creditInfoVO.setSettlePeriodType(hsaCreditInfo.getSettlePeriodType());
        creditInfoVO.setCurrentCredit(hsaCreditOrderRecordMapper.queryCreditUserOrderRecordTotal(creditInfoVO.getGuid()));
        //适用门店
        creditInfoVO.setCreditApplyStoreList(getStoreData(creditGuid));
        //挂账使用人
        creditInfoVO.setCreditUserList(hsaCreditUserMapper.queryCreditUser(creditGuid));
        creditInfoVO.setSettlePeriodDate(hsaCreditInfo.getSettlePeriodDate());
        //状态
        creditInfoVO.setIsEnable(getCreditStatus(hsaCreditInfo.getAccountValidity(), hsaCreditInfo.getAccountValidityDate(),
                hsaCreditInfo.getIsEnable()));
        return creditInfoVO;
    }

    @Override
    public PageResult queryCreditInfoList(CreditInfoListQO request) {

        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<CreditInfoVO> creditInfoVOS = hsaCreditInfoMapper.queryCreditInfoList(request);
        if (CollectionUtil.isEmpty(creditInfoVOS)) {
            return new PageResult();
        }
        for (CreditInfoVO creditInfoVO : creditInfoVOS) {
            try {
                packageCreditInfoVO(creditInfoVO);
            } catch (Exception e) {
                log.error("挂账信息错误guid=================>" + creditInfoVO.getGuid());
                log.error(e.getMessage(), e);
            }

        }
        return PageUtil.getPageResult(new PageInfo<>(creditInfoVOS));
    }

    /**
     * 封装挂账信息
     *
     * @param creditInfoVO 挂账信息vo
     */
    private void packageCreditInfoVO(CreditInfoVO creditInfoVO) {
        if (creditInfoVO.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            creditInfoVO.setCreditApplyStoreList(getStoreData(creditInfoVO.getGuid()));
        }
        //有效期
        String validityDate;
        if (creditInfoVO.getAccountValidity() == NumberConstant.NUMBER_0) {
            validityDate = StringConstant.FOREVER_VALIDITY;
        } else {
            validityDate = DateUtil.formatLocalDateTime(creditInfoVO.getAccountValidityDate(), DateUtil.PATTERN_DATETIME)
                    + " " + StringConstant.UN_VALIDITY;
        }
        creditInfoVO.setValidityDate(validityDate);
        creditInfoVO.setSettlePeriodDate(getSettlePeriodDate(creditInfoVO.getSettlePeriodDate(), creditInfoVO.getSettlePeriod(), creditInfoVO.getSettlePeriodType()));
        creditInfoVO.setIsEnable(getCreditStatus(creditInfoVO.getAccountValidity(), creditInfoVO.getAccountValidityDate(),
                creditInfoVO.getIsEnable()));
        creditInfoVO.setCurrentCredit(hsaCreditOrderRecordMapper.queryCreditUserOrderRecordTotal(creditInfoVO.getGuid()));
    }

    /**
     * 设置结算周期时间
     *
     * @param settlePeriodDate 结算周期时间
     * @param settlePeriod     结算周期 0：手动生成结算单 1：自动生成结算单
     * @param settlePeriodType 结算周期类型
     * @return 操作结果
     */
    private String getSettlePeriodDate(String settlePeriodDate, Integer settlePeriod, Integer settlePeriodType) {
        if (settlePeriod == SettlePeriodEnum.MANUAL.getCode()) {
            return SettlePeriodEnum.MANUAL.getDes();
        } else {
            cn.hutool.json.JSONObject json = JSONUtil.parseObj(settlePeriodDate);
            return "每" + DataUnitEnum.getNameByCode(settlePeriodType) +
                    " " +
                    (Objects.isNull(json.get("type")) ? "" : json.get("type")) +
                    " " +
                    (Objects.isNull(json.get("value")) ? "" : json.get("value"));
        }
    }

    @Override
    public PageResult queryMemberCreditList(CreditInfoListQO request) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        request.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        request.setSource(headerUserInfo.getSource());
        List<CreditInfoVO> creditInfoVOS = hsaCreditInfoMapper.queryMemberCreditList(request);
        for (CreditInfoVO creditInfoVO : creditInfoVOS) {
            packageCreditInfoVO(creditInfoVO);
            if (creditInfoVO.getIsEnable() == EnableEnum.NOT_ENABLE.getCode() ||
                    creditInfoVO.getPersonIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
                creditInfoVO.setIsEnable(EnableEnum.NOT_ENABLE.getCode());
            }
            BigDecimal remainingAmount;
            if (creditInfoVO.getSinglePersonUpperLimit().compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
                remainingAmount = hsaCreditInfoService.getRemainingAmount(creditInfoVO.getGuid(), creditInfoVO.getCreditLimitedAmount(), creditInfoVO.getCreditLimitedSet());
            } else {
                remainingAmount = creditInfoVO.getSinglePersonUpperLimit().subtract(creditInfoVO.getTotalCredit());
            }
            creditInfoVO.setResidueCreditAmount(remainingAmount);
        }
        return PageUtil.getPageResult(new PageInfo<>(creditInfoVOS));
    }

    /**
     * 获取挂账状态
     *
     * @param accountValidity     账户有效期 0：永久有效 1：固定有效期
     * @param accountValidityDate 固定有效期失效时间
     * @param isEnable            是否开启 0：启用  1：禁用
     * @return 操作结果
     */
    private Integer getCreditStatus(Integer accountValidity, LocalDateTime accountValidityDate, int isEnable) {
        int status;
        if (judgeOverdue(accountValidity, accountValidityDate)) {
            status = isEnable;
        } else {
            status = OVERDUE;
        }
        return status;
    }

    /**
     * 判断是否过期
     *
     * @param accountValidity     账户有效期 0：永久有效 1：固定有效期
     * @param accountValidityDate 固定有效期失效时间
     * @return 操作结果
     */
    private boolean judgeOverdue(Integer accountValidity, LocalDateTime accountValidityDate) {

        return accountValidity == 0 || (accountValidity == 1 && accountValidityDate.isAfter(LocalDateTime.now()));
    }

    /**
     * 获取门店数据
     *
     * @param guid 类型guid
     * @return 操作结果
     */
    private List<CreditApplyStoreVO> getStoreData(String guid) {
        List<HsaStoreRuleInfo> applyStoreList = hsaStoreRuleInfoMapper
                .selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                        .eq(HsaStoreRuleInfo::getTypeGuid, guid));
        List<CreditApplyStoreVO> creditApplyStoreVOS = new ArrayList<>();
        if (CollectionUtil.isEmpty(applyStoreList)) {
            return creditApplyStoreVOS;
        }
        //档口数据 key对应门店guid
        Map<String, List<HsaStoreRuleInfo>> map = applyStoreList
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getParentGuid()))
                .collect(Collectors.groupingBy(HsaStoreRuleInfo::getParentGuid));
        for (HsaStoreRuleInfo applyStore : applyStoreList) {
            //档口直接过滤掉
            if (StringUtils.isNotEmpty(applyStore.getParentGuid())) {
                continue;
            }
            //门店信息
            CreditApplyStoreVO creditApplyStoreVO = toCreditApplyStoreVO(applyStore);
            //档口信息
            List<HsaStoreRuleInfo> applyBooths = map.get(applyStore.getStoreGuid());
            if (CollectionUtil.isEmpty(applyBooths)) {
                creditApplyStoreVOS.add(creditApplyStoreVO);
                continue;
            }
            List<CreditApplyStoreVO> applyBoothVOs = new ArrayList<>();
            for (HsaStoreRuleInfo applyBooth : applyBooths) {
                applyBoothVOs.add(toCreditApplyStoreVO(applyBooth));
            }
            creditApplyStoreVO.setStoreBoothCardRuleQOList(applyBoothVOs);
            creditApplyStoreVOS.add(creditApplyStoreVO);
        }
        return creditApplyStoreVOS;
    }

    /**
     * 门店和档口平级展示
     *
     * @param guid 挂账信息guid
     * @return 门店档口信息
     */
    private List<CreditApplyStoreVO> getStoreInfo(String guid) {
        List<HsaStoreRuleInfo> applyStoreList = hsaStoreRuleInfoMapper.selectList(
                new LambdaQueryWrapper<HsaStoreRuleInfo>().eq(HsaStoreRuleInfo::getTypeGuid, guid));
        List<CreditApplyStoreVO> creditApplyStoreVOS = new ArrayList<>();
        if (CollUtil.isEmpty(applyStoreList)) {
            return creditApplyStoreVOS;
        }
        for (HsaStoreRuleInfo applyStore : applyStoreList) {
            //门店信息
            CreditApplyStoreVO creditApplyStoreVO = toCreditApplyStoreVO(applyStore);
            creditApplyStoreVOS.add(creditApplyStoreVO);
        }
        return creditApplyStoreVOS;
    }

    /**
     * 门店信息
     *
     * @param applyStore 适用门店
     * @return 操作结果
     */
    private CreditApplyStoreVO toCreditApplyStoreVO(HsaStoreRuleInfo applyStore) {
        CreditApplyStoreVO creditApplyStoreVO = new CreditApplyStoreVO();
        creditApplyStoreVO.setStoreGuid(applyStore.getStoreGuid());
        creditApplyStoreVO.setStoreName(applyStore.getStoreName());
        creditApplyStoreVO.setStoreNumber(applyStore.getStoreNumber());
        creditApplyStoreVO.setTime(applyStore.getTime());
        creditApplyStoreVO.setAddress(applyStore.getAddress());
        creditApplyStoreVO.setParentGuid(applyStore.getParentGuid());
        creditApplyStoreVO.setAddressPoint(applyStore.getAddressPoint());
        return creditApplyStoreVO;
    }

    @Override
    public boolean operationCredit(OperationCreditQO operationCreditQO) {

        HsaCreditInfo hsaCreditInfo = queryByGuid(operationCreditQO.getGuid());
        //获取操作类型枚举
        OperationLabelEnum enumByCode = OperationLabelEnum.getEnumByCode(operationCreditQO.getType());
        switch (enumByCode) {
            case START:
            case FORBIDDEN:
                hsaCreditInfo.setIsEnable(operationCreditQO.getType());
                break;
            case DELETE:
                hsaCreditInfo.setIsDelete(BooleanEnum.TRUE.getCode());
                break;
        }
        return updateByGuid(hsaCreditInfo);
    }

    @Override
    public ExcelResultVO importCreditRelationMember(MultipartFile file, String creditGuid) {
        List<String> phoneList;
        if (StringUtils.isEmpty(creditGuid)) {
            phoneList = new ArrayList<>();
        } else {
            phoneList = hsaCreditInfoMapper.findPhoneByCreditGuid(creditGuid);
        }

        return memberExcelService.importMemberInfo(file, phoneList, null);
    }

    @Override
    public List<CreditWalletLogVO> queryCreditWalletLog(String guid) {


        return hsaCreditWalletLogMapper.queryCreditWalletLog(guid);
    }

    @Override
    public boolean operationCreditUser(int type, String creditInfoGuid, String memberGuid) {


        HsaCreditUser hsaCreditUser = hsaCreditUserMapper.selectOne(new LambdaQueryWrapper<HsaCreditUser>()
                .eq(HsaCreditUser::getMemberInfoGuid, memberGuid)
                .eq(HsaCreditUser::getCreditInfoGuid, creditInfoGuid));
        if (Objects.isNull(hsaCreditUser)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CREDIT_ACCOUNT_NOT_EXIST);
        }
        hsaCreditUser.setIsEnable(type);
        return hsaCreditUserMapper.updateByGuid(hsaCreditUser);

    }

    @Override
    public AppletsCreditVO queryAppletsCreditAccount(CreditInfoListQO creditInfoListQO) {
        AppletsCreditVO appletsCreditVO = new AppletsCreditVO();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        creditInfoListQO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        creditInfoListQO.setSource(headerUserInfo.getSource());
        List<CreditInfoVO> creditInfoVOS = hsaCreditInfoMapper.queryMemberCreditList(creditInfoListQO);
        for (CreditInfoVO creditInfoVO : creditInfoVOS) {
            packageCreditInfoVO(creditInfoVO);
            creditInfoVO.setResidueCreditAmount(setResidueCreditAmount(creditInfoVO.getSinglePersonUpperLimit(),
                    creditInfoVO.getGuid(), creditInfoVO.getCreditLimitedAmount(), creditInfoVO.getTotalCredit(), creditInfoVO.getCreditLimitedSet()));
            if (creditInfoVO.getIsEnable() == EnableEnum.NOT_ENABLE.getCode() ||
                    creditInfoVO.getPersonIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
                creditInfoVO.setIsEnable(EnableEnum.NOT_ENABLE.getCode());
            }
        }
        appletsCreditVO.setCreditInfoVOS(creditInfoVOS);
        appletsCreditVO.setUnValidNumber(hsaCreditInfoMapper.queryCountMemberCredit(headerUserInfo.getOperSubjectGuid(),
                creditInfoListQO.getMemberGuid(), 2));
        appletsCreditVO.setValidNumber(hsaCreditInfoMapper.queryCountMemberCredit(headerUserInfo.getOperSubjectGuid(),
                creditInfoListQO.getMemberGuid(), 3));
        return appletsCreditVO;
    }

    @Override
    public TerminalCreditVO getTerminalCreditAccount(CreditInfoListQO creditInfoListQO) {
        AppletsCreditVO appletsCreditVO;
        if (StringUtils.isNotBlank(creditInfoListQO.getQrCode())) {
            log.info("二维码扫码，二维码key=======>{}", creditInfoListQO.getQrCode());
            Object content = redisTemplate.opsForValue().get(creditInfoListQO.getQrCode());
            if (null == content) {
                throw new MemberBaseException(CardOperationExceptionEnum.MEMBER_CREDIT_INVALID);
            }
            String guid = content.toString();
            log.info("二维码扫码，挂账户Guid======>{}", guid);
            HsaCreditUser hsaCreditUser = hsaCreditUserMapper.queryByGuid(guid);
            //扫挂账码进来的校验挂账信息
            HsaCreditInfo hsaCreditInfo = hsaCreditInfoMapper.queryByGuid(hsaCreditUser.getCreditInfoGuid());
            if (hsaCreditInfo.getIsEnable() == EnableEnum.NOT_ENABLE.getCode() || hsaCreditUser.getIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
                log.error("===========>挂账账户已禁用，请联系管理员");
                throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_GENERAL_STATUS_DISABLED);
            }

            if (hsaCreditInfo.getAccountValidity() == NUMBER_1 && hsaCreditInfo.getAccountValidityDate().isBefore(LocalDateTime.now())) {
                log.error("===========>挂账账户已过期，不可使用");
                throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_USER_STATUS_DISABLED);
            }

            creditInfoListQO.setCreditUserGuid(guid);
            creditInfoListQO.setMemberGuid(hsaCreditUser.getMemberInfoGuid());
        }
        appletsCreditVO = this.queryAppletsCreditAccount(creditInfoListQO);
        // 设置一体机需要的挂账信息
        return setTerminalParam(appletsCreditVO, creditInfoListQO);
    }

    /**
     * 设置一体机需要的挂账信息
     *
     * @param appletsCreditVO  小程序挂账信息
     * @param creditInfoListQO 挂账集合
     * @return 操作结果
     */
    private TerminalCreditVO setTerminalParam(AppletsCreditVO appletsCreditVO, CreditInfoListQO creditInfoListQO) {
        TerminalCreditVO terminalCreditVO = new TerminalCreditVO();
        terminalCreditVO.setValidNumber(appletsCreditVO.getValidNumber());
        terminalCreditVO.setUnValidNumber(appletsCreditVO.getUnValidNumber());
        String phoneNum = hsaOperationMemberInfoMapper.queryByGuid(creditInfoListQO.getMemberGuid()).getPhoneNum();
        terminalCreditVO.setPhoneNum(phoneNum);
        List<TerCreditInfoVO> terminalCreditInfo = getTerminalCreditInfo(appletsCreditVO.getCreditInfoVOS());
        terminalCreditVO.setCreditInfoVOS(terminalCreditInfo);
        return terminalCreditVO;
    }

    /**
     * 获取一体机挂账信息
     *
     * @param creditInfoVOS 挂账信息
     * @return 查询结果
     */
    private List<TerCreditInfoVO> getTerminalCreditInfo(List<CreditInfoVO> creditInfoVOS) {
        List<TerCreditInfoVO> terCreditInfoVOList = Lists.newArrayList();
        if (CollUtil.isEmpty(creditInfoVOS)) {
            return terCreditInfoVOList;
        }
        for (CreditInfoVO creditInfoVO : creditInfoVOS) {
            TerCreditInfoVO terCreditInfoVO = new TerCreditInfoVO();
            BeanUtils.copyProperties(creditInfoVO, terCreditInfoVO);

            Integer applicableAllStore = creditInfoVO.getApplicableAllStore();
            if (Objects.nonNull(applicableAllStore) && BooleanEnum.FALSE.getCode() == applicableAllStore) {
                // 门店和档口平级展示
                terCreditInfoVO.setCreditApplyStoreList(getStoreInfo(creditInfoVO.getGuid()));
            }
            terCreditInfoVOList.add(terCreditInfoVO);
        }
        return terCreditInfoVOList;
    }

    @Override
    public BigDecimal setResidueCreditAmount(BigDecimal singlePersonUpperLimit, String hsaCreditInfoGuid,
                                             BigDecimal creditLimitedAmount, BigDecimal totalCredit, Integer creditLimitedSet) {
        BigDecimal residueCreditAmount;
        if (BigDecimalUtil.nonNullValue(singlePersonUpperLimit).compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            residueCreditAmount = hsaCreditInfoService.getRemainingAmount(hsaCreditInfoGuid, creditLimitedAmount, creditLimitedSet);
        } else {
            residueCreditAmount = singlePersonUpperLimit.subtract(BigDecimalUtil.nonNullValue(totalCredit));
        }
        return residueCreditAmount;
    }

    @Override
    public List<CreditNameVO> queryCreditName() {

        List<HsaCreditInfo> hsaCreditInfoList = hsaCreditInfoMapper
                .selectList(new LambdaQueryWrapper<HsaCreditInfo>()
                        .eq(HsaCreditInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .orderByDesc(HsaCreditInfo::getId));
        List<CreditNameVO> creditNameVOS = Lists.newArrayList();
        if (CollectionUtil.isEmpty(hsaCreditInfoList)) {
            return creditNameVOS;
        }
        for (HsaCreditInfo hsaCreditInfo : hsaCreditInfoList) {
            CreditNameVO creditNameVO = new CreditNameVO();
            creditNameVO.setCreditGuid(hsaCreditInfo.getGuid());
            creditNameVO.setCreditName(hsaCreditInfo.getCreditAccountName());
            creditNameVO.setCreditNumber(hsaCreditInfo.getCreditNumber());
            creditNameVOS.add(creditNameVO);
        }
        return creditNameVOS;
    }

    @Override
    public BigDecimal getRemainingAmount(String hsaCreditInfoGuid, BigDecimal creditLimitedAmount, Integer creditLimitedSet) {
        BigDecimal sum;
        if (Objects.isNull(creditLimitedAmount) || creditLimitedSet == NUMBER_0) {
            return new BigDecimal("-1");
        }
        //限制用户可支配金额
        sum = hsaCreditUserMapper.getUnlimitedUserCreditUpperLimit(hsaCreditInfoGuid);
        //可支配金额
        BigDecimal availableSum;
        availableSum = getAvailableSum(creditLimitedAmount, sum);

        List<String> memberGuidList = hsaCreditUserMapper.selectList(new LambdaQueryWrapper<HsaCreditUser>()
                        .eq(HsaCreditUser::getCreditInfoGuid, hsaCreditInfoGuid)
                        .eq(HsaCreditUser::getSinglePersonUpperLimit, BigDecimal.ZERO))
                .stream()
                .map(HsaCreditUser::getMemberInfoGuid)
                .collect(Collectors.toList());
        //已使用金额
        BigDecimal orderAmount;
        orderAmount = hsaCreditOrderRecordMapper.getUserCreditOrderAmount(hsaCreditInfoGuid, memberGuidList);

        //剩余金额
        return Objects.isNull(orderAmount) ? availableSum : availableSum.subtract(orderAmount);
    }

    @Override
    public void handleCreditBusiness(RequestMemberCardPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo) {
        HsaCreditUser hsaCreditUser = hsaCreditUserMapper.queryByGuid(request.getCreditUserGuid());
        log.info("当前挂账用户信息======>{}", JSON.toJSONString(hsaCreditUser));

        if (ObjectUtils.isEmpty(hsaCreditUser)) {
            log.info("当前挂账用户信息不存在={}", request.getCreditUserGuid());
        }

        HsaCreditInfo hsaCreditInfo = hsaCreditInfoMapper.queryByGuid(hsaCreditUser.getCreditInfoGuid());
        log.info("当前挂账信息======>{}", JSON.toJSONString(hsaCreditInfo));

        //校验挂账用户条件
        checkCreditUserCondition(request, hsaCreditUser, hsaCreditInfo, hsaOperationMemberInfo);

        //计算挂账可用金额
        BigDecimal remainingAmount = getCreditUserAmount(hsaCreditUser, hsaCreditInfo);

        //校验挂账余额
        checkRemainingAmount(request, remainingAmount);

        //校验单笔挂账上限
        checkSingleAmount(request, hsaCreditUser);

        HsaCreditOrderRecord hsaCreditOrderRecord = getHsaCreditOrderRecord(request, hsaOperationMemberInfo, hsaCreditUser, hsaCreditInfo);

        hsaCreditOrderRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditOrderRecord.class.getSimpleName()));
        HsaCreditFundingDetail hsaCreditFundingDetail = MerchantPayAssembler.fromHsaCreditFundingDetail(
                hsaCreditInfo,
                hsaCreditOrderRecord,
                NumberConstant.NUMBER_1,
                NUMBER_0);

        hsaCreditFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditFundingDetail.class.getSimpleName()));
        hsaCreditFundingDetailMapper.insert(hsaCreditFundingDetail);
        hsaCreditUserMapper.updateByGuid(hsaCreditUser);
        hsaCreditOrderRecordMapper.insert(hsaCreditOrderRecord);
        log.info("处理挂账成功======>{}", request.getCreditUserGuid());
    }

    private static HsaCreditOrderRecord getHsaCreditOrderRecord(RequestMemberCardPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo) {
        BigDecimal totalCredit = Objects.nonNull(hsaCreditUser.getTotalCredit()) ?
                hsaCreditUser.getTotalCredit().add(request.getCreditPayAmount()) : request.getCreditPayAmount();
        hsaCreditUser.setTotalCredit(totalCredit);
        return MerchantPayAssembler.fromHsaCreditOrderRecord(
                request,
                hsaOperationMemberInfo,
                hsaCreditUser,
                hsaCreditInfo);
    }

    private BigDecimal getCreditUserAmount(HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo) {
        BigDecimal remainingAmount;
        if (hsaCreditUser.getSinglePersonUpperLimit().compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            remainingAmount = hsaCreditInfoService.getRemainingAmount(hsaCreditInfo.getGuid(), hsaCreditInfo.getCreditLimitedAmount(), hsaCreditInfo.getCreditLimitedSet());
        } else {
            remainingAmount = hsaCreditUser.getSinglePersonUpperLimit().subtract(hsaCreditUser.getTotalCredit());
        }
        return remainingAmount;
    }

    private static void checkSingleAmount(RequestMemberCardPayVO request, HsaCreditUser hsaCreditUser) {
        if (hsaCreditUser.getSingleCountUpperLimit().compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0 && request.getCreditPayAmount().compareTo(hsaCreditUser.getSingleCountUpperLimit()) > NUMBER_0) {
            log.error("===========>单笔超过挂账上限：" + hsaCreditUser.getSingleCountUpperLimit());
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getCode(),
                    MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getDes() + "账户单笔上限 ¥" + hsaCreditUser.getSingleCountUpperLimit());
        }
    }

    private void checkRemainingAmount(RequestMemberCardPayVO request, BigDecimal remainingAmount) {
        if (remainingAmount.compareTo(new BigDecimal("-1")) != NUMBER_0 && request.getCreditPayAmount().compareTo(remainingAmount) > NUMBER_0) {
            log.error("===========>剩余挂账金额不足：" + remainingAmount);
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getCode(), MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getDes() + "剩余挂账金额 ¥" + remainingAmount);
        }
    }

    private static BigDecimal getAvailableSum(BigDecimal creditLimitedAmount, BigDecimal sum) {
        if (Objects.isNull(sum)) {
            return creditLimitedAmount;
        } else {
            return sum.compareTo(BigDecimal.ZERO) > NUMBER_0 ? creditLimitedAmount.subtract(sum) : creditLimitedAmount;
        }
    }

    private void checkCreditUserCondition(RequestMemberCardPayVO request, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo, HsaOperationMemberInfo hsaOperationMemberInfo) {

        //挂账信息校验
        checkCreditInfo(request, hsaCreditUser, hsaCreditInfo, hsaOperationMemberInfo);

        //挂账门店校验
        checkCreditStore(request, hsaCreditInfo);
    }


    private void checkCreditStore(RequestMemberCardPayVO request, HsaCreditInfo hsaCreditInfo) {
        if (hsaCreditInfo.getApplicableAllStore() == NUMBER_0) {
            List<HsaStoreRuleInfo> hsaStoreCardRuleList = hsaStoreRuleInfoMapper.selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                    .eq(HsaStoreRuleInfo::getType, NUMBER_0)
                    .eq(HsaStoreRuleInfo::getTypeGuid, hsaCreditInfo.getGuid()));
            if (CollUtil.isNotEmpty(hsaStoreCardRuleList) && StringUtils.isNotEmpty(request.getStoreGuid())) {
                List<String> storeGuid = hsaStoreCardRuleList.stream().map(HsaStoreRuleInfo::getStoreGuid).collect(Collectors.toList());
                if (!storeGuid.contains(request.getStoreGuid())) {
                    log.error("===========>挂账无法在当前门店使用");
                    throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_STORE);
                }
            }
        }
    }


    private void checkCreditInfo(RequestMemberCardPayVO request, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (hsaCreditInfo.getIsEnable() == EnableEnum.NOT_ENABLE.getCode() || hsaCreditUser.getIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
            log.error("===========>挂账账户已禁用，请联系管理员");
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_GENERAL_STATUS_DISABLED);
        }

        if (hsaCreditInfo.getAccountValidity() == NUMBER_1 && hsaCreditInfo.getAccountValidityDate().isBefore(LocalDateTime.now())) {
            log.error("===========>挂账账户已过期，不可使用");
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_USER_STATUS_DISABLED);
        }

        if (StringUtils.isNotBlank(request.getMemberInfoGuid())
                && !request.getMemberInfoGuid().equals(hsaCreditUser.getMemberInfoGuid())) {
            log.error("===========>挂账手机号与会员支付手机号不一致");
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CREDIT_USER_PHONE, ThreadLocalCache.getOperSubjectGuid()));
        }

        //判断当前会员是否禁用
        if (hsaOperationMemberInfo.getAccountState() == BooleanEnum.TRUE.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
        }
    }

}
