package com.holderzone.member.base.service.card;


import com.holderzone.member.base.entity.card.HsaPhysicalCardStrategyRecord;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.card.PhysicalStrategyPayQO;

/**
 * <AUTHOR>
 */
public interface HsaPhysicalCardStrategyRecordService extends IHolderBaseService<HsaPhysicalCardStrategyRecord> {

    String saveRecord(HsaPhysicalCardStrategyRecord strategyRecord);

    boolean savePayStatus(PhysicalStrategyPayQO qo);
}
