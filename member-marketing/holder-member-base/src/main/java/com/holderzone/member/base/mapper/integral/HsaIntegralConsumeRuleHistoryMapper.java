package com.holderzone.member.base.mapper.integral;

import com.holderzone.member.base.entity.integral.HsaIntegralConsumeRuleHistory;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import org.apache.ibatis.annotations.Mapper;


/**
 * 积分消耗规则 历史
 *
 * <AUTHOR>
 */
@Mapper
public interface HsaIntegralConsumeRuleHistoryMapper extends HolderBaseMapper<HsaIntegralConsumeRuleHistory> {

}
