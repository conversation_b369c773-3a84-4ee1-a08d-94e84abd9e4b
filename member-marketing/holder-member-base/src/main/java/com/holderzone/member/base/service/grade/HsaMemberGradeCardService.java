package com.holderzone.member.base.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaMemberGradeCard;
import com.holderzone.member.common.qo.grade.MemberGradeCardQO;
import com.holderzone.member.common.vo.grade.MemberGradeCardVO;

import java.util.List;

/**
 * 会员等级卡Service接口
 */
public interface HsaMemberGradeCardService extends IService<HsaMemberGradeCard> {

    /**
     * 查询会员等级卡列表
     *
     * @param qo 查询条件
     * @return 会员等级卡列表
     */
    List<MemberGradeCardVO> list(MemberGradeCardQO qo);

    /**
     * 根据GUID获取会员等级卡
     *
     * @param guid 主键ID
     * @return 会员等级卡信息
     */
    MemberGradeCardVO getByGuid(String guid);

    /**
     * 根据会员GUID查询等级卡列表
     *
     * @param memberInfoGuid 会员GUID
     * @return 会员等级卡列表
     */
    List<MemberGradeCardVO> listByMember(String memberInfoGuid);

    /**
     * 新增会员等级卡
     *
     * @param qo 会员等级卡信息
     */
    void add(MemberGradeCardQO qo);


    /**
     * 新增会员等级卡并保存购买记录
     *
     * @param qo 会员等级卡信息
     */
    void addCardAndSavePurchaseHis(MemberGradeCardQO qo);

    /**
     * 更新会员等级卡
     *
     * @param qo 会员等级卡信息
     */
    void update(MemberGradeCardQO qo);

    /**
     * 删除会员等级卡
     *
     * @param guid 主键ID
     */
    void delete(String guid);

    /**
     * 批量删除会员等级卡
     *
     * @param guids 主键ID列表
     */
    void batchDelete(List<String> guids);
}
