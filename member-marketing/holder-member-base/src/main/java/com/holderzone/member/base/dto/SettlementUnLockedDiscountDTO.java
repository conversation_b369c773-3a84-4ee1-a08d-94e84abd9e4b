package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 下单锁定优惠
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
public class SettlementUnLockedDiscountDTO implements Serializable {


    private static final long serialVersionUID = -5239525768641874058L;

    /**
     * 主体
     */
    @ApiModelProperty("主体guid")
    private String operSubjectGuid;

    /**
     * 会员 guid
     */
    @ApiModelProperty("会员guid")
    private String memberGuid;

    /**
     * 当前 订单号 : 计算时必传
     * orderNum
     */
    @ApiModelProperty("当前订单号")
    @NotBlank(message = "订单号必传！")
    private String orderNo;

    private List<String> discountIdList;

    private Integer codeType;
}
