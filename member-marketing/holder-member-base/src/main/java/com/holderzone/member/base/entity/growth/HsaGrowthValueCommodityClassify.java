package com.holderzone.member.base.entity.growth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 成长值明细表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hsa_growth_value_commodity_classify")
public class HsaGrowthValueCommodityClassify implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String taskGuid;

    private String storeName;

    private String storeGuid;

    private String strategyName;

    private String strategyId;

    private Integer businessType;

    private String goodsCategory;

    private String categoryIds;

}
