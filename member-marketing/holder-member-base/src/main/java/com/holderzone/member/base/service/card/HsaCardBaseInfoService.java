package com.holderzone.member.base.service.card;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.qo.card.CardInfoQO;
import com.holderzone.member.common.qo.card.QueryCardInfoPageQO;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import com.holderzone.member.common.vo.card.CardNameVO;
import com.holderzone.member.common.vo.card.QueryCardInfoPageVO;

import java.util.List;

/**
 * <p>
 * 会员卡基础信息表接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface HsaCardBaseInfoService extends IHolderBaseService<HsaCardBaseInfo> {

    /**
     * 保存编辑会员卡
     *
     * @param qo CardInfoQO
     * @return guid
     */
    Boolean saveOrUpdateCardInfo(CardInfoQO qo);


    /**
     * 会员导入
     *
     * @param fileUrl 会员信息url地址
     */
    MemberUploadExcelVO memberUploadExcelUrl(String fileUrl, String cardGuid);


    /**
     * 查询
     *
     * @param cardGuid cardGuid
     * @return CardInfoQO
     */
    CardInfoQO cardInfoDetails(String cardGuid);

    /**
     * 更新电子卡发放
     *
     * @param cardGuid
     * @return
     */
    Boolean updateSendCardStatus(String cardGuid, Integer status);

    /**
     * 更新会员卡状态
     * 0 禁用 1 启用
     *
     * @param cardGuid
     * @return
     */
    Boolean updateCardStatus(String cardGuid, Integer status);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    Page<QueryCardInfoPageVO> getCardInfoPage(QueryCardInfoPageQO query);

    /**
     * 校验卡名称
     *
     * @param cardName cardName
     */
    Boolean checkCardName(String cardName);

    /**
     * 通过guids查询会员卡基础信息
     * @param guids guids
     * @param operSubjectGuid 运营主体
     * @return 会员卡基础信息集合
     */
    List<HsaCardBaseInfo> getCardBaseInfoList(List<String> guids, String operSubjectGuid);

    /**
     * 通过guids查询会员卡名称
     * @param guids guids
     * @param operSubjectGuid 运营主体
     * @return 会员卡基础信息集合
     */
    List<CardNameVO> getCardNameList(List<String> guids, String operSubjectGuid);


    /**
     * 查询卡基础信息
     *
     * @param cardGuid cardGuid
     * @return CardInfoQO
     */
    CardBaseInfoVO getCardInfoByGuid(String cardGuid);
}
