package com.holderzone.member.base.service.growth;

import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.growth.GrowthValueTaskListQO;
import com.holderzone.member.common.qo.growth.GrowthValueTaskQO;

import java.util.List;

/**
 * 成长值任务service
 * author: pantao
 */
public interface HsaGrowthValueTaskService extends IHolderBaseService<HsaGrowthValueTask> {

    /**
     * 添加或者修改成长值任务
     *
     * @param growthValueTaskQO 创建成长请求参数
     */
    boolean saveOrUpdateGrowthValueTask(GrowthValueTaskQO growthValueTaskQO);

    /**
     * 查询成长值任务
     *
     * @param guid guid
     * @return 查询结果
     */
    GrowthValueTaskQO queryGrowthValueTaskDetail(String guid);

    /**
     * 查询成长值任务列表
     *
     * @param request 请求参数
     * @return 查询结果
     */
    PageResult queryGrowthValueTaskList(GrowthValueTaskListQO request);

    /**
     * 操作成长值
     *
     * @param guid guid
     * @param type 类型 0：停止 1：启动 2：删除
     * @return 操作结果
     */
    boolean operationGrowthValue(String guid, int type, Integer position);

    /**
     * 保存成长值任务、积分任务暂停时间段
     *
     * @param operationType 成长值、积分操作类型
     * @param taskGuid      任务guid
     * @param taskName      任务名称
     * @param taskType      任务类型
     */
    void saveHsaSuspendTaskTimeQuantum(Integer operationType, String taskGuid, String taskName,
                                       Integer taskType, Integer type);

    /**
     * 是否能启动任务
     *
     * @param guid 任务guid
     * @return 查询结果
     */
    boolean canBeStartTask(String guid);

    /**
     * 是否需要提示
     *
     * @param growthValueTaskQO 创建成长请求参数
     * @return 操作结果
     */
    boolean whetherNeedToast(GrowthValueTaskQO growthValueTaskQO);

    /**
     * 查询所有任务动作
     *
     * @return 查询结果
     */
    List<Integer> findAllTaskActionType();

}
