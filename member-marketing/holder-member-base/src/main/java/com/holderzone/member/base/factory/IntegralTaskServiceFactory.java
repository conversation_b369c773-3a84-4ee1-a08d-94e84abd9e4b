package com.holderzone.member.base.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.base.service.integral.task.HsaIntegralTaskApplyService;
import com.holderzone.member.common.enums.growth.TaskActionEnum;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.exception.MallBaseException;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 积分任务服务工厂
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class IntegralTaskServiceFactory implements ApplicationContextAware {

    private final Map<TaskActionEnum, HsaIntegralTaskApplyService> taskServiceMap = new HashMap<>();


    @Setter
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, HsaIntegralTaskApplyService> beans = applicationContext.getBeansOfType(HsaIntegralTaskApplyService.class);
        if (CollUtil.isEmpty(beans)) {
            log.info("##################积分任务工厂异常告警##################");
            return;
        }
        log.info("############积分任务工厂初始化启动############");
        for (Map.Entry<String, HsaIntegralTaskApplyService> data : beans.entrySet()) {
            TaskActionEnum actionEnum = data.getValue().payWay();
            if (ObjectUtil.isNotNull(actionEnum)) {
                taskServiceMap.put(actionEnum, data.getValue());
                log.info("积分任务动作：{}", actionEnum.getDes() + "##启动完成");
            }
        }
    }

    public HsaIntegralTaskApplyService build(TaskActionEnum actionEnum) {
        HsaIntegralTaskApplyService tradeService = taskServiceMap.get(actionEnum);
        if (ObjectUtil.isNotNull(tradeService)) {
            return tradeService;
        }
        log.warn("任务动作：{}，存在的任务集合：{}", actionEnum, taskServiceMap);
        //重新获取
        if (applicationContext == null) {
            throw new MallBaseException("积分任务异常");
        }
        setApplicationContext(applicationContext);
        HsaIntegralTaskApplyService service = taskServiceMap.get(actionEnum);
        if (ObjectUtil.isNull(service)) {
            throw new MallBaseException("积分消费任务服务不存在");
        }
        return service;
    }
}
