package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-20 18:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HasCancellationOrder {

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;


    /**
     * 挂账信息guid
     */
    private String creditInfoGuid;
    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单类型 0:食堂预订 1:自助餐 2:堂食点餐 3:快速收款
     *
     * @see com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum
     */
    private Integer orderType;

    /**
     * 订单状态 0：已结账 1：已取消
     */
    private Integer orderStatus;

    /**
     * 销售门店
     */
    private String saleStore;

    /**
     * 订单实付金额
     */
    private BigDecimal orderPaidAmount;

    /**
     * 挂账金额
     */
    private BigDecimal creditAmount;

    /**
     * 结算单号
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String clearingStatementNumber;

    /**
     * 挂账使用人
     */
    private String creditUsername;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
