package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.*;
import com.holderzone.member.base.entity.member.HsaDepositStrategy;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.system.BaseEntity;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.member.HsaDepositStrategyMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.card.*;
import com.holderzone.member.base.service.grade.HsaBusinessEquitiesService;
import com.holderzone.member.base.support.SettlementMemberSupport;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.MemberCardExcelVO;
import com.holderzone.member.common.dto.excel.MemberUploadExcel;
import com.holderzone.member.common.dto.excel.CardMemberUploadExcelError;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.ElectronicOpenWayEnum;
import com.holderzone.member.common.enums.card.SendCardStateEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.equities.GradeEquitiesQO;
import com.holderzone.member.common.qo.equities.MemberEquitiesQO;
import com.holderzone.member.common.qo.equities.SendCardEquitiesQO;
import com.holderzone.member.common.qo.member.HsaDepositStrategyQO;
import com.holderzone.member.common.util.excel.CardMemberUploadExcelUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import com.holderzone.member.common.vo.card.CardNameVO;
import com.holderzone.member.common.vo.card.QueryCardInfoPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 会员卡基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Slf4j
@Service
public class HsaCardBaseInfoServiceImpl extends HolderBaseServiceImpl<HsaCardBaseInfoMapper, HsaCardBaseInfo> implements HsaCardBaseInfoService {

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    @Lazy
    @Resource
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Lazy
    @Resource
    private HsaBusinessEquitiesService hsaBusinessEquitiesService;

    @Lazy
    @Resource
    private HsaMemberCardRuleService hsaMemberCardRuleService;

    @Lazy
    @Resource
    private HsaCardOpenRuleService hsaCardOpenRuleService;

    @Autowired
    @Lazy
    private HsaMemberInfoCardService hsaMemberInfoCardService;

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaMemberCardRuleMapper hsaMemberCardRuleMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private FileOssService fileOssService;

    @Lazy
    @Resource
    private SettlementMemberSupport settlementMemberSupport;

    @Resource
    private HsaDepositStrategyMapper hsaDepositStrategyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateCardInfo(CardInfoQO qo) {
        //入参
        CardBaseInfoQO cardBaseInfoQO = qo.getCardBaseInfoQO();
        CardOpenRuleQO cardOpenRuleQO = qo.getCardOpenRuleQO();
        cardOpenRuleQO.validate();
        //统参
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //门店范围
        List<HsaStoreCardRule> hsaStoreCardRules = Lists.newArrayList();
        //开卡规则会员关联
        List<HsaMemberCardRule> hsaMemberCardRules = Lists.newArrayList();
        //处理卡基础信息数据
        HsaCardBaseInfo hsaCardBaseInfo = saveOrUpdateCardBaseInfo(cardBaseInfoQO, hsaStoreCardRules, headerUserInfo);
        //处理开卡规则
        HsaCardOpenRule hsaCardOpenRule = saveOrUpdateCardBaseInfo(cardOpenRuleQO, hsaMemberCardRules, headerUserInfo, hsaCardBaseInfo);
        //持久化
        return dataPersistence(hsaCardBaseInfo, hsaCardOpenRule, hsaStoreCardRules, hsaMemberCardRules, qo.getEquitiesQOS());
    }

    private void parameterValidation(CardOpenRuleQO cardOpenRuleQO) {
        if (Objects.isNull(cardOpenRuleQO)) {
            return;
        }
        Integer selfType = cardOpenRuleQO.getSelfType();
        if (Objects.isNull(selfType) || NumberConstant.NUMBER_0 == selfType) {
            return;
        }
        if (NumberConstant.NUMBER_1 == selfType) {
            cardOpenRuleQO.setSelfRechargeMoney(null);
            if (BigDecimalUtil.lessEqual(BigDecimalUtil.nonNullValue(cardOpenRuleQO.getSelfPaymentMoney()), BigDecimal.ZERO)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_OPEN_CARD_AMOUNT);
            }
        }
        if (NumberConstant.NUMBER_2 == selfType) {
            cardOpenRuleQO.setSelfPaymentMoney(null);
            if (BigDecimalUtil.lessEqual(BigDecimalUtil.nonNullValue(cardOpenRuleQO.getSelfRechargeMoney()), BigDecimal.ZERO)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_OPEN_CARD_AMOUNT);
            }
        }

    }

    @Override
    public MemberUploadExcelVO memberUploadExcelUrl(String fileUrl, String cardGuid) {
        MemberUploadExcelVO memberUploadExcelVO = new MemberUploadExcelVO();
        List<MemberUploadExcel> memberUploadExcels = CardMemberUploadExcelUtil.read(fileUrl);
        // 条件验证(默认不读取第一条模板数据)
        if (CollUtil.isEmpty(memberUploadExcels) || memberUploadExcels.size() == 1) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberUploadExcels.get(memberUploadExcels.size() - 1).getPhoneNum())) {
            memberUploadExcels.remove(memberUploadExcels.size() - 1);
        }
        if (memberUploadExcels.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        //会员
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = null;
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getPhoneCountryCode, StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE)
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (CollUtil.isNotEmpty(hsaOperationMemberInfos)) {
            hsaOperationMemberInfoMap = hsaOperationMemberInfos
                    .stream()
                    .collect(Collectors.toMap(HsaOperationMemberInfo::getPhoneNum, Function.identity(), (entity1, entity2) -> entity1));
        }
        List<String> memberGuid = hsaOperationMemberInfos.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList());
        //是否开通过会员卡
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = new HashMap<>();
        if (StringUtils.isNotEmpty(cardGuid)) {
            hsaMemberInfoCardMap = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                            .in(HsaMemberInfoCard::getMemberInfoGuid, memberGuid)
                            .eq(HsaMemberInfoCard::getCardGuid, cardGuid))
                    .stream()
                    .collect(Collectors.toMap(HsaMemberInfoCard::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        //返回会员数据
        List<MemberCardExcelVO> memberCardExcelVOS = Lists.newArrayList();
        //手机号重复校验
        Set<String> checkPhoneNumSet = new HashSet<>();
        //返回表单失败数据
        List<CardMemberUploadExcelError> cardMemberUploadExcelErrors = Lists.newArrayList();
        for (int i = 1, j = memberUploadExcels.size(); i < j; i++) {
            CardMemberUploadExcelError cardMemberUploadExcelError = checkUploadExcelUrl(memberUploadExcels.get(i), hsaOperationMemberInfoMap, hsaMemberInfoCardMap, checkPhoneNumSet, memberCardExcelVOS);
            if (ObjectUtil.isNotNull(cardMemberUploadExcelError)) {
                Objects.requireNonNull(cardMemberUploadExcelError).setSerial(String.valueOf(cardMemberUploadExcelErrors.size() + 1));
                cardMemberUploadExcelErrors.add(cardMemberUploadExcelError);
                log.info("错误信息-->>>序号:" + (i + 1) + "\t错误原因" + cardMemberUploadExcelError.getErrorMessage());
            }
        }
        //上传至阿里oos
        memberUploadExcel(memberUploadExcelVO, memberUploadExcels, cardMemberUploadExcelErrors);
        memberUploadExcelVO.setSuccess(memberCardExcelVOS.size());
        memberUploadExcelVO.setMemberCardExcelVOS(memberCardExcelVOS);
        return memberUploadExcelVO;
    }

    private void memberUploadExcel(MemberUploadExcelVO memberUploadExcelVO, List<MemberUploadExcel> memberUploadExcels, List<CardMemberUploadExcelError> cardMemberUploadExcelErrors) {
        LocalDateTime now = LocalDateTime.now();
        memberUploadExcelVO.setSuccess(Math.max((memberUploadExcels.size() - cardMemberUploadExcelErrors.size() - 1), 0));
        memberUploadExcelVO.setFail(cardMemberUploadExcelErrors.size());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDateTime = now.format(formatter);
        if (!cardMemberUploadExcelErrors.isEmpty()) {
            ExcelResult excelResult = com.aimilin.utils.BeanUtils.toResult(cardMemberUploadExcelErrors);
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(systemRoleHelper.getReplace("快速导入选择会员模板失败数据", ThreadLocalCache.getOperSubjectGuid()) + formatDateTime + "." + ExcelType.XLSX);
                String upload = fileOssService.upload(fileDto);
                String newUpload = upload.replace("http", "https");
                log.info("错误信息文件下载路径->>>>>{}", newUpload);
                memberUploadExcelVO.setFailUrl(newUpload);
                memberUploadExcelVO.setFail(cardMemberUploadExcelErrors.size());
            } catch (Exception e) {
                log.error("上传文件失败");
                memberUploadExcelVO.setFailUrl("上传错误信息失败");
            }
        }
    }

    private CardMemberUploadExcelError checkUploadExcelUrl(MemberUploadExcel memberUploadExcel,
                                                           Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap,
                                                           Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap,
                                                           Set<String> checkPhoneNumSet,
                                                           List<MemberCardExcelVO> hsaMemberImportRecordErrorList) {
        CardMemberUploadExcelError error = new CardMemberUploadExcelError();
        String phoneNum = memberUploadExcel.getPhoneNum();
        StringBuilder stringBuilder = new StringBuilder();
        HsaOperationMemberInfo info = null;
        info = checkMember(hsaOperationMemberInfoMap, phoneNum, stringBuilder, info);

        checkPhoneNum(checkPhoneNumSet, phoneNum, stringBuilder);

        //姓名
        if (!org.springframework.util.StringUtils.isEmpty(memberUploadExcel.getUserName()) && (memberUploadExcel.getUserName().length() > NumberConstant.NUMBER_20)) {
            stringBuilder.append(systemRoleHelper.getReplace("【会员姓名】格式错误" + "\r\n", ThreadLocalCache.getOperSubjectGuid()));
        }

        if (CollUtil.isNotEmpty(hsaMemberInfoCardMap) && Objects.nonNull(info) && (hsaMemberInfoCardMap.containsKey(info.getGuid()))) {
            stringBuilder.append(systemRoleHelper.getReplace("【手机号】会员卡已存在" + "\r\n", ThreadLocalCache.getOperSubjectGuid()));

        }

        try {
            //导入失败
            if (org.springframework.util.StringUtils.hasText(stringBuilder.toString())) {
                error.setErrorMessage(stringBuilder.toString());
                error.setPhoneNum(phoneNum);
                error.setUserName(memberUploadExcel.getUserName());
                return error;
            } else {
                //导入成功
                MemberCardExcelVO vo = new MemberCardExcelVO();
                if (info != null) {
                    vo.setMemberAccount(info.getMemberAccount())
                            .setPhoneCountryCode(info.getPhoneCountryCode())
                            .setMemberGuid(String.valueOf(info.getGuid()))
                            .setPhoneNum(phoneNum)
                            .setUserName(info.getUserName());
                }
                hsaMemberImportRecordErrorList.add(vo);
                return null;
            }
        } catch (Exception e) {
            log.error("导入失败==============", e);
            error.setErrorMessage("【导入失败】" + e.getMessage());
            return error;
        }

    }

    private static HsaOperationMemberInfo checkMember(Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap, String phoneNum, StringBuilder stringBuilder, HsaOperationMemberInfo info) {
        if (CollUtil.isNotEmpty(hsaOperationMemberInfoMap) && !hsaOperationMemberInfoMap.containsKey(phoneNum)) {
            stringBuilder.append("【手机号】未注册" + "\r\n");
        } else {
            info = hsaOperationMemberInfoMap.get(phoneNum);
            if (info.getAccountState() == 1) {
                stringBuilder.append("【手机号】已禁用" + "\r\n");
            }

        }
        return info;
    }

    private static void checkPhoneNum(Set<String> checkPhoneNumSet, String phoneNum, StringBuilder stringBuilder) {
        if (org.springframework.util.StringUtils.hasText(phoneNum)) {
            if (!NumberUtil.isPhoneNum11(phoneNum)) {
                stringBuilder.append("【手机号】格式错误" + "\r\n");
            }
            if (checkPhoneNumSet.contains(phoneNum)) {
                stringBuilder.append("【手机号】重复导入" + "\r\n");
            }
            checkPhoneNumSet.add(phoneNum);
        } else {
            stringBuilder.append("【手机号】格式错误" + "\r\n");
        }
    }

    @Override
    public CardInfoQO cardInfoDetails(String cardGuid) {
        CardBaseInfoQO cardBaseInfoQO = new CardBaseInfoQO();
        CardOpenRuleQO cardOpenRuleQO = new CardOpenRuleQO();
        HsaCardBaseInfo hsaCardBaseInfo = queryByGuid(cardGuid);
        BeanUtils.copyProperties(hsaCardBaseInfo, cardBaseInfoQO);
        cardBaseInfoQO.setGuid(String.valueOf(hsaCardBaseInfo.getGuid()));
        //固定充值金额列表
        setRechargeMoneys(hsaCardBaseInfo, cardBaseInfoQO);
        //查询适用门店
        setStore(hsaCardBaseInfo, cardBaseInfoQO);
        //开卡规则
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, hsaCardBaseInfo.getGuid()));
        BeanUtils.copyProperties(hsaCardOpenRule, cardOpenRuleQO);
        cardOpenRuleQO.setOpenCardScopeType(hsaCardOpenRule.getOpenCardScopeType());
        cardOpenRuleQO.setGuid(String.valueOf(hsaCardOpenRule.getGuid()))
                .setCardGuid(String.valueOf(hsaCardBaseInfo.getGuid()));
        //电子卡指定开卡用户
        List<MemberCardRuleQO> memberOpenCardList = Lists.newArrayList();
        //只能开卡范围
        List<MemberCardRuleQO> memberCardRuleList = Lists.newArrayList();
        //获取电子卡开卡指定会员
        setElectronicCard(hsaCardOpenRule, cardBaseInfoQO, cardOpenRuleQO, memberOpenCardList);
        //获取限制开卡会员范围
        setOpenCardScope(hsaCardOpenRule, cardBaseInfoQO, cardOpenRuleQO, memberCardRuleList);
        //渠道条件
        setOpenCardRegisterChannels(hsaCardOpenRule, cardOpenRuleQO);
        //标签条件
        setOpenCardMemberLabelGuid(hsaCardOpenRule, cardOpenRuleQO);
        //路径
        setOpenCardPaths(hsaCardOpenRule, cardOpenRuleQO);
        //押金策略
        setDepositStrategy(hsaCardOpenRule, cardOpenRuleQO);
        cardOpenRuleQO.setIsPhysicalCardRetreat(hsaCardOpenRule.getIsPhysicalCardRetreat());
        //开卡渠道
        setOpenPhysicalCardChannel(hsaCardOpenRule, cardOpenRuleQO);
        cardOpenRuleQO.setElectronicDecideOpenDate(hsaCardOpenRule.getElectronicDecideOpenDate());
        //卡数量统计
        cardOpenRuleQO.setSendOpenCountLimit(cardOpenRuleQO.getSendOpenCountLimit());
        cardOpenRuleQO.setSurplusSendOpenCountLimit(hsaCardOpenRule.getSurplusSendOpenCountLimit());
        //获取卡 权益
        return new CardInfoQO()
                .setCardBaseInfoQO(cardBaseInfoQO)
                .setCardOpenRuleQO(cardOpenRuleQO)
                .setEquitiesDetailVOS(hsaBusinessEquitiesService.getEquitiesVOList(cardBaseInfoQO.getGuid()));
    }

    private static void setOpenPhysicalCardChannel(HsaCardOpenRule hsaCardOpenRule, CardOpenRuleQO cardOpenRuleQO) {
        if (StringUtils.isNotEmpty(hsaCardOpenRule.getOpenPhysicalCardChannel())) {
            cardOpenRuleQO.setOpenPhysicalCardChannel(JSON.parseArray(hsaCardOpenRule.getOpenPhysicalCardChannel(), String.class));
        }
    }

    private void setDepositStrategy(HsaCardOpenRule hsaCardOpenRule, CardOpenRuleQO cardOpenRuleQO) {
        if (ObjectUtil.isNotNull(hsaCardOpenRule.getOpenPhysicalCardStrategyGuid())) {
            HsaDepositStrategyQO hsaDepositStrategyQO = new HsaDepositStrategyQO();
            HsaDepositStrategy hsaDepositStrategy = hsaDepositStrategyMapper.selectOne(new LambdaQueryWrapper<HsaDepositStrategy>()
                    .eq(BaseEntity::getGuid, String.valueOf(hsaCardOpenRule.getOpenPhysicalCardStrategyGuid()))
                    .ne(HsaDepositStrategy::getIsDelete, -1));
            if (ObjectUtil.isNotNull(hsaDepositStrategy)) {
                BeanUtils.copyProperties(hsaDepositStrategy, hsaDepositStrategyQO);
                hsaDepositStrategyQO.setGuid(hsaDepositStrategy.getGuid());
                cardOpenRuleQO.setHsaDepositStrategyQO(hsaDepositStrategyQO);
                cardOpenRuleQO.setOpenPhysicalCardStrategyGuid(String.valueOf(hsaCardOpenRule.getOpenPhysicalCardStrategyGuid()));
            } else {
                cardOpenRuleQO.setHsaDepositStrategyQO(new HsaDepositStrategyQO());
            }
        }
    }

    private static void setOpenCardPaths(HsaCardOpenRule hsaCardOpenRule, CardOpenRuleQO cardOpenRuleQO) {
        if (StringUtils.isNotEmpty(hsaCardOpenRule.getOpenCardPath())) {
            cardOpenRuleQO.setOpenCardPaths(JSON.parseArray(hsaCardOpenRule.getOpenCardPath(), String.class));
        }
    }

    private static void setOpenCardMemberLabelGuid(HsaCardOpenRule hsaCardOpenRule, CardOpenRuleQO cardOpenRuleQO) {
        if (StringUtils.isNotEmpty(hsaCardOpenRule.getOpenCardMemberLabelGuid())) {
            cardOpenRuleQO.setOpenCardMemberLabelGuid(JSON.parseArray(hsaCardOpenRule.getOpenCardMemberLabelGuid(), String.class));
        }
    }

    private static void setOpenCardRegisterChannels(HsaCardOpenRule hsaCardOpenRule, CardOpenRuleQO cardOpenRuleQO) {
        if (StringUtils.isNotEmpty(hsaCardOpenRule.getOpenCardRegisterChannel())) {
            cardOpenRuleQO.setOpenCardRegisterChannels(JSON.parseArray(hsaCardOpenRule.getOpenCardRegisterChannel(), String.class));
        }
    }

    private void setOpenCardScope(HsaCardOpenRule hsaCardOpenRule, CardBaseInfoQO cardBaseInfoQO, CardOpenRuleQO cardOpenRuleQO, List<MemberCardRuleQO> memberCardRuleList) {
        if (ObjectUtil.isNotNull(hsaCardOpenRule.getOpenCardScopeType()) && hsaCardOpenRule.getOpenCardScopeType() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()) {
            getElectronicOpenCard(cardBaseInfoQO, cardOpenRuleQO, hsaCardOpenRule, memberCardRuleList, 1);
            if (CollUtil.isNotEmpty(memberCardRuleList)) {
                cardOpenRuleQO.setMemberCardRuleList(memberCardRuleList);
            }
        }
    }

    private void setElectronicCard(HsaCardOpenRule hsaCardOpenRule, CardBaseInfoQO cardBaseInfoQO, CardOpenRuleQO cardOpenRuleQO, List<MemberCardRuleQO> memberOpenCardList) {
        if (ObjectUtil.isNotNull(hsaCardOpenRule.getElectronicOpenWay()) && (hsaCardOpenRule.getElectronicOpenWay() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode())) {
            getElectronicOpenCard(cardBaseInfoQO, cardOpenRuleQO, hsaCardOpenRule, memberOpenCardList, 0);
            if (CollUtil.isNotEmpty(memberOpenCardList)) {
                cardOpenRuleQO.setMemberOpenCardList(memberOpenCardList);
            }

        }
    }

    private void setStore(HsaCardBaseInfo hsaCardBaseInfo, CardBaseInfoQO cardBaseInfoQO) {
        if (hsaCardBaseInfo.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<StoreCardRuleQO> storeCardRuleQOList = hsaMemberInfoCardService.getStoreCardRule(Collections.
                    singletonList(hsaCardBaseInfo.getGuid()), null);
            if (CollUtil.isNotEmpty(storeCardRuleQOList)) {
                cardBaseInfoQO.setStoreCardRuleQOList(storeCardRuleQOList);
            }
        }
    }

    private void setRechargeMoneys(HsaCardBaseInfo hsaCardBaseInfo, CardBaseInfoQO cardBaseInfoQO) {
        if (StringUtils.isNotEmpty(hsaCardBaseInfo.getRechargeMoneys())) {
            cardBaseInfoQO.setRechargeMoneys(JSON.parseArray(hsaCardBaseInfo.getRechargeMoneys(), String.class));
        }
    }

    @Override
    public Boolean updateSendCardStatus(String cardGuid, Integer isStatus) {
        LocalDateTime now = LocalDateTime.now();
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, cardGuid));
        if (isStatus == SendCardStateEnum.CARD_STATE_START.getCode()) {
            if (hsaCardOpenRule.getElectronicOpenWay() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()) {
                setSendStatusByAssIgnOpen(now, hsaCardOpenRule);
            } else {
                setSendStatusByNotAssIgnOpen(hsaCardOpenRule, now);
            }
        } else {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_CAN_SEND_STOP.getCode());
        }
        return hsaCardOpenRuleMapper.updateByGuid(hsaCardOpenRule);
    }

    private static void setSendStatusByNotAssIgnOpen(HsaCardOpenRule hsaCardOpenRule, LocalDateTime now) {
        if (hsaCardOpenRule.getOpenCardTimeType() == BooleanEnum.FALSE.getCode()) {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
        } else {
            if (now.isBefore(hsaCardOpenRule.getOpenCardStartTime())) {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_NOT_START.getCode());
            } else if (now.isAfter(hsaCardOpenRule.getOpenCardStartTime()) && now.isBefore(hsaCardOpenRule.getOpenCardEndTime())) {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
            } else {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
            }
        }
    }

    private static void setSendStatusByAssIgnOpen(LocalDateTime now, HsaCardOpenRule hsaCardOpenRule) {
        if (now.isBefore(hsaCardOpenRule.getElectronicDecideOpenDate())) {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
        } else {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
        }
    }

    @Override
    public Boolean updateCardStatus(String cardGuid, Integer isStatus) {
        HsaCardBaseInfo hsaCardBaseInfo = queryByGuid(cardGuid);
        if (isStatus == 2) {
            LocalDate now = LocalDate.now();
            if (hsaCardBaseInfo.getCardValidity() == 2) {
                if (now.isBefore(hsaCardBaseInfo.getCardValidityDate())) {
                    hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_START.getCode());
                } else {
                    hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
                    hsaBusinessEquitiesService.sendExpireCardEquities(
                            new SendCardEquitiesQO().setCardGuid(Collections.singletonList(hsaCardBaseInfo.getGuid())));
                }
            } else {
                hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_START.getCode());
            }
        } else {
            hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_CAN_SEND_STOP.getCode());
        }
        return updateByGuid(hsaCardBaseInfo);
    }

    /**
     * 获取会员卡信息页面
     *
     * @param query 查询会员卡信息页面的查询对象
     * @return 会员卡信息页面的分页对象
     */
    @Override
    public Page<QueryCardInfoPageVO> getCardInfoPage(QueryCardInfoPageQO query) {
        Page<QueryCardInfoPageVO> queryCardInfoPageVOPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<QueryCardInfoPageVO> queryCardInfoPageVOS = hsaCardBaseInfoMapper.getCardInfoPage(queryCardInfoPageVOPage, query);
        List<String> stopHairGuid = new ArrayList<>();  //已经停止发送会员卡集合

        //返回列表详情，=0可以少查点
        final boolean showDetails = !Objects.equals(query.getShowDetails(), BooleanEnum.FALSE.getCode());
        if (showDetails && CollUtil.isNotEmpty(queryCardInfoPageVOS)) {
            for (QueryCardInfoPageVO queryCardInfoPageVO : queryCardInfoPageVOS) {
                //路径设置
                setOpenCardPaths(queryCardInfoPageVO);

                //卡数量统计
                countOpenCountLimit(queryCardInfoPageVO);

                //设置开卡时间
                setElectronicDecideOpenDate(queryCardInfoPageVO);

                //范围
                setStore(queryCardInfoPageVO);

                //add停发卡片
                addStopHairGuid(queryCardInfoPageVO, stopHairGuid);

                //获取卡权益范围
                queryCardInfoPageVO.setEquitiesDetailVOList(hsaBusinessEquitiesService.getEquitiesName(queryCardInfoPageVO.getCardGuid()));
            }

            if (CollUtil.isNotEmpty(stopHairGuid)) {  //解决发卡数量<=0的会员卡状态为：已停发
                hsaCardOpenRuleMapper.batchUpdateCardSendStatus(stopHairGuid, SendCardStateEnum.CARD_STATE_CAN_SEND_STOP.getCode());
            }
        }

        queryCardInfoPageVOPage.setRecords(queryCardInfoPageVOS);
        return queryCardInfoPageVOPage;
    }


    private static void addStopHairGuid(QueryCardInfoPageVO queryCardInfoPageVO, List<String> stopHairGuid) {
        if (queryCardInfoPageVO.getSendCountLimit() != 0 &&  //发卡数量有限制
                queryCardInfoPageVO.getSurplusSendOpenCountLimit() <= 0 &&  //并且发卡数量<=0
                SendCardStateEnum.CARD_STATE_START.getCode() == queryCardInfoPageVO.getSendStatus()) {
            queryCardInfoPageVO.setSendStatus(3);  //如果剩余可发卡数量小于等于0，那么状态为  已停止（3）
            stopHairGuid.add(queryCardInfoPageVO.getCardGuid()); //更新已停发会员卡状态
        }
    }

    private void setStore(QueryCardInfoPageVO queryCardInfoPageVO) {
        if (queryCardInfoPageVO.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<StoreCardRuleQO> storeCardRule = hsaMemberInfoCardService.getStoreCardRule(Collections.
                    singletonList(queryCardInfoPageVO.getCardGuid()), null);
            queryCardInfoPageVO.setStoreList(storeCardRule);
        }
    }

    private static void setElectronicDecideOpenDate(QueryCardInfoPageVO queryCardInfoPageVO) {
        if (queryCardInfoPageVO.getIsSupportElectronicCard() == BooleanEnum.TRUE.getCode()
                && (queryCardInfoPageVO.getElectronicOpenWay() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()
                && Objects.nonNull(queryCardInfoPageVO.getElectronicDecideOpenDate()))) {
            queryCardInfoPageVO.setElectronicDecideOpenDate(queryCardInfoPageVO.getElectronicDecideOpenDate());
        }
    }

    private static void countOpenCountLimit(QueryCardInfoPageVO queryCardInfoPageVO) {
        if (queryCardInfoPageVO.getSendCountLimit() == BooleanEnum.TRUE.getCode() && queryCardInfoPageVO.getSendCountNum() >= 0) {
            queryCardInfoPageVO.setSendOpenCountLimit(queryCardInfoPageVO.getSendOpenCountLimit());
            queryCardInfoPageVO.setSurplusSendOpenCountLimit(queryCardInfoPageVO.getSurplusSendOpenCountLimit());
        }
    }

    private static void setOpenCardPaths(QueryCardInfoPageVO queryCardInfoPageVO) {
        if (StringUtils.isNotEmpty(queryCardInfoPageVO.getOpenCardPath())) {
            queryCardInfoPageVO.setOpenCardPaths(JSON.parseArray(queryCardInfoPageVO.getOpenCardPath(), String.class));
        }
    }

    public void getElectronicOpenCard(CardBaseInfoQO cardBaseInfoQO, CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, List<MemberCardRuleQO> memberOpenCardList, Integer type) {
        List<HsaMemberCardRule> hsaMemberCardRuleList = hsaMemberCardRuleMapper.selectList(new LambdaQueryWrapper<HsaMemberCardRule>()
                .eq(HsaMemberCardRule::getOperSubjectGuid, hsaCardOpenRule.getOperSubjectGuid())
                .eq(HsaMemberCardRule::getCardOpenRuleGuid, hsaCardOpenRule.getGuid())
                .eq(HsaMemberCardRule::getBusinessType, type));
        if (CollUtil.isNotEmpty(hsaMemberCardRuleList)) {
            addMemberOpenCard(cardBaseInfoQO, hsaCardOpenRule, memberOpenCardList, hsaMemberCardRuleList);

            if (CollUtil.isNotEmpty(memberOpenCardList)) { //给手机号+区号操作
                Set<String> collect = memberOpenCardList.stream().map(MemberCardRuleQO::getPhoneNum).collect(Collectors.toSet());
                Map<String, String> memberInfoMap = getMemberPhoneNumMap(collect);
                for (MemberCardRuleQO memberCardRuleQO : memberOpenCardList) {
                    if (memberInfoMap.containsKey(memberCardRuleQO.getPhoneNum())) {
                        String phoneCountryCode = memberInfoMap.get(memberCardRuleQO.getPhoneNum());
                        memberCardRuleQO.setPhoneCountryCode(phoneCountryCode);
                    }
                }
            }
            cardOpenRuleQO.setMemberOpenCardList(memberOpenCardList);
        }
    }

    /**
     * 根据指定的手机号码集合获取成员手机号码和国家码的映射关系
     * @param collect 手机号码集合
     * @return 成员手机号码和国家码的映射关系
     */
    private Map<String, String> getMemberPhoneNumMap(Set<String> collect) {
        return hsaOperationMemberInfoMapper.selectList(
                        new LambdaQueryWrapper<HsaOperationMemberInfo>()
                                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                .in(HsaOperationMemberInfo::getPhoneNum, collect))
                .stream().filter(Objects::nonNull).distinct()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getPhoneNum, HsaOperationMemberInfo::getPhoneCountryCode));
    }


    private static void addMemberOpenCard(CardBaseInfoQO cardBaseInfoQO, HsaCardOpenRule hsaCardOpenRule, List<MemberCardRuleQO> memberOpenCardList, List<HsaMemberCardRule> hsaMemberCardRuleList) {
        hsaMemberCardRuleList.forEach(in ->
                memberOpenCardList.add(
                        new MemberCardRuleQO()
                                .setCardGuid(cardBaseInfoQO.getGuid())
                                .setCardOpenRuleGuid(String.valueOf(hsaCardOpenRule.getGuid()))
                                .setPhoneNum(in.getMemberPhoneNum())
                                .setUserName(in.getUserName())
                                .setMemberAccount(in.getMemberAccount())
                                .setMemberGuid(String.valueOf(in.getOperationMemberInfoGuid()))));
    }

    /**
     * 获取会员卡下适用门店
     *
     * @param cardGuid hsaCardBaseInfo
     */
    public List<StoreCardRuleQO> getStoreCardRule(List<String> cardGuid) {
        List<StoreCardRuleQO> saveStoreCardRuleQOList = Lists.newArrayList();
        //门店级
        List<HsaStoreCardRule> hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getCardGuid, cardGuid)
                .isNull(HsaStoreCardRule::getParentGuid));
        List<String> parentGuidList = hsaStoreCardRules.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());

        //档口级
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                        .in(HsaStoreCardRule::getCardGuid, cardGuid)
                        .in(HsaStoreCardRule::getParentGuid, parentGuidList))
                .stream().collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isNotEmpty(hsaStoreCardRules)) {
            setStoreBoothCardRule(cardGuid, hsaStoreCardRules, hsaStoreCardRuleMap, saveStoreCardRuleQOList);
        }
        return saveStoreCardRuleQOList;
    }

    /**
     * 设置店铺档口卡规则
     * @param cardGuid 卡号列表
     * @param hsaStoreCardRules 店铺卡规则列表
     * @param hsaStoreCardRuleMap 店铺卡规则映射表
     * @param saveStoreCardRuleQOList 保存店铺卡规则请求对象列表
     */
    private static void setStoreBoothCardRule(List<String> cardGuid, List<HsaStoreCardRule> hsaStoreCardRules, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, List<StoreCardRuleQO> saveStoreCardRuleQOList) {
        for (HsaStoreCardRule hsaStoreCardRule : hsaStoreCardRules) {
            StoreCardRuleQO storeCardRuleQO = new StoreCardRuleQO();
            storeCardRuleQO.setStoreGuid(String.valueOf(hsaStoreCardRule.getStoreGuid()));
            storeCardRuleQO.setStoreName(hsaStoreCardRule.getStoreName());
            storeCardRuleQO.setGuid(String.valueOf(hsaStoreCardRule.getGuid()));
            storeCardRuleQO.setCardGuid(String.valueOf(cardGuid));

            //处理档口数据
            List<StoreBoothCardRuleQO> storeBoothCardRuleQOList = Lists.newArrayList();
            if (hsaStoreCardRuleMap.containsKey(hsaStoreCardRule.getGuid())) {
                List<HsaStoreCardRule> ruleList = hsaStoreCardRuleMap.get(hsaStoreCardRule.getGuid());
                ruleList.forEach(in -> {
                    StoreBoothCardRuleQO storeBoothCardRuleQO = new StoreBoothCardRuleQO();
                    storeBoothCardRuleQO.setStoreGuid(String.valueOf(in.getStoreGuid()));
                    storeBoothCardRuleQO.setStoreName(in.getStoreName());
                    storeBoothCardRuleQO.setGuid(String.valueOf(in.getGuid()));
                    storeBoothCardRuleQO.setCardGuid(String.valueOf(cardGuid));
                    storeBoothCardRuleQOList.add(storeBoothCardRuleQO);
                });
            }
            storeCardRuleQO.setStoreBoothCardRuleQOList(storeBoothCardRuleQOList);
            saveStoreCardRuleQOList.add(storeCardRuleQO);
        }
    }


    public Boolean dataPersistence(HsaCardBaseInfo hsaCardBaseInfo,
                                   HsaCardOpenRule hsaCardOpenRule,
                                   List<HsaStoreCardRule> hsaStoreCardRules,
                                   List<HsaMemberCardRule> hsaMemberCardRules,
                                   List<GradeEquitiesQO> equitiesQOS) {
        //会员卡基础信息
        if (Objects.nonNull(queryByGuid(String.valueOf(hsaCardBaseInfo.getGuid())))) {
            updateByGuid(hsaCardBaseInfo);
        } else {
            save(hsaCardBaseInfo);
        }
        //会员卡开卡规则
        if (Objects.nonNull(hsaCardOpenRuleMapper.queryByGuid(String.valueOf(hsaCardOpenRule.getGuid())))) {
            hsaCardOpenRuleMapper.updateByGuid(hsaCardOpenRule);
        } else {
            hsaCardOpenRuleService.save(hsaCardOpenRule);
        }
        //会员卡门店范围更新
        if (CollUtil.isNotEmpty(hsaStoreCardRules)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRules);
        }
        //开卡会员规则
        String openRuleGuid = hsaCardOpenRule.getGuid();
        if (StringUtils.isNotEmpty(openRuleGuid)) {
            hsaMemberCardRuleMapper.delete(new LambdaQueryWrapper<HsaMemberCardRule>()
                    .eq(HsaMemberCardRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaMemberCardRule::getCardOpenRuleGuid, openRuleGuid));
        }
        //保存会员卡权益
        MemberEquitiesQO memberEquitiesQO = new MemberEquitiesQO();
        memberEquitiesQO.setBusinessGuid(hsaCardBaseInfo.getGuid());
        memberEquitiesQO.setGradeEquitiesQOS(equitiesQOS);
        memberEquitiesQO.setBusinessType(BusinessTypeEnum.CARD_EQUITIES.getCode());
        memberEquitiesQO.setCardStatus(hsaCardBaseInfo.getCardStatus());
        memberEquitiesQO.setCardName(hsaCardBaseInfo.getCardName());
        hsaBusinessEquitiesService.saveEquities(memberEquitiesQO);
        return hsaMemberCardRuleService.saveBatch(hsaMemberCardRules);
    }

    private HsaCardOpenRule saveOrUpdateCardBaseInfo(CardOpenRuleQO cardOpenRuleQO, List<HsaMemberCardRule> hsaMemberCardRules, HeaderUserInfo headerUserInfo, HsaCardBaseInfo hsaCardBaseInfo) {
        parameterValidation(cardOpenRuleQO);
        HsaCardOpenRule hsaCardOpenRule;
        if (StringUtils.isNotEmpty(cardOpenRuleQO.getGuid())) {
            hsaCardOpenRule = hsaCardOpenRuleMapper.queryByGuid(cardOpenRuleQO.getGuid());
            if (Objects.isNull(hsaCardOpenRule)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_FIND_CARD_RULE);
            }
            BeanUtils.copyProperties(cardOpenRuleQO, hsaCardOpenRule);
        } else {
            hsaCardOpenRule = new HsaCardOpenRule();
            //默认通道
            BeanUtils.copyProperties(cardOpenRuleQO, hsaCardOpenRule);
            hsaCardOpenRule.setGuid(guidGeneratorUtil.getStringGuid(HsaCardOpenRule.class.getSimpleName()));
        }
        processorPO(cardOpenRuleQO, hsaCardOpenRule, hsaMemberCardRules, headerUserInfo, hsaCardBaseInfo);
        return hsaCardOpenRule;
    }

    private void defaultData(CardOpenRuleQO cardOpenRuleQO, HeaderUserInfo headerUserInfo, HsaCardBaseInfo hsaCardBaseInfo, HsaCardOpenRule hsaCardOpenRule) {
        if (cardOpenRuleQO.getSendStatus() == SendCardStateEnum.CARD_STATE_ROUGH.getCode()) {
            BeanUtils.copyProperties(headerUserInfo, hsaCardOpenRule);
            hsaCardOpenRule.setIsSupportElectronicCard(BooleanEnum.TRUE.getCode());
            hsaCardOpenRule.setElectronicOpenWay(ElectronicOpenWayEnum.AUTONOMOUSLY_OPEN.getCode());
            hsaCardOpenRule.setSelfType(0);
            hsaCardOpenRule.setIsSupportPhysicalCard(BooleanEnum.TRUE.getCode());
//            hsaCardOpenRule.setGuid(guidGeneratorUtil.getStringGuid(HsaCardOpenRule.class.getSimpleName()));
            hsaCardOpenRule.setOpenCardPath(JSON.toJSONString(NumberConstant.OPEN_CARD_PATH));
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_ROUGH.getCode());
            hsaCardOpenRule.setCardGuid(hsaCardBaseInfo.getGuid());
            hsaCardOpenRule.setIsPhysicalCardRetreat(BooleanEnum.FALSE.getCode());
        }
    }

    /**
     * 处理开卡规则
     *
     * @param cardOpenRuleQO     cardOpenRuleQO
     * @param hsaCardOpenRule    hsaCardOpenRule
     * @param hsaMemberCardRules hsaMemberCardRules
     * @param headerUserInfo     headerUserInfo
     */
    private void processorPO(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, List<HsaMemberCardRule> hsaMemberCardRules, HeaderUserInfo headerUserInfo, HsaCardBaseInfo hsaCardBaseInfo) {
        if (ObjectUtil.isNull(cardOpenRuleQO.getIsSupportElectronicCard())) {
            defaultData(cardOpenRuleQO, headerUserInfo, hsaCardBaseInfo, hsaCardOpenRule);
        }
        //状态
        if (hsaCardOpenRule.getIsSupportElectronicCard() == BooleanEnum.TRUE.getCode()) {
            setSendStatus(cardOpenRuleQO, hsaCardOpenRule);
        }

        //校验卡规则
        checkCardOpenRule(cardOpenRuleQO, hsaCardOpenRule);

        BeanUtils.copyProperties(headerUserInfo, hsaCardOpenRule);

        //指定需要开卡的会员
        addMemberCardRules(cardOpenRuleQO, hsaCardOpenRule, hsaMemberCardRules, headerUserInfo);

        //指定只能开卡的会员
        addMemberCardRules(cardOpenRuleQO, hsaCardOpenRule, hsaMemberCardRules, headerUserInfo, hsaCardBaseInfo);

        //校验开卡条件
        openCardCheck(cardOpenRuleQO, hsaCardOpenRule, hsaCardBaseInfo);
    }

    private static void openCardCheck(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, HsaCardBaseInfo hsaCardBaseInfo) {
        //开卡路径
        if (Objects.nonNull(cardOpenRuleQO.getOpenCardScopeType())
                && (CollUtil.isNotEmpty(cardOpenRuleQO.getOpenCardPaths()))) {
            hsaCardOpenRule.setOpenCardPath(JSON.toJSONString(cardOpenRuleQO.getOpenCardPaths()));
        }

        //渠道条件
        if (Objects.nonNull(cardOpenRuleQO.getOpenCardRegisterChannels())
                && (CollUtil.isNotEmpty(cardOpenRuleQO.getOpenCardRegisterChannels()))) {
            hsaCardOpenRule.setOpenCardRegisterChannel(JSON.toJSONString(cardOpenRuleQO.getOpenCardRegisterChannels()));

        }
        //开卡渠道
        if (CollUtil.isNotEmpty(cardOpenRuleQO.getOpenPhysicalCardChannel())) {
            hsaCardOpenRule.setOpenPhysicalCardChannel(JSON.toJSONString(cardOpenRuleQO.getOpenPhysicalCardChannel()));
        }
        //标签条件
        if (Objects.nonNull(cardOpenRuleQO.getOpenCardRegisterChannels())
                && (CollUtil.isNotEmpty(cardOpenRuleQO.getOpenCardMemberLabelGuid()))) {
            hsaCardOpenRule.setOpenCardMemberLabelGuid(JSON.toJSONString(cardOpenRuleQO.getOpenCardMemberLabelGuid()));

        }
        if (StringUtils.isNotEmpty(cardOpenRuleQO.getOpenPhysicalCardStrategyGuid())) {
            hsaCardOpenRule.setOpenPhysicalCardStrategyGuid(Long.parseLong(cardOpenRuleQO.getOpenPhysicalCardStrategyGuid()));
        }
        hsaCardOpenRule.setCardGuid(hsaCardBaseInfo.getGuid());
        if (StringUtils.isEmpty(hsaCardOpenRule.getOpenCardPath())) {
            hsaCardOpenRule.setOpenCardPath(JSON.toJSONString(NumberConstant.OPEN_CARD_PATH));
        }
        if (hsaCardOpenRule.getIsSupportElectronicCard() == BooleanEnum.FALSE.getCode()) {
            hsaCardOpenRule.setElectronicOpenWay(null);
            hsaCardOpenRule.setOpenCardTimeType(null);
            hsaCardOpenRule.setOpenCardPath(null);
        }
    }

    private void addMemberCardRules(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, List<HsaMemberCardRule> hsaMemberCardRules, HeaderUserInfo headerUserInfo, HsaCardBaseInfo hsaCardBaseInfo) {
        if (Objects.nonNull(cardOpenRuleQO.getOpenCardScopeType())
                && (cardOpenRuleQO.getOpenCardScopeType() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()
                && CollUtil.isNotEmpty(cardOpenRuleQO.getMemberCardRuleList()))) {
            for (MemberCardRuleQO memberCardRuleQO : cardOpenRuleQO.getMemberCardRuleList()) {
                HsaMemberCardRule hsaMemberCardRule = new HsaMemberCardRule();
                getHsaMemberCardRule(hsaCardOpenRule, headerUserInfo, memberCardRuleQO, hsaMemberCardRule);
                hsaMemberCardRule.setCardGuid(hsaCardBaseInfo.getGuid());
                hsaMemberCardRule.setBusinessType(1);
                hsaMemberCardRules.add(hsaMemberCardRule);
            }
            hsaCardOpenRule.setOpenCardPath(JSON.toJSONString(Arrays.asList(
                    SourceTypeEnum.ADD_ONE_MACHINE.getCode(),
                    SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode())));

        }
    }

    /**
     * 检查卡片开户规则
     *
     * @param cardOpenRuleQO 卡片开户规则查询对象
     * @param hsaCardOpenRule HSA卡片开户规则对象
     */
    private static void checkCardOpenRule(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule) {
        if (Objects.nonNull(hsaCardOpenRule.getSendStatus())
                && hsaCardOpenRule.getSendStatus() != SendCardStateEnum.CARD_STATE_ROUGH.getCode()
                && (cardOpenRuleQO.getIsSupportElectronicCard() == BooleanEnum.FALSE.getCode()
                && cardOpenRuleQO.getIsSupportPhysicalCard() == BooleanEnum.FALSE.getCode())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_OPEN_CARD);
        }
    }


    private void addMemberCardRules(CardOpenRuleQO cardOpenRuleQO,
                                    HsaCardOpenRule hsaCardOpenRule,
                                    List<HsaMemberCardRule> hsaMemberCardRules,
                                    HeaderUserInfo headerUserInfo) {
        if (Objects.nonNull(cardOpenRuleQO.getElectronicOpenWay())
                && (cardOpenRuleQO.getElectronicOpenWay() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()
                && CollUtil.isNotEmpty(cardOpenRuleQO.getMemberOpenCardList()))) {
            for (MemberCardRuleQO memberCardRuleQO : cardOpenRuleQO.getMemberOpenCardList()) {
                HsaMemberCardRule hsaMemberCardRule = new HsaMemberCardRule();
                getHsaMemberCardRule(hsaCardOpenRule, headerUserInfo, memberCardRuleQO, hsaMemberCardRule);
                hsaMemberCardRule.setBusinessType(0);
                hsaMemberCardRules.add(hsaMemberCardRule);
            }
            hsaCardOpenRule.setOpenCardPath(JSON.toJSONString(Arrays.asList(
                    SourceTypeEnum.ADD_ONE_MACHINE.getCode(),
                    SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode())));
            hsaCardOpenRule.setSurplusSendOpenCountLimit(cardOpenRuleQO.getMemberOpenCardList().size());
            hsaCardOpenRule.setSendCountLimit(BooleanEnum.TRUE.getCode());

        }
    }

    /**
     * 设置发送状态
     * @param cardOpenRuleQO 卡开门规则对象
     * @param hsaCardOpenRule HsaCardOpenRule对象
     */
    private void setSendStatus(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule) {
        LocalDateTime now = LocalDateTime.now();
        // 查询HsaCardOpenRule对象
        HsaCardOpenRule openRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>().eq(HsaCardOpenRule::getGuid, hsaCardOpenRule.getGuid()));
        // 判断是否存在符合条件的HsaCardOpenRule对象以及发送状态为可发送/停止
        if (ObjectUtil.isNotNull(openRule) && ObjectUtil.isNotNull(openRule.getSendStatus()) && openRule.getSendStatus() == SendCardStateEnum.CARD_STATE_CAN_SEND_STOP.getCode()) {
            // 设置发送状态为可发送/停止
            hsaCardOpenRule.setSendStatus(openRule.getSendStatus());
        } else {
            // 判断卡开门规则对象的发送状态是否为空
            if (ObjectUtil.isNull(cardOpenRuleQO.getSendStatus())) {
                // 判断卡开门规则对象的电子开门方式是否为指定分配
                if (cardOpenRuleQO.getElectronicOpenWay() == ElectronicOpenWayEnum.ASSIGN_OPEN.getCode()) {
                    // 检查指定分配的卡开门规则
                    checkAssignOpen(cardOpenRuleQO, hsaCardOpenRule, now);
                } else {
                    // 检查非指定分配的卡开门规则
                    checkNotAssignOpen(cardOpenRuleQO, hsaCardOpenRule, now);
                }
            } else {
                // 设置发送状态为草稿
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_ROUGH.getCode());
            }
        }
    }

    /**
     * 检查是否已设置打开卡片时间类型，并根据当前时间设置发送状态。
     *
     * @param cardOpenRuleQO 卡片打开规则
     * @param hsaCardOpenRule 卡片打开规则
     * @param now 当前时间
     */
    private static void checkNotAssignOpen(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, LocalDateTime now) {
        if (ObjectUtil.isNotNull(cardOpenRuleQO.getOpenCardTimeType()) && cardOpenRuleQO.getOpenCardTimeType() == BooleanEnum.TRUE.getCode()) {
            hsaCardOpenRule.setOpenCardStartTime(cardOpenRuleQO.getOpenCardStartTime());
            hsaCardOpenRule.setOpenCardEndTime(cardOpenRuleQO.getOpenCardEndTime());
            if (now.isBefore(hsaCardOpenRule.getOpenCardStartTime())) {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_NOT_START.getCode());
            } else if (now.isAfter(hsaCardOpenRule.getOpenCardEndTime())) {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
            } else {
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
            }
        } else {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
        }
    }


    private static void checkAssignOpen(CardOpenRuleQO cardOpenRuleQO, HsaCardOpenRule hsaCardOpenRule, LocalDateTime now) {
        if (now.isBefore(cardOpenRuleQO.getElectronicDecideOpenDate())) {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_START.getCode());
        } else {
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
        }
    }

    private void getHsaMemberCardRule(HsaCardOpenRule hsaCardOpenRule, HeaderUserInfo headerUserInfo, MemberCardRuleQO memberCardRuleQO, HsaMemberCardRule hsaMemberCardRule) {
        hsaMemberCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCardRule.class.getSimpleName()))
                .setMemberPhoneNum(memberCardRuleQO.getPhoneNum())
                .setCardOpenRuleGuid(hsaCardOpenRule.getGuid())
                .setCardGuid(hsaCardOpenRule.getCardGuid())
                .setUserName(memberCardRuleQO.getUserName())
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setOperationMemberInfoGuid(Long.parseLong(memberCardRuleQO.getMemberGuid()))
                .setMemberAccount(memberCardRuleQO.getMemberAccount());
    }

    private HsaCardBaseInfo saveOrUpdateCardBaseInfo(CardBaseInfoQO cardBaseInfoQO, List<HsaStoreCardRule> hsaStoreCardRules, HeaderUserInfo headerUserInfo) {
        //实体
        HsaCardBaseInfo hsaCardBaseInfo;
        //验证
        checkCardName(cardBaseInfoQO, headerUserInfo);
        if (StringUtils.isNotEmpty(cardBaseInfoQO.getGuid())) {
            hsaCardBaseInfo = queryByGuid(cardBaseInfoQO.getGuid());
            if (Objects.isNull(hsaCardBaseInfo)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_FIND_CARD);
            }
            BeanUtils.copyProperties(cardBaseInfoQO, hsaCardBaseInfo);
        } else {
            hsaCardBaseInfo = new HsaCardBaseInfo();
            BeanUtils.copyProperties(headerUserInfo, hsaCardBaseInfo);
            BeanUtils.copyProperties(cardBaseInfoQO, hsaCardBaseInfo);
            hsaCardBaseInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaCardBaseInfo.class.getSimpleName()));
        }
        //封装
        processorPO(cardBaseInfoQO, hsaCardBaseInfo, headerUserInfo, hsaStoreCardRules);
        hsaCardBaseInfo.setOperatorName(headerUserInfo.getUserName() + StringConstant.STR_BIAS
                + headerUserInfo.getTel());
        return hsaCardBaseInfo;
    }

    /**
     * 处理卡基础信息
     *
     * @param cardBaseInfoQO    cardBaseInfoQO
     * @param hsaCardBaseInfo   hsaCardBaseInfo
     * @param headerUserInfo    headerUserInfo
     * @param hsaStoreCardRules hsaStoreCardRules
     */
    private void processorPO(CardBaseInfoQO cardBaseInfoQO, HsaCardBaseInfo hsaCardBaseInfo, HeaderUserInfo headerUserInfo, List<HsaStoreCardRule> hsaStoreCardRules) {

        //卡充值门槛
        setRechargeMoneys(cardBaseInfoQO, hsaCardBaseInfo);

        //门店范围修改
        updateCardStore(cardBaseInfoQO, hsaCardBaseInfo, headerUserInfo, hsaStoreCardRules);

        //卡状态设置
        setCardStatus(cardBaseInfoQO, hsaCardBaseInfo);
    }

    private static void setCardStatus(CardBaseInfoQO cardBaseInfoQO, HsaCardBaseInfo hsaCardBaseInfo) {
        if (Objects.nonNull(cardBaseInfoQO.getCardValidity())) {
            if (ObjectUtil.isNull(cardBaseInfoQO.getCardStatus())) {
                if (hsaCardBaseInfo.getCardValidity() == 2) {
                    LocalDate now = LocalDate.now();
                    if (now.isBefore(hsaCardBaseInfo.getCardValidityDate())) {
                        hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_START.getCode());
                    } else {
                        hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
                    }
                } else {
                    hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_START.getCode());
                }
            } else {
                hsaCardBaseInfo.setCardStatus(SendCardStateEnum.CARD_STATE_ROUGH.getCode());
            }
        }
    }

    private void updateCardStore(CardBaseInfoQO cardBaseInfoQO, HsaCardBaseInfo hsaCardBaseInfo, HeaderUserInfo headerUserInfo, List<HsaStoreCardRule> hsaStoreCardRules) {
        if (Objects.nonNull(cardBaseInfoQO.getApplicableAllStore())
                && (cardBaseInfoQO.getApplicableAllStore() == BooleanEnum.FALSE.getCode()
                && CollUtil.isNotEmpty(cardBaseInfoQO.getStoreCardRuleQOList()))) {

            //只删除卡范围
            hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .eq(HsaStoreCardRule::getCardGuid, cardBaseInfoQO.getGuid())
                    .isNull(HsaStoreCardRule::getMemberInfoCardGuid));
            cardBaseInfoQO.getStoreCardRuleQOList().forEach(in -> {
                HsaStoreCardRule hsaStoreCardRule = getHsaStoreCardRule(hsaCardBaseInfo, headerUserInfo, in);
                hsaStoreCardRules.add(hsaStoreCardRule);
                if (CollUtil.isNotEmpty(in.getStoreBoothCardRuleQOList())) {
                    in.getStoreBoothCardRuleQOList().forEach(bo -> {
                        HsaStoreCardRule rule = getHsaStoreCardRule(hsaCardBaseInfo, headerUserInfo, bo, hsaStoreCardRule);
                        hsaStoreCardRules.add(rule);
                    });
                }
            });

        }
    }

    private HsaStoreCardRule getHsaStoreCardRule(HsaCardBaseInfo hsaCardBaseInfo, HeaderUserInfo headerUserInfo, StoreCardRuleQO in) {
        HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setCardGuid(hsaCardBaseInfo.getGuid())
                .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setStoreGuid(in.getStoreGuid())
                .setAddress(in.getAddress())
                .setTime(in.getTime())
                .setAddressPoint(in.getAddress_point())
                .setStoreName(in.getStoreName())
                .setMemberInfoCardGuid(null)
                .setStoreNumber(in.getStoreNumber());
        return hsaStoreCardRule;
    }

    private HsaStoreCardRule getHsaStoreCardRule(HsaCardBaseInfo hsaCardBaseInfo, HeaderUserInfo headerUserInfo, StoreBoothCardRuleQO bo, HsaStoreCardRule hsaStoreCardRule) {
        HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setCardGuid(hsaCardBaseInfo.getGuid())
                .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setStoreName(bo.getStoreName())
                .setAddress(bo.getAddress())
                .setAddressPoint(bo.getAddressPoint())
                .setTime(bo.getTime())
                .setStoreGuid(bo.getStoreGuid())
                .setStoreNumber(bo.getStoreNumber())
                .setParentGuid(hsaStoreCardRule.getGuid())
                .setMemberInfoCardGuid(null);
        return rule;
    }

    private static void setRechargeMoneys(CardBaseInfoQO cardBaseInfoQO, HsaCardBaseInfo hsaCardBaseInfo) {
        if (Objects.nonNull(cardBaseInfoQO.getIsRechargeStipulate())
                && (cardBaseInfoQO.getIsRechargeStipulate() == BooleanEnum.TRUE.getCode()
                && CollUtil.isNotEmpty(cardBaseInfoQO.getRechargeMoneys()))) {
            hsaCardBaseInfo.setRechargeMoneys(JSON.toJSONString(cardBaseInfoQO.getRechargeMoneys()));

        }
    }

    @Override
    public Boolean checkCardName(String cardName) {
        checkCardName(new CardBaseInfoQO().setCardName(cardName), ThreadLocalCache.getHeaderUserInfo());
        return true;
    }

    @Override
    public List<HsaCardBaseInfo> getCardBaseInfoList(List<String> guids, String operSubjectGuid) {
        return hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(!StringUtils.isEmpty(operSubjectGuid), HsaCardBaseInfo::getOperSubjectGuid, operSubjectGuid)
                .in(HsaCardBaseInfo::getGuid, guids));
    }

    @Override
    public List<CardNameVO> getCardNameList(List<String> guids, String operSubjectGuid) {
        if (CollUtil.isEmpty(guids)) {
            return Lists.newArrayList();
        }
        return hsaCardBaseInfoMapper.getCardNameList(guids, operSubjectGuid);
    }

    @Override
    public CardBaseInfoVO getCardInfoByGuid(String cardGuid) {
        CardBaseInfoVO cardBaseInfoVO = new CardBaseInfoVO();
        HsaCardBaseInfo hsaCardBaseInfo = baseMapper.queryByGuid(cardGuid);
        BeanUtils.copyProperties(hsaCardBaseInfo, cardBaseInfoVO);
        return cardBaseInfoVO;
    }

    public void checkCardName(CardBaseInfoQO cardBaseInfoQO, HeaderUserInfo headerUserInfo) {
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(HsaCardBaseInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                .eq(HsaCardBaseInfo::getCardName, cardBaseInfoQO.getCardName()));
        if (StringUtils.isNotEmpty(cardBaseInfoQO.getGuid())) {
            if (Objects.nonNull(hsaCardBaseInfo) && !String.valueOf(hsaCardBaseInfo.getGuid()).equals(cardBaseInfoQO.getGuid())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NAME, ThreadLocalCache.getOperSubjectGuid()));
            }
        } else {
            if (Objects.nonNull(hsaCardBaseInfo)) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NAME, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
    }
}
