package com.holderzone.member.base.mapper.activity;


import com.holderzone.member.base.entity.activity.HsaSubsidyActivityDetailRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.RequestSubsidyRecordDetailQO;
import com.holderzone.member.common.vo.card.SubsidyRecordDetailVO;
import com.holderzone.member.common.vo.card.SubsidyRecordStatisticalVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 补贴活动记录明细表
 * @date 2021/10/26 9:42
 */
public interface HsaSubsidyActivityDetailRecordMapper extends HolderBaseMapper<HsaSubsidyActivityDetailRecord> {

    List<HsaSubsidyActivityDetailRecord> subsidyRecordDetailList(@Param("request") RequestSubsidyRecordDetailQO request);

    List<SubsidyRecordDetailVO> subsidyRecordDetailListVO(@Param("request") RequestSubsidyRecordDetailQO request);

    List<SubsidyRecordDetailVO> subsidySpecifiedRecordDetailListVO(@Param("request") RequestSubsidyRecordDetailQO request);

    SubsidyRecordStatisticalVO subsidyDetailStatisticalLista(@Param("request") RequestSubsidyRecordDetailQO request);

    void batchUpdateName(@Param("request") String request, @Param("name") String name);

    void batchUpdateDestroy(@Param("subsidyGuids") List<String> subsidyGuids,@Param("status") Integer status);

    BigDecimal findAllSoonOverdueSubsidy(@Param("detailRecordGuid") List<String> detailRecordGuid);
}
