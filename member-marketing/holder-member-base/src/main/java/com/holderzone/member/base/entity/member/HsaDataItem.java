package com.holderzone.member.base.entity.member;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class HsaDataItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GUID
     */
    private String guid;

    /**
     * 平台信息
     */
    private String platform;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 资料项信息（JSON格式存储）
     */
    private String dataItem;

    /**
     * 操作人名字
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
