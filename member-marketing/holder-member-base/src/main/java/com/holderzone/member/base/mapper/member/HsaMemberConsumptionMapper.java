package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.growth.HsaSuspendTaskTimeQuantum;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.dto.member.MemberConsumptionInfoDTO;
import com.holderzone.member.common.qo.card.ConsumptionDetailQO;
import com.holderzone.member.common.qo.card.RechargeDetailQO;
import com.holderzone.member.common.vo.card.ConsumptionDetailVO;
import com.holderzone.member.common.vo.card.RechargeDetailVO;
import com.holderzone.member.common.vo.member.ConsumptionOrderTotalVO;
import com.holderzone.member.common.vo.member.MemberConsumptionInfoVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员消费记录表
 */
public interface HsaMemberConsumptionMapper extends HolderBaseMapper<HsaMemberConsumption> {


    BigDecimal getAllConsumption(@Param("memberGuid") String memberGuid);

    List<RechargeDetailVO> getRechargeDetail(@Param("request") RechargeDetailQO request);

    List<ConsumptionDetailVO> getConsumptionDetail(@Param("request") ConsumptionDetailQO request);

    ConsumptionOrderTotalVO getTotalConsumptionDetail(@Param("memberGuid") String memberGuid);

    ConsumptionOrderTotalVO getTotalRefundDetail(@Param("memberGuid") String memberGuid);

    List<MemberConsumptionInfoDTO> getConsumptionDetailByMemberGuid(@Param("memberGuidList") List<String> memberGuidList);

    List<MemberConsumptionInfoDTO> getConsumptionCountByMemberGuid(@Param("memberGuidList") List<String> memberGuidList);

    List<MemberConsumptionInfoDTO> getRechargeDetailByMemberGuid(@Param("memberGuidList") List<String> memberGuidList);

    BigDecimal getAllRecharge(@Param("memberGuid") String memberGuid);

    /**
     * 功能描述：累计消费的金额
     *
     * @param beginTime       开始时间
     * @param totalPeriodType 统计周期类型
     * @param memberInfoGuid  会员guid
     * @param consumptionType 消费类型
     * @param gmtCreate       成长值任务创建时间
     * @return java.math.BigDecimal
     * @date 2021/11/26
     */
    BigDecimal totalPeriodAmount(@Param("beginTime") String beginTime,
                                 @Param("periodType") Integer totalPeriodType,
                                 @Param("memberInfoGuid") String memberInfoGuid,
                                 @Param("consumptionType") Integer consumptionType,
                                 @Param("gmtCreate") LocalDateTime gmtCreate);

    /**
     * 功能描述：累计消费的笔数
     *
     * @param beginTime       开始时间
     * @param totalPeriodType 统计周期类型
     * @param memberInfoGuid  会员guid
     * @return java.lang.Integer
     * @date 2021/11/29
     */
    Integer totalPeriodCount(@Param("beginTime") String beginTime,
                             @Param("periodType") Integer totalPeriodType,
                             @Param("memberInfoGuid") String memberInfoGuid,
                             @Param("gmtCreate") LocalDateTime gmtCreate,
                             @Param("consumptionIgnoreAmount") BigDecimal consumptionIgnoreAmount);


    /**
     * 查询订单
     *
     * @param cardNum
     * @param operSubjectGuid
     * @return
     */
    List<HsaMemberConsumption> getOneOrder(@Param("cardNum") String cardNum, @Param("operSubjectGuid") String operSubjectGuid, @Param("memberConsumptionGuid") String memberConsumption);

    /**
     * 消费后满足关联标签条件的会员guid
     *
     * @param query sql请求参数
     * @return 查询结果
     */
    List<MemberConsumptionInfoVO> findRelationLabelMemberGuid(@Param("query") RequestLabelQuery query);

    BigDecimal selectRechargeAmountMemberGuid(@Param("memberInfoGuid") String memberInfoGuid, @Param("guid") String guid,
                                              @Param("beginTime") LocalDateTime beginTime,
                                              @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    BigDecimal selectRechargeBackAmountMemberGuid(@Param("memberInfoGuid") String memberInfoGuid,
                                                  @Param("guid") String guid,
                                                  @Param("beginTime") LocalDateTime beginTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    BigDecimal selectByMemberGuid(@Param("memberInfoGuid") String memberInfoGuid, @Param("guid") String guid,
                                  @Param("beginTime") LocalDateTime beginTime, @Param("apply") List<String> apply,
                                  @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    BigDecimal selectByMemberGuidBackAmount(@Param("memberInfoGuid") String memberInfoGuid,
                                            @Param("guid") String guid,
                                            @Param("beginTime") LocalDateTime beginTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("apply") List<String> apply,
                                            @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    Integer getConsumptionCount(@Param("memberInfoGuid") String memberInfoGuid,
                                @Param("guid") String guid,
                                @Param("beginTime") LocalDateTime beginTime,
                                @Param("consumptionAmount") BigDecimal consumptionAmount,
                                @Param("apply") List<String> apply,
                                @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    Integer getBackConsumptionCount(@Param("memberInfoGuid") String memberInfoGuid,
                                    @Param("guid") String guid,
                                    @Param("beginTime") LocalDateTime beginTime,
                                    @Param("endTime") LocalDateTime endTime,
                                    @Param("consumptionAmount") BigDecimal consumptionAmount,
                                    @Param("apply") List<String> apply,
                                    @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    void updateIsNull(@Param("memberConsumption") HsaMemberConsumption memberConsumption);


    void updateCompany(@Param("memberInfo") HsaOperationMemberInfo hsaOperationMemberInfo);

    BigDecimal getTotalRefundAmount(@Param("memberGuid") String memberGuid);

    Integer getTotalRefundNum(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberTotalRefundAmount(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberAllRecharge(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberAllGift(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberTotalConsumptionDiscounts(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberTotalConsumptionPaid(@Param("memberGuid") String memberGuid);

    Integer getMemberTotalConsumptionCount(@Param("memberGuid") String memberGuid);

    BigDecimal getAllConsumptionRecharge(@Param("memberGuid") String memberGuid);

    Integer getAllConsumptionRechargeCount(@Param("memberGuid") String memberGuid);

    BigDecimal getMemberConsumptionGift(@Param("memberGuid") String memberGuid);


    List<String> getConsumptionCountNew(@Param("memberInfoGuid") String memberInfoGuid,
                                @Param("guid") String guid,
                                @Param("beginTime") LocalDateTime beginTime,
                                @Param("consumptionAmount") BigDecimal consumptionAmount,
                                @Param("apply") List<String> apply,
                                @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);
}
