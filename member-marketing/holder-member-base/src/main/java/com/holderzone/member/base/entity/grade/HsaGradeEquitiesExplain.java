package com.holderzone.member.base.entity.grade;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 会员等级权益说明
 * @author: pan tao
 * @create: 2022-01-05 10:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGradeEquitiesExplain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 等级说明类型 0：系统生成 1：自定义生成
     * @see com.holderzone.member.common.enums.grade.GradeExplainTypeEnum
     */
    private int type;

    /**
     * 自定义内容
     */
    private String customContent;

    /**
     * 操作人 姓名/账号
     */
    private String operatorName;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

}
