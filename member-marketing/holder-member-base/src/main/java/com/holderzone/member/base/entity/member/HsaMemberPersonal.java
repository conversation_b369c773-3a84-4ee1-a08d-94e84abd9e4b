package com.holderzone.member.base.entity.member;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 会员个人经历信息表
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberPersonal implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String guid;

    /**
     * 会员GUID
     */
    private String memberGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 视频地址
     */
    private String videoJson;


    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

}
