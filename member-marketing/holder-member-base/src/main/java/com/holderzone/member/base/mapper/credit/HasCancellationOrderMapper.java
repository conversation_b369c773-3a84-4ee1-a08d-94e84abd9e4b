package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HasCancellationOrder;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.credit.CreditOrderRecordQO;
import com.holderzone.member.common.vo.credit.CreditOrderRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-21 09:24
 */
public interface HasCancellationOrderMapper extends HolderBaseMapper<HasCancellationOrder> {


    List<CreditOrderRecordVO> queryCreditOrderRecords(@Param("request") CreditOrderRecordQO request);


}
