package com.holderzone.member.base.mapper.growth;

import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.growth.GrowthValueTaskListQO;
import com.holderzone.member.common.vo.grade.GrowthValueVO;
import com.holderzone.member.common.vo.growth.GrowthValueTaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成长值任务mapper
 * author: pantao
 */
public interface HsaGrowthValueTaskMapper extends HolderBaseMapper<HsaGrowthValueTask> {

    /**
     * 查询成长值任务列表
     *
     * @param request 成长值任务请求QO
     * @return 成长值任务列表返回vo
     */
    List<GrowthValueTaskVO> queryGrowthValueTask(@Param("request") GrowthValueTaskListQO request);

    /**
     * 查询门店下应用业务数量
     *
     * @param storeGuids 门店guid
     * @param keywords   关键字查询
     * @return 查询结果
     */
    int findStoreApplyBusinessNumber(@Param("storeGuids") List<String> storeGuids, @Param("keywords") String keywords);

    /**
     * 所有排序位置+1
     *
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    int addOnePosition(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 上移位置
     *
     * @param operSubjectGuid 运营主体
     * @param minPosition     最小位置
     * @param maxPosition     最大位置
     * @return 操作结果
     */
    int moveUpPosition(@Param("operSubjectGuid") String operSubjectGuid, @Param("minPosition") Integer minPosition,
                       @Param("maxPosition") Integer maxPosition);

    /**
     * 下移位置
     *
     * @param operSubjectGuid 运营主体
     * @param minPosition     最小位置
     * @param maxPosition     最大位置
     * @return 操作结果
     */
    int moveDownPosition(@Param("operSubjectGuid") String operSubjectGuid, @Param("minPosition") Integer minPosition,
                         @Param("maxPosition") Integer maxPosition);

    /**
     * 查询最大位置
     *
     * @param operSubjectGuid 运营主体
     * @return 查询结果
     */
    int findMaxPosition(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 查询运营主体下 进行中，未开始的所有活动类型
     *
     * @param operSubjectGuid 运营主体
     * @return 查询结果
     */
    List<Integer> findAllTaskActionType(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 查询进行中的成长值任务
     *
     * @param operSubjectGuid 运营主体guid
     * @return 查询结果
     */
    List<GrowthValueVO> findNormalGrowthValue(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 判断时候存在相同活动的开始任务
     *
     * @param operSubjectGuid 运营主体
     * @param taskAction      活动类型
     * @return 查询结果
     */
    int findHadExistTaskAction(@Param("operSubjectGuid") String operSubjectGuid, @Param("taskAction") Integer taskAction,
                               @Param("applyBusiness") String applyBusiness);

}
