package com.holderzone.member.base.service.credit;

import com.holderzone.member.base.entity.credit.HsaCreditFundingDetail;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.credit.CreditFundingQO;
import com.holderzone.member.common.vo.credit.CreditFundingTotalVO;

/**
 * @description: 挂账资金来往记录service
 * <AUTHOR>
 */
public interface HsaCreditFundingDetailService extends IHolderBaseService<HsaCreditFundingDetail> {

    /**
     * 获取当前用户挂账明细信息
     * @param creditFundingQO 请求参数
     * @return 操作结果
     */
    PageResult creditFundingDetail(CreditFundingQO creditFundingQO);

    /**
     * 获取当前用户挂账明细统计信息
     * @param memberInfoGuid 请求参数guid
     * @param creditInfoGuid 挂账账户guid
     * @return 操作结果
     */
    CreditFundingTotalVO creditFundingTotal(String memberInfoGuid, String creditInfoGuid);
}
