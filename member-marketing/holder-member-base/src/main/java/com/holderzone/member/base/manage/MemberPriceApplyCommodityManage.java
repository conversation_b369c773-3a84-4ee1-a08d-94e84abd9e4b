package com.holderzone.member.base.manage;

import com.holderzone.member.base.dto.CardEquitiesListDTO;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.service.grade.HsaMemberGradePriceDetailService;
import com.holderzone.member.base.support.CardSettlementSupport;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.qo.equities.MemberPriceApplyCommodityQO;
import com.holderzone.member.common.vo.equities.MemberPriceApplyCommodityVO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 商品展示页优惠计算
 *
 * <AUTHOR>
 * @date 2023/11/15
 * @since 1.8
 */
@Component
public class MemberPriceApplyCommodityManage {

    /**
     * 等级折扣
     */
    @Resource
    private HsaMemberGradePriceDetailService hsaMemberGradePriceDetailService;

    /**
     * 卡权益查询
     */
    @Resource
    private CardSettlementSupport cardSettlementSupport;

    /**
     * 获取等级 or 卡权益（最优）
     *
     * @param qo 条件入参
     * @return 折扣结果
     */
    public MemberPriceApplyCommodityVO getMemberPriceApplyCommodity(@RequestBody MemberPriceApplyCommodityQO qo) {
        //等级折扣
        final MemberPriceApplyCommodityVO gradeDiscountVo = hsaMemberGradePriceDetailService.getMemberPriceApplyCommodity(qo);
        if (Objects.nonNull(gradeDiscountVo.getDiscountDynamics())) {
            //优先等级
            return gradeDiscountVo;
        }
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        //查询卡权益列表
        final List<CardEquitiesListDTO> equitiesDtoList = cardSettlementSupport.listEquitiesByCards(operSubjectGuid, qo.getMemberInfoGuid(),qo.getStoreGuid());
        if (equitiesDtoList.isEmpty()) {
            //空对象
            return gradeDiscountVo;
        }
        //折扣排序，从小到大
        equitiesDtoList.sort(Comparator.comparing(e -> e.getEquities().getDiscountDynamics()));
        for (CardEquitiesListDTO equitiesListDTO : equitiesDtoList) {
            final HsaBusinessEquities equities = equitiesListDTO.getEquities();
            //满足的折扣对象
            final MemberPriceApplyCommodityVO priceApplyCommodityVO = hsaMemberGradePriceDetailService.getMemberPriceApplyCommodityVO(qo, equities);
            if (Objects.nonNull(priceApplyCommodityVO.getDiscountDynamics())) {
                //返回卡折扣最大
                return priceApplyCommodityVO;
            }
        }
        return gradeDiscountVo;
    }

}
