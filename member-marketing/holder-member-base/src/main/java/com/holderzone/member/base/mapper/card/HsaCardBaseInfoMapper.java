package com.holderzone.member.base.mapper.card;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.QueryCardInfoPageQO;
import com.holderzone.member.common.vo.card.CardNameVO;
import com.holderzone.member.common.vo.card.QueryCardInfoPageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaCardBaseInfoMapper extends HolderBaseMapper<HsaCardBaseInfo> {


    List<QueryCardInfoPageVO> getCardInfoPage(Page<QueryCardInfoPageVO> page, @Param("request") QueryCardInfoPageQO request);

    /**
     * 通过guids查询会员卡名称
     * @param guids guids
     * @param operSubjectGuid 运营主体
     * @return 会员卡基础信息集合
     */
    List<CardNameVO> getCardNameList(@Param("guids") List<String> guids,@Param("operSubjectGuid") String operSubjectGuid);

}
