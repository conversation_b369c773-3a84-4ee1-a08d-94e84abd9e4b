package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 挂账使用人实体
 * @author: pan tao
 * @create: 2022-02-09 14:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCreditUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 挂账信息guid
     */
    private String creditInfoGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 单人可挂账金额上限 0代表不限制
     */
    private BigDecimal singlePersonUpperLimit;

    /**
     * 单笔上限 0代表不限制
     */
    private BigDecimal singleCountUpperLimit;

    /**
     * 是否开启 0：开启  1：禁用
     *
     * @see com.holderzone.member.common.enums.member.EnableEnum
     */
    private Integer isEnable;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 累计挂账
     */
    private BigDecimal totalCredit;

    /**
     * 付款账户默认选择
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer defaultChoose;

}
