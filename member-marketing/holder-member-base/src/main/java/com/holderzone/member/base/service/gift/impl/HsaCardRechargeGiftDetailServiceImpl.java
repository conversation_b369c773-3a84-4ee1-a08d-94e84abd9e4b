package com.holderzone.member.base.service.gift.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.MemberCardAssembler;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.base.entity.recharge.HsaRechargeGiftAmountRecord;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaCardInfoMapper;
import com.holderzone.member.base.mapper.card.HsaElectronicCardMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.gift.HsaCardRechargeGiftDetailMapper;
import com.holderzone.member.base.mapper.gift.HsaRechargeGiftAmountRecordMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaMemberFundingDetailMapper;
import com.holderzone.member.base.service.assembler.MemberCouponApplyAssembler;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.base.service.gift.HsaRechargeGiftAmountRecordService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.CardInfoDetailDTO;
import com.holderzone.member.common.dto.gift.RechargeGiftEntity;
import com.holderzone.member.common.dto.gift.RechargeGiftThresholdEntity;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.enums.gift.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.common.qo.card.TerCardRechargeGiftQO;
import com.holderzone.member.common.qo.card.TerMemberCardRechargeQO;
import com.holderzone.member.common.qo.gift.RechargeGiftStoreQO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityRunVO;
import com.holderzone.member.common.vo.gift.RechargeGiftAmountRecordVO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;
import com.holderzone.member.common.vo.gift.RechargeSuccessGiftDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HsaCardRechargeGiftDetailServiceImpl extends HolderBaseServiceImpl<HsaCardRechargeGiftDetailMapper, HsaCardRechargeGiftDetail>
        implements HsaCardRechargeGiftDetailService {

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private MemberCouponApplyAssembler assembler;

    @Resource
    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    @Lazy
    private HsaCardInfoService hsaCardInfoService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaCardInfoMapper hsaCardInfoMapper;

    @Resource
    private HsaElectronicCardMapper hsaElectronicCardMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    @Resource
    private HsaCardRechargeGiftDetailMapper hsaCardRechargeGiftDetailMapper;

    @Resource
    private HsaRechargeGiftAmountRecordService hsaRechargeGiftAmountRecordService;

    @Resource
    private HsaRechargeGiftAmountRecordMapper hsaRechargeGiftAmountRecordMapper;

    /**
     * 标签关联service
     */
    private final HsaMemberLabelService hsaMemberLabelService;

    private final Executor memberBaseThreadExecutor;

    public HsaCardRechargeGiftDetailServiceImpl(HsaMemberLabelService hsaMemberLabelService, Executor memberBaseThreadExecutor) {
        this.hsaMemberLabelService = hsaMemberLabelService;
        this.memberBaseThreadExecutor = memberBaseThreadExecutor;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RechargeOrderGiftSummaryVO dealRechargeGift(TerCardRechargeGiftQO terCardRechargeGiftQO) {
        RechargeOrderGiftSummaryVO giftSummaryVO = new RechargeOrderGiftSummaryVO();
        log.info("处理充值赠送入参：{}", JSON.toJSONString(terCardRechargeGiftQO));
        List<RechargeGiftActivityRunVO> activityRunVOS = memberMarketingFeign.getActivityByRun(terCardRechargeGiftQO.getOperSubjectGuid());
        if (CollUtil.isNotEmpty(activityRunVOS)) {
            String memberLockId = getLockId(terCardRechargeGiftQO);
            //获取用户标签
            terCardRechargeGiftQO.setLabelGuid(hsaLabelSettingService.getLabelGuidByMember(memberLockId));
            //处理活动
            forDealActivityHandle(terCardRechargeGiftQO, giftSummaryVO, activityRunVOS);
        }

        return giftSummaryVO;
    }


    @Override
    public void fixedEffectiveGiftAmount() {

        //获取未触发过的  金额
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGift = hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                .le(HsaCardRechargeGiftDetail::getMoneyEffectiveTime, LocalDateTime.now())
                .eq(HsaCardRechargeGiftDetail::getIsRefresh, BooleanEnum.FALSE.getCode())
                .eq(HsaCardRechargeGiftDetail::getMoneyEffectiveType, GiftEffectiveEnum.FIXED_EFFECTIVE.getCode()));

        log.info("未触发的数据：{}", JSON.toJSONString(hsaCardRechargeGift));
        if (CollUtil.isEmpty(hsaCardRechargeGift)) {
            return;
        }
        checkMemberInfoCards(hsaCardRechargeGift);
    }

    @Override
    public List<String> getActivityDetailsMemberLabels(String fundingDetailGuid) {
        log.info("[删除充值标签]fundingDetailGuid={}", fundingDetailGuid);
        //获取未触发过的  金额
        final List<HsaCardRechargeGiftDetail> giftDetails = hsaCardRechargeGiftDetailMapper.selectList(
                new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .eq(HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, fundingDetailGuid)
        );
        if (CollectionUtils.isEmpty(giftDetails)) {
            log.warn("[删除充值标签]没有充值记录");
            return Collections.emptyList();
        }
        List<String> memberGuidList = giftDetails.stream()
                .map(HsaCardRechargeGiftDetail::getMemberGuid)
                .filter(com.holderzone.framework.util.StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(memberGuidList)) {
            log.warn("[删除充值标签]没有会员id");
            return Collections.emptyList();
        }
        List<String> activityGuidList = giftDetails.stream()
                .map(HsaCardRechargeGiftDetail::getActivityGuid)
                .distinct()
                .collect(Collectors.toList());

        List<HsaRechargeGiftAmountRecord> amountRecordList = hsaRechargeGiftAmountRecordService.list(
                new LambdaQueryWrapper<HsaRechargeGiftAmountRecord>()
                        .in(HsaRechargeGiftAmountRecord::getMemberInfoGuid, memberGuidList)
                        .in(HsaRechargeGiftAmountRecord::getRechargeActivityGuid, activityGuidList)
                        .ne(HsaRechargeGiftAmountRecord::getMemberFundingDetailGuid, fundingDetailGuid)
                        .eq(HsaRechargeGiftAmountRecord::getRechargeStatus, BooleanEnum.TRUE.getCode())
        );
        if (!CollectionUtils.isEmpty(amountRecordList)) {
            log.info("[删除充值标签]有记录，不删标签");
            return Collections.emptyList();
        }

        List<String> list = new ArrayList<>();
        giftDetails.stream().map(HsaCardRechargeGiftDetail::getMarkingLabelGuidJson)
                .forEach(label -> list.addAll(JacksonUtils.toObjectList(String.class, label)));
        return list.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<HsaCardRechargeGiftDetail> getNoRefreshDetails(String fundingDetailGuid) {
        //获取未触发过的  金额
        final List<HsaCardRechargeGiftDetail> giftDetails = hsaCardRechargeGiftDetailMapper.selectList(
                new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .eq(HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, fundingDetailGuid));
        return giftDetails == null ? Collections.emptyList() : giftDetails;

    }

    private void checkMemberInfoCards(List<HsaCardRechargeGiftDetail> hsaCardRechargeGift) {
        List<String> cardGuidList = hsaCardRechargeGift.stream().map(HsaCardRechargeGiftDetail::getCardGuid).collect(Collectors.toList());
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = getHsaCardBaseInfoMap(cardGuidList);

        //处理电子卡赠送
        dealRechargeGiftElectronic(hsaCardRechargeGift, hsaCardBaseInfoMap);

        //处理实体卡赠送
        dealRechargeGiftPhysical(hsaCardRechargeGift, hsaCardBaseInfoMap);
    }

    private Map<String, HsaCardBaseInfo> getHsaCardBaseInfoMap(List<String> cardGuidList) {
        return hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, cardGuidList))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (obj1, obj2) -> obj1));
    }

    private void dealRechargeGiftElectronic(List<HsaCardRechargeGiftDetail> hsaCardRechargeGift, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap) {
        List<HsaCardRechargeGiftDetail> rechargeGiftElectronicList = hsaCardRechargeGift.stream()
                .filter(in -> StringUtils.isNotEmpty(in.getElectronicCardGuid()))
                .collect(Collectors.toList());


        if (isNotEmpty(rechargeGiftElectronicList)) {

            List<String> electronicCardGuid = rechargeGiftElectronicList.stream()
                    .map(HsaCardRechargeGiftDetail::getElectronicCardGuid)
                    .collect(Collectors.toList());

            List<HsaMemberInfoCard> memberElectronicList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getElectronicCardGuid, electronicCardGuid));


            Map<String, List<HsaCardRechargeGiftDetail>> rechargeGiftElectronicMap = rechargeGiftElectronicList.stream()
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getElectronicCardGuid));
            dealGiftAmount(hsaCardBaseInfoMap, memberElectronicList, rechargeGiftElectronicMap);

        }
    }

    private void dealRechargeGiftPhysical(List<HsaCardRechargeGiftDetail> hsaCardRechargeGift, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap) {
        List<HsaCardRechargeGiftDetail> rechargeGiftPhysicalList = hsaCardRechargeGift.stream()
                .filter(in -> StringUtils.isNotEmpty(in.getPhysicalCardGuid()))
                .collect(Collectors.toList());

        if (isNotEmpty(rechargeGiftPhysicalList)) {

            List<String> physicalCardGuid = rechargeGiftPhysicalList.stream()
                    .map(HsaCardRechargeGiftDetail::getPhysicalCardGuid)
                    .collect(Collectors.toList());

            List<HsaMemberInfoCard> memberPhysicalList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getPhysicalCardGuid, physicalCardGuid));

            Map<String, List<HsaCardRechargeGiftDetail>> rechargeGiftElectronicMap = rechargeGiftPhysicalList.stream()
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getPhysicalCardGuid));
            dealGiftAmount(hsaCardBaseInfoMap, memberPhysicalList, rechargeGiftElectronicMap);
        }
    }

    private static boolean isNotEmpty(List<HsaCardRechargeGiftDetail> rechargeGiftPhysicalList) {
        return CollUtil.isNotEmpty(rechargeGiftPhysicalList);
    }

    private void dealGiftAmount(Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap,
                                List<HsaMemberInfoCard> memberPhysicalList,
                                Map<String, List<HsaCardRechargeGiftDetail>> rechargeGiftElectronicMap) {
        if (CollUtil.isNotEmpty(memberPhysicalList)) {
            for (HsaMemberInfoCard memberInfoCard : memberPhysicalList) {
                //防重复提交
                RLock lock = redissonClient.getLock("CARD_PAY_ORDER_MEMBER:" + memberInfoCard.getGuid());
                try {
                    List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetail = rechargeGiftElectronicMap.get(memberInfoCard.getElectronicCardGuid());
                    dealMemberCardGiftAmount(hsaCardBaseInfoMap, memberInfoCard, hsaCardRechargeGiftDetail);
                } catch (Exception e) {
                    log.error("充值赠送金额发生异常：{}", e.getMessage());
                    throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
                } finally {
                    if (lock.isLocked()) {
                        lock.unlock();
                    }
                }
            }
        }
    }

    private void dealMemberCardGiftAmount(Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, HsaMemberInfoCard memberInfoCard, List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetail) {
        if (CollUtil.isNotEmpty(hsaCardRechargeGiftDetail)) {
            for (HsaCardRechargeGiftDetail cardRechargeGiftDetail : hsaCardRechargeGiftDetail) {
                cardRechargeGiftDetail.setIsRefresh(BooleanEnum.TRUE.getCode());
                hsaCardRechargeGiftDetailMapper.updateByGuid(cardRechargeGiftDetail);
            }
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(memberInfoCard.getCardGuid());
            //生效金额
            BigDecimal moneyAmount = hsaCardRechargeGiftDetail.stream()
                    .map(HsaCardRechargeGiftDetail::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //冲正处理
            TerMemberCardRechargeQO terMemberInfo = new TerMemberCardRechargeQO();
            terMemberInfo.setRechargeMoney(BigDecimal.ZERO);
            terMemberInfo.setGiftAmount(moneyAmount);
            hsaCardInfoService.excessProcessor(terMemberInfo, memberInfoCard, hsaCardBaseInfo);
            hsaMemberInfoCardMapper.updateByGuid(memberInfoCard);
        }
    }

    @Override
    public RechargeSuccessGiftDetailVO getRechargeSuccessGiftDetail(String orderNumber) {
        HsaMemberConsumption hsaMemberConsumer = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, orderNumber));

        if (Objects.isNull(hsaMemberConsumer)) {
            throw new MemberBaseException("没有此订单");
        }

        HsaMemberFundingDetail hsaMemberFundingDetail = hsaMemberFundingDetailMapper.selectOne(new LambdaQueryWrapper<HsaMemberFundingDetail>()
                .eq(HsaMemberFundingDetail::getMemberConsumptionGuid, hsaMemberConsumer.getGuid()));

        if (Objects.isNull(hsaMemberFundingDetail)) {
            throw new MemberBaseException("订单未充值完成");
        }
        RechargeSuccessGiftDetailVO successGiftDetailVO = new RechargeSuccessGiftDetailVO();

        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetail = hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                .eq(HsaCardRechargeGiftDetail::getOrderNum, orderNumber));

        if (isNotEmpty(hsaCardRechargeGiftDetail)) {
            dealRechargeGiftDetail(successGiftDetailVO, hsaCardRechargeGiftDetail);
        }

        return successGiftDetailVO;
    }

    @Override
    public Map<String, Integer> getActivityOrderNum(List<String> activityGuidList) {
        Map<String, Integer> orderNumMap = new HashMap<>();
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList = hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                .in(HsaCardRechargeGiftDetail::getActivityGuid, activityGuidList));
        if (CollUtil.isEmpty(hsaCardRechargeGiftDetailList)) {
            return orderNumMap;
        }

        //获取成功的订单
        Map<String, List<HsaCardRechargeGiftDetail>> hsaCardRechargeGiftDetailMap =
                getHsaCardRechargeGiftDetailMap(activityGuidList, hsaCardRechargeGiftDetailList);


        for (String activityGuid : activityGuidList) {
            if (hsaCardRechargeGiftDetailMap.containsKey(activityGuid)) {
                List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = hsaCardRechargeGiftDetailMap.get(activityGuid);
                orderNumMap.put(activityGuid, hsaCardRechargeGiftDetails.size());
            }
        }
        return orderNumMap;
    }

    private Map<String, List<HsaCardRechargeGiftDetail>> getHsaCardRechargeGiftDetailMap(List<String> activityGuidList, List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList) {
        List<String> successOrder = hsaRechargeGiftAmountRecordService.getSuccessOrder(activityGuidList);
        Map<String, List<HsaCardRechargeGiftDetail>> hsaCardRechargeGiftDetailMap;
        if (CollUtil.isNotEmpty(successOrder)) {
            hsaCardRechargeGiftDetailMap = hsaCardRechargeGiftDetailList.stream()
                    .filter(in -> successOrder.contains(in.getMemberFundingDetailGuid()))
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getActivityGuid));
        } else {
            hsaCardRechargeGiftDetailMap = hsaCardRechargeGiftDetailList.stream()
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getActivityGuid));
        }
        return hsaCardRechargeGiftDetailMap;
    }

    private void dealRechargeGiftDetail(RechargeSuccessGiftDetailVO successGiftDetailVO, List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetail) {
        Map<String, String> cardNameMap = new HashMap<>();
        List<String> cardGuidList = hsaCardRechargeGiftDetail.stream().map(HsaCardRechargeGiftDetail::getGiveCardGuid).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cardGuidList)) {
            cardNameMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                            .in(HsaCardBaseInfo::getGuid, cardGuidList))
                    .stream()
                    .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, HsaCardBaseInfo::getCardName, (entity1, entity2) -> entity1));
        }

        List<String> giftTotalCardNameList = successGiftDetailVO.getGiftTotalCardNameList();
        for (HsaCardRechargeGiftDetail cardRechargeGiftDetail : hsaCardRechargeGiftDetail) {
            if (cardRechargeGiftDetail.getMoneyAmount().compareTo(BigDecimal.ZERO) > 0) {
                successGiftDetailVO.setGiftTotalAmount(successGiftDetailVO.getGiftTotalAmount()
                        .add(cardRechargeGiftDetail.getMoneyAmount()));
            }

            if (cardRechargeGiftDetail.getGiveIntegralValue() > 0) {
                successGiftDetailVO.setGiftTotalIntegralValue(successGiftDetailVO.getGiftTotalIntegralValue()
                        + cardRechargeGiftDetail.getGiveIntegralValue());
            }

            if (cardRechargeGiftDetail.getGiveGrowthValue() > 0) {
                successGiftDetailVO.setGiftTotalGrowthValue(successGiftDetailVO.getGiftTotalGrowthValue()
                        + cardRechargeGiftDetail.getGiveGrowthValue());
            }

            if (StringUtils.isNotBlank(cardRechargeGiftDetail.getGiveCardGuid())
                    && cardNameMap.containsKey(cardRechargeGiftDetail.getGiveCardGuid())) {
                giftTotalCardNameList.add(cardNameMap.get(cardRechargeGiftDetail.getGiveCardGuid()));
            }
        }
        successGiftDetailVO.setGiftTotalCardNameList(giftTotalCardNameList);
    }

    private void forDealActivityHandle(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                       RechargeOrderGiftSummaryVO giftSummaryVO,
                                       List<RechargeGiftActivityRunVO> activityRunVOS) {
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList;
        List<String> markingLabelGuidList = new ArrayList<>();
        List<RechargeGiftAmountRecordVO> recordVOArrayList = Lists.newArrayList();
        for (RechargeGiftActivityRunVO activityRunVO : activityRunVOS) {
            //条件校验
            if (checkCondition(terCardRechargeGiftQO, activityRunVO)) {
                continue;
            }

            //设置基础赠送统计
            RechargeGiftAmountRecordVO rechargeGiftAmountRecordVO = new RechargeGiftAmountRecordVO();
            rechargeGiftAmountRecordVO.setRechargeActivityGuid(activityRunVO.getGuid());
            rechargeGiftAmountRecordVO.setRechargeActivityName(activityRunVO.getActivityName());
            terCardRechargeGiftQO.setRechargeGiftAmountRecordVO(rechargeGiftAmountRecordVO);


            //获取以往门槛触发明细
            List<RechargeGiftThresholdEntity> runVORechargeGiftThresholdList = activityRunVO.getRechargeGiftThresholdList();
            List<String> thresholdGuidList = runVORechargeGiftThresholdList
                    .stream().map(RechargeGiftThresholdEntity::getGuid).collect(Collectors.toList());

            //获取赠送明细
            Map<String, List<HsaCardRechargeGiftDetail>> cardRechargeGiftRuleMap = getCardRechargeGiftRuleMap(
                    terCardRechargeGiftQO,
                    activityRunVO,
                    thresholdGuidList);

            //门槛次数过滤
            checkGiftThresholdLimitSum(activityRunVO, cardRechargeGiftRuleMap, runVORechargeGiftThresholdList);

            if (CollUtil.isNotEmpty(runVORechargeGiftThresholdList)) {
                //处理门槛赠送
                hsaCardRechargeGiftDetailList =
                        forDealThresholdHandle(terCardRechargeGiftQO, giftSummaryVO, activityRunVO, runVORechargeGiftThresholdList);

                //记录统计活动赠送
                RechargeGiftAmountRecordVO giftAmountRecordVO = terCardRechargeGiftQO.getRechargeGiftAmountRecordVO();
                if (giftAmountRecordVO.getGiftAmount().compareTo(BigDecimal.ZERO) > 0) {
                    recordVOArrayList.add(giftAmountRecordVO);
                }

                if (isNotEmpty(hsaCardRechargeGiftDetailList)) {
                    this.saveBatch(hsaCardRechargeGiftDetailList);
                    Optional.ofNullable(activityRunVO.getMarkingLabelGuidList()).ifPresent(markingLabelGuidList::addAll);
                }
            }
        }
        giftSummaryVO.setRechargeGiftAmountRecordList(recordVOArrayList);

        //为参与活动用户打标签
        dealOrderLabel(terCardRechargeGiftQO.getMemberGuid(), markingLabelGuidList);
    }

    private static void checkGiftThresholdLimitSum(RechargeGiftActivityRunVO activityRunVO,
                                                   Map<String, List<HsaCardRechargeGiftDetail>> cardRechargeGiftRuleMap,
                                                   List<RechargeGiftThresholdEntity> runVORechargeGiftThresholdList) {

        List<String> thresholdList = Lists.newArrayList();
        //校验次数
        if (CollUtil.isNotEmpty(cardRechargeGiftRuleMap)) {
            //需要校验次数
            for (RechargeGiftThresholdEntity rechargeGiftThresholdEntity : runVORechargeGiftThresholdList) {
                if (cardRechargeGiftRuleMap.containsKey(rechargeGiftThresholdEntity.getGuid())
                        && rechargeGiftThresholdEntity.getGiftThresholdLimitType() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
                    List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = cardRechargeGiftRuleMap.get(rechargeGiftThresholdEntity.getGuid());

                    int num = hsaCardRechargeGiftDetails.size();


                    if (rechargeGiftThresholdEntity.getGiftThresholdLimitSum() <= num) {
                        thresholdList.add(rechargeGiftThresholdEntity.getGuid());
                        log.info("门槛达到次数限制! 活动名称:{} 门槛guid:{} 限制次数:{}",
                                activityRunVO.getActivityName(),
                                rechargeGiftThresholdEntity.getGuid(),
                                rechargeGiftThresholdEntity.getGiftThresholdLimitSum());
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(thresholdList)) {
            runVORechargeGiftThresholdList.removeIf(in -> thresholdList.contains(in.getGuid()));
        }
    }

    private void dealOrderLabel(String memberGuid, List<String> markingLabelGuidList) {
        if (CollUtil.isNotEmpty(markingLabelGuidList)) {
            String userStr = ThreadLocalCache.get();
            memberBaseThreadExecutor.execute(() -> {
                ThreadLocalCache.put(userStr);
                AddMemberLabelCorrelationQO labelCorrelationQo = new AddMemberLabelCorrelationQO();
                labelCorrelationQo.setLabelGuid(markingLabelGuidList);
                labelCorrelationQo.setMemberInfoGuid(Collections.singletonList(memberGuid));
                hsaMemberLabelService.addMemberInfoLabel(labelCorrelationQo);
            });
        }
    }


    private List<HsaCardRechargeGiftDetail> forDealThresholdHandle(
            TerCardRechargeGiftQO terCardRechargeGiftQO,
            RechargeOrderGiftSummaryVO giftSummaryVO,
            RechargeGiftActivityRunVO activityRunVO,
            List<RechargeGiftThresholdEntity> runVORechargeGiftThresholdList) {

        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList = Lists.newArrayList();

        //获取金额满足的最高门槛
        RechargeGiftThresholdEntity rechargeGiftThresholdEntity =
                getRechargeGiftThresholdEntity(terCardRechargeGiftQO, runVORechargeGiftThresholdList);
        if (Objects.isNull(rechargeGiftThresholdEntity)) {
            return hsaCardRechargeGiftDetailList;
        }

        List<RechargeGiftEntity> rechargeGiftEntityList = rechargeGiftThresholdEntity.getRechargeGiftEntityList();
        //门槛赠送记录
        HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail = new HsaCardRechargeGiftDetail();
        //处理赠送
        forDealGiftHandle(terCardRechargeGiftQO, giftSummaryVO, rechargeGiftThresholdEntity, rechargeGiftEntityList, hsaCardRechargeGiftDetail, activityRunVO);

        addRechargeGiftBaseData(terCardRechargeGiftQO, activityRunVO, hsaCardRechargeGiftDetailList, rechargeGiftThresholdEntity, hsaCardRechargeGiftDetail);

        return hsaCardRechargeGiftDetailList;
    }

    private static RechargeGiftThresholdEntity getRechargeGiftThresholdEntity(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                                                              List<RechargeGiftThresholdEntity> runVORechargeGiftThresholdList) {
        RechargeGiftThresholdEntity rechargeGiftThresholdEntity = null;
        //获取满足的最高门槛
        BigDecimal rechargeMoney = terCardRechargeGiftQO.getRechargeMoney();
        runVORechargeGiftThresholdList = runVORechargeGiftThresholdList.stream()
                .sorted(Comparator.comparing(RechargeGiftThresholdEntity::getGiftThresholdAmount))
                .collect(Collectors.toList());

        for (int i = 0; i < runVORechargeGiftThresholdList.size(); i++) {
            rechargeGiftThresholdEntity = runVORechargeGiftThresholdList.get(i);
            if (rechargeMoney.compareTo(rechargeGiftThresholdEntity.getGiftThresholdAmount()) < 0) {
                if (i != 0) {
                    rechargeGiftThresholdEntity = runVORechargeGiftThresholdList.get(i - 1);
                }
                break;
            }
            if (i == runVORechargeGiftThresholdList.size() - 1) {
                rechargeGiftThresholdEntity = runVORechargeGiftThresholdList.get(i);
            }
        }

        if (Objects.isNull(rechargeGiftThresholdEntity)) {
            return rechargeGiftThresholdEntity;
        }

        if (rechargeMoney.compareTo(rechargeGiftThresholdEntity.getGiftThresholdAmount()) < 0) {
            log.error("充值金额不满足门槛限制金额! 门槛guid:{}", rechargeGiftThresholdEntity.getGuid());
            return null;
        }
        return rechargeGiftThresholdEntity;
    }


    private void addRechargeGiftBaseData(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                         RechargeGiftActivityRunVO activityRunVO,
                                         List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList,
                                         RechargeGiftThresholdEntity rechargeGiftThresholdEntity,
                                         HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail) {
        hsaCardRechargeGiftDetail
                .setGuid(guidGeneratorUtil.getStringGuid(HsaCardRechargeGiftDetail.class.getSimpleName()));

        MemberCardAssembler.buildCardRechargeGiftDetail(terCardRechargeGiftQO, activityRunVO, rechargeGiftThresholdEntity, hsaCardRechargeGiftDetail);
        hsaCardRechargeGiftDetailList.add(hsaCardRechargeGiftDetail);
    }

    private void forDealGiftHandle(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                   RechargeOrderGiftSummaryVO giftSummaryVO,
                                   RechargeGiftThresholdEntity rechargeGiftThresholdEntity,
                                   List<RechargeGiftEntity> rechargeGiftEntityList,
                                   HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail,
                                   RechargeGiftActivityRunVO activityRunVO) {
        // 获取次数限制
        int twice = getTwice(terCardRechargeGiftQO, rechargeGiftThresholdEntity);
        terCardRechargeGiftQO.setTwice(twice);

        //获取卡map
        Map<String, CardInfoDetailDTO> cardInfoDetailDTOMap = getCardInfoDetailDTOMap(rechargeGiftEntityList, terCardRechargeGiftQO);

        for (RechargeGiftEntity rechargeGiftEntity : rechargeGiftEntityList) {
            GiftTypeEnum giftTypeEnum = GiftTypeEnum.getEnum(rechargeGiftEntity.getGiftType());
            switch (Objects.requireNonNull(giftTypeEnum)) {
                case GIFT_MONEY_AMOUNT:
                    calculateGiftMoneyAmount(terCardRechargeGiftQO, giftSummaryVO, hsaCardRechargeGiftDetail, rechargeGiftEntity);
                    break;
                case GIFT_INTEGRAL_VALUE:
                    calculateGiftIntegral(terCardRechargeGiftQO, giftSummaryVO, hsaCardRechargeGiftDetail, rechargeGiftEntity.getIntegralValue());
                    break;
                case GIFT_MEMBER_CARD:
                    dealGiftCard(giftSummaryVO, hsaCardRechargeGiftDetail, cardInfoDetailDTOMap, rechargeGiftEntity);
                    break;
                case GIFT_MEMBER_GROWTH:
                    calculateGiftGrowth(terCardRechargeGiftQO, giftSummaryVO, hsaCardRechargeGiftDetail, rechargeGiftEntity.getGrowthValue());
                    break;
                case GIFT_MEMBER_COUPON:
                    giftSummaryVO.setCouponGuidList(rechargeGiftEntity.getCouponPackageDetailsDTOS());
                    break;
                default:
                    log.info("赠送类型错误");
                    break;
            }
        }
        //若会员卡未完成赠送  依然算次数
        hsaCardRechargeGiftDetail.setGiftThresholdLimitSum(1);
        giftSummaryVO.setIsTrip(1);
        giftSummaryVO.setActivityCode(activityRunVO.getActivityCode());
        giftSummaryVO.setActivityName(activityRunVO.getActivityName());
    }

    private static void dealGiftCard(RechargeOrderGiftSummaryVO giftSummaryVO, HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail, Map<String, CardInfoDetailDTO> cardInfoDetailDTOMap, RechargeGiftEntity rechargeGiftEntity) {
        if (CollUtil.isNotEmpty(cardInfoDetailDTOMap) && cardInfoDetailDTOMap.containsKey(rechargeGiftEntity.getCardGuid())) {
            hsaCardRechargeGiftDetail.setCardGuid(rechargeGiftEntity.getCardGuid());
            List<String> getCardGuidSet = giftSummaryVO.getGiftTotalCardNameList();
            if (!getCardGuidSet.contains(rechargeGiftEntity.getCardGuid())) {
                getCardGuidSet.add(cardInfoDetailDTOMap.get(rechargeGiftEntity.getCardGuid()).getCardName());
                giftSummaryVO.setGiftTotalCardNameList(getCardGuidSet);
            }
        }
    }

    private Map<String, CardInfoDetailDTO> getCardInfoDetailDTOMap(List<RechargeGiftEntity> rechargeGiftEntityList,
                                                                   TerCardRechargeGiftQO terCardRechargeGiftQO) {
        Set<String> cardGuidList = rechargeGiftEntityList.stream().map(RechargeGiftEntity::getCardGuid).collect(Collectors.toSet());
        Map<String, CardInfoDetailDTO> cardInfoDetailDTOMap = new HashMap<>();
        if (StringUtils.isEmpty(terCardRechargeGiftQO.getMemberGuid())) {
            return cardInfoDetailDTOMap;
        }
        if (CollUtil.isNotEmpty(cardGuidList)) {
            //过滤已拥有的电子卡
            List<HsaElectronicCard> hsaElectronicCards = hsaElectronicCardMapper.selectList(new LambdaQueryWrapper<HsaElectronicCard>()
                    .eq(HsaElectronicCard::getMemberInfoGuid, terCardRechargeGiftQO.getMemberGuid()));


            if (CollUtil.isNotEmpty(hsaElectronicCards)) {
                List<String> electronicCards = hsaElectronicCards.stream().map(HsaElectronicCard::getCardGuid).collect(Collectors.toList());
                cardGuidList = cardGuidList.stream().filter(in -> !electronicCards.contains(in)).collect(Collectors.toSet());
            }


            List<CardInfoDetailDTO> cardInfoDTOList = hsaCardInfoMapper.getCardInfoDetailList(cardGuidList);


            cardInfoDetailDTOMap = cardInfoDTOList.stream()
                    .collect(Collectors.toMap(CardInfoDetailDTO::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));

        }
        return cardInfoDetailDTOMap;
    }

    private static void calculateGiftGrowth(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                            RechargeOrderGiftSummaryVO giftSummaryVO,
                                            HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail,
                                            int growthValue) {
        int growthNum = growthValue * terCardRechargeGiftQO.getTwice();
        log.info("总赠送成长值：{}", growthNum);
        hsaCardRechargeGiftDetail.setGiveGrowthValue(growthNum);
        giftSummaryVO.setGiftTotalGrowthValue(giftSummaryVO.getGiftTotalGrowthValue() + growthNum);
    }

    private static void calculateGiftIntegral(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                              RechargeOrderGiftSummaryVO giftSummaryVO,
                                              HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail,
                                              int integralValue) {
        int integralNum = integralValue * terCardRechargeGiftQO.getTwice();
        log.info("总赠送积分：{}", integralNum);
        hsaCardRechargeGiftDetail.setGiveIntegralValue(integralNum);
        giftSummaryVO.setGiftTotalIntegralValue(giftSummaryVO.getGiftTotalIntegralValue() + integralNum);
    }

    private static void calculateGiftMoneyAmount(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                                 RechargeOrderGiftSummaryVO giftSummaryVO,
                                                 HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail,
                                                 RechargeGiftEntity rechargeGiftEntity) {

        BigDecimal rechargeMoney = rechargeGiftEntity.getMoneyAmount().multiply(new BigDecimal(terCardRechargeGiftQO.getTwice()));
        log.info("总赠送金额：{}", rechargeMoney);

        //金额赠送记录
        hsaCardRechargeGiftDetail.setMoneyAmount(rechargeMoney)
                .setMemberFundingDetailGuid(terCardRechargeGiftQO.getMemberFundingDetailGuid())
                .setMoneyEffectiveUnitType(rechargeGiftEntity.getMoneyEffectiveUnitType())
                .setMoneyEffectiveType(rechargeGiftEntity.getMoneyEffectiveType());
        //生效时间
        dealMoneyEffectiveTime(hsaCardRechargeGiftDetail, rechargeGiftEntity, giftSummaryVO);

        //赠送金额记录
        RechargeGiftAmountRecordVO rechargeGiftAmountRecordVO = terCardRechargeGiftQO.getRechargeGiftAmountRecordVO();
        rechargeGiftAmountRecordVO
                .setGiftAmount(rechargeMoney)
                .setChangeSource(ThreadLocalCache.getSource());
    }

    /**
     * 倍数
     *
     * @param terCardRechargeGiftQO       terCardRechargeGiftQO
     * @param rechargeGiftThresholdEntity rechargeGiftThresholdEntity
     * @return twice
     */
    private static int getTwice(TerCardRechargeGiftQO terCardRechargeGiftQO, RechargeGiftThresholdEntity rechargeGiftThresholdEntity) {
        int twice = 1;
        if (rechargeGiftThresholdEntity.getGiftThresholdType() == GiftThresholdEnum.THRESHOLD_EVERY_FULL.getCode()) {
            BigDecimal rechargeMoney = terCardRechargeGiftQO.getRechargeMoney();
            twice = rechargeMoney
                    .divide(rechargeGiftThresholdEntity.getGiftThresholdAmount(), 0, RoundingMode.DOWN).intValue();

        }
        return twice;
    }

    private static void dealMoneyEffectiveTime(HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail,
                                               RechargeGiftEntity rechargeGiftEntity,
                                               RechargeOrderGiftSummaryVO giftSummaryVO) {
        if (rechargeGiftEntity.getMoneyEffectiveType() == GiftEffectiveEnum.FIXED_EFFECTIVE.getCode()) {
            if (rechargeGiftEntity.getMoneyEffectiveUnitType() == GiftEffectiveUnitEnum.DAY_EFFECTIVE.getCode()) {
                hsaCardRechargeGiftDetail.setMoneyEffectiveTime(LocalDateTime.now().plusDays(rechargeGiftEntity.getMoneyEffectiveValue()));
            } else {
                hsaCardRechargeGiftDetail.setMoneyEffectiveTime(LocalDateTime.now().plusHours(rechargeGiftEntity.getMoneyEffectiveValue()));
            }
            hsaCardRechargeGiftDetail.setIsRefresh(0);
            //记录金额时段
            giftSummaryVO.setGiftFixedTotalAmount(giftSummaryVO.getGiftFixedTotalAmount().add(hsaCardRechargeGiftDetail.getMoneyAmount()));
        } else {
            giftSummaryVO.setGiftEffectuateTotalAmount(giftSummaryVO.getGiftEffectuateTotalAmount().add(hsaCardRechargeGiftDetail.getMoneyAmount()));
        }
    }


    private Map<String, List<HsaCardRechargeGiftDetail>> getCardRechargeGiftRuleMap(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                                                                    RechargeGiftActivityRunVO activityRunVO,
                                                                                    List<String> thresholdGuidList) {
        Map<String, List<HsaCardRechargeGiftDetail>> rechargeGiftDetailHashMap = new HashMap<>();
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetailList = hsaCardRechargeGiftDetailMapper
                .selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .eq(HsaCardRechargeGiftDetail::getOperSubjectGuid, terCardRechargeGiftQO.getOperSubjectGuid())
                        .eq(HsaCardRechargeGiftDetail::getMemberCardGuid, terCardRechargeGiftQO.getMemberInfoCardGuid())
                        .eq(StringUtils.isNotEmpty(terCardRechargeGiftQO.getMemberGuid()), HsaCardRechargeGiftDetail::getMemberGuid, terCardRechargeGiftQO.getMemberGuid())
                        .in(HsaCardRechargeGiftDetail::getThresholdGuid, thresholdGuidList)
                        .and(wq -> wq.isNull(HsaCardRechargeGiftDetail::getIsRefresh)
                                .or().notIn(HsaCardRechargeGiftDetail::getIsRefresh, GiftDetailRefreshEnum.CANCEL.getCode())
                        )
                        .eq(HsaCardRechargeGiftDetail::getActivityGuid, activityRunVO.getGuid()));


        if (isNotEmpty(hsaCardRechargeGiftDetailList)) {
            rechargeGiftDetailHashMap = hsaCardRechargeGiftDetailList.stream()
                    .collect(Collectors.groupingBy(HsaCardRechargeGiftDetail::getThresholdGuid));

        }
        return rechargeGiftDetailHashMap;
    }


    private String getLockId(TerCardRechargeGiftQO terCardRechargeGiftQO) {
        if (StringUtils.isNotBlank(terCardRechargeGiftQO.getMemberGuid())) {
            return terCardRechargeGiftQO.getMemberGuid();
        } else {
            return terCardRechargeGiftQO.getMemberInfoCardGuid();
        }
    }

    private static boolean checkCondition(TerCardRechargeGiftQO terCardRechargeGiftQO,
                                          RechargeGiftActivityRunVO activityRunVO) {
        // 校验适用终端
        if (checkTerminalType(activityRunVO)) {
            return true;
        }

        //校验门店
        if (activityRunVO.getApplicableAllStore() == 0 && !activityRunVO.getHsaRechargeGiftStoreVOList()
                .stream()
                .map(RechargeGiftStoreQO::getStoreGuid).collect(Collectors.toList()).contains(terCardRechargeGiftQO.getStoreId())) {
            log.error("门店不符合,活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        // 校验活动对象
        List<String> memberCardGuidList = activityRunVO.getCardGuid();
        if (!memberCardGuidList.contains(terCardRechargeGiftQO.getCardGuid())) {
            log.error("活动对象卡不符合,活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        //适用人群
        RechargeGiftTypeEnum giftTypeEnum = RechargeGiftTypeEnum.getEnum(activityRunVO.getRechargeFilterType());
        switch (Objects.requireNonNull(giftTypeEnum)) {

            case RECHARGE_UNRESTRICTED:
                return false;

            case RECHARGE_ALL_MEMBER:
                return StringUtils.isEmpty(terCardRechargeGiftQO.getMemberGuid());

            case RECHARGE_LABEL_MEMBER:
                return checkLabel(terCardRechargeGiftQO, activityRunVO);

            case RECHARGE_GRADE_MEMBER:
                return checkGrade(activityRunVO, terCardRechargeGiftQO);

            case RECHARGE_MEMBER:
                return checkMember(activityRunVO, terCardRechargeGiftQO);
            default:
                log.info("适用人群类型错误");
                return true;
        }
    }

    private static boolean checkTerminalType(RechargeGiftActivityRunVO activityRunVO) {
        if (ObjectUtils.isEmpty(activityRunVO.getTerminalType())) {
            log.error("适用终端不存在,活动名：{}", activityRunVO.getActivityName());
            return true;
        }
        if (!activityRunVO.getTerminalType().contains(String.valueOf(ThreadLocalCache.getSource()))) {
            log.error("适用终端不符合,活动名：{}", activityRunVO.getActivityName());
            return true;
        }
        return false;
    }

    private static boolean checkMember(RechargeGiftActivityRunVO activityRunVO, TerCardRechargeGiftQO terCardRechargeGiftQO) {
        if (StringUtils.isBlank(terCardRechargeGiftQO.getMemberGuid())) {
            log.error("指定会员校验 会员不存在 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }
        if (!activityRunVO.getMemberInfoGuidList().contains(terCardRechargeGiftQO.getMemberGuid())) {
            log.error("指定会员校验 会员不匹配 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }
        return false;
    }

    private static boolean checkLabel(TerCardRechargeGiftQO terCardRechargeGiftQO, RechargeGiftActivityRunVO activityRunVO) {
        List<String> memberLabelGuidList = activityRunVO.getLabelGuidList();
        if (CollectionUtils.isEmpty(memberLabelGuidList)) {
            log.error("标签校验 活动标签为空 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        if (CollUtil.isEmpty(terCardRechargeGiftQO.getLabelGuid())) {
            log.error("标签校验 用户标签为空 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        if (memberLabelGuidList.stream().noneMatch(terCardRechargeGiftQO.getLabelGuid()::contains)) {
            log.error("标签校验 标签不匹配 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }
        return false;
    }

    private static boolean checkGrade(RechargeGiftActivityRunVO activityRunVO, TerCardRechargeGiftQO terCardRechargeGiftQO) {
        if (StringUtils.isEmpty(terCardRechargeGiftQO.getMemberGuid())) {
            log.error("等级校验 会员不存在 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        if (StringUtils.isEmpty(terCardRechargeGiftQO.getMemberGradeInfoGuid())) {
            log.error("等级校验 会员无等级 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }

        if (!activityRunVO.getGradeGuidList().contains(terCardRechargeGiftQO.getMemberGradeInfoGuid())) {
            log.error("等级校验 等级不符合 活动名称：{}", activityRunVO.getActivityName());
            return true;
        }
        return false;
    }


    /**
     * 获取卡剩余冻结金额
     *
     * @return BigDecimal
     */
    @Override
    public BigDecimal getMemberCardFreezeAmount(CardFreezeBalanceAmountQO cardQO) {
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = new ArrayList<>();

        getMemberInfoCard(cardQO);

        if (StringUtils.isEmpty(cardQO.getElectronicCardGuid()) && StringUtils.isEmpty(cardQO.getPhysicalCardGuid())) {
            return BigDecimal.ZERO;
        }

        getHsaCardRechargeGiftDetails(cardQO, hsaCardRechargeGiftDetails);

        if (CollUtil.isEmpty(hsaCardRechargeGiftDetails)) {
            return BigDecimal.ZERO;
        } else {
            return hsaCardRechargeGiftDetails.stream()
                    .map(HsaCardRechargeGiftDetail::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

    }

    @Override
    public Map<String, BigDecimal> getMemberCardFreezeAmountMap(List<CardFreezeBalanceAmountQO> cardList) {
        getMemberInfoCards(cardList);
        Map<String, BigDecimal> memberCardFreezeAmountMap = Maps.newHashMap();
        for (CardFreezeBalanceAmountQO amountQO : cardList) {
            if (StringUtils.isEmpty(amountQO.getElectronicCardGuid()) && StringUtils.isEmpty(amountQO.getPhysicalCardGuid())) {
                continue;
            }
            List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = new ArrayList<>();
            getHsaCardRechargeGiftDetails(amountQO, hsaCardRechargeGiftDetails);
            if (CollectionUtils.isEmpty(hsaCardRechargeGiftDetails)) {
                memberCardFreezeAmountMap.put(amountQO.getMemberInfoCardGuid(), BigDecimal.ZERO);
            } else {
                BigDecimal totalAmount = hsaCardRechargeGiftDetails.stream()
                        .map(HsaCardRechargeGiftDetail::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                memberCardFreezeAmountMap.put(amountQO.getMemberInfoCardGuid(), totalAmount);
            }
        }
        return memberCardFreezeAmountMap;
    }

    private void getHsaCardRechargeGiftDetails(CardFreezeBalanceAmountQO cardQO, List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails) {
        if (StringUtils.isNotEmpty(cardQO.getElectronicCardGuid())) {
            List<HsaCardRechargeGiftDetail> getElectronicCardList = hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                    .eq(HsaCardRechargeGiftDetail::getIsRefresh, BooleanEnum.FALSE.getCode())
                    .eq(StringUtils.isNotEmpty(cardQO.getMemberInfoGuid()), HsaCardRechargeGiftDetail::getMemberGuid, cardQO.getMemberInfoGuid())
                    .eq(HsaCardRechargeGiftDetail::getMoneyEffectiveType, GiftEffectiveEnum.FIXED_EFFECTIVE.getCode())
                    .eq(HsaCardRechargeGiftDetail::getElectronicCardGuid, cardQO.getElectronicCardGuid())
                    //排除退款
                    .ne(Objects.nonNull(cardQO.getExcludeMemberFundingDetail()), HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, cardQO.getExcludeMemberFundingDetail())
            );
            hsaCardRechargeGiftDetails.addAll(getElectronicCardList);

        }

        if (StringUtils.isNotEmpty(cardQO.getPhysicalCardGuid())) {
            List<HsaCardRechargeGiftDetail> getPhysicalCardList = hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                    .eq(HsaCardRechargeGiftDetail::getIsRefresh, BooleanEnum.FALSE.getCode())
                    .eq(StringUtils.isNotEmpty(cardQO.getMemberInfoGuid()), HsaCardRechargeGiftDetail::getMemberGuid, cardQO.getMemberInfoGuid())
                    .eq(HsaCardRechargeGiftDetail::getMoneyEffectiveType, GiftEffectiveEnum.FIXED_EFFECTIVE.getCode())
                    .eq(HsaCardRechargeGiftDetail::getPhysicalCardGuid, cardQO.getPhysicalCardGuid())
                    //排除退款
                    .ne(Objects.nonNull(cardQO.getExcludeMemberFundingDetail()), HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, cardQO.getExcludeMemberFundingDetail())
            );
            hsaCardRechargeGiftDetails.addAll(getPhysicalCardList);
        }
    }

    private void getMemberInfoCard(CardFreezeBalanceAmountQO cardQO) {
        if (StringUtils.isNotBlank(cardQO.getMemberInfoCardGuid())) {
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(cardQO.getMemberInfoCardGuid());
            cardQO.setElectronicCardGuid(hsaMemberInfoCard.getElectronicCardGuid());
            cardQO.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid());
            cardQO.setMemberInfoGuid(hsaMemberInfoCard.getMemberInfoGuid());
        }
    }


    private void getMemberInfoCards(List<CardFreezeBalanceAmountQO> cardList) {
        List<CardFreezeBalanceAmountQO> filterCardList = cardList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getMemberInfoCardGuid())).collect(Collectors.toList());
        List<String> memberInfoCardGuidList = filterCardList.stream()
                .map(CardFreezeBalanceAmountQO::getMemberInfoCardGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaMemberInfoCard> memberInfoCardList = hsaMemberInfoCardMapper.queryByGuids(memberInfoCardGuidList);
        Map<String, HsaMemberInfoCard> memberInfoCardMap = memberInfoCardList.stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (key1, key2) -> key2));
        for (CardFreezeBalanceAmountQO amountQO : filterCardList) {
            HsaMemberInfoCard memberInfoCard = memberInfoCardMap.get(amountQO.getMemberInfoCardGuid());
            if (Objects.nonNull(memberInfoCard)) {
                amountQO.setElectronicCardGuid(memberInfoCard.getElectronicCardGuid());
                amountQO.setPhysicalCardGuid(memberInfoCard.getPhysicalCardGuid());
                amountQO.setMemberInfoGuid(memberInfoCard.getMemberInfoGuid());
            }
        }
    }

    @Override
    public void clearMemberCardFreezeAmount(String memberInfoGuid) {
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = new ArrayList<>();

        CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();

        cardQO.setMemberInfoCardGuid(memberInfoGuid);

        getMemberInfoCard(cardQO);

        getHsaCardRechargeGiftDetails(cardQO, hsaCardRechargeGiftDetails);

        clear(hsaCardRechargeGiftDetails);
    }

    private void clear(List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails) {
        if (CollUtil.isNotEmpty(hsaCardRechargeGiftDetails)) {
            for (HsaCardRechargeGiftDetail hsaCardRechargeGiftDetail : hsaCardRechargeGiftDetails) {
                hsaCardRechargeGiftDetailMapper.removeByGuid(hsaCardRechargeGiftDetail.getGuid());
            }
        }
    }
}
