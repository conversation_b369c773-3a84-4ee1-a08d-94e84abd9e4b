package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 挂账信息实体
 * @author: pan tao
 * @create: 2022-02-09 11:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCreditInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 挂账信息guid
     */
    private String guid;

    /**
     * 挂账编号
     */
    private String creditNumber;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 收款方企业名称
     */
    private String receiverCompanyName;

    /**
     * 挂账账户名称
     */
    private String creditAccountName;

    /**
     * 挂账类型 0：个人 1：单位
     *
     * @see com.holderzone.member.common.enums.credit.CreditTypeEnum
     */
    private Integer creditType;

    /**
     * 付款方企业名称
     */
    private String companyName;

    /**
     * 区号
     */
    private String areaCode;

    /**
     * 联系人电话
     */
    private String linkmanPhone;

    /**
     * 挂账证明
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String creditProve;

    /**
     * 预存金额
     *
     * @see com.holderzone.member.common.enums.member.AmountSourceTypeEnum
     */
    private BigDecimal storedAmount;

    /**
     * 挂账钱包
     */
    private BigDecimal creditWallet;

    /**
     * 挂账上限设置 0:不设置上限 1：设置
     *
     * @see com.holderzone.member.common.enums.credit.UpperLimitSet
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer creditLimitedSet;

    /**
     * 挂账上限金额
     */
    private BigDecimal creditLimitedAmount;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applicableAllStore;

    /**
     * 账户有效期 0：永久有效 1：固定有效期
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer accountValidity;

    /**
     * 固定有效期失效时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime accountValidityDate;

    /**
     * 结算周期 0：手动生成结算单 1：自动生成结算单
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer settlePeriod;

    /**
     * 结算周期类型 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer settlePeriodType;

    /**
     * 结算周期时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String settlePeriodDate;

    /**
     * 操作人
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否开启 0：启用  1：禁用
     *
     * @see com.holderzone.member.common.enums.member.EnableEnum
     */
    private Integer isEnable;

    /**
     * 是否删除,0未删除,1已删除
     */
    @TableLogic//逻辑删除标识
    @TableField(select = false)//查询的时候不显示
    private Integer isDelete;

}
