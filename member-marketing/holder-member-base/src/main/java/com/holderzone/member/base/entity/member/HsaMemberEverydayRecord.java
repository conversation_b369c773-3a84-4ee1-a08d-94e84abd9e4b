package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员日常记录表
 */
@ApiModel("会员日常记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberEverydayRecord implements Serializable {
    private static final long serialVersionUID = -57323975547772397L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    private String guid;
    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 企业
     */
    @ApiModelProperty("企业")
    private String enterpriseGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 记录类型
     * @see com.holderzone.member.common.enums.member.MemberEverydayTypeEnum
     */
    @ApiModelProperty("记录类型")
    private Integer recordType;

    /**
     * 记录来源
     */
    @ApiModelProperty("记录来源")
    private Integer recordSource;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
