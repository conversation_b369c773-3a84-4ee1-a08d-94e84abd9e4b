package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaMemberInfoCardTotal;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.card.HsaMemberInfoCardDTO;
import com.holderzone.member.common.dto.card.MemberCardBindDTO;
import com.holderzone.member.common.dto.card.MemberCardGuidDTO;
import com.holderzone.member.common.dto.card.OwnPhysicalCardDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.member.ImportRechargeAmountQO;
import com.holderzone.member.common.vo.card.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface HsaMemberInfoCardMapper extends HolderBaseMapper<HsaMemberInfoCard> {

    List<MemberCardGuidDTO> findCardGuidByGuidList(@Param("cardGuidList") List<String> cardGuidList, @Param("memberGuidList") List<String> memberGuidList);

    List<String> findEntityCardGuidByGuidList(@Param("cardGuidList") List<String> cardGuidList, @Param("memberGuidList") List<String> memberGuidList);

    void updateByCardGuidList(@Param("updateList") List<HsaMemberInfoCardDTO> updateList);

    void updateCardAmountByCardGuidList(@Param("updateList") List<ImportRechargeAmountQO> updateList,
                                        @Param("operSubjectGuid") String operSubjectGuid);

    void updatePhysicalCardBangding(@Param("physicalCardGuid") String physicalCardGuid, @Param("guid") String guid);

    boolean updateMemberInfoCardByGuid(@Param("guid") String guid,
                                       @Param("cardAmount") BigDecimal cardAmount,
                                       @Param("excessTimes") Integer excessTimes,
                                       @Param("excessAmount") BigDecimal excessAmount);

    void updateElectronicCardBind(@Param("updateList") List<MemberCardBindDTO> updateList);

    List<AbleECardVO> listAbleECard(AbleOpenECardQO qo);

    List<OwnCardVO> listOwnCardPage(OwnCardQO qo);

    List<CardInfoBasicVO> listMemberCard(@Param("memberInfoGuid") String memberInfoGuid);

    OwnPhysicalCardDTO getPhysicalCard(@Param("physical_card_guid") String physicalCardGuid);

    List<MiniProgramCardDTO> findUnRelationMiniProgramCard(@Param("qo") ListMiniProgramCardQO qo);

    BigDecimal findUnOpenMaxFaceValue(@Param("operSubjectGuid") String operSubjectGuid, @Param("sendChannel") String sendChannel);

    List<MiniProgramCardDTO> findMiniProgramCard(@Param("qo") ListMiniProgramCardQO qo);

    List<MiniProgramCardDTO> findPayMiniProgramCard(@Param("qo") ListMiniProgramCardQO qo);

    List<AppletPaymentAccountVO> queryAppletPaymentAccount(@Param("operSubjectGuid") String operSubjectGuid,
                                                           @Param("memberInfoGuid") String memberInfoGuid);

    MiniProgramCardDTO findQrCodeOpenCardDetail(@Param("sendChannel") String sendChannel, @Param("cardGuid") String cardGuid);

    Integer queryValidElectronicCardNum(@Param("qo") ListMiniProgramCardQO qo);

    List<String> findAllOwnGuid(@Param("memberInfoGuid") String memberInfoGuid,@Param("operSubjectGuid") String operSubjectGuid);

    String getValidDate(@Param("guid") String guid);

    List<AbleECardVO> listAllAbleECard(@Param("operSubjectGuid") String operSubjectGuid);

    List<AbleECardVO> listAllAbleECardByChannel(@Param("operSubjectGuid") String operSubjectGuid,@Param("source") Object source);

    List<AbleEntityCardVO> listAbleEntityCard(@Param("operSubjectGuid") String operSubjectGuid,@Param("source") String source);

    MiniProgramCardDetailVO findMiniProgramCardDetail(@Param("guid") String guid);

    List<MemberCardDTO> findAllMemberCard(@Param("memberGuid") List<String> memberGuid);

    List<MemberCardAmountDTO> findAmountMemberCard(@Param("memberGuid") List<String> memberGuid);

    void batchUpdateBalance(@Param("list") List<HsaMemberInfoCard> hsaMemberInfoCards);

    void updateBalance(@Param("guid") String guid, @Param("cardAmount") BigDecimal cardAmount);

    void batchUpdatePhone(@Param("list") List<String> memberInfoGuids, @Param("phone") String phone);

    int queryMemberInfoCardCount(@Param("request") QueryMemberInfoCardQO request);

    List<QueryMemberInfoCardVO> queryMemberInfoCard(@Param("request") QueryMemberInfoCardQO request);

    List<QueryMemberInfoCardVO> queryMemberInfoCardGuid(@Param("request") List<String> request);

    int batchUpdateByGuid(@Param("guids") List<String> guids, @Param("password") String password);

    int batchUpdateByApplicableAllStore(@Param("list") List<HsaMemberInfoCard> hsaMemberInfoCards);

    int updateElectronicCardState(@Param("guids") List<String> guids, @Param("status") Integer status);

    int updatePhysicalCardState(@Param("guids") List<String> guids, @Param("status") Integer status);

    int updatePhysicalCardStateByGuid(@Param("guid") String guid, @Param("status") Integer status);

    void updateBatchMemberInfoGuid(@Param("list") List<HsaMemberInfoCard> hsaPhysicalCards,
                                   @Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 批量更新
     *
     * @param activityList 活动列表
     * @return 影响行数
     */
    int batchUpdate(@Param("list") List<HsaMemberInfoCard> activityList);

    /**
     * 批量更新
     *
     * @param activityList 活动列表
     * @return 影响行数
     */
    int batchUpdateValue(@Param("list") List<HsaMemberInfoCard> activityList);

    /**
     * 批量回收补贴金额
     *
     * @param activityList 活动列表
     * @return 影响行数
     */
    int subsidyRecycling(@Param("list") List<SubsidyRecyclingQO> activityList);

    void updateDefaultCardByMemberGuid(@Param("memberGuid") String memberGuid);

    void updateDefaultChooseByMemberGuid(@Param("memberGuid") String memberGuid);

    /**
     * 更新卡余额
     * @param card
     */
    int updateCardAmount(@Param("card") HsaMemberInfoCard card);

    List<String> queryValidElectronicCardGuidList(@Param("qo")ListMiniProgramCardQO qo);

    HsaMemberInfoCard queryByPhysicalCard(@Param("physicalCardGuid") String physicalCardGuid);

    HsaMemberInfoCard queryByElectronicCard(@Param("electronicCardGuid") String electronicCardGuid);

    List<HsaMemberInfoCardTotal> listCardAmountByDay();

    HsaMemberInfoCard queryByGuidForUpdate(@Param("guid") String guid);

    void updateDefaultCardByGuid(@Param("guid")String memberInfoCardGuid);

    List<RedeemOwnCardVO> getMemberCardList(@Param("guid") String guid);

    /**
     * 查询支付会员卡
     *
     * @param dto 请求参数
     * @return 会员卡列表
     */
    List<PayMemberCardVO> listPayMemberCard(MemberCardQueryDTO dto);

}
