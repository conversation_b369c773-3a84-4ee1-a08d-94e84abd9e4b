package com.holderzone.member.base.entity.permission;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 运营主体权限
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_oper_subject_permission")
public class HsaOperSubjectPermission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 岗位id
     */
    private String positionGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 运营主体名称
     */
    private String multiMemberName;

    /**
     * 运营主体状态（1:启用 0未启用）
     */
    private Integer multiMemberStatus;

    /**
     * 是否选择（1：选中，0未选中）
     * 默认选中
     */
    private Integer isChecked = 1;

    /**
     * 1：会员管理
     * 2：营销中心
     * @see com.holderzone.member.common.enums.SystemPermissionEnum
     */
    private Integer sourceType;

    /**
     * 0：岗位
     * 1:角色
     */
    private Integer isRole;
}
