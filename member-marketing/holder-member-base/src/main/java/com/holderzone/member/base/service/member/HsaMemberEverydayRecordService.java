package com.holderzone.member.base.service.member;

import com.holderzone.member.base.entity.member.HsaMemberEverydayRecord;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.member.MemberEverydayRecordQO;

import java.util.List;

public interface HsaMemberEverydayRecordService extends IHolderBaseService<HsaMemberEverydayRecord> {
    /**
     * 保存或更新记录
     * @param recordDTO 请求参数 DTO
     * @return 保存或更新后的记录
     */
    void saveOrUpdate(MemberEverydayRecordQO recordDTO);

    /**
     * 查询连续打卡记录
     * 从今天开始往前查询，返回所有连续打卡的记录列表（每天一条）
     *
     * @param memberInfoGuid 会员ID
     * @param recordType 记录类型
     * @return 连续打卡的记录列表
     */
    List<HsaMemberEverydayRecord> queryConsecutiveRecords(String memberInfoGuid, Integer recordType);

    /**
     * 计算实际赠送积分值
     */
    Integer calculateIntegralCheck(MemberEverydayRecordQO recordDTO);
}
