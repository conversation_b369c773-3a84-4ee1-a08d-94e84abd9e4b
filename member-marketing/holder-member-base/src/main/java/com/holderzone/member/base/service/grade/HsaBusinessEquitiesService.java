package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.dto.CardGrantEquitiesDTO;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.event.GrantMemberCouponPackageEvent;
import com.holderzone.member.common.qo.card.CardEquitiesQO;
import com.holderzone.member.common.qo.equities.MemberEquitiesQO;
import com.holderzone.member.common.qo.equities.SendCardEquitiesQO;
import com.holderzone.member.common.vo.equities.EquitiesDetailVO;
import com.holderzone.member.common.vo.grade.DoubleValueRequest;
import com.holderzone.member.common.vo.grade.GradeEquitiesPreviewVO;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-01-18 18:21
 */
public interface HsaBusinessEquitiesService extends IHolderBaseService<HsaBusinessEquities> {

    /**
     * 保存会员权益
     *
     * @param memberEquitiesQO 权益对象
     */
    void saveEquities(MemberEquitiesQO memberEquitiesQO);

    /**
     * 计算当前会员是否拥有翻倍成长值权益
     *
     * @param request 请求参数
     * @return 翻倍成长值
     */
    Long isDoubleGrowthValue(DoubleValueRequest request);


    /**
     * 获取权益信息
     *
     * @param guid 等级或会员卡
     * @return
     */
    List<EquitiesDetailVO> getEquitiesVOList(String guid);

    /**
     * 获取权益名称
     *
     * @param businessGuid businessGuid
     * @return
     */
    List<String> getEquitiesName(String businessGuid);

    /**
     * 获取权益弹窗
     *
     * @param businessGuid businessGuid
     * @return
     */
    List<GradeEquitiesPreviewVO> getEquitiesCard(String businessGuid, String memberInfoGuid);

    /**
     * 过期权益推送
     *
     * @param getEquipesPreviewVO
     */
    void sendExpireCardEquities(SendCardEquitiesQO getEquipesPreviewVO);

    void giveCardRightsJob(GrantMemberCouponPackageEvent giveCardRightsJob);

    /**
     * 查询可用卡的权益
     *
     * @param memberCards 卡列表
     * @return
     */
    List<HsaBusinessEquities> listByCard(List<String> memberCards);


    /**
     * 会员卡赠送权益
     * @param cardGrantEquitiesDTO cardGrantEquitiesDTO
     */
    void grantCardRights(CardGrantEquitiesDTO cardGrantEquitiesDTO);


    /**
     * 适配消息调用
     * @param cardGrantEquitiesDTO cardGrantEquitiesDTO
     */
    void grantFeignCardRights(CardEquitiesQO cardGrantEquitiesDTO);
}
