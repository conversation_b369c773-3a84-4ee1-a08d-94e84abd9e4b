package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.entity.grade.HsaControlledGradeState;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.grade.ControlledStateQO;
import com.holderzone.member.common.qo.grade.UpdateStateRequest;
import com.holderzone.member.common.vo.grade.ControlledGradeStateVO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface HsaControlledGradeStateService extends IHolderBaseService<HsaControlledGradeState> {

    /**
     * 新增或者修改
     * @param controlledState 请求对象
     * @return 返回结果
     */
    boolean addOrUpdate(ControlledStateQO controlledState);

    /**
     * 初始化当前运营主体升级礼包基础信息
     * @param operSubjectGuids 运营主体集合
     * @return 操作结果
     */
    void addGradeState(List<String> operSubjectGuids);

    /**
     * 修改状态
     * @param request 请求对象
     * @return 操作结果
     */
    boolean updateState(UpdateStateRequest request);

    List<String> getEnableRoleType(String operSubjectGuid);

    boolean isNotEnableByRole(String operSubjectGuid, String roleType);

    /**
     * 是否禁用
     * @param operSubjectGuid
     * @return
     */
    boolean isNotEnable(String operSubjectGuid);

    boolean isNotEnable();

    /**
     * 通过guid，查询信息
     * @param operSubjectGuid 运营主体guid
     * @return 操作结果
     */
    ControlledGradeStateVO getInfo(String operSubjectGuid);
}
