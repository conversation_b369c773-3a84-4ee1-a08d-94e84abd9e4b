package com.holderzone.member.base.config;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import javax.servlet.http.HttpServletRequest;
import java.util.Locale;
import static com.holderzone.member.common.constant.LocaleConstant.LOCALES;

/**
 * <AUTHOR>
 * @description 国际化解析器
 * @date 2021/8/27
 */
public class MemberBaseLocalResolver extends AcceptHeaderLocaleResolver{

    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        String headerLang = request.getHeader("Accept-Language");
        return headerLang == null || headerLang.isEmpty()
                ? Locale.CHINESE
                : Locale.lookup(Locale.LanguageRange.parse(headerLang), LOCALES);
    }
}
