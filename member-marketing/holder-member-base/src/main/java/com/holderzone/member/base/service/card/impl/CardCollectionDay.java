package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.service.card.CardValidityType;
import com.holderzone.member.base.util.ValidTimeUtils;
import com.holderzone.member.common.dto.card.ValidTime;
import com.holderzone.member.common.enums.card.CardMessageEnum;
import com.holderzone.member.common.enums.card.DateTypeEnum;
import com.holderzone.member.common.enums.card.ReturnPeriodTypeEnum;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

/**
 * 处理会员卡-固定效使用时间
 * 使用有效期：显示当前会员电子卡、实体卡总使用有效期；电子卡从开通成功开始计算，实体卡从激活成功开始计算；
 * ① 仅存在会员电子卡：使用有效期=电子卡使用有效期；
 * ② 仅存在会员实体卡：使用有效期=实体卡使用有效期；
 * ③ 存在实体卡+电子卡：使用有效期=实体卡+电子卡合计覆盖的使用有效期范围；（如任一存在“永久有效”即默认为永久有效）；
 */
public class CardCollectionDay extends ValidTime implements CardValidityType {

    @Override
    public String cardTimeHandler(HsaCardBaseInfo hsaCardBaseInfo, HsaElectronicCard hsaElectronicCard, HsaPhysicalCard hsaPhysicalCard) {

        //电子卡开卡时间
        LocalDateTime electronic = Optional.ofNullable(hsaElectronicCard)
                .map(HsaElectronicCard::getGmtCreate).orElse(null);
        //实体卡激活时间
        LocalDateTime physical = Optional.ofNullable(hsaPhysicalCard)
                .map(HsaPhysicalCard::getActivationTime).orElse(null);

        if(ObjectUtil.isNull(electronic) && ObjectUtil.isNull(physical)){
            return CardMessageEnum.AFTER_RECEIVING.getDes()+
                    hsaCardBaseInfo.getCardValidityTime()+
                    ReturnPeriodTypeEnum.findMsgByCode(hsaCardBaseInfo.getValidityUnit());
        }
        //时间往后推 多少天  单位  1日、2周、3月、4年
        Integer type = Optional.ofNullable(hsaCardBaseInfo).map(HsaCardBaseInfo::getValidityUnit).orElse(-1);
        ChronoUnit codeByCode = DateTypeEnum.findCodeByCode(type);

        LocalDateTime electronicTime = null;
        LocalDateTime physicalTime = null;
        if(ObjectUtil.isNotNull(electronic)){
             electronicTime = hsaElectronicCard.getGmtCreate()  //为了解决sonarLint问题，被迫这样写
                     .plus(hsaCardBaseInfo.getCardValidityTime(), codeByCode);
        }
        if(ObjectUtil.isNotNull(physical)){
             physicalTime = hsaPhysicalCard.getActivationTime()  //同上
                     .plus(hsaCardBaseInfo.getCardValidityTime(), codeByCode);
        }
        this.setStartTime(ValidTimeUtils.validTimeStart(electronic,physical));
        this.setEndTime(ValidTimeUtils.validTimeEnd(electronicTime,physicalTime));
        StringBuilder time = new StringBuilder();
        time.append(this.getStartTime()).append("至");
        time.append(this.getEndTime());
        return time.toString();
    }
}
