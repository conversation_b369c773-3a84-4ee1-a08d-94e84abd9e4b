package com.holderzone.member.base.entity.commodity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 商品信息
 * @author: pan tao
 * @create: 2022-07-11 10:08
 */
@Data
public class HsaCommodityInfo {

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 商品渠道
     */
    private String channel;

    /**
     * 门店
     */
    private String storeName;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 档口名称
     */
    private String stallName;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 菜单
     */
    private String strategyMenu;

    /**
     * 商品分类
     */
    private String commodityCategory;

    /**
     * 商品类型
     */
    private String commodityType;

    /**
     * 商品编号
     */
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 商品来源 store:门店 stall:档口
     */
    private String commoditySource;


    /**
     * 策略单名称
     */
    private String strategyName;

    /**
     * 策略单编号
     */
    private String strategyCode;

    /**
     * 0：未删除 1：已删除
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 商品会员价id
     */
    private Long commodityMemberPriceId;
}
