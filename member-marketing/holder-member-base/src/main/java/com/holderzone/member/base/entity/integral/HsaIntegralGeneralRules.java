package com.holderzone.member.base.entity.integral;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.enums.integral.RemindTheWayEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分通用规则
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@TableName("hsa_integral_general_rules")
@EqualsAndHashCode(callSuper = false)
public class HsaIntegralGeneralRules implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 积分名称
     */
    private String integralName;

    /**
     * 积分过期是否提醒（0：默认关闭，1：开启）
     */
    private Integer isOverdueReminder;

    /**
     * 提醒天数（用于提醒多少天内将要过期的积分）
     */
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer overdueDayNum;

    /**
     * 提醒方式(1.短信通知2.微信公众号通知3.微信小程序通知)
     * {@link RemindTheWayEnum}
     */
    @TableField(strategy=FieldStrategy.IGNORED)
    private String remindTheWay;

    /**
     * 是否设置积分保护期（0：默认关闭，1：开启）
     */
    private Integer isIntegralProtect;

    /**
     * 积分保护期，设置的天数
     */
    @TableField(strategy=FieldStrategy.IGNORED)
    private Integer integralProtectDayNum;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
