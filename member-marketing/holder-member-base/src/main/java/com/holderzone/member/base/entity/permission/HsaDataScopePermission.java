package com.holderzone.member.base.entity.permission;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 运营主体权限
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_data_scope_permission")
public class HsaDataScopePermission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体名称
     */
    private String positionGuid;

    /**
     * 列表名称
     */
    private String listName;

    /**
     * 列表字段
     */
    private String listField;

    /**
     * 是否选择（1：选中，0未选中）
     */
    private Integer isChecked = 1;

    /**
     * 权限类型
     * 0：岗位
     * 1：角色
     */
    private Integer isRole;
    /**
     * @see com.holderzone.member.common.enums.SystemPermissionEnum
     */
    private Integer systemType;
}
