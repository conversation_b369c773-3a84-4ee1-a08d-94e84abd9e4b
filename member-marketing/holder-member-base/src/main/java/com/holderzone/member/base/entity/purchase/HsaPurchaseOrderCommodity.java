package com.holderzone.member.base.entity.purchase;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动-订单商品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="HsaPurchaseOrderCommodity对象", description="限量抢购活动-订单商品")
public class HsaPurchaseOrderCommodity extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "限购活动订单guid")
    private String purchaseOrderGuid;

    @ApiModelProperty(value = "渠道")
    private String channel;
    
    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "商品编号")
    private String commodityCode;

    @ApiModelProperty(value = "商品数量")
    private Integer commodityNum;

    /**
     * 订单时间
     */
    @ApiModelProperty("预定时间")
    private LocalDateTime reserveTime;

}
