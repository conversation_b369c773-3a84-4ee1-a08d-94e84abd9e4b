package com.holderzone.member.base.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponPackageLinkMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponPackageLinkService;
import com.holderzone.member.base.service.coupon.assembler.MemberCouponAssembler;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponNumDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员券包发放 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
@Slf4j
public class HsaMemberCouponPackageLinkServiceImpl extends HolderBaseServiceImpl<HsaMemberCouponPackageLinkMapper, HsaMemberCouponPackageLink>
        implements IHsaMemberCouponPackageLinkService {
    @Resource
    private HsaMemberCouponPackageLinkMapper mapper;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationInfoMapper;

    @Resource
    @Lazy
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private CacheService cacheService;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private MemberCouponAssembler memberCouponAssembler;

    @Resource
    private ShortMessageSendService sendService;

    /**
     * 标签关联service
     */
    @Resource
    private HsaMemberLabelService hsaMemberLabelService;


    private void dealLabel(List<String> labelSetGuid, List<String> memberGuid, String operSubjectGuid) {
        if (CollUtil.isNotEmpty(labelSetGuid)) {


            memberBaseThreadExecutor.execute(() -> {
                HeaderUserInfo headerUserInfo = new HeaderUserInfo();
                headerUserInfo.setOperSubjectGuid(operSubjectGuid);
                String userInfo = JSONUtil.toJsonStr(headerUserInfo);
                try {
                    ThreadLocalCache.put(URLDecoder.decode(userInfo, "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    throw new MemberBaseException(e.getMessage());
                }

                AddMemberLabelCorrelationQO labelCorrelationQo = new AddMemberLabelCorrelationQO();
                labelCorrelationQo.setLabelGuid(labelSetGuid);
                labelCorrelationQo.setMemberInfoGuid(memberGuid);
                hsaMemberLabelService.addMemberInfoLabel(labelCorrelationQo);
            });
        }
    }

    @Override
    public Boolean couponPackageReissue(List<String> reissueGuid) {
        List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks = mapper.selectList(new LambdaQueryWrapper<HsaMemberCouponPackageLink>()
                .eq(HsaMemberCouponPackageLink::getState, BooleanEnum.FALSE.getCode())
                .in(HsaBaseEntity::getGuid, reissueGuid));

        if (CollUtil.isEmpty(hsaMemberCouponPackageLinks)) {
            log.info("记录已补发完成Guid：{}", JSON.toJSONString(reissueGuid));
            return false;
        }

        //获取活动
        Map<String, EditCouponPackageActivityVO> couponPackageActivityMap = getStringEditCouponPackageActivityVOMap(hsaMemberCouponPackageLinks);

        LocalDateTime now = LocalDateTime.now();

        //发券包记录
        List<HsaMemberCouponPackageLink> updateMemberCouponPackageLinks = Lists.newArrayList();

        //发券记录
        List<HsaMemberCouponLink> hsaMemberCouponLinks = Lists.newArrayList();

        //获取会员
        Map<String, MemberPhoneDTO> memberInfoMap = getMemberPhoneDTOMap(hsaMemberCouponPackageLinks);

        //券码去重
        Map<String, String> codeMap = new HashMap<>();

        for (HsaMemberCouponPackageLink hsaMemberCouponPackageLink : hsaMemberCouponPackageLinks) {

            //校验优惠券是否异常
            if (checkCondition(
                    updateMemberCouponPackageLinks,
                    now,
                    memberInfoMap,
                    hsaMemberCouponPackageLink,
                    couponPackageActivityMap,
                    hsaMemberCouponLinks,
                    codeMap)) {
                continue;
            }

            log.info("补发成功记录guid:{}", hsaMemberCouponPackageLink.getGuid());
        }

        if (CollUtil.isNotEmpty(hsaMemberCouponPackageLinks)) {
            memberBaseThreadExecutor.execute(() -> hsaMemberCouponPackageLinks.forEach(in -> mapper.updateByGuid(in)));
        }

        if (CollUtil.isNotEmpty(hsaMemberCouponLinks)) {
            memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));
        }
        return true;
    }


    private Map<String, EditCouponPackageActivityVO> getStringEditCouponPackageActivityVOMap(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks) {
        List<String> activityGuid = hsaMemberCouponPackageLinks.stream().map(HsaMemberCouponPackageLink::getActivityGuid)
                .collect(Collectors.toList());

        List<EditCouponPackageActivityVO> couponPackageList = memberMarketingFeign.queryRunActivity(activityGuid);
        return couponPackageList.stream()
                .collect(Collectors.toMap(EditCouponPackageActivityVO::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, MemberPhoneDTO> getMemberPhoneDTOMap(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks) {
        Set<String> memberGuidSet = hsaMemberCouponPackageLinks.stream().map(HsaMemberCouponPackageLink::getMemberGuid).collect(Collectors.toSet());
        return checkMemberGuid(new ArrayList<>(memberGuidSet)).stream()
                .collect(Collectors.toMap(MemberPhoneDTO::getGuid,
                        Function.identity(), (entity1, entity2) -> entity1));
    }

    private boolean checkCondition(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks,
                                   LocalDateTime now,
                                   Map<String, MemberPhoneDTO> memberInfoMap,
                                   HsaMemberCouponPackageLink hsaMemberCouponPackageLink,
                                   Map<String, EditCouponPackageActivityVO> couponPackageActivityMap,
                                   List<HsaMemberCouponLink> hsaMemberCouponLinks,
                                   Map<String, String> codeMap) {
        //判断活动是否存在
        if (CollUtil.isEmpty(couponPackageActivityMap)
                || !couponPackageActivityMap.containsKey(hsaMemberCouponPackageLink.getActivityGuid())) {

            hsaMemberCouponPackageLinks.add(MemberCouponAssembler.updateMemberCouponPackageLink(
                    hsaMemberCouponPackageLink,
                    now,
                    BooleanEnum.FALSE.getCode(),
                    "活动不存在"));
            return true;
        }

        EditCouponPackageActivityVO activityVO = couponPackageActivityMap.get(hsaMemberCouponPackageLink.getActivityGuid());

        //判断优惠券是否变更
        if (checkCoupon(hsaMemberCouponPackageLinks, now, hsaMemberCouponPackageLink, activityVO)) {
            return true;
        }

        List<ResponseCouponDTO> responseCouponDTOS = activityVO.getCouponGuidList().stream().filter(in -> in.getIsExist() == 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(responseCouponDTOS)) {
            String reason = "部分优惠券状态异常";
            if (responseCouponDTOS.size() == activityVO.getCouponGuidList().size()) {
                reason = "优惠券状态异常";
            }
            hsaMemberCouponPackageLinks.add(MemberCouponAssembler.updateMemberCouponPackageLink(
                    hsaMemberCouponPackageLink,
                    now,
                    BooleanEnum.FALSE.getCode(),
                    reason));
            return true;
        }

        int num = cacheService.getInventoryNum(StringConstant.COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                activityVO.getActivityCode());

        if (num != -100 && num <= 0) {
            hsaMemberCouponPackageLinks.add(
                    MemberCouponAssembler.updateMemberCouponPackageLink(
                            hsaMemberCouponPackageLink,
                            now,
                            BooleanEnum.FALSE.getCode(),
                            "活动券包库存不足"));
            return true;
        }

        MemberPhoneDTO memberPhoneDTO = memberInfoMap.get(hsaMemberCouponPackageLink.getMemberGuid());
        //会员校验
        if (memberPhoneDTO.getAccountState() == 1) {
            hsaMemberCouponPackageLinks.add(
                    MemberCouponAssembler.updateMemberCouponPackageLink(
                            hsaMemberCouponPackageLink,
                            now,
                            BooleanEnum.FALSE.getCode(),
                            "会员账户已禁用"));
            return true;
        }

        hsaMemberCouponPackageLinks.add(
                MemberCouponAssembler.updateMemberCouponPackageLink(
                        hsaMemberCouponPackageLink,
                        now,
                        BooleanEnum.TRUE.getCode(),
                        ""));

        if (num != -100) {
            cacheService.updateInventoryNum(StringConstant.COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                    activityVO.getActivityCode());
        }

        Set<String> labelGuid = new HashSet<>();


        hsaMemberCouponLinks.addAll(memberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO, now, codeMap, labelGuid));
        dealLabel(new ArrayList<>(labelGuid), Collections.singletonList(hsaMemberCouponPackageLink.getMemberGuid())
                , hsaMemberCouponPackageLink.getOperSubjectGuid());
        dealMemberCouponNotice(hsaMemberCouponLinks, memberPhoneDTO, activityVO);
        return false;
    }

    private static boolean checkCoupon(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks,
                                       LocalDateTime now,
                                       HsaMemberCouponPackageLink hsaMemberCouponPackageLink,
                                       EditCouponPackageActivityVO activityVO) {
        Map<String, ResponseCouponDTO> responseCouponDTOMap = activityVO.getCouponGuidList()
                .stream()
                .collect(Collectors.toMap(ResponseCouponDTO::getCouponCode, Function.identity(), (entity1, entity2) -> entity1));
        List<ResponseCouponNumDTO> responseCouponNumDTO = JSON.parseArray(hsaMemberCouponPackageLink.getCouponPackageJson(), ResponseCouponNumDTO.class);
        if (responseCouponNumDTO.size() != activityVO.getCouponGuidList().size()) {
            setUpdateCouponPackageLinks(hsaMemberCouponPackageLinks, now, hsaMemberCouponPackageLink);
            return true;
        }
        for (ResponseCouponNumDTO couponNumDTO : responseCouponNumDTO) {
            ResponseCouponDTO responseCouponDTO = responseCouponDTOMap.get(couponNumDTO.getCouponCode());
            if (checkUpdateCoupon(couponNumDTO, responseCouponDTO)) {
                setUpdateCouponPackageLinks(hsaMemberCouponPackageLinks, now, hsaMemberCouponPackageLink);
                return true;
            }
        }
        return false;
    }

    private static boolean checkUpdateCoupon(ResponseCouponNumDTO couponNumDTO,
                                             ResponseCouponDTO responseCouponDTO) {
        return Objects.isNull(responseCouponDTO)
                || responseCouponDTO.getDiscountAmount().compareTo(couponNumDTO.getDiscountAmount()) != 0
                || !Objects.equals(responseCouponDTO.getNum(), couponNumDTO.getNum());
    }

    private static void setUpdateCouponPackageLinks(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks, LocalDateTime now, HsaMemberCouponPackageLink hsaMemberCouponPackageLink) {
        hsaMemberCouponPackageLinks.add(
                MemberCouponAssembler.updateMemberCouponPackageLink(
                        hsaMemberCouponPackageLink,
                        now,
                        BooleanEnum.FALSE.getCode(),
                        "活动券包有变更，补发失败"));
    }

    private void dealMemberCouponNotice(List<HsaMemberCouponLink> hsaMemberCouponLinks, MemberPhoneDTO memberPhoneDTO
            , EditCouponPackageActivityVO activityVO) {
        if (Objects.nonNull(memberPhoneDTO.getWechatState()) && memberPhoneDTO.getWechatState() == 1) {
            MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
            memberCouponPackageVO.setMemberGuid(memberPhoneDTO.getGuid())
                    .setCouponNum(hsaMemberCouponLinks.size())
                    .setPhoneNum(memberPhoneDTO.getPhoneNum())
                    .setCouponPackageName(activityVO.getActivityName())
                    .setCouponPackageNum(1)
                    .setOperSubjectGuid(hsaMemberCouponLinks.get(0).getOperSubjectGuid())
                    .setEnterpriseGuid(memberPhoneDTO.getEnterpriseGuid());


            HsaMemberCouponLink memberCouponLink = hsaMemberCouponLinks.stream()
                    .sorted(Comparator.comparing(HsaMemberCouponLink::getCouponEffectiveEndTime))
                    .collect(Collectors.toList()).get(0);
            //时间比对
            memberCouponPackageVO.setCouponName(memberCouponLink.getCouponName());
            memberCouponPackageVO.setCouponType(memberCouponLink.getCouponType());
            memberCouponPackageVO.setCouponEffectiveEndTime(memberCouponLink.getCouponEffectiveEndTime());
            memberCouponPackageVO.setOperSubjectGuid(memberCouponLink.getOperSubjectGuid());
            memberCouponPackageVO.setEnterpriseGuid(memberCouponLink.getEnterpriseGuid());
            memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.sendMemberCouponNotice(Collections.singletonList(memberCouponPackageVO)));
            memberBaseThreadExecutor.execute(() -> sendService.sendMemberCouponNotice(Collections.singletonList(memberCouponPackageVO)));
        }
    }

    /**
     * 分批发放
     */
    public Set<MemberPhoneDTO> checkMemberGuid(List<String> memberGuid) {
        Set<MemberPhoneDTO> operationMemberInfoSet = new HashSet<>();
        if (CollUtil.isEmpty(memberGuid)) {
            return operationMemberInfoSet;
        }
        int pointsDataLimit = 5000;//限制条数
        int size = memberGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("总条数：{}，分为:{}批", size, part);
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberGuid.subList(0, pointsDataLimit);

                operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(guid));
                //剔除
                memberGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberGuid.isEmpty()) {
                log.info("最后剩下的数据:{}", memberGuid);
                //获取数据
                operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(memberGuid));
            }
        } else {
            //获取数据
            operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(memberGuid));
        }
        return operationMemberInfoSet;
    }
}
