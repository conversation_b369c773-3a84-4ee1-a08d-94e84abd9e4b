package com.holderzone.member.base.service.cache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis操作类
 */
@Slf4j
public class RedisService {

    private RedisTemplate<String, Object> redisTemplate;

    /**
     * redis 模版对象，用于查询聊天室缓存最新消息
     */
    private RedisTemplate<String, String> stringRedisTemplate;

    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void setStringRedisTemplate(RedisTemplate<String, String> stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }
    private static final String OK = "OK";

    /**
     * lua加锁脚本
     */
    private static final RedisScript<String> SCRIPT_LOCK = new DefaultRedisScript<>("return redis.call('set',KEYS[1]," +
            "ARGV[1],'NX','PX',ARGV[2])", String.class);
    /**
     * lua解锁脚本
     */
    private static final RedisScript<Long> SCRIPT_UNLOCK = new DefaultRedisScript<>("if redis.call('get',KEYS[1]) " +
            "== ARGV[1] then return (redis.call('del', KEYS[1])==1) else return 0 end", Long.class);

    private static final int DEFAULT_DAY = 3;


    //================================================== String =============================================

    /**
     * 写入redis缓存
     *
     * @param key      键
     * @param value    值
     * @param expire   过期时间
     * @param timeUnit 时间类型
     * @return 是否写入成功
     */
    private boolean baseSet(final String key, Object value, long expire, TimeUnit timeUnit) {
        if (StringUtils.isNotEmpty(key)) {
            try {
                ValueOperations<String, Object> operations = redisTemplate.opsForValue();
                operations.set(key, value, expire, timeUnit);
                return true;
            } catch (Exception e) {
                log.error("写入redis缓存失败！错误信息为：" + e.getMessage());
            } finally {
                RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
            }
        }
        return false;
    }

    public boolean set(final String key, Object value) {
        // 默认保存3天
        return baseSet(key, JSONUtil.toJsonStr(value), DEFAULT_DAY, TimeUnit.DAYS);
    }

    public boolean setNoJson(final String key, Object value, long expire, TimeUnit timeUnit) {
        return baseSet(key, value, expire, timeUnit);
    }

    public boolean set(final String key, Object value, long expire, TimeUnit timeUnit) {
        // 默认保存第一个数据库
        return baseSet(key, JSONUtil.toJsonStr(value), expire, timeUnit);
    }

    /**
     * 批量设置redis缓存
     *
     * @param map 简直map
     * @return 值
     */
    public void setAll(final Map<String, String> map) {
        try {
            redisTemplate.opsForValue().multiSet(map);
            // 循环设置过期时间
            CompletableFuture.runAsync(() -> {
                for (Map.Entry<String, String> stringEntry : map.entrySet()) {
                    redisTemplate.expire(stringEntry.getKey(), DEFAULT_DAY, TimeUnit.DAYS);
                }
            });
        } catch (Exception e) {
            log.error("批量读取redis缓存失败！错误信息为：" + e.getMessage());
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 尝试在指定时间段内获取Redis分布式锁,如果超时则跳过
     *
     * @param lockKey        锁的key
     * @param lockValue      锁的值
     * @param expireTime     过期时间(毫秒)
     * @param acquireTimeout 超时时间(毫秒)
     * @return 拿到锁的实例
     */
    public String tryAcquireLock(String lockKey, String lockValue, long expireTime, long acquireTimeout) {
        long nanoAcquireTimeout = TimeUnit.NANOSECONDS.convert(acquireTimeout * 1000 * 1000, TimeUnit.NANOSECONDS);
        // 系统时间(纳秒)
        long nowTime = System.nanoTime();
        while ((System.nanoTime() - nowTime) < nanoAcquireTimeout) {
            String lockResult = redisTemplate.execute((RedisCallback<String>) connection -> {
                Object nativeConnection = connection.getNativeConnection();
                String result = null;

                // 集群模式
                if (nativeConnection instanceof JedisCluster) {
                    result = ((JedisCluster) nativeConnection).set(lockKey, lockValue, "NX", "EX", expireTime);
                }

                // 单机模式
                if (nativeConnection instanceof Jedis) {
                    result = ((Jedis) nativeConnection).set(lockKey, lockValue, "NX", "EX", expireTime);
                }

                if (!StringUtils.isEmpty(result)) {
                    log.debug("获取锁成功:[{}],时间：[{}]", lockKey, System.currentTimeMillis());
                }
                return result;
            });
            // 拿锁成功
            final boolean locked = OK.equalsIgnoreCase(lockResult);
            if (locked) {
                // 结束本次请求
                return lockResult;
            }
            // 每次请求等待一段时间
            try {
                Thread.sleep(10, RandomUtil.randomInt(66666));
            } catch (InterruptedException e) {
                log.info("获取分布式锁休眠被中断：", e);
            } finally {
                RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
            }
        }
        return null;
    }

    /**
     * 尝试获取Redis分布式锁,没有获取到则立刻返回
     *
     * @param lockKey    锁的key
     * @param lockValue  锁的值
     * @param expireTime 超时时间(毫秒)
     * @return 是否成功获得锁
     */
    public String tryLock(String lockKey, String lockValue, long expireTime) {
        try {
            String lockResult = redisTemplate.execute(SCRIPT_LOCK,
                    redisTemplate.getStringSerializer(),
                    redisTemplate.getStringSerializer(),
                    Collections.singletonList(lockKey),
                    lockValue, String.valueOf(expireTime));
            if (StringUtils.isEmpty(lockResult)) {
                return null;
            }
            final boolean locked = OK.equalsIgnoreCase(lockResult);
            return locked ? lockResult : null;
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 释放Redis分布式锁
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的value
     * @return 是否解锁成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            Object nativeConnection = connection.getNativeConnection();
            long result = 0;
            List<String> keys = Collections.singletonList(lockKey);
            List<String> values = Collections.singletonList(lockValue);

            // 集群模式
            if (nativeConnection instanceof JedisCluster) {
                result = (Long) ((JedisCluster) nativeConnection).eval(SCRIPT_UNLOCK.getScriptAsString(), keys, values);
            }

            // 单机模式
            if (nativeConnection instanceof Jedis) {
                result = (Long) ((Jedis) nativeConnection).eval(SCRIPT_UNLOCK.getScriptAsString(), keys, values);
            }

            if (result == 0) {
                log.debug("Redis分布式锁解锁{}失败！解锁时间：{}", lockKey, System.currentTimeMillis());
            }
            return result == 1;
        });
    }

    /**
     * 读取redis缓存
     *
     * @param key 键
     * @return 值
     */
    public String baseGet(final String key) {
        String result = "";
        if (StringUtils.isNotEmpty(key)) {
            try {
                Object value = redisTemplate.opsForValue().get(key);
                result = null != value ? value.toString() : result;
            } catch (Exception e) {
                log.error("读取redis缓存失败！错误信息为：" + e.getMessage());
            } finally {
                RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
            }
        }
        return result;
    }

    private <T> T getClass(final String key, Class<T> tClass, TypeReference<T> type) {
        String value = baseGet(key);
        if (StringUtils.isNotEmpty(value)) {
            return (type == null) ? JSON.parseObject(value, tClass) : JSON.parseObject(value, type);
        }
        return null;
    }

    public String get(final String key) {
        return baseGet(key);
    }

    public <T> T get(final String key, Class<T> tClass) {
        return getClass(key, tClass, null);
    }

    public <T> T get(final String key, TypeReference<T> type) {
        return getClass(key, null, type);
    }

    /**
     * 读取聊天室内容redis缓存
     *
     * @param key 键
     * @return 值
     */
    public String stringGet(final String key) {
        String result = "";
        if (StringUtils.isNotEmpty(key)) {
            try {
                return stringRedisTemplate.opsForValue().get(key);
            } catch (Exception e) {
                log.error("读取redis缓存失败！错误信息为：" + e.getMessage());
            } finally {
                RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
            }
        }
        return result;
    }

    /**
     * 批量读取redis缓存【泛型返回】
     *
     * @param keys   键集合
     * @param tClass 指定泛型
     * @return 值
     */
    public <T> List<T> getAll(final List<String> keys, Class<T> tClass) {
        List<T> valueList = new ArrayList<>();
        try {
            List<Object> jsonStr = redisTemplate.opsForValue().multiGet(keys);
            if (!CollectionUtils.isEmpty(jsonStr)) {
                //空行过滤掉
                valueList = jsonStr.stream()
                        .filter(Objects::nonNull)
                        .map(item -> {
                            String s = JSONUtil.toJsonStr(item);
                            if (JSONUtil.isJson(s)) {
                                return JSONUtil.toBean(s, tClass);
                            } else {
                                return null;
                            }
                        })
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("批量读取redis缓存失败！错误信息为：" + e.getMessage());
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
        return valueList;
    }

    //================================================== String =============================================

    /**
     * 模糊匹配key
     *
     * @param keyLike 关键字
     * @return 查询结果
     */
    public Set<String> keys(String keyLike) {
        //如果关键字为空 或者 匹配全部  直接跳出
        if (StringUtils.isEmpty(keyLike) || "*".equals(keyLike)) {
            return new HashSet<>();
        }
        try {
            return redisTemplate.keys(keyLike);
        } catch (Exception e) {
            log.error("redis keys found error ...");
            return new HashSet<>();
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 扫描key
     *
     * @param matchKey 键
     * @param count    每次遍历字典槽位数量
     * @return 返回结果
     */
    public Set<String> scan(String matchKey, int count) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            Cursor<byte[]> cursor = connection.scan(new ScanOptions.ScanOptionsBuilder().match(matchKey).count(count).build());
            while (cursor.hasNext()) {
                keysTmp.add(new String(cursor.next()));
            }
            try {
                cursor.close();
            } catch (IOException e) {
                log.error("游标关闭失败", e);
            }
            return keysTmp;
        });
    }

    /**
     * 批量删除
     *
     * @param keys key的集合
     */
    public void removeBatch(Collection<String> keys) {
        try {
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }
            redisTemplate.delete(keys);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    //================================================== set =============================================

    /**
     * 添加到无序set集合
     *
     * @param key    key
     * @param values 结果集
     * @return 保存的个数
     */
    public Long mSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 判断set中是否存在某个值
     *
     * @param key   键
     * @param value 值
     * @return 是否存在
     */
    public Boolean isMember(String key, String value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 获取Set集合
     *
     * @param key key
     * @return 结果
     */
    public Set<String> getMSet(String key) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Jedis conn = (Jedis) connection.getNativeConnection();
            return conn.smembers(key);
        });
    }

    /**
     * 从set集合移除
     *
     * @param key    key
     * @param values value
     */
    public void removeMSet(String key, Object... values) {
        try {
            redisTemplate.opsForSet().remove(key, values);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    //================================================== set =============================================

    //================================================== List =============================================

    /**
     * 往列首存入一个信息
     *
     * @param consumerKey 键
     * @param value       值
     */
    public void lPush(String consumerKey, String value) {
        try {
            redisTemplate.opsForList().leftPush(consumerKey, value);
        } catch (Exception e) {
            // 入队失败的消息记录日志，定时任务扫描错误消息补偿提交
            log.error("消息:[{}],入队:[{}]异常", value, consumerKey);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 获取键的集合个数
     *
     * @param key 键
     * @return 个数
     */
    public Long getListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key) == null ? 0 : redisTemplate.opsForList().size(key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 从列首移除一个
     *
     * @param key      键
     * @param time     时间
     * @param timeUnit 时间单位
     */
    public Object leftPop(String key, int time, TimeUnit timeUnit) {
        Object pushObj = null;
        try {
            pushObj = redisTemplate.opsForList().leftPop(key, time, timeUnit);
        } catch (Exception ex) {
            log.error("消息出队异常,key:[{}]", key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
        return pushObj;
    }

    /**
     * 从右侧取出
     *
     * @param key      键
     * @param time     时间
     * @param timeUnit 时间单位
     */
    public Object rightPop(String key, int time, TimeUnit timeUnit) {
        Object pushObj = null;
        try {
            pushObj = redisTemplate.opsForList().rightPop(key, time, timeUnit);
        } catch (Exception ex) {
            log.error("消息出队异常,key:[{}]", key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
        return pushObj;
    }

    /**
     * 指定删除list中的值
     *
     * @param count 删除个数
     * @param value 值
     * @param key   键
     */
    public void remove(String key, long count, Object value) {
        try {
            redisTemplate.opsForList().remove(key, count, value);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    //================================================== List =============================================


    //================================================== hash =============================================

    /**
     * 更新hash
     *
     * @param key       键
     * @param hashKey   hash Key
     * @param hashValue hash 值
     */
    public void hashSet(String key, String hashKey, Object hashValue) {
        try {
            redisTemplate.opsForHash().put(key, hashKey, hashValue);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 获取hash值
     *
     * @param key     键
     * @param hashKey hash Key
     */
    public Object hashGet(String key, String hashKey) {
        try {
            return redisTemplate.opsForHash().get(key, hashKey);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 更新hash
     *
     * @param key 键
     * @param map map集合
     */
    public void hashSet(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 移除多个hash值
     *
     * @param key      键
     * @param hashKeys hash 多个或者单个key
     */
    public Long removeHash(String key, String... hashKeys) {
        try {
            return redisTemplate.opsForHash().delete(key, hashKeys);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 根据key获取hash个数
     *
     * @param key 键
     * @return 个数
     */
    public Long getHashSize(String key) {
        try {
            return redisTemplate.opsForHash().size(key) == null ? 0 : redisTemplate.opsForHash().size(key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 根据key获取hash列表
     *
     * @param key 键
     * @return hash键值
     */
    public Map<Object, Object> getHashCollection(String key) {
        try {
            return redisTemplate.opsForHash().entries(key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    //================================================== hash =============================================

    /**
     * 监听消息通道
     *
     * @param messageListener 监听任务
     * @param channels        要监听的消息通道
     */
    public void subscribe(MessageListener messageListener, byte[] channels) {
        RedisConnection connection = null;
        try {
            connection = redisTemplate.getConnectionFactory().getConnection();
            connection.subscribe(messageListener, channels);
        } finally {
            connection.close();
        }
    }

    //================================================== scribe =============================================

    /**
     * 对某个键的值自增
     *
     * @param key          键
     * @param cacheSeconds 超时时间(秒)
     * @return 自增后的值
     */
    public long setIncr(String key, int cacheSeconds) {
        long result;
        RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();
        try {
            //每次自增加1
            result = redisTemplate.opsForValue().increment(key, 1);
            redisTemplate.expire(key, cacheSeconds, TimeUnit.SECONDS);
        } finally {
            connection.close();
        }
        return result;
    }

    /**
     * 判断key是否存在
     *
     * @param key key
     * @return true 存在
     */
    public boolean exists(String key) {
        try {
            return redisTemplate.hasKey(key);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }


    /**
     * redis根据key删除对应的value
     *
     * @param key 键
     * @return 删除结果
     */
    private boolean baseRemove(final String key) {
        boolean result = false;
        try {
            if (exists(key)) {
                redisTemplate.delete(key);
            }
            result = true;
        } catch (Exception e) {
            log.error("redis根据key删除对应的value失败！错误信息为：" + e.getMessage());
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
        return result;
    }

    public boolean remove(final String key) {
        // 默认数据库删除key
        return baseRemove(key);
    }

    /**
     * 发送post请求 json格式参数提交
     * 不需要header
     *
     * @param url 请求路径
     * @return 返回model
     */
    private <T> T post(String url, Long id, Class<T> clazz) {
        RestTemplate restTemplate = new RestTemplate();
        //传递表单
        MultiValueMap<String, Long> param = new LinkedMultiValueMap<>(1);
        param.add("id", id);
        return restTemplate.postForObject(url, param, clazz);
    }


    /**
     * 右侧出队的同时放置到新的消费队列
     *
     * @param consumerKey                  消息队列名称
     * @param mobileTargetPushConsumeQueue 送至目标队列
     * @param time                         时间
     * @param timeUnit                     时间单位
     */
    public Object rPopAndLeftPush(String consumerKey, String mobileTargetPushConsumeQueue, int time, TimeUnit timeUnit) {
        Object pushObj = null;
        try {
            pushObj = redisTemplate.opsForList().rightPopAndLeftPush(consumerKey, mobileTargetPushConsumeQueue, time, timeUnit);
        } catch (Exception e) {
            // 入队失败的消息记录日志，定时任务扫描错误消息补偿提交
            log.error("消息:[{}],出队:[{}]异常", consumerKey, e);
        }
        return pushObj;
    }

    /**
     * 获一次性取指定类型集合
     *
     * @param keys  key集合
     * @param clazz clazz类
     * @return 列表
     */
    public <T> List<T> getList(Collection<String> keys, Class<T> clazz) {
        try {
            List<Object> objects = redisTemplate.opsForValue().multiGet(keys);
            if (CollectionUtil.isEmpty(objects)) {
                return CollectionUtil.newArrayList();
            }
            return JSONUtil.parseArray(objects.toString()).toList(clazz);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    public void removeAppToken(String key) {
        try {
            Set<String> keys = redisTemplate.keys(key);
            if (CollectionUtil.isEmpty(keys)) {
                return;
            }
            redisTemplate.delete(keys);
        } finally {
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }


    /**
     * 管道
     *
     * @param callback redis回调接口
     */
    public <T> List<Object> pipelined(RedisCallback<T> callback) {
        return redisTemplate.executePipelined(callback);
    }

    /**
     * 位图统计
     *
     * @param key 键
     * @return 个数
     */
    public Long bitCount(String key) {
        return redisTemplate.execute((RedisCallback<Long>) connection -> {
            Object nativeConnection = connection.getNativeConnection();
            // 单机模式
            if (nativeConnection instanceof Jedis) {
                final Jedis jedis = (Jedis) nativeConnection;
                jedis.pipelined();
            }
            final Jedis jedis = (Jedis) nativeConnection;
            return jedis.bitcount(key);
        });
    }



}
