package com.holderzone.member.base.service.grade;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaMemberRelevanceGrade;
import com.holderzone.member.common.vo.grade.MemberRelevanceGradeVO;

import java.util.List;


public interface HsaMemberRelevanceGradeService extends IService<HsaMemberRelevanceGrade> {

    /**
     * 获取用户当前等级
     *
     * @param memberGuid memberGuid
     * @return
     */
    MemberRelevanceGradeVO getUserCurrentGrade(String memberGuid, Integer roleType);

    /**
     * 获取已开通的所有等级
     *
     * @param memberGuid memberGuid
     * @param roleType   roleType
     * @return
     */
    List<MemberRelevanceGradeVO> getUserGradeAll(String memberGuid, Integer roleType);
}
