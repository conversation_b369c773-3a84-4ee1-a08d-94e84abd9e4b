package com.holderzone.member.base.service.card.impl;

import com.holderzone.member.base.entity.card.HsaMemberCardRule;
import com.holderzone.member.base.mapper.card.HsaMemberCardRuleMapper;
import com.holderzone.member.base.service.card.HsaMemberCardRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员卡会员关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Slf4j
@Service
public class HsaMemberCardRuleServiceImpl extends HolderBaseServiceImpl<HsaMemberCardRuleMapper, HsaMemberCardRule> implements HsaMemberCardRuleService {

}
