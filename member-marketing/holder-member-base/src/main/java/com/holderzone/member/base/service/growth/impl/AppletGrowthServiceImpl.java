package com.holderzone.member.base.service.growth.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.ShopBaseService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.equities.HsaApplyDictionaries;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.equities.HsaApplyDictionariesMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.growth.HsaExtraAwardRuleMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueCommodityRuleMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.growth.AppletGrowthService;
import com.holderzone.member.base.service.growth.HsaGrowthValueCommodityClassifyService;
import com.holderzone.member.base.service.growth.HsaGrowthValueRuleService;
import com.holderzone.member.base.service.growth.HsaGrowthValueTaskService;
import com.holderzone.member.base.service.integral.IAppletIntegralService;
import com.holderzone.member.base.service.integral.business.IntegralTaskBusinessService;
import com.holderzone.member.base.service.member.HsaMemberConsumptionService;
import com.holderzone.member.base.transform.growth.GrowthValueTaskTransform;
import com.holderzone.member.base.util.ServiceCommonUtil;
import com.holderzone.member.common.constant.GrowthValueConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.growth.AppointClassifyGoodsDTO;
import com.holderzone.member.common.dto.growth.HsaExtraAwardRuleDTO;
import com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO;
import com.holderzone.member.common.dto.growth.HsaGrowthValueTaskDTO;
import com.holderzone.member.common.dto.growth.wechat.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.ApplyModuleEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.growth.*;
import com.holderzone.member.common.util.DistanceUtils;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.card.MemberCardStoreDataQO;
import com.holderzone.member.common.vo.grade.GradeVo;
import com.holderzone.member.common.vo.growth.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 小程序成长值相关
 * @date 2021/11/24
 */
@Service
@Slf4j
public class AppletGrowthServiceImpl implements AppletGrowthService {

    private final HsaOperationMemberInfoMapper memberInfoMapper;

    private final HsaGrowthValueTaskService growthValueTaskService;

    private final HsaGrowthValueDetailMapper detailMapper;

    private final HsaExtraAwardRuleMapper ruleMapper;

    private final HsaGrowthValueRuleService valueRuleService;

    private final HsaMemberConsumptionService consumptionService;

    private final HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    private final StoreBaseService storeBaseService;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final HsaGrowthValueCommodityRuleMapper hsaGrowthValueCommodityRuleMapper;

    private final ShopBaseService shopBaseService;

    private static final String APPLET_SEPARATOR = ",";

    private static final String FALSE_STRING = "false";

    private final RequestGoalgoService hsaRequestGoalgoService;

    private final HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    private final IntegralTaskBusinessService appletIntegralService;

    private final HsaApplyDictionariesMapper hsaApplyDictionariesMapper;

    @Resource
    private HsaGrowthValueCommodityClassifyService growthValueCommodityClassifyService;

    public AppletGrowthServiceImpl(HsaOperationMemberInfoMapper memberInfoMapper,
                                   HsaGrowthValueTaskService growthValueTaskService,
                                   HsaGrowthValueDetailMapper detailMapper,
                                   HsaExtraAwardRuleMapper ruleMapper,
                                   HsaGrowthValueRuleService valueRuleService,
                                   HsaMemberConsumptionService consumptionService,
                                   HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper,
                                   StoreBaseService storeBaseService,
                                   HsaMemberInfoCardMapper hsaMemberInfoCardMapper,
                                   HsaGrowthValueCommodityRuleMapper hsaGrowthValueCommodityRuleMapper,
                                   ShopBaseService shopBaseService,
                                   RequestGoalgoService hsaRequestGoalgoService,
                                   HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper,
                                   IntegralTaskBusinessService appletIntegralService,
                                   HsaApplyDictionariesMapper hsaApplyDictionariesMapper) {
        this.memberInfoMapper = memberInfoMapper;
        this.growthValueTaskService = growthValueTaskService;
        this.detailMapper = detailMapper;
        this.ruleMapper = ruleMapper;
        this.valueRuleService = valueRuleService;
        this.consumptionService = consumptionService;
        this.hsaStoreRuleInfoMapper = hsaStoreRuleInfoMapper;
        this.storeBaseService = storeBaseService;
        this.hsaMemberInfoCardMapper = hsaMemberInfoCardMapper;
        this.hsaGrowthValueCommodityRuleMapper = hsaGrowthValueCommodityRuleMapper;
        this.shopBaseService = shopBaseService;
        this.hsaRequestGoalgoService = hsaRequestGoalgoService;
        this.hsaMemberGradeInfoMapper = hsaMemberGradeInfoMapper;
        this.appletIntegralService = appletIntegralService;
        this.hsaApplyDictionariesMapper = hsaApplyDictionariesMapper;
    }

    @Override
    public AppletGrowthTaskDTO getMemberGrowthTaskDetail(String guid) {
        if (StringUtils.isEmpty(guid)) {
            throw new MallBaseException("任务guid不能为空");
        }
        HsaGrowthValueTask hsaGrowthValueTask = growthValueTaskService.queryByGuid(guid);
        List<HsaExtraAwardRuleDTO> rule = ruleMapper.listExtraAwardRule(guid);
        Map<String, List<HsaExtraAwardRuleDTO>> ruleMap = Maps.newHashMap();
        ruleMap.put(guid, rule);
        //构建使用数据
        AppletGrowthTaskTransDTO transData = AppletGrowthTaskTransDTO.builder()
                .collectRule(ruleMap)
                .name(valueRuleService.getGrowthRuleName(ThreadLocalCache.getOperSubjectGuid()).getGrowthValueRule())
                .build();
        return createAppletTask(GrowthValueTaskTransform.INSTANCE.entitiesToDTO(hsaGrowthValueTask), transData);
    }

    @Override
    public AppletOwnLevelVO getOwnLevel(AppletGrowthQO qo) {
        HsaOperationMemberInfo memberInfo = memberInfoMapper.queryByGuid(qo.getMemberInfoGuid());
        boolean isExistCard = false;
        List<HsaMemberInfoCard> hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getOperSubjectGuid, qo.getOperSubjectGuid())
                .eq(HsaMemberInfoCard::getMemberInfoGuid, qo.getMemberInfoGuid()));
        if (CollUtil.isNotEmpty(hsaMemberInfoCardList)) {
            isExistCard = true;
        }
        if (ObjectUtil.isNull(memberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        //根据主体去查询任务信息
        List<HsaGrowthValueTask> listTask = growthValueTaskService.list(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, qo.getOperSubjectGuid())
                .eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode())
                .orderByAsc(HsaGrowthValueTask::getPosition));
        //去查询设置的成长值的名称
        GrowthValueRuleVO ruleName = valueRuleService.getGrowthRuleName(qo.getOperSubjectGuid());
        String name = ruleName.getGrowthValueRule();

        AppletOwnLevelVO vo = new AppletOwnLevelVO();
        vo.setMemberGrowthValue(memberInfo.getMemberGrowthValue());
        vo.setGrowthValueName(name);
        vo.setIsExistCard(isExistCard);

        List<OwnGrowthTaskDTO> appletTaskList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(listTask)) {
            fillListTask(qo, memberInfo, listTask, name, vo, appletTaskList);
        }
        if (!StringUtils.isEmpty(memberInfo.getMemberGradeInfoGuid())) {
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .eq(HsaMemberGradeInfo::getGuid, memberInfo.getMemberGradeInfoGuid())
                            .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                            .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
            vo.setCurrentGrade(hsaMemberGradeInfo.getVipGrade());
        }
        List<HsaMemberGradeInfo> hsaMemberGradeInfos = hsaMemberGradeInfoMapper
                .selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getOperSubjectGuid, qo.getOperSubjectGuid())
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                        .orderByAsc(HsaMemberGradeInfo::getVipGrade));
        List<GradeVo> gradeVoList = new ArrayList<>();
        for (HsaMemberGradeInfo hsaMemberGradeInfo : hsaMemberGradeInfos) {
            GradeVo gradeVo = new GradeVo();
            gradeVo.setVipGrade(hsaMemberGradeInfo.getVipGrade());
            gradeVo.setGrowthValue(hsaMemberGradeInfo.getGrowthValue());
            gradeVoList.add(gradeVo);
        }
        vo.setGradeVoList(gradeVoList);
        return vo;
    }

    private void fillListTask(AppletGrowthQO qo, HsaOperationMemberInfo memberInfo,
                              List<HsaGrowthValueTask> listTask, String name, AppletOwnLevelVO vo,
                              List<OwnGrowthTaskDTO> appletTaskList) {
        List<HsaGrowthValueDetailDTO> detailList = detailMapper.listAllGrowthDetail(
                listTask.stream().map(HsaGrowthValueTask::getId).collect(Collectors.toList()), memberInfo.getGuid());
        Map<Integer, List<HsaGrowthValueDetailDTO>> collectDetail = null;

        Map<String, List<HsaGrowthValueDetailDTO>> collectDetailBuNum = null;
        if (CollectionUtil.isNotEmpty(detailList)) {
            collectDetail = detailList.stream()
                    .collect(Collectors.groupingBy(HsaGrowthValueDetailDTO::getTaskAction));

            collectDetailBuNum = detailList.stream()
                    .collect(Collectors.groupingBy(HsaGrowthValueDetailDTO::getGrowthValueTaskId));
        }

        List<HsaExtraAwardRuleDTO> ruleList = ruleMapper.listAllExtraAwardRuleByTask(listTask.stream()
                .map(HsaGrowthValueTask::getGuid).collect(Collectors.toList()));
        Map<String, List<HsaExtraAwardRuleDTO>> collectRule = null;
        if (CollectionUtil.isNotEmpty(ruleList)) {
            collectRule = ruleList.stream()
                    .collect(Collectors.groupingBy(HsaExtraAwardRuleDTO::getGrowthValueTaskGuid));
        }

        //构建使用数据
        AppletGrowthTaskTransDTO transData = AppletGrowthTaskTransDTO.builder()
                .collectDetail(collectDetail)
                .collectDetailBuNum(collectDetailBuNum)
                .collectRule(collectRule)
                .name(name)
                .memberInfoGuid(qo.getMemberInfoGuid())
                .build();

        GrowthValueTaskTransform.INSTANCE.entitiesToDTOs(listTask).forEach(obj -> {
            OwnGrowthTaskDTO appletTask = createOwnTask(obj, transData, memberInfo);
            if (ObjectUtil.isNotNull(appletTask)) {
                appletTaskList.add(appletTask);
            }
        });
        if (CollectionUtil.isNotEmpty(appletTaskList)) {
            vo.setListTask(appletTaskList.stream()
                    .sorted(Comparator.comparing(e -> e.getTaskFinish() == null ? 0 : e.getTaskFinish())).collect(Collectors.toList()));
        }
    }

    @Override
    public AppletGrowthLevelVO getGrowthLevelDetail(AppletGrowthQO qo) {
        HsaOperationMemberInfo memberInfo = memberInfoMapper.queryByGuid(qo.getMemberInfoGuid());
        //去查询即将失效的成长值信息
        Integer immediatelyInvalidGrowth = detailMapper.sumImmediatelyInvalidGrowth(qo.getMemberInfoGuid(), qo.getOperSubjectGuid());

        return AppletGrowthLevelVO.builder().validGrowthValue(memberInfo.getMemberGrowthValue())
                .immediatelyInvalidGrowthValue(immediatelyInvalidGrowth)
                .build();
    }

    @Override
    public List<AppletGrowthValueDetailVO> listGrowthValueDetail(AppletGrowthDetailPageQO qo) {

        //去查询设置的成长值的名称
        GrowthValueRuleVO ruleName = valueRuleService.getGrowthRuleName(qo.getOperSubjectGuid());
        String name = ruleName.getGrowthValueRule();

        //去分页查询
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<HsaGrowthValueDetailDTO> detailList = detailMapper.listAppletGrowthPage(qo);

        if (CollectionUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }

        HsaGrowthValueDetailDTO max = detailList.stream().max(Comparator.comparing(HsaGrowthValueDetailDTO::getGmtCreate)).orElse(null);
        HsaGrowthValueDetailDTO min = detailList.stream().min(Comparator.comparing(HsaGrowthValueDetailDTO::getGmtCreate)).orElse(null);

        if (ObjectUtil.isNotNull(min) && ObjectUtil.isNotNull(max)) {
            qo.setMinTime(min.getGmtCreate());
            qo.setMaxTime(max.getGmtCreate().plusMonths(NumberConstant.NUMBER_1));
        }

        List<StatisticsMonthGrowthDTO> statisticsList = detailMapper.statisticsMonthGrowth(qo);
        Map<String, List<StatisticsMonthGrowthDTO>> collectStatistics = statisticsList.stream().collect(
                Collectors.groupingBy(obj -> obj.getGmtCreate().format(DateTimeFormatter.ofPattern("yyyy-MM"))));
        //按照年份和月份分组
        Map<String, List<HsaGrowthValueDetailDTO>> groupDetail = detailList.stream().collect(
                Collectors.groupingBy(obj -> obj.getGmtCreate().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                        LinkedHashMap::new, Collectors.toList()));
        List<AppletGrowthValueDetailVO> appletDetail = new ArrayList<>(detailList.size());
        groupDetail.forEach((k, v) -> {
            AppletGrowthValueDetailVO detail = new AppletGrowthValueDetailVO();
            detail.setMonth(k.replace(StringConstant.STR_BIAS_TWO, StringConstant.YEAR) + StringConstant.MONTH);
            //设置月度统计默认值
            AtomicInteger monthGrowthValue = new AtomicInteger();
            List<StatisticsMonthGrowthDTO> listStatistics = collectStatistics.get(k);
            listStatistics.forEach(s -> {
                if (ObjectUtil.equal(s.getGrowthValueType(), BooleanEnum.FALSE.getCode())) {
                    monthGrowthValue.addAndGet(s.getGrowthValue());
                } else {
                    monthGrowthValue.set(monthGrowthValue.get() - s.getGrowthValue());
                }
            });
            int value = monthGrowthValue.get();
            if (value > NumberConstant.NUMBER_0) {
                detail.setMonthGrowthValue(StringConstant.STR_ADD + monthGrowthValue.get());
            } else {
                detail.setMonthGrowthValue(String.valueOf(monthGrowthValue.get()));
            }
            fillGrowthValueDetail(name, v, detail);
            appletDetail.add(detail);
        });
        return appletDetail;
    }

    private void fillGrowthValueDetail(String name, List<HsaGrowthValueDetailDTO> v, AppletGrowthValueDetailVO detail) {
        //遍历k
        List<AppletGrowthDetailDTO> growthDetailList = new ArrayList<>(v.size());
        v.forEach(obj -> {
            AppletGrowthDetailDTO dto = new AppletGrowthDetailDTO();
            dto.setChangeType(obj.getChangeType());
            //设置成长值名称
            if (ObjectUtil.equal(obj.getChangeType(), SumValueChangeEnum.TASK.getCode())
                    && !StringUtils.isEmpty(obj.getTaskName())) {
                dto.setChangeTypeName(obj.getTaskName());
            } else {
                dto.setChangeTypeName(String.format(SumValueChangeEnum.getNameByCode(obj.getChangeType()), name));
            }
            dto.setGuid(obj.getGuid());
            if (Objects.nonNull(obj.getGrowthValueTaskId())) {
                dto.setGrowthValueTaskId(obj.getGrowthValueTaskId());
            }

            //设置记录产生条件
            LocalDateTime gmtCreate = obj.getGmtCreate();
            dto.setGmtCreate(DateTimeFormatter.ofPattern("dd日 HH:mm:ss").format(gmtCreate));
            //设置记录是否失效
            dto.setImmediatelyInvalid(buildImmediatelyInvalid(obj));
            //设置是否翻倍
            dto.setMultipleGrowth(BooleanEnum.FALSE.getCode());
            if (ObjectUtil.isNotNull(obj.getMultipleGrowth())) {
                dto.setMultipleGrowth(BooleanEnum.TRUE.getCode());
                dto.setMultipleNumber(obj.getMultipleGrowth());
            }

            dto.setMultipleCardGrowth(BooleanEnum.FALSE.getCode());
            if (ObjectUtil.isNotNull(obj.getMultipleCardGrowth())) {
                dto.setMultipleCardGrowth(BooleanEnum.TRUE.getCode());
                dto.setMultipleCardNumber(obj.getMultipleCardGrowth());
            }
            //设置成长值增减
            dto.setGrowthValueType(obj.getGrowthValueType());
            dto.setGrowthValue(obj.getGrowthValue());
            growthDetailList.add(dto);
        });
        detail.setGrowthDetailList(growthDetailList);
    }

    private Integer buildImmediatelyInvalid(HsaGrowthValueDetailDTO obj) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime growthValidityDate = obj.getGrowthValidityDate();
        if (ObjectUtil.equal(obj.getGrowthValidity(), BooleanEnum.TRUE.getCode()) && obj.getRecordRemainGrowthValue() > 0) {
            LocalDateTime hourTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), now.getHour(), NumberConstant.NUMBER_0);
            long hours = ChronoUnit.HOURS.between(
                    hourTime
                    , LocalDateTime.of(growthValidityDate.getYear(), growthValidityDate.getMonth(), growthValidityDate.getDayOfMonth(), growthValidityDate.getHour(), NumberConstant.NUMBER_0));
            //如果小于30天并大于0
            LocalDateTime validityHourTime = LocalDateTime.of(growthValidityDate.getYear(), growthValidityDate.getMonth(), growthValidityDate.getDayOfMonth(), growthValidityDate.getHour(), growthValidityDate.getMinute());
            LocalDateTime nowHourTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), now.getHour(), now.getMinute());

            long minutes = ChronoUnit.MINUTES.between(hourTime, validityHourTime);
            long nowMinutes = ChronoUnit.MINUTES.between(validityHourTime, nowHourTime);

            boolean includeTime = hours > NumberConstant.NUMBER_0 || (hours == NumberConstant.NUMBER_0 &&
                    nowMinutes < NumberConstant.NUMBER_60 && minutes > NumberConstant.NUMBER_0);
            if (hours <= NumberConstant.NUMBER_720 && includeTime) {
                return BooleanEnum.TRUE.getCode();
            }
        }
        return BooleanEnum.FALSE.getCode();
    }

    @Override
    public PageResult<StoreBaseInfo> listGrowthValueStore(AppletGrowthStorePageQO qo) {
        Integer currentPage = qo.getCurrentPage();
        if (Objects.nonNull(currentPage) && currentPage == 2) {//前端需要
            return new PageResult<>();
        }
        //根据任务id查询任务信息
        HsaGrowthValueTask task = growthValueTaskService.queryByGuid(qo.getTaskGuid());
        Integer applicableAllStore = task.getApplicableAllStore();
        List<StoreBaseInfo> storeBaseInfos;

        storeBaseInfos = storeBaseService.appletGetStoreInfo();
        if (ObjectUtil.equal(applicableAllStore, GoodsApplicableStoreEnum.PORTION_STORE.getCode())) {
            //去分页查询
            PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
            List<StoreBaseInfo> storeBaseInfoList = hsaStoreRuleInfoMapper.listGrowthValueStore(qo);
            //门店id集合
            List<String> storeIds = storeBaseInfoList.stream()
                    .filter(x -> Objects.nonNull(x) && !StringUtils.isEmpty(x.getStoreGuid()))
                    .map(StoreBaseInfo::getStoreGuid).collect(Collectors.toList());
            storeBaseInfos = storeFilter(storeBaseInfos, storeIds);
        }
        if (ObjectUtil.equal(task.getConsumptionGoodsType(), GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode())) {
            List<AppointClassifyGoodsDTO> classifyGoodsList = growthValueCommodityClassifyService.listCommodityClassifyByTask(task.getGuid());
            Set<String> storeIdSet = classifyGoodsList.stream().map(AppointClassifyGoodsDTO::getStoreGuid).collect(Collectors.toSet());
            storeBaseInfos = storeFilter(storeBaseInfos, Lists.newArrayList(storeIdSet));
        }
        //门店根据经纬度，排序
        storeBaseInfos = calculateDistance(storeBaseInfos, qo.getLongitude(), qo.getLatitude());
        return PageUtil.getPageResult(new PageInfo<>(storeBaseInfos));
    }

    /**
     * 门店条件筛选
     *
     * @param storeBaseInfos 门店信息集合
     * @param storeIds       门店id
     * @return 过滤结果
     */
    private List<StoreBaseInfo> storeFilter(List<StoreBaseInfo> storeBaseInfos, List<String> storeIds) {
        if (CollUtil.isEmpty(storeBaseInfos) || CollUtil.isEmpty(storeIds)) {
            return Collections.emptyList();
        }
        return storeBaseInfos.stream().filter(x -> Objects.nonNull(x.getId()) && storeIds.contains(x.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 门店根据经纬度，计算门店距离
     *
     * @param storeBaseInfos 门店集合
     * @param longitude      经度
     * @param latitude       纬度
     * @return 排序后的门店信息
     */
    private List<StoreBaseInfo> calculateDistance(List<StoreBaseInfo> storeBaseInfos, String longitude, String latitude) {
        if (CollUtil.isEmpty(storeBaseInfos)) {
            return Collections.emptyList();
        }
        String longitudeV;  //经度
        String latitudeV;   //纬度
        BigDecimal distance;//门店距离
        for (StoreBaseInfo storeBaseInfo : storeBaseInfos) {
            String addressPoint = storeBaseInfo.getAddress_point();
            if (StringUtils.isEmpty(addressPoint)) {
                storeBaseInfo.setDistance(new BigDecimal(-1));
                continue;
            }
            //addressPoint:由纬度 + "," + 经度 组成
            String[] split = addressPoint.split(APPLET_SEPARATOR);
            if (split.length == 2) {
                latitudeV = split[0]; //第一个参数为：纬度
                longitudeV = split[1]; //第二个参数为：经度
                //当前门店距离
                distance = DistanceUtils.getDistance(longitudeV, latitudeV, longitude, latitude);
            } else {
                distance = new BigDecimal(-1);
            }
            storeBaseInfo.setDistance(distance);
        }
        return storeBaseInfos;
    }

    @Override
    public AppletsGrowthCommodityVO listGrowthDesignatedGoods(AppletGrowthGoodsPageQO request) {
        AppletsGrowthCommodityVO appletsGrowthCommodityVO = new AppletsGrowthCommodityVO();
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules = hsaGrowthValueCommodityRuleMapper.selectList(
                new LambdaQueryWrapper<HsaGrowthValueCommodityRule>()
                        .eq(HsaGrowthValueCommodityRule::getOperSubjectGuid, request.getOperSubjectGuid())
                        .eq(HsaGrowthValueCommodityRule::getGrowthValueTaskGuid, request.getGrowthValueTaskGuid()));
        if (CollUtil.isEmpty(hsaGrowthValueCommodityRules)) {
            return appletsGrowthCommodityVO;
        }
        List<String> commodityIds = hsaGrowthValueCommodityRules.stream()
                .filter(x -> Objects.nonNull(x.getCommodityId()))
                .map(x -> x.getCommodityId()).collect(Collectors.toList());
        GradeCommodityBasePageQO gradeCommodityBasePageQO = new GradeCommodityBasePageQO();
        gradeCommodityBasePageQO.setCommityIds(commodityIds);
        List<GrowthCommodityBaseVO> growthCommodityBaseVOS = shopBaseService.queryGradeCommodityPage(gradeCommodityBasePageQO)
                .getGrowthCommodityBaseVOS();
        //移除重复商品
        List<GrowthCommodityBaseVO> commodityMultiple = isCommodityMultiple(growthCommodityBaseVOS);
        //移除已下架或者失效的商品
        commodityMultiple.removeIf(e -> e.getStoreState() == CommodityStatusEnum.COMMODITY_DOWN.getCode() || e.getStoreState() == CommodityStatusEnum.COMMODITY_LOSE.getCode());
        //当前成长值任务信息
        HsaGrowthValueTask hsaGrowthValueTask = growthValueTaskService.queryByGuid(request.getGrowthValueTaskGuid());
        Optional<HsaGrowthValueTask> growthValueTask = Optional.ofNullable(hsaGrowthValueTask);
        //选择商品类型
        Integer integer = growthValueTask.map(HsaGrowthValueTask::getChooseGoodsType).orElse(null);
        //成长值任务guid
        String taskGuid = growthValueTask.map(HsaGrowthValueTask::getGuid).orElse("");
        appletsGrowthCommodityVO.setGrowthCommodityBaseVOList(commodityMultiple);
        appletsGrowthCommodityVO.setType(integer);
        appletsGrowthCommodityVO.setGrowthValueTaskGuid(taskGuid);
        return appletsGrowthCommodityVO;
    }

    @Override
    public PageResult listGrowthStoreByStrategyId(AppletGrowthStoreQO request) {
        Integer currentPage = request.getCurrentPage();
        if (Objects.nonNull(currentPage) && currentPage == 2) { //前端需要
            return new PageResult();
        }
        List<StoreBaseInfo> storeBaseInfos = storeBaseService.executeRequest(request);
        paramValidate(storeBaseInfos);
        //门店根据经纬度，排序
        storeBaseInfos = calculateDistance(storeBaseInfos, request.getLongitude(), request.getLatitude());
        return PageUtil.getPageResult(new PageInfo<>(storeBaseInfos));
    }

    private void paramValidate(List<StoreBaseInfo> storeBaseInfos) {
        if (CollUtil.isEmpty(storeBaseInfos)) {
            return;
        }
        for (StoreBaseInfo storeBaseInfo : storeBaseInfos) {
            String address = storeBaseInfo.getAddress();
            if (!StringUtils.isEmpty(address) && address.equals(FALSE_STRING)) {
                storeBaseInfo.setAddress(null);
            }

            String addressPoint = storeBaseInfo.getAddressPoint();
            if (!StringUtils.isEmpty(addressPoint) && addressPoint.equals(FALSE_STRING)) {
                storeBaseInfo.setAddressPoint(null);
            }

            String contact = storeBaseInfo.getContact();
            if (!StringUtils.isEmpty(contact) && contact.equals(FALSE_STRING)) {
                storeBaseInfo.setContact(null);
            }

        }

    }


    /**
     * 是否存在不同策略单下相同商品
     * 如果存在：商品售价展示为：最低价 + "起"  （￥20 起）
     */
    private List<GrowthCommodityBaseVO> isCommodityMultiple(List<GrowthCommodityBaseVO> growthCommodityBaseVOS) {
        if (CollUtil.isEmpty(growthCommodityBaseVOS)) {
            return growthCommodityBaseVOS;
        }
        //去重：同一个策略单下相同的商品
        ArrayList<GrowthCommodityBaseVO> collect = growthCommodityBaseVOS.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(x -> x.getCommodityId() + ";" + x.getStrategyId()))), ArrayList::new));

        //将价格排序
        collect.sort((r1, r2) -> {
            String basePriceR1 = r1.getBasePrice();
            if (basePriceR1.contains("-")) {
                basePriceR1 = basePriceR1.split("-")[0];
            }
            String basePriceR2 = r2.getBasePrice();
            if (basePriceR2.contains("-")) {
                basePriceR2 = basePriceR2.split("-")[0];
            }

            BigDecimal seq1 = new BigDecimal(basePriceR1);
            BigDecimal seq2 = new BigDecimal(basePriceR2);
            return seq1.compareTo(seq2);
        });
        //通过商品名称分组，并统计分组数量
        Map<String, Long> nameMap = collect.stream().collect(Collectors.groupingBy(GrowthCommodityBaseVO::getCommodityName, Collectors.counting()));

        //将相同商品名称去重
        ArrayList<GrowthCommodityBaseVO> commodityBaseVOS = collect.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(GrowthCommodityBaseVO::getCommodityName))), ArrayList::new));

        if (CollUtil.isNotEmpty(commodityBaseVOS)) {
            for (GrowthCommodityBaseVO commodityBaseVO : commodityBaseVOS) {
                Optional<GrowthCommodityBaseVO> growthCommodityBase = Optional.of(commodityBaseVO);
                String name = growthCommodityBase.map(GrowthCommodityBaseVO::getCommodityName).orElse("");
                //判断商品是否存在多个
                if (Objects.nonNull(nameMap) && nameMap.containsKey(name) && nameMap.get(name) > 1) {
                    commodityBaseVO.setIsMultiple(true);
                } else {
                    commodityBaseVO.setIsMultiple(false);
                }
            }
        }
        return commodityBaseVOS;
    }

    private OwnGrowthTaskDTO createOwnTask(HsaGrowthValueTaskDTO obj
            , AppletGrowthTaskTransDTO transData, HsaOperationMemberInfo memberInfo) {
        //若未开启
        if (ObjectUtil.equal(obj.getIsEnable(), BooleanEnum.FALSE.getCode())) {
            return null;
        }
        OwnGrowthTaskDTO ownGrowthTaskDTO = null;
        switch (Objects.requireNonNull(TaskActionEnum.Enum(obj.getTaskAction()))) {
            case REGISTER:
                ownGrowthTaskDTO = getGrowthRegisterDto(obj, transData, memberInfo, ownGrowthTaskDTO);
                if (ownGrowthTaskDTO == null) return null;
                break;
            case PERFECT_PERSONAL_DETAILS:
                String typeJson = obj.getPersonalDetailsTypeJson();
                String[] split = typeJson.split(StringConstant.COMMA);
                if (appletIntegralService.checkPerfectPersonalTask(memberInfo, obj.getGmtCreate(), split)) {
                    return null;
                }
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildPerfectPersonal(obj, transData);
                break;
            case SINGLE_CONSUMPTION_AMOUNT:
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildSingleConsumption(obj, transData);
                break;
            case SINGLE_RECHARGE_AMOUNT:
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildSingleRecharge(obj, transData);
                break;
            case CONSUMPTION_SPECIFIED_GOODS:
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildSpecifiedGoods(obj, transData);
                break;
            case TOTAL_CONSUMPTION_AMOUNT:
                //查询周期累计的金额
                BigDecimal periodAmount = consumptionService.totalPeriodAmount(transData.getMemberInfoGuid()
                        , obj.getTotalPeriodType()
                        , obj.getTotalPeriod()
                        , obj.getGmtCreate());
                transData.setPeriodAmount(periodAmount);
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildConsumptionAmountAction(obj, transData);
                break;
            case TOTAL_CONSUMPTION_COUNT:
                //查询周期累计消费的笔数
                Integer periodCount = consumptionService.totalPeriodCount(transData.getMemberInfoGuid()
                        , obj.getTotalPeriodType()
                        , obj.getTotalPeriod()
                        , obj.getGmtCreate()
                        , obj.getConsumptionIgnoreAmount());
                transData.setPeriodCount(periodCount);
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildConsumptionCountAction(obj, transData);
                break;
            case TOTAL_RECHARGE_AMOUNT:
                //查询周期累计的充值金额
                BigDecimal periodRechargeAmount = consumptionService.totalPeriodRechargeAmount(transData.getMemberInfoGuid()
                        , obj.getTotalPeriodType()
                        , obj.getTotalPeriod()
                        , obj.getGmtCreate());
                transData.setPeriodAmount(periodRechargeAmount);
                ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
                ownGrowthTaskDTO.buildTotalRechargeAction(obj, transData);
                break;
            default:
                break;
        }
        if (ownGrowthTaskDTO == null) {
            return null;
        }
        //若任务描述是自定义的
        if (ObjectUtil.equal(obj.getDescriptionType(), DescriptionTypeEnum.CUSTOM.getCode())) {
            ownGrowthTaskDTO.setTaskDescription(obj.getDescription());
        }
        if (ObjectUtil.equal(obj.getTaskValidityType(), TaskValidityTypeEnum.PERMANENT_VALIDITY.getCode())) {
            return ownGrowthTaskDTO;
        }
        boolean underway = LocalDateTime.now().isBefore(obj.getEndFixedTaskValidityDate())
                && LocalDateTime.now().isAfter(obj.getStartFixedTaskValidityDate());
        //不满足时间
        if (!underway) {
            return null;
        }
        return ownGrowthTaskDTO;
    }

    private static OwnGrowthTaskDTO getGrowthRegisterDto(HsaGrowthValueTaskDTO obj, AppletGrowthTaskTransDTO transData, HsaOperationMemberInfo memberInfo, OwnGrowthTaskDTO ownGrowthTaskDTO) {
        if (memberInfo.getGmtCreate().isBefore(obj.getGmtCreate())) {
            return null;
        }

        if (obj.getSourceTypeJson().contains(String.valueOf(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()))) {
            ownGrowthTaskDTO = OwnGrowthTaskDTO.init(obj);
            ownGrowthTaskDTO.setTaskDescription(String.format(GrowthValueConstant.GROWTH_DESCRIPTION_REGISTER, obj.getGrowthValue(), transData.getName()));
            if (!transData.notNullDetail(obj.getTaskAction())) {
                return null;
            }
            ownGrowthTaskDTO.setTaskFinish(BooleanEnum.TRUE.getCode());
        }
        return ownGrowthTaskDTO;
    }

    private AppletGrowthTaskDTO createAppletTask(HsaGrowthValueTaskDTO obj, AppletGrowthTaskTransDTO transData) {
        AppletGrowthTaskDTO taskDTO = null;
        //若未开启
        if (ObjectUtil.equal(obj.getIsEnable(), BooleanEnum.FALSE.getCode())) {
            return null;
        }
        setApplyBusinessMap(transData);

        switch (Objects.requireNonNull(TaskActionEnum.Enum(obj.getTaskAction()))) {
            case REGISTER:
                taskDTO = new RegisterActionDTO(obj, transData);
                break;
            case PERFECT_PERSONAL_DETAILS:
                taskDTO = new PerfectPersonalActionDTO(obj, transData);
                break;
            case SINGLE_CONSUMPTION_AMOUNT:
                taskDTO = new SingleConsumptionActionDTO(obj, transData);
                break;
            case SINGLE_RECHARGE_AMOUNT:
                taskDTO = new SingleRechargeActionDTO(obj, transData);
                break;
            case CONSUMPTION_SPECIFIED_GOODS:
                transData.setApplicableAllStore(obj.getApplicableAllStore());
                if (ObjectUtil.equal(obj.getApplicableAllStore(), GoodsApplicableStoreEnum.PORTION_STORE.getCode())) {
                    List<String> storeIds = hsaStoreRuleInfoMapper.selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                                    .eq(HsaStoreRuleInfo::getTypeGuid, obj.getGuid()))
                            .stream().filter(Objects::nonNull).map(HsaStoreRuleInfo::getStoreGuid).collect(Collectors.toList());
                    //过滤被禁用的门店
                    filterDisableStore(storeIds);
                    transData.setStoreNum(storeIds.size());
                    transData.setStoreIdList(storeIds);
                }
                //指定商品需要统计门店和商品
                if (ObjectUtil.equal(obj.getConsumptionGoodsType(), GoodsTypeEnum.APPOINT_GOODS.getCode())) {
                    //查询当前任务指定的商品数量
                    List<HsaGrowthValueCommodityRule> commodityRules = hsaGrowthValueCommodityRuleMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueCommodityRule>()
                            .eq(HsaGrowthValueCommodityRule::getGrowthValueTaskGuid, obj.getGuid()));
                    List<String> collectCommodityId = commodityRules.stream().map(HsaGrowthValueCommodityRule::getCommodityId).collect(Collectors.toList());
                    //过滤商品
                    filterDisableCommodity(collectCommodityId);

                    transData.setGoodsNum(collectCommodityId.size());
                    taskDTO = SpecifiedGoodsActionDTO.build(obj, transData);
                }
                //指定商品分类需要统计门店
                if (ObjectUtil.equal(obj.getConsumptionGoodsType(), GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode())) {
                    List<AppointClassifyGoodsDTO> classifyGoodsList = growthValueCommodityClassifyService.listCommodityClassifyByTask(obj.getGuid());
                    List<String> storeIdList = Lists.newArrayList(classifyGoodsList.stream().map(AppointClassifyGoodsDTO::getStoreGuid).collect(Collectors.toSet()));
                    Set<Integer> businessTypeSet = classifyGoodsList.stream().map(AppointClassifyGoodsDTO::getBusinessType).collect(Collectors.toSet());
                    //过滤被禁用的门店
                    filterDisableStore(storeIdList);

                    transData.setStoreNum(storeIdList.size());
                    obj.setCategoryNameData(buildAppointGoodsTypeName(classifyGoodsList));
                    obj.setApplyBusinessJson(Joiner.on(StringConstant.COMMA).join(businessTypeSet));
                    taskDTO = SpecifiedGoodsTypeActionDTO.build(obj, transData);
                    taskDTO.setStoreIdList(storeIdList);

                }
                if (Objects.nonNull(taskDTO) && ObjectUtil.equal(obj.getApplicableAllStore(), GoodsApplicableStoreEnum.PORTION_STORE.getCode())) {
                    taskDTO.setStoreIdList(transData.getStoreIdList());
                }
                break;
            case TOTAL_CONSUMPTION_AMOUNT:
                taskDTO = new ConsumptionAmountActionDTO(obj, transData);
                break;
            case TOTAL_CONSUMPTION_COUNT:
                taskDTO = new ConsumptionCountActionDTO(obj, transData);
                break;
            case TOTAL_RECHARGE_AMOUNT:
                taskDTO = new TotalRechargeActionDTO(obj, transData);
                break;
            default:
                break;
        }
        //去构建条件的公共属性
        Boolean flag = ServiceCommonUtil.buildCommonCondition(taskDTO, obj);
        if (flag && taskDTO != null) {
            //设置成长值有效期
            taskDTO.setGrowthValidityDate(buildValidityDate(obj));
        }
        if (!flag) {
            return null;
        }

        return taskDTO;
    }

    private void filterDisableCommodity(List<String> collectCommodityId) {
        GradeCommodityBasePageQO qo = new GradeCommodityBasePageQO();
        qo.setCommityIds(collectCommodityId);
        List<GrowthCommodityBaseVO> baseList = shopBaseService.queryGradeCommodityPage(qo).getGrowthCommodityBaseVOS();
        if (CollUtil.isEmpty(baseList)) {
            collectCommodityId.clear();
            return;
        }
        Set<String> set = baseList.stream().map(GrowthCommodityBaseVO::getCommodityId).collect(Collectors.toSet());
        collectCommodityId.removeIf(e -> !set.contains(e));
    }

    private void filterDisableStore(List<String> storeIdList) {
        if (CollectionUtil.isEmpty(storeIdList)) {
            return;
        }
        List<StoreBaseInfo> storeBaseInfoList = storeBaseService.listEnableStoreByIdList(storeIdList, ThreadLocalCache.getOperSubjectGuid());
        if (CollectionUtil.isEmpty(storeBaseInfoList)) {
            storeIdList.clear();
            return;
        }
        Set<String> baseStoreIdSet = storeBaseInfoList.stream().map(StoreBaseInfo::getId).collect(Collectors.toSet());
        storeIdList.removeIf(e -> !baseStoreIdSet.contains(e));
    }

    private String buildAppointGoodsTypeName(List<AppointClassifyGoodsDTO> classifyGoodsList) {
        Set<String> set = Sets.newHashSet();
        classifyGoodsList.forEach(e -> e.getGoodsCategory().forEach(c -> set.add(c.getCategoryName())));
        return Joiner.on(StringConstant.MARK).join(set);
    }

    private static String buildValidityDate(HsaGrowthValueTaskDTO obj) {
        if (ObjectUtil.equal(obj.getGrowthValueValidityType(), GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode())) {
            return GrowthValueInvalidEnum.GROWTH_VALUE_INVALID_PERMANENT.getDes();
        }
        if (ObjectUtil.equal(obj.getGrowthValueValidityType(), GrowthValueValidityTypeEnum.DYNAMIC_VALIDITY.getCode())) {
            return String.format(GrowthValueInvalidEnum.getNameByCode(obj.getDynamicValidityType()), obj.getDynamicValidityNumber());
        }
        if (ObjectUtil.equal(obj.getGrowthValueValidityType(), GrowthValueValidityTypeEnum.FIXED_VALIDITY.getCode())) {
            return String.format(GrowthValueInvalidEnum.GROWTH_VALUE_VALIDITY_DATE.getDes()
                    , obj.getFixedGrowthValueValidityDate());
        }
        return null;
    }

    private void setApplyBusinessMap(AppletGrowthTaskTransDTO transData) {
        List<HsaApplyDictionaries> hsaApplyDictionaries = hsaApplyDictionariesMapper.selectList(
                new LambdaQueryWrapper<HsaApplyDictionaries>()
                        .eq(HsaApplyDictionaries::getModule, ApplyModuleEnum.APPLY_BUSINESS.getType()));
        if (CollUtil.isEmpty(hsaApplyDictionaries)) {
            return;
        }
        Map<String, String> applyBusinessMap = new HashMap<>();
        for (HsaApplyDictionaries hsaApplyDictionary : hsaApplyDictionaries) {
            applyBusinessMap.put(hsaApplyDictionary.getType(), hsaApplyDictionary.getTypeName());
        }
        transData.setApplyBusinessMap(applyBusinessMap);
    }
}
