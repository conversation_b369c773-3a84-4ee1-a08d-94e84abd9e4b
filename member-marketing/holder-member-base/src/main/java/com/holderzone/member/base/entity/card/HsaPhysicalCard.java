package com.holderzone.member.base.entity.card;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.annotation.FieldLabel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

 /**
 * <AUTHOR>
 * @description 实体卡实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaPhysicalCard implements Serializable {
    private static final long serialVersionUID = 1L;

     /**
     *
     * 实体卡GUID
     */
     @FieldLabel
    private String guid;

    /**
     * uid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String uid;

    /**
     /**
     *
     * 运营主体GUID
     */
     @FieldLabel
    private String operSubjectGuid;

     /**
     *
     * 企业GUID
     */
     @FieldLabel
    private String enterpriseGuid;


     /**
     *
     * 会员GUID
     */
     @FieldLabel
    private String memberInfoGuid;

     /**
     *
     * 会员卡GUID
     */
     @FieldLabel
    private String cardGuid;

     /**
     *
     * 实体卡卡号
     */
     @FieldLabel
    private String cardNum;

     /**
     *
     * 来源,0后台添加,2一体机注册
     */
     @FieldLabel
    private Integer source;

     /**
     *
     * 卡状态：-1 未激活  0已冻结 1 正常 2 已过期
     */
     @FieldLabel
    private Integer cardState;

     /**
     *
     * 是否已经绑定会员：  0 否 1 是
     */
     @FieldLabel
    private Integer memberBindingState;

     /**
     *
     * 实体卡绑定码
     */
     @FieldLabel
    private String cardBindingNum;

     /**
     *
     * 实体卡绑定号码
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String phoneNum;

     /**
     *
     * 实体卡绑定用户名称
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String userName;

     /**
     *
     * 是否删除
     */
     @FieldLabel
    private Integer isDelete;

     /**
     *
     * 补贴余额
     */
     @FieldLabel
    private BigDecimal subsidyAmount;

     /**
     *
     * 充值余额
     */
     @FieldLabel
    private BigDecimal cardAmount;

     /**
     *
     * 赠送余额
     */
     @FieldLabel
    private BigDecimal giftAmount;


     /**
     *
     * 押金名称
     */
     @FieldLabel
    private String cashName;

     /**
     *
     * 开卡押金
     */
     @FieldLabel
    private BigDecimal cashPledge;

     /**
     *
     * 可退押金
     */
     @FieldLabel
    private BigDecimal returnableCashPledge;

     /**
     *
     * 超额使用余额
     */
     @FieldLabel
    private BigDecimal excessAmount;

     /**
     *
     * 超额使用次数
     */
     @FieldLabel
    private Integer excessTimes;

     /**
     *
     * 实体卡激活时间
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activationTime;

     /**
     *
     * 实体卡绑定用户时间
     */
     @FieldLabel
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime bindUserTime;


     /**
     *
     * 绑定账户时是否开通电子卡
     */
     @FieldLabel
    private Integer issueElectronicCard;

    /**
     * 押金支付记录guid
     */
    private String cardStrategyRecordGuid;

     @FieldLabel
     private LocalDateTime gmtCreate;
     @FieldLabel
     private LocalDateTime gmtModified;
     @FieldLabel
    private String makePhysicalCardRecordGuid;


}
