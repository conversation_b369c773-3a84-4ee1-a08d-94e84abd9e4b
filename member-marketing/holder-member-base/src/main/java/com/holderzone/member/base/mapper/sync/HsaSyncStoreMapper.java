package com.holderzone.member.base.mapper.sync;

import com.holderzone.member.base.entity.sync.HsaSyncStore;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.base.store.dto.SyncStoreQo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 同步门店 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface HsaSyncStoreMapper extends HolderBaseMapper<HsaSyncStore> {

    Integer insertStore(HsaSyncStore store);

    List<SyncStoreDTO> list(@Param("qo") SyncStoreQo qo);
}
