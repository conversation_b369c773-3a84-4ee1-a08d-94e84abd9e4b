package com.holderzone.member.base.service.credit.impl;

import com.holderzone.member.base.entity.credit.HsaClearingStatementOperateRecord;
import com.holderzone.member.base.mapper.credit.HsaClearingStatementOperateRecordMapper;
import com.holderzone.member.base.service.credit.HsaClearingStatementOperateRecordService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.vo.credit.CreditAdjustRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-14 17:15
 */
@Slf4j
@Service
public class HsaClearingStatementOperateRecordServiceImpl extends HolderBaseServiceImpl<HsaClearingStatementOperateRecordMapper,
        HsaClearingStatementOperateRecord> implements HsaClearingStatementOperateRecordService {

    private final HsaClearingStatementOperateRecordMapper hsaClearingStatementOperateRecordMapper;

    public HsaClearingStatementOperateRecordServiceImpl(HsaClearingStatementOperateRecordMapper hsaClearingStatementOperateRecordMapper) {
        this.hsaClearingStatementOperateRecordMapper = hsaClearingStatementOperateRecordMapper;
    }

    @Override
    public List<CreditAdjustRecordVO> getCreditAdjustRecord(String clearingStatementNumber) {
        return hsaClearingStatementOperateRecordMapper.getCreditAdjustRecord(clearingStatementNumber);
    }
}
