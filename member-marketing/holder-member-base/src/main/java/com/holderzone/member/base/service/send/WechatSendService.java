package com.holderzone.member.base.service.send;

import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;

import java.util.List;

public interface WechatSendService {

    void send(MessagesSendQO messagesSend);

    /**
     * 批量发送
     */
    void sendBatch(List<MessagesSendQO> messagesSendList);

    SendMessagesConfigVO getMessagesConfigByName( String operSubjectGuid, String name);
}
