package com.holderzone.member.base.config;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ScanPackageUtils;
import com.holderzone.member.base.service.member.chain.label.Responsibility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Configuration
public class ChainConfig {

    private static final String LABEL_PACKAGE_NAME = "com.holderzone.member.base.service.member.chain.label";

    @Autowired
    private ApplicationContext applicationContext;

    private final List<Responsibility> responsibilities = new ArrayList<>();

    @Bean
    public ChainConfig init() {
        final List<Class<Responsibility>> classList = new ArrayList<>();
        ChainConfig chainConfig = new ChainConfig();
        Set<Class<?>> classes = ScanPackageUtils.getClasses(LABEL_PACKAGE_NAME);
        classes.stream()
                .filter(aClass -> {
                    Type[] genericInterfaces = aClass.getGenericInterfaces();
                    return Arrays.stream(genericInterfaces)
                            .map(Type::getTypeName)
                            .anyMatch(s -> Responsibility.class.getName().equals(s));
                })
                .forEach(aClass ->
                        classList.add((Class<Responsibility>) aClass)
                );
        classList.forEach(responsibilityClass ->
                responsibilities.add(applicationContext.getBean(responsibilityClass))
        );
        if (responsibilities.isEmpty()) {
            throw new BusinessException("未加载初始化责任service");
        }
        return chainConfig;
    }

    public List<Responsibility> getResponsibilities() {
        return this.responsibilities;
    }
}
