package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.entity.member.HsaMemberLabelRecord;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelRecordMapper;
import com.holderzone.member.base.service.member.HsaMemberLabelRecordService;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.excel.MemberLabelRecordExcelVO;
import com.holderzone.member.common.dto.label.MemberLabelRecordDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.member.MemberLabelExcelRecordQO;
import com.holderzone.member.common.qo.member.MemberLabelRecordQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.member.MemberLabelRecordVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员标签记录实现类
 * @date 2021/9/27 10:09
 */
@Service
@Slf4j
public class HsaMemberLabelRecordServiceImpl extends HolderBaseServiceImpl<HsaMemberLabelRecordMapper, HsaMemberLabelRecord> implements HsaMemberLabelRecordService {

    @Resource
    private HsaMemberLabelRecordMapper hsaMemberLabelRecordMapper;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private HsaMemberLabelRecordService hsaMemberLabelRecordService;

    /**
     * 生成guid工具类
     */
    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private FileOssService fileOssService;

    @Resource
    private RequestGoalgoService operatingSubjectService;


    @Override
    public Page<MemberLabelRecordVO> queryMemberLabelRecord(MemberLabelRecordQO memberLabelRecordQO) {
        memberLabelRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<MemberLabelRecordVO> memberLabelRecordVOS;
        Page<MemberLabelRecordVO> queryCardInfoPageVOPage = new Page<>(memberLabelRecordQO.getCurrentPage(), memberLabelRecordQO.getPageSize());
        PageHelper.startPage(memberLabelRecordQO.getCurrentPage(), memberLabelRecordQO.getPageSize());
        memberLabelRecordVOS = hsaMemberLabelRecordMapper.queryMemberLabelRecord(memberLabelRecordQO);
        queryCardInfoPageVOPage.setRecords(memberLabelRecordVOS);
        PageResult pageResult = PageUtil.getPageResult(new PageInfo<>(memberLabelRecordVOS));
        queryCardInfoPageVOPage.setTotal(pageResult.getTotal());
        return queryCardInfoPageVOPage;
    }

    @Override
    public String exportMemberLabelRecord(MemberLabelExcelRecordQO memberLabelExcelRecordQO) {
        MemberLabelRecordQO qo = new MemberLabelRecordQO();
        BeanUtils.copyProperties(memberLabelExcelRecordQO, qo);
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        qo.setCurrentPage(NumberConstant.NUMBER_0);
        qo.setPageSize(NumberConstant.NUMBER_9999999);
        qo.setMemberInfoGuid(memberLabelExcelRecordQO.getMemberInfoGuid());
        int count = hsaMemberLabelRecordMapper.queryMemberLabelRecordCount(qo);
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        boolean phonePermission;
        try {
            phonePermission = operatingSubjectService.phonePermission(SystemPermissionEnum.MEMBER_PERMISSION.getDes());
        } catch (Exception e) {
            phonePermission = false;
        }
        List<MemberLabelRecordVO> memberLabelRecordVOS = queryMemberLabelRecord(qo).getRecords();
        if (CollUtil.isNotEmpty(memberLabelRecordVOS)) {
            List<MemberLabelRecordExcelVO> memberLabelRecordExcelVOS = Lists.newArrayList();
            addLabelRecordExcel(memberLabelRecordVOS, memberLabelRecordExcelVOS, phonePermission);
            return getExcel(memberLabelRecordExcelVOS);
        }
        return StringConstant.EMPTY;
    }

    private static void addLabelRecordExcel(List<MemberLabelRecordVO> memberLabelRecordVOS, List<MemberLabelRecordExcelVO> memberLabelRecordExcelVOS, boolean phonePermission) {
        for (MemberLabelRecordVO memberLabelRecordVO : memberLabelRecordVOS) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            MemberLabelRecordExcelVO excelVO = new MemberLabelRecordExcelVO();
            excelVO.setNum(memberLabelRecordExcelVOS.size() + 1)
                    .setPhoneNum(StringBaseHandlerUtil.phoneCountryCodeHandler(memberLabelRecordVO.getPhoneCountryCode(), memberLabelRecordVO.getPhoneNum(), phonePermission))
                    .setIsConnection(ConnectionLabelTypeEnum.getDesByCode(memberLabelRecordVO.getIsConnection()))
                    .setLabelName(memberLabelRecordVO.getLabelName())
                    .setLabelType(LabelTypeEnum.getDesByCode(memberLabelRecordVO.getLabelType()))
                    .setConnectionTime(DateUtil.formatLocalDateTime(memberLabelRecordVO.getConnectionTime(), DateUtil.PATTERN_DATETIME))
                    .setConnectionType(LabelConnectTypeEnum.getDesByCode(memberLabelRecordVO.getConnectionType()))
                    .setConnectionOperator(memberLabelRecordVO.getConnectionOperator())
                    .setCancelConnectionTime(ObjectUtil.isNotNull(memberLabelRecordVO.getCancelConnectionTime()) ?
                            df.format(memberLabelRecordVO.getCancelConnectionTime()) : StringConstant.STR_BIAS_TWO)
                    .setCancelConnectionType(ObjectUtil.isNotNull(memberLabelRecordVO.getCancelConnectionType()) ?
                            LabelCancelConnectTypeEnum.getDesByCode(memberLabelRecordVO.getCancelConnectionType()) : StringConstant.STR_BIAS_TWO)
                    .setCancelConnectionOperator(ObjectUtil.isNotNull(memberLabelRecordVO.getCancelConnectionOperator()) ?
                            memberLabelRecordVO.getCancelConnectionOperator() : StringConstant.STR_BIAS_TWO);
            memberLabelRecordExcelVOS.add(excelVO);
        }
    }

    private String getExcel(List<MemberLabelRecordExcelVO> memberLabelRecordExcelVOS) {
        if (CollUtil.isNotEmpty(memberLabelRecordExcelVOS)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formatDateTime = LocalDateTime.now().format(formatter);
            ExcelResult excelResult = com.aimilin.utils.BeanUtils.toResult(memberLabelRecordExcelVOS);
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName("标签关联记录" + formatDateTime + "." + ExcelType.XLSX);
                return fileOssService.upload(fileDto).replace("http", "https");
            } catch (Exception e) {
                log.error("上传文件失败");
                e.printStackTrace();
            }
        }
        return null;
    }

//    @Override
//    public Boolean batchUpdateAuto(BatchMemberLabelUpdateAutoQO updateAutoQO) {
//        UpdateLabelCorrelationStatusQO qo = new UpdateLabelCorrelationStatusQO();
//        BeanUtils.copyProperties(updateAutoQO, qo);
//        return hsaMemberLabelService.updateCorrelationStatus(qo);
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMemberLabelRecord(List<MemberLabelRecordDTO> memberLabelRecordQOList, HeaderUserInfo headerUserInfo) {
        if (CollUtil.isNotEmpty(memberLabelRecordQOList)) {

            List<HsaMemberLabelRecord> saveMemberLabelRecordList = Lists.newArrayList();
            List<HsaMemberLabelRecord> updateMemberLabelRecordList = Lists.newArrayList();
            List<String> memberLabelGuids = memberLabelRecordQOList.stream().map(MemberLabelRecordDTO::getMemberLabelGuid).collect(Collectors.toList());
            List<HsaMemberLabelRecord> hsaMemberLabelRecords = hsaMemberLabelRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberLabelRecord>()
                    .in(HsaMemberLabelRecord::getMemberLabelGuid, memberLabelGuids)
                    .eq(HsaMemberLabelRecord::getIsConnection, BooleanEnum.TRUE.getCode()));
            Map<String, HsaMemberLabelRecord> hsaMemberLabelRecordMap = hsaMemberLabelRecords.stream().collect(Collectors.
                    toMap(HsaMemberLabelRecord::getMemberLabelGuid, Function.identity(), (entity1, entity2) -> entity1));
            // 注意下：memberLabelRecordQOList 里面包含了取关的记录和增加关联的记录 需要分别处理
            froDealMemberLabelRecordQOList(memberLabelRecordQOList, headerUserInfo, hsaMemberLabelRecordMap, updateMemberLabelRecordList, saveMemberLabelRecordList);
            if (CollUtil.isNotEmpty(updateMemberLabelRecordList)) {
                hsaMemberLabelRecordMapper.updateIsConnectionByGuid(updateMemberLabelRecordList);
            }
            if (CollUtil.isNotEmpty(saveMemberLabelRecordList)) {
                hsaMemberLabelRecordService.saveBatch(saveMemberLabelRecordList);
            }
        }
    }

    private void froDealMemberLabelRecordQOList(List<MemberLabelRecordDTO> memberLabelRecordQOList, HeaderUserInfo headerUserInfo, Map<String, HsaMemberLabelRecord> hsaMemberLabelRecordMap, List<HsaMemberLabelRecord> updateMemberLabelRecordList, List<HsaMemberLabelRecord> saveMemberLabelRecordList) {
        for (MemberLabelRecordDTO memberLabelRecordQO : memberLabelRecordQOList) {
            HsaMemberLabelRecord memberLabelRecord = hsaMemberLabelRecordMap.get(memberLabelRecordQO.getMemberLabelGuid());
            if (ObjectUtil.isNotNull(memberLabelRecord)) {
                addUpdateMemberLabelRecordList(headerUserInfo, updateMemberLabelRecordList, memberLabelRecordQO, memberLabelRecord);
            } else {
                //此通道默认新增关联
                addSaveMemberLabelRecordList(headerUserInfo, saveMemberLabelRecordList, memberLabelRecordQO);
            }
        }
    }

    private void addSaveMemberLabelRecordList(HeaderUserInfo headerUserInfo, List<HsaMemberLabelRecord> saveMemberLabelRecordList, MemberLabelRecordDTO memberLabelRecordQO) {
        // 检查关键字段是否为空，避免创建无效记录, 取关的记录也不要添加了
        if (StringUtil.isEmpty(memberLabelRecordQO.getMemberInfoGuid()) || 
            StringUtil.isEmpty(memberLabelRecordQO.getLabelSettingGuid()) ||
            StringUtil.isEmpty(memberLabelRecordQO.getMemberLabelGuid()) ||
                    memberLabelRecordQO.getIsConnection() == NumberConstant.NUMBER_0) {
            log.warn("跳过创建标签记录，关键字段为空: memberInfoGuid={}, labelSettingGuid={}, memberLabelGuid={}", 
                memberLabelRecordQO.getMemberInfoGuid(), 
                memberLabelRecordQO.getLabelSettingGuid(), 
                memberLabelRecordQO.getMemberLabelGuid());
            return;
        }
        
        HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabelRecord.class.getSimpleName()))
                .setConnectionTime(memberLabelRecordQO.getConnectionTime())
                .setConnectionType(memberLabelRecordQO.getConnectionType())
                .setIsConnection(BooleanEnum.TRUE.getCode())
                .setOperSubjectGuid(memberLabelRecordQO.getOperSubjectGuid())
                .setMemberInfoGuid(memberLabelRecordQO.getMemberInfoGuid())
                .setLabelType(memberLabelRecordQO.getLabelType())
                .setLabelSettingGuid(memberLabelRecordQO.getLabelSettingGuid())
                .setLabelName(memberLabelRecordQO.getLabelName())
                .setMemberLabelGuid(memberLabelRecordQO.getMemberLabelGuid());
        if (memberLabelRecordQO.getConnectionType() == LabelConnectTypeEnum.MANUAL.getCode()) {
            hsaMemberLabelRecord.setConnectionOperator(StringUtil.isNotEmpty(memberLabelRecordQO.getConnectionOperator()) ?
                    memberLabelRecordQO.getConnectionOperator() : StringUtil.isNotEmpty(headerUserInfo.getUserName()) ?
                    headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel() : null);
        }
        saveMemberLabelRecordList.add(hsaMemberLabelRecord);
    }

    private static void addUpdateMemberLabelRecordList(HeaderUserInfo headerUserInfo, List<HsaMemberLabelRecord> updateMemberLabelRecordList, MemberLabelRecordDTO memberLabelRecordQO, HsaMemberLabelRecord labelRecord) {
        if (memberLabelRecordQO.getIsConnection() == BooleanEnum.FALSE.getCode()) {
            labelRecord.setIsConnection(BooleanEnum.FALSE.getCode())
                    .setCancelConnectionTime(memberLabelRecordQO.getCancelConnectionTime())
                    .setCancelConnectionType(memberLabelRecordQO.getCancelConnectionType());
            if (memberLabelRecordQO.getCancelConnectionType() == LabelCancelConnectTypeEnum.MANUAL.getCode()) {
                labelRecord.setCancelConnectionOperator(StringUtil.isNotEmpty(memberLabelRecordQO.getCancelConnectionOperator()) ?
                        memberLabelRecordQO.getCancelConnectionOperator() : StringUtil.isNotEmpty(headerUserInfo.getUserName()) ?
                        headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel() : null);
            }
            updateMemberLabelRecordList.add(labelRecord);
        } else {
            if (!labelRecord.getConnectionType().equals(memberLabelRecordQO.getConnectionType())) {
                labelRecord.setConnectionType(memberLabelRecordQO.getConnectionType());
                updateMemberLabelRecordList.add(labelRecord);
            }
        }
    }
}
