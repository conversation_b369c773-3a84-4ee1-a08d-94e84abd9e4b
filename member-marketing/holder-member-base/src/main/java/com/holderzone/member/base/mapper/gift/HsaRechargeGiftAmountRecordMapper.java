package com.holderzone.member.base.mapper.gift;

import com.holderzone.member.base.entity.recharge.HsaRechargeGiftAmountRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.gift.QueryGiftAmountRecordPage;
import com.holderzone.member.common.vo.gift.CalculateRechargeGiftRecordVO;
import com.holderzone.member.common.vo.gift.RechargeGiftAmountRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @author: rw
 * @create: 2023-06-27 14:37
 */
public interface HsaRechargeGiftAmountRecordMapper extends HolderBaseMapper<HsaRechargeGiftAmountRecord> {


    List<RechargeGiftAmountRecordVO> queryGiftAmountRecordPage(@Param("request") QueryGiftAmountRecordPage request);

    void updateRechargeStatus(@Param("operSubjectGuid") String operSubjectGuid,
                              @Param("memberFundingDetailGuid") String memberFundingDetailGuid,
                              @Param("rechargeStatus") Integer rechargeStatus);
    CalculateRechargeGiftRecordVO sumRechargeAmount(@Param("request") QueryGiftAmountRecordPage request);

    Integer countMemberPhone(@Param("request") QueryGiftAmountRecordPage request);

    Integer countMemberCard(@Param("request") QueryGiftAmountRecordPage request);

    Integer countRechargeOrder(@Param("request") QueryGiftAmountRecordPage request);
    String getTop1Store(@Param("request") QueryGiftAmountRecordPage request);

    Integer queryGiftAmountRecordNum(@Param("request") QueryGiftAmountRecordPage request);
}
