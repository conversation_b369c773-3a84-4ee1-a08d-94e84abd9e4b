package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.card.HsaMemberInfoCardTotal;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardTotalMapper;
import com.holderzone.member.base.service.card.IHsaMemberInfoCardTotalService;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;


/**
 * <p>
 * 会员卡余额历史 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Service
@AllArgsConstructor
@Slf4j
public class HsaMemberInfoCardTotalServiceImpl extends HolderBaseServiceImpl<HsaMemberInfoCardTotalMapper, HsaMemberInfoCardTotal> implements IHsaMemberInfoCardTotalService {

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final HsaMemberInfoCardTotalMapper hsaMemberInfoCardTotalMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    @RedissonLock(lockName = "SAVE_TODAY_CARD_AMOUNT", tryLock = true, leaseTime = 30)
    @Override
    public void saveTodayCardAmount(){
        //记录时间算昨天的
        final String recordDate = LocalDate.now().plusDays(-1).toString();
        //每天写入一次
        final Integer count = hsaMemberInfoCardTotalMapper.selectCount(new LambdaQueryWrapper<HsaMemberInfoCardTotal>().eq(HsaMemberInfoCardTotal::getRecordDate, recordDate));
        if(count > 0){
            log.info("每日卡余额统计：重复调用，卡余额汇总记录，{}",recordDate);
            return;
        }
        final List<HsaMemberInfoCardTotal> histories = hsaMemberInfoCardMapper.listCardAmountByDay();
        if(CollectionUtil.isEmpty(histories)){
            return;
        }
        final List<String> guids = guidGeneratorUtil.getGuids(HsaMemberInfoCardTotal.class.getSimpleName(), histories.size());
        for (int i = 0; i < guids.size(); i++) {
            final HsaMemberInfoCardTotal total = histories.get(i);
            total.setGuid(guids.get(i));
            total.setRecordDate(recordDate);
        }
        saveBatch(histories);
    }
}
