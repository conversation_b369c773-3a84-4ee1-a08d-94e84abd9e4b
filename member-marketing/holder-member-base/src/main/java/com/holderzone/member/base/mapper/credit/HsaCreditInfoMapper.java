package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.credit.CreditInfoListQO;
import com.holderzone.member.common.vo.credit.CreditInfoVO;
import com.holderzone.member.common.vo.credit.ReconciliationStatusVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: 挂账信息mapper
 * @author: pan tao
 * @create: 2022-02-09 11:41
 */
public interface HsaCreditInfoMapper extends HolderBaseMapper<HsaCreditInfo> {


    List<CreditInfoVO> queryCreditInfoList(@Param("request") CreditInfoListQO request);


    List<CreditInfoVO> queryMemberCreditList(@Param("request") CreditInfoListQO request);

    Integer queryCountMemberCredit(@Param("operSubjectGuid") String operSubjectGuid,@Param("memberGuid") String memberGuid,
                                   @Param("status") Integer status);

    Integer queryValidCreditNumber(@Param("operSubjectGuid") String operSubjectGuid,@Param("memberInfoGuid") String memberInfoGuid,
                                   @Param("valid") Integer valid);

    List<String> findPhoneByCreditGuid(@Param("creditGuid") String creditGuid);

    /**
     * 通过手机号查询，结算单状态列表
     * @param operSubjectGuid 运营主体
     * @param phone 手机号
     * @return 操作结果
     */
    List<ReconciliationStatusVO> queryClearingStatement(@Param("operSubjectGuid") String operSubjectGuid, @Param("phone")String phone);
}
