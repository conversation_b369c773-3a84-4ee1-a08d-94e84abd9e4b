package com.holderzone.member.base.entity.system;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 欢迎语
 *
 * <AUTHOR>
 * @date 2023/4/19
 **/
@Data
public class HsaWelcomeMessage implements Serializable {

    private static final long serialVersionUID = 8707646243382390264L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 欢迎语
     */
    private String welcomeMessage;
}
