package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaCardInfoMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.card.*;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.MemberExcelService;
import com.holderzone.member.base.transform.card.HsaMemberInfoCardTransform;
import com.holderzone.member.base.transform.card.HsaPhysicalCardTransform;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.*;
import com.holderzone.member.common.dto.member.MemberCardDTO;
import com.holderzone.member.common.dto.member.MemberRelationLabelDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.member.MemberListExcelQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.RandomNumUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.TransactionUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.excel.ExcelResultVO;
import com.holderzone.member.common.vo.member.MemberInfoExcelVO;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/31
 */
@Service
@AllArgsConstructor
@Slf4j
public class MemberCardOperationServiceImpl implements MemberCardOperationService {

    private final HsaOperationMemberInfoMapper memberInfoMapper;

    private final MemberExcelService memberExcelService;

    private final SerialNumberBuilder serialNumberBuilder;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaMemberInfoCardService hsaMemberInfoCardService;

    private final HsaPhysicalCardCreateRecordService physicalCardCreateRecordService;

    private final HsaPhysicalCardService physicalCardService;

    private final HsaElectronicCardService electronicCardService;

    public Executor memberBaseThreadExecutor;

    private final TransactionUtil transactionUtil;

    private final HsaCardOpenRuleService cardOpenRuleService;

    private final HsaCardInfoMapper hsaCardInfoMapper;

    private final HsaLabelSettingService hsaLabelSettingService;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private final HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Lazy
    @Autowired
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Resource
    private SystemRoleHelper systemRoleHelper;


    @Override
    public ExcelResultVO importMemberInfo(MultipartFile file, List<String> phoneList, String cardGuid) {
        //判断已有电话号码
        if (CollectionUtil.isEmpty(phoneList)) {
            MemberListExcelQO qo = new MemberListExcelQO();
            qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            qo.setCardGuidList(Collections.singletonList(cardGuid));
            qo.setPhysicalHas(Boolean.TRUE);
            List<MemberInfoExcelVO> hasMemberInfo = memberInfoMapper.findExcelMemberInfo(qo);
            phoneList = hasMemberInfo.stream().map(MemberInfoExcelVO::getPhoneNum).collect(Collectors.toList());
        }
        return memberExcelService.importMemberInfo(file, phoneList, null);
    }

    @Override
    public PageResult listMemberPage(MemberListExcelQO qo) {
        //查询当前的会员绑定实体卡状况
        if (ObjectUtil.isNull(qo.getCurrentCardGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_GUID_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        //设置运营主体
        qo.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<MemberInfoExcelVO> list = memberInfoMapper.findPageExcelMemberInfo(qo);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> memberGuids = list.stream().map(MemberInfoExcelVO::getMemberGuid).distinct().collect(Collectors.toList());
            // 会员卡
            List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
            Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid, MemberCardDTO::getMemberCard));
            // 会员标签
            List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
            Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid, MemberRelationLabelDTO::getLabelName));
            for (MemberInfoExcelVO memberInfo : list) {
                memberInfo.setMemberCard(allMemberMap.get(memberInfo.getMemberGuid()));
                memberInfo.setMemberLabel(allLabelMap.get(memberInfo.getMemberGuid()));
            }
        }
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    @RedissonLock(lockName = "CREATE_PHYSICAL_CARD_SECRET", leaseTime = 60, tryLock = true)
    public CreateSecretDTO createPhysicalCardSecret(CreatePhysicalCardSecretQO qo) {
        int count = validateCreatePhysicalCard(qo);
        return handlerCreatePhysicalCardSecret(qo, count);
    }

    @Override
    public int validateCreatePhysicalCard(CreatePhysicalCardSecretQO qo) {
        //校验参数
        verifyParams(qo);
        //前端请求参数校验
        getLetter(qo);
        //校验数量
        return checkMakeCardNum(qo);
    }

    @Override
    public CreateSecretDTO handlerCreatePhysicalCardSecret(CreatePhysicalCardSecretQO qo, int count) {
        //查询基础信息
        CardInfoDetailDTO cardBaseInfo = getCardBaseInfo(qo.getCardGuid());
        //记录生成结果记录
        CreateCardResultEnum resultEnum = CreateCardResultEnum.CREATE_RESULT_SUCCESS;
        //批次号
        String recordGuid = guidGeneratorUtil.getStringGuid(MemberCardOperationService.class.getSimpleName());
        //统一时间
        LocalDateTime now = LocalDateTime.now();
        //实体卡开卡,预存余额
        ProduceSecretDTO produceSecretDTO = saveData(count, cardBaseInfo, recordGuid, qo, now);
        //保存生成结果记录
        HsaPhysicalCardCreateRecordDTO recordDTO = buildPhysicalCardCreateRecord(qo);
        recordDTO.setCreateResult(resultEnum.getCode());
        recordDTO.setGuid(recordGuid);
        if (qo.getSourceType() == 1) {
            recordDTO.setSource(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
        }
        physicalCardCreateRecordService.saveCreateRecord(recordDTO);
        //同时去开通电子卡
        qo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        if (ObjectUtil.equal(BooleanEnum.TRUE.getCode(), qo.getIssueElectronicCard())
                && CollectionUtil.isNotEmpty(qo.getMemberInfoList())) {
            memberBaseThreadExecutor.execute(() -> {
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                electronicCardService.openCardWithPhysicalCreate(qo, now, produceSecretDTO);
            });
        }
        //生成成功去更新生成数量
        memberBaseThreadExecutor.execute(() -> {
            cardOpenRuleService.updatePhysicalCreateCount(qo.getCardGuid(), qo.getCreateCount());
        });
        //刷新标签
        if (CollectionUtil.isNotEmpty(qo.getMemberInfoList())) {
            hsaLabelSettingService.refreshLabel(qo.getMemberInfoList()
                            .stream()
                            .map(CardBindMemberQO::getMemberGuid)
                            .collect(Collectors.toList()), null, BooleanEnum.FALSE.getCode(),
                    null,
                    LabelTriggerTypeEnum.IDENTITY_INFO.getCode());
        }
        return CreateSecretDTO.builder()
                .recordGuid(recordGuid)
                .resultEnum(resultEnum)
                .produceSecretDTO(produceSecretDTO)
                .cardNumber(produceSecretDTO.getCardNumber())  //生成的卡号
                .bindingCode(produceSecretDTO.getBindingCode()).build();
    }

    /**
     * 查询会员卡实体卡卡号规则
     *
     * @param qo 请求参数
     */
    private void getLetter(CreatePhysicalCardSecretQO qo) {
        HsaCardOpenRule hsaCardOpenRule = cardOpenRuleService.getOne(
                new LambdaQueryWrapper<HsaCardOpenRule>()
                        .eq(HsaCardOpenRule::getCardGuid, qo.getCardGuid())
                        .eq(HsaCardOpenRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        //如果 letter 存在并且不为空字符则为字母+数字模式：否则为纯数字模式
        Integer cardNumType = Optional.ofNullable(hsaCardOpenRule).map(HsaCardOpenRule::getCardNumType).orElse(1);
        qo.setCardNumType(cardNumType);
        String letter = Optional.ofNullable(hsaCardOpenRule).map(HsaCardOpenRule::getLetter).orElse(null);
        if (CardNumTypeEnum.LETTER_NUMBER.getCode() == cardNumType) {
            qo.setCardNumType(CardNumTypeEnum.LETTER_NUMBER.getCode());
            qo.setLetter(letter);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeOrThawMemberCard(FreezeOrThawQO qo) {
        //校验参数
        verifyFreezeOrThawParams(qo);
        //判断操作得卡
        if (ObjectUtil.equal(BooleanEnum.TRUE.getCode(), qo.getChoiceElectronicCard())) {
            electronicCardService.freezeOrThawCard(qo);
            hsaMemberInfoCardService.batchUpdateElectronicCardState(qo.getOwnGuidList(), qo.getStatus()); //更新关联表状态
        }

        //判断操作得卡
        if (ObjectUtil.equal(BooleanEnum.TRUE.getCode(), qo.getChoicePhysicalCard())) {
            if (CollectionUtil.isNotEmpty(qo.getOwnGuidList())) {
                handlerStatus(qo);  //处理实体卡-为激活状态的冻结或者解冻
            }
            physicalCardService.freezeOrThawCard(qo);
        }
    }

    /**
     * 处理实体卡-为激活状态的冻结或者解冻
     * 实体卡如果是未激活状态，点击冻结。那么状态为：  未激活->冻结状态
     * 实体卡如果时未激活->冻结状态，点击解冻。 那么状态为：未激活状态
     *
     * @param request
     */
    public void handlerStatus(FreezeOrThawQO request) {
        List<String> guids = new ArrayList<>();
        List<String> ownGuidList = request.getOwnGuidList();
        List<String> collect = ownGuidList;
        for (String guid : ownGuidList) {
            Integer cardStatus = physicalCardService.findCardStatus(guid);
            Integer status = Optional.ofNullable(cardStatus).orElse(request.getStatus());

            if (PhysicalCardStateEnum.ALREADY_FROZEN.getCode() == status ||
                    PhysicalCardStateEnum.NORMAL.getCode() == status ||
                    PhysicalCardStateEnum.HAVE_EXPIRED.getCode() == status) {
                continue;
            }

            if (PhysicalCardStateEnum.NOT_ACTIVATE.getCode() == status &&
                    BooleanEnum.FALSE.getCode() == request.getStatus()) {
                status = PhysicalCardStateEnum.TO_ALREADY_FROZEN.getCode();
            }
            if (PhysicalCardStateEnum.TO_ALREADY_FROZEN.getCode() == status &&
                    BooleanEnum.TRUE.getCode() == request.getStatus()) {
                status = PhysicalCardStateEnum.NOT_ACTIVATE.getCode();
            }
            guids.add(guid);  // 实体卡特殊状态guid

            physicalCardService.inactiveCardFreezeOrThawCard(guid, status);
            hsaMemberInfoCardService.updatePhysicalCardState(guid, status);  //更新关联表状态
        }

        if (CollectionUtil.isNotEmpty(guids)) {
            collect = ownGuidList.stream()
                    .filter(guid -> StringUtil.isNotEmpty(guid) && !guids.contains(guid))
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(collect)) {
            hsaMemberInfoCardService.batchUpdatePhysicalCardState(collect, request.getStatus());  //更新关联表状态
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPhysicalCard(BindPhysicalCardQO qo) {
        verifyBindPhysicalCardParams(qo);
        String physicalGuid = physicalCardService.bindCard(qo);
        hsaMemberInfoCardMapper.updatePhysicalCardBangding(physicalGuid, qo.getOwnGuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberCardFreezeOperation(CardFreezeOperationQO qo) {
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(qo.getOwnGuid());
        if (Objects.isNull(hsaMemberInfoCard) || Objects.isNull(hsaMemberInfoCard.getPhysicalCardGuid())) {
            throw new MemberBaseException(CardOperationExceptionEnum.PHYSICAL_CARD_NULL);
        }
        HsaPhysicalCard physicalCard = physicalCardService.queryByGuid(hsaMemberInfoCard.getPhysicalCardGuid());
        if (Objects.isNull(physicalCard)) {
            throw new MemberBaseException(CardOperationExceptionEnum.PHYSICAL_CARD_NULL);
        }
        hsaMemberInfoCard.setPhysicalCardState(qo.getType());
        hsaMemberInfoCardMapper.updateById(hsaMemberInfoCard);
        physicalCard.setCardState(qo.getType());
        physicalCardService.updateByGuid(physicalCard);

    }

    @Override
    public void openElectronicCard(String ownGuid) {
        electronicCardService.openCardByBackStage(ownGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindMemberAccount(BindMemberAccountQO qo) {
        HsaOperationMemberInfo memberInfo = memberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getPhoneNum, qo.getPhoneNum())
                .eq(HsaOperationMemberInfo::getAccountState, BooleanEnum.FALSE.getCode())
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtil.isNull(memberInfo)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_NOT_EXIST_OR_STATE_ERROR,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        BindMemberAccountDTO memberAccountDTO = new BindMemberAccountDTO();
        memberAccountDTO.setMemberInfoGuid(String.valueOf(memberInfo.getGuid()));
        memberAccountDTO.setOwnGuid(qo.getOwnGuid());
        memberAccountDTO.setPhoneNum(qo.getPhoneNum());
        memberAccountDTO.setUserName(qo.getUserName());
        memberAccountDTO.setBindUserTime(LocalDateTime.now());
        hsaMemberInfoCardService.updateMemberAccount(qo.getOwnGuid(), String.valueOf(memberInfo.getGuid()));
        physicalCardService.bindAccount(memberAccountDTO);
    }

    @Override
    public void cancelPhysicalCard(CancelPhysicalCardQO qo) {
        hsaMemberInfoCardService.cancelMemberInfoCard(qo);
    }

    public ProduceSecretDTO saveData(int count, CardInfoDetailDTO cardBaseInfo,
                                     String recordGuid, CreatePhysicalCardSecretQO qo,
                                     LocalDateTime now) {
        ProduceSecretDTO produceSecretDTO = new ProduceSecretDTO();
        produceSecretDTO.setCardName(qo.getCardName());
        produceSecretDTO.setCardStatus(cardBaseInfo.getCardStatus().toString());
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(qo.getCardGuid());
        transactionUtil.transactional(s -> {
            CardNumTypeEnum numType = CardNumTypeEnum.formatEnum(qo.getCardNumType());
            //需要存的开通会员卡信息和实体卡信息
            List<CardBindMemberQO> memberInfoGuidList = qo.getMemberInfoList();
            //已开通实体卡
            List<HsaPhysicalCard> physicalCardList = new ArrayList<>();
            //绑定会员关联表
            List<HsaMemberInfoCard> memberInfoCardList = new ArrayList<>();
            //根据创建数量去构建卡号和绑定码
            for (int i = 0; i < qo.getCreateCount(); i++) {
                HsaPhysicalCard hsaPhysicalCard = new HsaPhysicalCard();
                if (createPhysicalCardNumber(count, qo, numType, i, hsaPhysicalCard)) {
                    continue;
                }
                //实体卡guid
                String physicalCardGuid = guidGeneratorUtil.getStringGuid(HsaPhysicalCardService.class.getSimpleName());
                hsaPhysicalCard.setMakePhysicalCardRecordGuid(recordGuid);
                CardBindMemberQO memberInfo = getCardBindMemberQO(qo, now, hsaPhysicalCard, memberInfoGuidList, i, physicalCardGuid, physicalCardList);

                //构建会员卡绑定
                buildMemberInfoCard(hsaPhysicalCard, cardBaseInfo, memberInfoCardList, memberInfo, qo, physicalCardGuid, hsaCardBaseInfo);
            }
            if (CollectionUtil.isEmpty(physicalCardList)) {
                throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_ERROR);
            }
            //处理一体机开通实体卡，生成的卡号和绑定码
            cardNumHandler(physicalCardList, produceSecretDTO, qo.getSourceType());
            //保存会员关联数据
            List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardService.savePhysicalCardBind(HsaMemberInfoCardTransform.
                    INSTANCE.cardInfo2DTOs(memberInfoCardList));
            //保存实体卡数据
            physicalCardService.saveBind(HsaPhysicalCardTransform.INSTANCE.cardInfo2DTOs(physicalCardList));
            Map<String, BigDecimal> cardMoney = new HashMap<>();
            cardMoney.put(qo.getCardGuid(), qo.getCardAmount());
            //预存余额
            electronicCardService.addOpenCardBalanceRecord(hsaMemberInfoCards, cardMoney, qo.getSourceType() == 1 ? SourceTypeEnum.ADD_ONE_MACHINE.getCode() : qo.getSourceType(),
                    CardTypeEnum.CARD_TYPE_MAIN.getCode());
        });
        return produceSecretDTO;
    }

    private static CardBindMemberQO getCardBindMemberQO(CreatePhysicalCardSecretQO qo,
                                                        LocalDateTime now,
                                                        HsaPhysicalCard hsaPhysicalCard,
                                                        List<CardBindMemberQO> memberInfoGuidList,
                                                        int i,
                                                        String physicalCardGuid,
                                                        List<HsaPhysicalCard> physicalCardList) {
        hsaPhysicalCard.setCardStrategyRecordGuid(qo.getCashRecordGuid());

        hsaPhysicalCard.setCashPledge(qo.getCashPledge());
        hsaPhysicalCard.setReturnableCashPledge(qo.getReturnableCashPledge());
        hsaPhysicalCard.setCashName(qo.getCashName());
        hsaPhysicalCard.setCardGuid(qo.getCardGuid());
        hsaPhysicalCard.setGmtCreate(now);
        hsaPhysicalCard.setGmtModified(now);
        hsaPhysicalCard.setCardBindingNum(RandomNumUtil.getPhysicalBindingCode());
        hsaPhysicalCard.setIssueElectronicCard(qo.getIssueElectronicCard());

        CardBindMemberQO memberInfo = getCardBindMember(memberInfoGuidList, i);
        hsaPhysicalCard.setMemberBindingState(StringUtil.isEmpty(memberInfo.getPhoneNum()) ? BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());
        hsaPhysicalCard.setCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
        hsaPhysicalCard.setMemberInfoGuid(memberInfo.getMemberGuid());
        hsaPhysicalCard.setPhoneNum(memberInfo.getPhoneNum());
        hsaPhysicalCard.setUserName(memberInfo.getUserName());
        hsaPhysicalCard.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        hsaPhysicalCard.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaPhysicalCard.setGuid(physicalCardGuid);
        hsaPhysicalCard.setSource(qo.getSourceType());
        if (qo.getSourceType() == 1) {
            hsaPhysicalCard.setSource(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
        }
        hsaPhysicalCard.setIsDelete(BooleanEnum.FALSE.getCode());
        hsaPhysicalCard.setBindUserTime(now);
        physicalCardList.add(hsaPhysicalCard);
        return memberInfo;
    }

    private static CardBindMemberQO getCardBindMember(List<CardBindMemberQO> memberInfoGuidList, int i) {
        if (CollectionUtil.isNotEmpty(memberInfoGuidList) && memberInfoGuidList.size() > i) {
            //取出会员guid
            return memberInfoGuidList.get(i);
        }
        return new CardBindMemberQO();
    }

    private boolean createPhysicalCardNumber(int count, CreatePhysicalCardSecretQO qo, CardNumTypeEnum numType, int i, HsaPhysicalCard hsaPhysicalCard) {
        if (CardNumTypeEnum.LETTER_NUMBER == numType) {
            hsaPhysicalCard.setCardNum(serialNumberBuilder.getLetterNumLocal(count + NumberConstant.NUMBER_1 + i, qo.getLetter()));
        } else if (CardNumTypeEnum.PURE_NUMBER == numType) {
            hsaPhysicalCard.setCardNum(serialNumberBuilder.getPureNumLocal(count + NumberConstant.NUMBER_1 + i));
        } else {
            return true;
        }
        //若位数已超出则不生成
        if (hsaPhysicalCard.getCardNum().length() > NumberConstant.NUMBER_12) {
            if (qo.getSourceType() == 2) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_CEILING_RE_ELECTION.getDes(), ThreadLocalCache.getOperSubjectGuid()));
            }
            throw new MemberBaseException(String.format(CardOperationExceptionEnum.THE_CURRENT_RULE_IS_UP_TODAY.getDes(), i));
        }
        return false;
    }

    /**
     * 处理一体机开通实体卡，生成的卡号和绑定码
     *
     * @param physicalCardList 实体卡集合
     * @param produceSecretDTO 生成卡信息
     * @param sourceType       sourceType = 1  表示从一体机操作数据
     */
    private void cardNumHandler(List<HsaPhysicalCard> physicalCardList, ProduceSecretDTO produceSecretDTO, Integer sourceType) {
        if (ObjectUtil.isNull(physicalCardList) || physicalCardList.isEmpty()
                || (2 != sourceType && SourceTypeEnum.ADD_SELF_HELP.getCode() != sourceType)) {
            return;
        }
        Optional<HsaPhysicalCard> physicalCardInfo = physicalCardList.stream().findFirst();
        //获取生成的卡号和绑定码
        String cardNum = physicalCardInfo.map(HsaPhysicalCard::getCardNum).orElse("");
        String cardBindingNum = physicalCardInfo.map(HsaPhysicalCard::getCardBindingNum).orElse("");
        produceSecretDTO.setCardNumber(cardNum);
        produceSecretDTO.setBindingCode(cardBindingNum);
    }

    private HsaPhysicalCardCreateRecordDTO buildPhysicalCardCreateRecord(CreatePhysicalCardSecretQO qo) {
        Integer sourceType = qo.getSourceType();
        HsaPhysicalCardCreateRecordDTO recordDTO = new HsaPhysicalCardCreateRecordDTO();
        recordDTO.setCreateCount(qo.getCreateCount());
        List<CardBindMemberQO> memberInfoList = qo.getMemberInfoList();
        //一体机开卡特殊处理
        if (Objects.nonNull(sourceType)
                && (sourceType == 2 || sourceType == SourceTypeEnum.ADD_SELF_HELP.getCode())
                && CollUtil.isNotEmpty(memberInfoList)
        ) {
            Optional<CardBindMemberQO> cardBindMember = memberInfoList.stream().findFirst();
            String memberGuid = cardBindMember.map(CardBindMemberQO::getMemberGuid).orElse(null);
            recordDTO.setMemberBindingState(
                    StringUtil.isNotBlank(memberGuid) ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        } else {
            recordDTO.setMemberBindingState(
                    CollUtil.isNotEmpty(memberInfoList) ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        }
        recordDTO.setCardGuid(qo.getCardGuid());
        recordDTO.setSource(ThreadLocalCache.getSource());
        recordDTO.setOperatorAccount(ThreadLocalCache.getHeaderUserInfo().getTel());
        recordDTO.setOperatorName(ThreadLocalCache.getHeaderUserInfo().getUserName());
        return recordDTO;
    }

    private void buildMemberInfoCard(HsaPhysicalCard hsaPhysicalCard, CardInfoDetailDTO cardBaseInfo, List<HsaMemberInfoCard> memberInfoCardList, CardBindMemberQO memberInfo
            , CreatePhysicalCardSecretQO qo, String physicalCardGuid, HsaCardBaseInfo hsaCardBaseInfo) {
        HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setCardGuid(qo.getCardGuid());
        memberInfoCard.setMemberInfoGuid(memberInfo.getMemberGuid());
        memberInfoCard.setCardName(qo.getCardName());
        memberInfoCard.setPhysicalCardGuid(physicalCardGuid);
        memberInfoCard.setPhysicalCardState(hsaPhysicalCard.getCardState());
        memberInfoCard.setPhysicalCardActivationTime(hsaPhysicalCard.getActivationTime());
        memberInfoCard.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        memberInfoCard.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        memberInfoCard.setCardAmount(BigDecimalUtil.nonNullValue(qo.getCardAmount()));
        memberInfoCard.setApplicableAllStore(cardBaseInfo.getApplicableAllStore());
        memberInfoCard.setCardColor(cardBaseInfo.getCardColor());
        memberInfoCard.setCardImage(cardBaseInfo.getCardImage());
        memberInfoCard.setCardRechargeExplain(cardBaseInfo.getCardRechargeExplain());
        memberInfoCard.setCardEmployExplain(cardBaseInfo.getCardEmployExplain());
        memberInfoCard.setCardPayPassword(SecureUtil.md5(cardBaseInfo.getCardPayPassword()).toUpperCase()); //给初始化密码加密
        memberInfoCard.setIsPhysicalCardRetreat(cardBaseInfo.getIsPhysicalCardRetreat());
        memberInfoCard.setPhysicalCardNum(hsaPhysicalCard.getCardNum());
        memberInfoCard.setMemberPhoneNum(memberInfo.getPhoneNum());
        memberInfoCard.setCardAmount(cardBaseInfo.getCardValueMoney());

        //是否超额
        if (hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
            if (hsaCardBaseInfo.getExcessType() == 0) {
                memberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
            } else {
                memberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
            }
        }
        memberInfoCardList.add(memberInfoCard);
    }


    private CardInfoDetailDTO getCardBaseInfo(String cardGuid) {
        //去查询会员卡基础信息
        CardInfoDetailDTO cardInfoDetail = hsaCardInfoMapper.getCardInfoDetail(cardGuid);
        if (ObjectUtil.isNull(cardInfoDetail)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        //判断卡状态
        boolean canSend = ObjectUtil.equal(cardInfoDetail.getIsSupportPhysicalCard(), BooleanEnum.TRUE.getCode())
                && ObjectUtil.equal(cardInfoDetail.getCardStatus(), CardStatusEnum.ENABLE.getCode());
        if (!canSend) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.THE_MEMBER_CAN_NOT_CREATE_PHYSICAL_CARD,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        return cardInfoDetail;
    }

    private void verifyParams(CreatePhysicalCardSecretQO qo) {
        if (ObjectUtil.isNull(qo)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        if (ObjectUtil.isNull(qo.getCardGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_GUID_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        if (ObjectUtil.isNull(qo.getCreateCount())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_NULL);
        }
        if (CollectionUtil.isNotEmpty(qo.getMemberInfoList())
                && qo.getCreateCount() < qo.getMemberInfoList().size()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_LITTLE,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        if (ObjectUtil.isNull(ThreadLocalCache.getOperSubjectGuid())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
    }

    private int checkMakeCardNum(CreatePhysicalCardSecretQO qo) {
        int count;
        int maxCount;
        if (ObjectUtil.equal(CardNumTypeEnum.LETTER_NUMBER.getCode(), qo.getCardNumType())) {
            // 查询剩余可用制卡数量
            String currentDate = qo.getLetter() + serialNumberBuilder.getString();
            count = physicalCardService.getCardNumCountLike(currentDate, ThreadLocalCache.getOperSubjectGuid());
            maxCount = NumberConstant.NUMBER_9999 - count;
        } else {
            // 查询剩余可用制卡数量
            String currentDate = serialNumberBuilder.getString();
            count = physicalCardService.getCardNumCountLike(currentDate, ThreadLocalCache.getOperSubjectGuid());
            maxCount = NumberConstant.NUMBER_999999 - count;
        }
        //判断数量
        if (qo.getCreateCount() > maxCount || maxCount <= NumberConstant.NUMBER_0) {
            if (qo.getSourceType() == 2 || qo.getSourceType() == SourceTypeEnum.ADD_SELF_HELP.getCode()) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_CEILING_RE_ELECTION.getDes(),
                        ThreadLocalCache.getOperSubjectGuid()));
            }
            throw new MemberBaseException(String.format(CardOperationExceptionEnum.THE_CURRENT_RULE_IS_UP_TODAY.getDes(), maxCount));
        }
        return count;
    }

    private void verifyFreezeOrThawParams(FreezeOrThawQO qo) {
        if (ObjectUtil.isNull(qo)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        if (!CollectionUtil.isNotEmpty(qo.getOwnGuidList())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        if (ObjectUtil.isNull(qo.getStatus())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
    }

    private void verifyBindPhysicalCardParams(BindPhysicalCardQO qo) {
        if (ObjectUtil.isNull(qo)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }

        if (ObjectUtil.isNull(qo.getCardGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_GUID_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }

        if (ObjectUtil.isNull(qo.getOwnGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_WITH_MEMBER_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
    }

}
