package com.holderzone.member.base.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.grade.HsaMemberGradePayRecord;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberGradePayRecordMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.grade.IHsaMemberGradePayRecordService;
import com.holderzone.member.base.service.member.HsaMemberEducationService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.mall.order.PayStateEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.MemberGradePayRecordQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordRespVO;
import com.holderzone.member.common.vo.grade.SaveMemberGradeDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 会员等级付费记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaMemberGradePayRecordServiceImpl extends ServiceImpl<HsaMemberGradePayRecordMapper, HsaMemberGradePayRecord>
        implements IHsaMemberGradePayRecordService {

    @Resource
    private HsaMemberGradePayRecordMapper mapper;

    @Autowired
    private HsaOperationMemberInfoMapper operationMemberInfoMapper;

    private HsaMemberGradePayRecordMapper memberGradePayRecordMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private IHsaMemberGradeInfoService memberGradeInfoService;

    @Override
    public List<MemberGradePayRecordRespVO> pageRecord(MemberGradePayRecordQO gradePayRecordQO) {
        return memberGradePayRecordMapper.pageRecord(gradePayRecordQO);
    }

    @Override
    public void saveRecord(SaveMemberGradeDTO saveMemberGradeDTO, LocalDateTime time, HsaMemberGradePayRecord payRecord) {
        HsaOperationMemberInfo memberInfo = operationMemberInfoMapper.queryByGuid(saveMemberGradeDTO.getMemberInfoGuid());
        payRecord.setMemberInfoGuid(saveMemberGradeDTO.getMemberInfoGuid())
                .setChangeType(saveMemberGradeDTO.getChangeType())
                .setMemberInfoGradeGuid(saveMemberGradeDTO.getMemberInfoGradeGuid())
                .setMemberInfoGradeName(saveMemberGradeDTO.getMemberInfoGradeName())
                .setUnit(saveMemberGradeDTO.getUnit())
                .setRemark("微信支付")
                .setNum(saveMemberGradeDTO.getNum())
                .setMemberInfoName(memberInfo.getUserName())
                .setExpireTime(time)
                .setPhoneNum(memberInfo.getPhoneNum())
                .setRoleType(saveMemberGradeDTO.getRoleType() == 0 ? RoleTypeEnum.MEMBER + "" : RoleTypeEnum.MERCHANT + "");
        mapper.updateById(payRecord);
    }

    @Override
    public HsaMemberGradePayRecord checkPayRecord(String payGuid) {
        HsaMemberGradePayRecord payRecord = getByGuid(payGuid);
        if (Objects.isNull(payRecord)) {
            log.error("会员支付记录不存在payGuid：{}", payGuid);
            throw new MemberBaseException("会员支付记录不存在");
        }
        if (PayStateEnum.SUCCESS.getCode() != payRecord.getState()) {
            log.error("会员支付未完成payGuid：{}", payGuid);
            throw new MemberBaseException("会员支付未完成");
        }
        return payRecord;
    }

    @Override
    public HsaMemberGradePayRecord getByGuid(String guid) {
        return mapper.selectOne(new LambdaQueryWrapper<HsaMemberGradePayRecord>()
                .eq(HsaMemberGradePayRecord::getGuid, guid));
    }

    @Override
    public void batchAdd(MemberGradePayRecordReqVO gradePayRecordReqVO) {
        gradePayRecordReqVO.validatedSave();

        HsaMemberGradePayRecord memberGradePayRecord = new HsaMemberGradePayRecord();
        memberGradePayRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberEducationService.class.getSimpleName()));
        memberGradePayRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        HsaMemberGradeInfo memberGradeInfo = memberGradeInfoService.getOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, gradePayRecordReqVO.getMemberInfoGradeGuid())
                .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(memberGradeInfo)) {
            throw new BusinessException("未查询到等级信息");
        }
        memberGradePayRecord.setMemberInfoGradeGuid(gradePayRecordReqVO.getMemberInfoGradeGuid());
        memberGradePayRecord.setMemberInfoGradeName(memberGradeInfo.getName());
        memberGradePayRecord.setPayTime(LocalDateTime.now());
        memberGradePayRecord.setExpireTime(DateUtil.getFirstExpireTime(gradePayRecordReqVO.getNum(), gradePayRecordReqVO.getUnit()));
        memberGradePayRecord.setRoleType(gradePayRecordReqVO.getRoleType());
        memberGradePayRecord.setNum(gradePayRecordReqVO.getNum());
        memberGradePayRecord.setUnit(gradePayRecordReqVO.getUnit());
        memberGradePayRecord.setChangeType(gradePayRecordReqVO.getChangeType());
        memberGradePayRecord.setRemark(gradePayRecordReqVO.getRemark());
        memberGradePayRecord.setPayAmount(gradePayRecordReqVO.getPayAmount());
        List<HsaOperationMemberInfo> memberInfoList = operationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, gradePayRecordReqVO.getMemberGuidList()));
        if (CollectionUtils.isEmpty(memberInfoList)) {
            throw new BusinessException("未查询到会员信息");
        }
        List<HsaMemberGradePayRecord> recordList = new ArrayList<>();
        memberInfoList.forEach(memberInfo -> {
            HsaMemberGradePayRecord toSave = new HsaMemberGradePayRecord();
            BeanUtils.copyProperties(memberGradePayRecord, toSave);
            toSave.setMemberInfoGuid(memberInfo.getGuid());
            toSave.setMemberInfoName(memberInfo.getUserName());
            toSave.setPhoneNum(memberInfo.getPhoneNum());
            recordList.add(toSave);
        });
        this.saveBatch(recordList);
    }
}
