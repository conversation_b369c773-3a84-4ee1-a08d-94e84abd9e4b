package com.holderzone.member.base.service.card;

import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.card.BindMemberAccountDTO;
import com.holderzone.member.common.dto.card.HsaPhysicalCardDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.BindPhysicalCardQO;
import com.holderzone.member.common.qo.card.FreezeOrThawQO;
import com.holderzone.member.common.qo.card.MemberCardExcelQO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 实体卡服务
 * @date 2021/9/1
 */
public interface HsaPhysicalCardService extends IHolderBaseService<HsaPhysicalCard> {

    /**
     * 功能描述：保存实体卡绑定
     *
     * @param cardInfo2DTOs 实体卡信息
     * @date 2021/9/6
     */
    void saveBind(List<HsaPhysicalCardDTO> cardInfo2DTOs);

    /**
     * 功能描述：下载卡密
     *
     * @param cardGuid 会员卡guid
     * @date 2021/9/6
     */
    void uploadCardSecret(String cardGuid);


    /**
     * 功能描述：查询卡密
     *
     * @param memberCardExcelQO memberCardExcelQO
     * @date 2021/9/6
     */
    PageResult getUploadCardSecret(MemberCardExcelQO memberCardExcelQO);

    /**
     * 功能描述：冻结或者解冻
     *
     * @param qo 冻结或者解冻传递参数
     * @date 2021/9/6
     */
    void freezeOrThawCard(FreezeOrThawQO qo);

    /**
     * 功能描述：冻结或者解冻(未激活的实体卡)
     *
     * @param guid   guid
     * @param status 更新实体卡状态
     * @date 2021/9/6
     */
    void inactiveCardFreezeOrThawCard(String guid, Integer status);

    /**
     * 功能描述：绑定实体卡
     *
     * @param qo 传递参数
     * @return java.lang.Long 实体卡guid
     * @date 2021/9/6
     */
    String bindCard(BindPhysicalCardQO qo);

    /**
     * 功能描述：实体卡绑定账户
     *
     * @param memberAccountDTO 绑定参数
     * @date 2021/9/6
     */
    void bindAccount(BindMemberAccountDTO memberAccountDTO);

    /**
     * 通过memberInfoCardGuid 查询当前实体卡状态
     *
     * @param guid
     */
    Integer findCardStatus(String guid);

    Integer getCardNumCountLike(String currentDate, String operSubjectGuid);
}
