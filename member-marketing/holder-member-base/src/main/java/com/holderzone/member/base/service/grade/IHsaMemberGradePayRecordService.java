package com.holderzone.member.base.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaMemberGradePayRecord;
import com.holderzone.member.common.qo.grade.MemberGradePayRecordQO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordRespVO;
import com.holderzone.member.common.vo.grade.SaveMemberGradeDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员等级付费记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IHsaMemberGradePayRecordService extends IService<HsaMemberGradePayRecord> {

    List<MemberGradePayRecordRespVO> pageRecord(MemberGradePayRecordQO gradePayRecordQO);

    void saveRecord(SaveMemberGradeDTO saveMemberGradeDTO, LocalDateTime time,HsaMemberGradePayRecord payRecord);

    HsaMemberGradePayRecord checkPayRecord(String payGuid);

    HsaMemberGradePayRecord getByGuid(String guid);

    void batchAdd(MemberGradePayRecordReqVO gradePayRecordReqVO);
}
