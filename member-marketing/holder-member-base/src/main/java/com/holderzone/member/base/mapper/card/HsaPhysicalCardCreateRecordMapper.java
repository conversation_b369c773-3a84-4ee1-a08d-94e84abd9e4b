package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaPhysicalCardCreateRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.CreateRecordQO;
import com.holderzone.member.common.vo.card.CreateRecordVO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HsaPhysicalCardCreateRecordMapper extends HolderBaseMapper<HsaPhysicalCardCreateRecord> {


    List<CreateRecordVO> list(CreateRecordQO qo);
}
