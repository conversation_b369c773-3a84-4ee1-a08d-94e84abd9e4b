package com.holderzone.member.base.entity.member;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员证件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberCertificateInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    private String operationMemberInfoGuid;

    /**
     * 证件名
     */
    private String certificateName;

    /**
     * 证件号码
     */
    private String certificateNum;

    /**
     * 证件类型，0身份证，1军人证，2护照，3港澳台通行证，4其它
     */
    private Integer certificateType;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
