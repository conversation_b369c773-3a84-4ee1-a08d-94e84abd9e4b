package com.holderzone.member.base.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaMemberGradeRelation;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.vo.grade.MemberGradeCountVO;
import com.holderzone.member.common.vo.grade.MemberGradeDTO;
import com.holderzone.member.common.vo.grade.MemberNewGradeDTO;
import com.holderzone.member.common.vo.grade.SaveMemberGradeDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员和等级关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface IHsaMemberGradeRelationService extends IService<HsaMemberGradeRelation> {

    void batchSaveMember(MemberGradeRelationDTO relationDTO);

    /**
     * 获取用户当前等级
     *
     * @param memberGuid memberGuid
     * @return
     */
    MemberGradeDTO getUserCurrentGrade(String memberGuid, Integer roleType);

    List<MemberGradeCountVO> queryMemberGradeCount(List<String> memberGradeInfoGuids, List<String> memberGuidList);

    /**
     * 获取已开通的所有等级
     *
     * @param memberGuid memberGuid
     * @param roleType   roleType
     * @return
     */
    List<MemberGradeDTO> getUserGradeAll(String memberGuid, Integer roleType);

    /**
     * 用户是否存在此等级
     *
     * @param memberGuid
     * @param grade
     * @return
     */
    boolean checkUserExistGrade(String memberGuid, String grade, Integer roleType);


    /**
     * 小程序等级
     *
     * @param saveMemberGradeDTO
     */
    void saveUserGrade(LocalDateTime expireTime, SaveMemberGradeDTO saveMemberGradeDTO);

    /**
     * 修改等级到期时间
     *
     * @param saveMemberGradeDTO
     */
    LocalDateTime updateExpiration(SaveMemberGradeDTO saveMemberGradeDTO);

    /**
     * 获取最新等级
     *
     * @return MemberNewGradeDTO
     */
    List<MemberNewGradeDTO> getNewCurrentGradeList(List<String> memberGuid);


    /**
     * 获取最新等级图标
     *
     * @return MemberNewGradeDTO
     */
    List<MemberNewGradeDTO> getNewCurrentGradeIconList(List<String> memberGuid);

    /**
     * 获取商家等级关联数据
     *
     * @param guid
     * @return
     */
    List<String> getMerchantMember(String guid);

    Map<String, List<String>> queryMemberGradeIcon(List<String> memberGuidList);
}
