package com.holderzone.member.base.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.equities.HsaEquitiesInfo;
import com.holderzone.member.base.entity.equities.HsaEquitiesInit;
import com.holderzone.member.base.service.equities.IHsaEquitiesInfoService;
import com.holderzone.member.base.service.equities.IHsaEquitiesInitService;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.module.rights.EquitiesInitEnum;
import com.holderzone.member.common.qo.equities.ADDEquitiesInfoQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 权益中心初始化
 *
 * <AUTHOR>
 * @date 2024/1/10
 * @since 1.8
 */
@Component
@Slf4j
public class EquitiesInitManage {

    /**
     * 权益中心Service
     */
    @Resource
    private IHsaEquitiesInfoService hsaEquitiesInfoService;

    /**
     * 权益初始化
     */
    @Resource
    private IHsaEquitiesInitService hsaEquitiesInitService;

    /**
     * 生成guid
     */
    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    /**
     * redis
     */
    @Resource
    private RedissonClient redissonClient;


    /**
     * 按企业初始化权益
     * 并且启用
     * 创建主体时调用
     */
    @Transactional(rollbackFor = Exception.class)
    public void init(String enterpriseGuid) {

        //todo 创建主体调用，有企业吗？
        if (StringUtil.isEmpty(enterpriseGuid)) {
            //无企业
            log.error("企业不存在，未初始化权益中心");
            return;
        }

        //权益初始化
        final String key = RedisKeyConstant.EQUITIES_INIT + enterpriseGuid;
        //释放数据操作
        RLock lock = redissonClient.getLock(key);
        try {
            if (!lock.tryLock(30, 30, TimeUnit.SECONDS)) {
                throw new MemberSettlementException("初始化默认权益超时");
            }

            //按企业查询,只初始化一次
            HsaEquitiesInit equitiesInit = hsaEquitiesInitService.getOne(new LambdaQueryWrapper<HsaEquitiesInit>()
                    .eq(HsaEquitiesInit::getEnterpriseGuid, enterpriseGuid));
            if (equitiesInit != null && !Objects.equals(equitiesInit.getIsInit(), 0)) {
                log.info("enterpriseGuid:{},默认权益已初始化过", enterpriseGuid);
                return;
            }
            
            log.info("enterpriseGuid:{},开始初始化默认权益", enterpriseGuid);

            final EquitiesInitEnum[] values = EquitiesInitEnum.values();
            for (EquitiesInitEnum value : values) {

                //静态参数
                final ADDEquitiesInfoQO equitiesInfoQo = value.getEquitiesInfoQo();
                //动态参数
                equitiesInfoQo.getHsaEquitiesInfo().setEnterpriseGuid(enterpriseGuid);
                hsaEquitiesInfoService.saveOrUpdateEquitiesInfo(equitiesInfoQo);
            }

            //初始化标记
            saveInitRecord(enterpriseGuid, equitiesInit);

            log.info("enterpriseGuid:{},初始化默认权益完成", enterpriseGuid);
        } catch (Exception e) {
            log.error("enterpriseGuid:{},初始化默认权益失败：{}", enterpriseGuid, e.getMessage());

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 保存初始化标记
     *
     * @param enterpriseGuid 企业
     * @param equitiesInit   初始化记录
     */
    private void saveInitRecord(String enterpriseGuid, HsaEquitiesInit equitiesInit) {
        if (equitiesInit == null) {
            equitiesInit = new HsaEquitiesInit();
            final String guid = guidGeneratorUtil.getStringGuid(equitiesInit.getClass().getSimpleName());
            equitiesInit.setGuid(guid);
            equitiesInit.setEnterpriseGuid(enterpriseGuid);
        }
        equitiesInit.setIsInit(BooleanEnum.TRUE.getCode());
        hsaEquitiesInitService.saveOrUpdate(equitiesInit);
    }


    @Transactional(rollbackFor = Exception.class)
    public void handleInit(List<String> enterpriseGuidList) {
        if (CollectionUtils.isEmpty(enterpriseGuidList)) {
            log.error("企业为空直接返回");
            return;
        }

        // 按企业查询
        List<HsaEquitiesInit> equitiesInitList = hsaEquitiesInitService.list(new LambdaQueryWrapper<HsaEquitiesInit>()
                .in(HsaEquitiesInit::getEnterpriseGuid, enterpriseGuidList)
        );
        if (CollectionUtils.isEmpty(equitiesInitList)) {
            log.info("[企业未初始化过]enterpriseGuidList={}", enterpriseGuidList);
            return;
        }

        equitiesInitList.forEach(equitiesInit -> {
            String enterpriseGuid = equitiesInit.getEnterpriseGuid();
            //权益初始化
            final String key = RedisKeyConstant.EQUITIES_INIT + enterpriseGuid;
            //释放数据操作
            RLock lock = redissonClient.getLock(key);
            try {
                if (!lock.tryLock(30, 30, TimeUnit.SECONDS)) {
                    throw new MemberSettlementException("初始化默认权益超时");
                }

                log.info("[开始手动初始化默认权益]enterpriseGuid={}", enterpriseGuid);

                final EquitiesInitEnum[] values = EquitiesInitEnum.values();
                for (EquitiesInitEnum value : values) {

                    //静态参数
                    final ADDEquitiesInfoQO equitiesInfoQo = value.getEquitiesInfoQo();
                    //动态参数
                    equitiesInfoQo.getHsaEquitiesInfo().setEnterpriseGuid(enterpriseGuid);
                    hsaEquitiesInfoService.saveOrUpdateEquitiesInfo(equitiesInfoQo);
                }
                log.info("[手动初始化默认权益完成]enterpriseGuid={}", enterpriseGuid);
            } catch (Exception e) {
                log.error("enterpriseGuid:{},手动初始化默认权益失败：{}", enterpriseGuid, e.getMessage());

            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        });

    }

    /**
     * 增量初始化默认权益
     * @param enterpriseGuidList 企业id列表
     */
    public void incrementHandleInit(List<String> enterpriseGuidList) {
        if (CollectionUtils.isEmpty(enterpriseGuidList)) {
            log.error("企业为空直接返回");
            return;
        }

        // 查询所有企业的权益信息
        List<HsaEquitiesInfo> equitiesInfos = hsaEquitiesInfoService.list(new LambdaQueryWrapper<HsaEquitiesInfo>()
                                                                                  .in(HsaEquitiesInfo::getEnterpriseGuid, enterpriseGuidList)
                                                                                  .eq(HsaEquitiesInfo::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        // 按企业分组
        Map<String, List<HsaEquitiesInfo>> equitiesInfoMap = equitiesInfos.stream().collect(Collectors.groupingBy(HsaEquitiesInfo::getEnterpriseGuid));

        for (String enterpriseGuid : enterpriseGuidList) {
            List<HsaEquitiesInfo> infoList = equitiesInfoMap.getOrDefault(enterpriseGuid, Collections.emptyList());
            // 获取当前企业已存在的权益名称
            List<String> existNames = infoList.stream()
                                              .map(HsaEquitiesInfo::getEquitiesName)
                                              .filter(Objects::nonNull).collect(Collectors.toList());

            // 遍历所有默认权益枚举，缺少的名称则初始化
            for (EquitiesInitEnum e : EquitiesInitEnum.values()) {
                String defaultName = e.getDes();
                if (!existNames.contains(defaultName)) {
                    ADDEquitiesInfoQO addEquitiesInfoQO = e.getEquitiesInfoQo();
                    if (addEquitiesInfoQO.getHsaEquitiesInfo() != null) {
                        addEquitiesInfoQO.getHsaEquitiesInfo().setEnterpriseGuid(enterpriseGuid);
                        hsaEquitiesInfoService.saveOrUpdateEquitiesInfo(addEquitiesInfoQO);
                        log.info("企业 {} 增量初始化权益 name: {}", enterpriseGuid, defaultName);
                    }
                }
            }
        }
    }
}

