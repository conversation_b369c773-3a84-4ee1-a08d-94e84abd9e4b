package com.holderzone.member.base.service.credit;

import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.RequestMemberCardPayVO;
import com.holderzone.member.common.qo.credit.CreditInfoListQO;
import com.holderzone.member.common.qo.credit.CreditInfoQO;
import com.holderzone.member.common.qo.credit.OperationCreditQO;
import com.holderzone.member.common.vo.credit.*;
import com.holderzone.member.common.vo.excel.ExcelResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: member-marketing
 * @description: 挂账server
 * @author: pan tao
 * @create: 2022-02-09 11:43
 */
public interface HsaCreditInfoService extends IHolderBaseService<HsaCreditInfo> {

    /**
     * 新增、编辑挂账
     *
     * @param creditInfoQO 新增挂账请求qo
     * @return 操作结果
     */
    boolean saveOrUpdateCredit(CreditInfoQO creditInfoQO);

    /**
     * 查询挂账详情
     *
     * @param creditGuid 挂账guid
     * @return 查询结果
     */
    CreditInfoDetailVO queryCreditDetail(String creditGuid);

    /**
     * 查询挂账列表
     *
     * @param request 挂账列表请求qo
     * @return 查询结果
     */
    PageResult queryCreditInfoList(CreditInfoListQO request);

    /**
     * 会员账户查询挂账列表
     *
     * @param request 挂账列表请求qo
     * @return 查询结果
     */
    PageResult queryMemberCreditList(CreditInfoListQO request);

    /**
     * 快速导入挂账使用人
     *
     * @param file       附件
     * @param creditGuid 挂账guid
     * @return 操作结果
     */
    ExcelResultVO importCreditRelationMember(MultipartFile file, String creditGuid);

    /**
     * 查询挂账钱包记录
     *
     * @param guid 挂账guid
     * @return 查询结果
     */
    List<CreditWalletLogVO> queryCreditWalletLog(String guid);

    /**
     * 操作挂账人
     *
     * @param type           类型 0：启用 1：禁用
     * @param creditInfoGuid 挂账账户guid
     * @param memberGuid     用户guid
     * @return 操作结果
     */
    boolean operationCreditUser(int type, String creditInfoGuid, String memberGuid);

    /**
     * 小程序查询挂账账户列表
     *
     * @param creditInfoListQO 挂账列表请求qo
     * @return 查询结果
     */
    AppletsCreditVO queryAppletsCreditAccount(CreditInfoListQO creditInfoListQO);

    /**
     * 一体机查询挂账账户列表
     *
     * @param creditInfoListQO 挂账列表请求qo
     * @return 查询结果
     */
    TerminalCreditVO getTerminalCreditAccount(CreditInfoListQO creditInfoListQO);

    /**
     * 设置剩余挂账余额
     *
     * @param singlePersonUpperLimit 单人挂账上限
     * @param hsaCreditInfoGuid      挂账信息guid
     * @param creditLimitedAmount    挂账上限金额
     * @param totalCredit            累计挂账
     * @param creditLimitedSet       挂账上限设置 0:不设置上限 1：设置
     * @return 操作结果
     */
    BigDecimal setResidueCreditAmount(BigDecimal singlePersonUpperLimit, String hsaCreditInfoGuid,
                                      BigDecimal creditLimitedAmount, BigDecimal totalCredit, Integer creditLimitedSet);

    /**
     * 操作挂账账户
     *
     * @param operationCreditQO 操作挂账账户请求qo
     */
    boolean operationCredit(OperationCreditQO operationCreditQO);

    /**
     * 查询挂账账户名称
     *
     * @return 挂账账户名称
     */
    List<CreditNameVO> queryCreditName();


    /**
     * 获取剩余金额
     *
     * @param hsaCreditInfoGuid   挂账信息guid
     * @param creditLimitedAmount 挂账上限金额
     * @param creditLimitedSet    挂账上限设置 0:不设置上限 1：设置
     * @return 操作结果
     */
    BigDecimal getRemainingAmount(String hsaCreditInfoGuid, BigDecimal creditLimitedAmount, Integer creditLimitedSet);


    /**
     * 处理挂账支付
     * @param request
     * @param hsaOperationMemberInfo
     */
    void handleCreditBusiness(RequestMemberCardPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo);

}
