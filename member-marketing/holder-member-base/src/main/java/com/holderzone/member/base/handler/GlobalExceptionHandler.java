package com.holderzone.member.base.handler;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberLabelExceptionEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @description 全局异常拦截
 * @date 2021/8/9
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private static final String HAPPEN_BASE_EXCEPTION = "发生业务异常！原因是：{}";

    @Autowired
    private SystemRoleHelper systemRoleHelper;

    /**
     * 处理自定义的业务异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = MemberBaseException.class)
    @ResponseBody
    public Result<Void> memberBaseExceptionHandler(MemberBaseException e) {
        logger.error(HAPPEN_BASE_EXCEPTION, e.getDes(), e);
        if (e.getDes() == null) {
            logger.error(HAPPEN_BASE_EXCEPTION, e.getMessage());
        }
        HashSet<Integer> errorCodeSet = new HashSet<>();
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CARD_PHONE_REPETITION.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CARD_PLEASE_SELECT_AGAIN.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CARD_NOT_BOUND_MEMBER.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.CREDIT_ACCOUNT_NOT_PERMISSION.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ACCOUNT_RULE_UNINITIALIZED.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ACCOUNT_NONEXISTENT.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_MEMBER_PHONE.getCode());

        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_CURRENTLY_UNAVAILABLE.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_OPEN_TYPE_ERROR.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_OPEN_INFO_UPDATED.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_OPEN_PAY_MONEY_ERROR.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_DISABLED.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_NOT_ACTIVATED.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_EXPIRED.getCode());
        errorCodeSet.add(MemberInfoCardExceptionEnum.CARD_NOT_EXISTS.getCode());
        errorCodeSet.add(MemberLabelExceptionEnum.ERROR_NOT_LABEL.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getCode());

        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CARD_FREEZE_NEW.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CARD_NOT_OPEN_NEW.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_NOT_MEMBER.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_MEMBER_NOT_REGISTER.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_CONTENT_REQUEST.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_MEMBER_ORDER_NOT_EXIT.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_MONEY_LACK.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_MEMBER_PAY_MONEY_LIMIT.getCode());
        errorCodeSet.add(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_PAST_DISABLED.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_NOT_PAST.getCode());

        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_NOT_ACTIVATE.getCode());

        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_FREEZE.getCode());

        errorCodeSet.add(CardOperationExceptionEnum.MEMBER_CARD_INVALID.getCode());
        errorCodeSet.add(MemberTerminalExceptionEnum.ERROR_CARD_NOT_STORE.getCode());

        errorCodeSet.add(MemberAccountExceptionEnum.BACK_AMOUNT_NOT_EXIST.getCode());

        errorCodeSet.add(MemberTerminalExceptionEnum.UN_SUPPORT_RECHARGE.getCode());
        if (errorCodeSet.contains(e.getCode())) {
            return Result.error(e.getCode(), systemRoleHelper.getReplace(e.getDes(), ThreadLocalCache.getOperSubjectGuid()));
        }

        return Result.error(systemRoleHelper.getReplace(e.getDes(), ThreadLocalCache.getOperSubjectGuid()));
    }

    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public Result<Void> businessExceptionExceptionHandler(ResponseStatusException e) {
        logger.error(HAPPEN_BASE_EXCEPTION, e.getMessage());
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = ResponseStatusException.class)
    @ResponseBody
    public Result<Void> respStatusExceptionHandler(ResponseStatusException exception) {
        logger.error("base发生服务异常！原因是：{}", exception.getMessage());
        return Result.error(exception.getStatus().value(), exception.getMessage());
    }

    /**
     * 处理自定义的业务异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = MemberSettlementException.class)
    @ResponseBody
    public Result<Void> settlementExceptionHandler(MemberSettlementException e) {
        logger.error("会员资产错误！原因是：{}", e.getMessage());
        return Result.error(e.getCode(), e.getDes());
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result<Void> handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = MemberAccountExceptionEnum.ERROR_DATA_REQUEST.getDes();
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        return Result.error(message);
    }

    @ResponseBody
    @ExceptionHandler(value = BindException.class)
    public Result<Void> handleValidException(BindException bindException) {
        BindingResult bindingResult = bindException.getBindingResult();
        String message = MemberAccountExceptionEnum.ERROR_DATA_REQUEST.getDes();
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        return Result.error(message);
    }

    /**
     * 系统异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = Exception.class)
    public Result<Void> handleException(Exception e) {
        logger.error("系统异常，{}", e.getMessage(),e);
        return Result.error("系统异常，请联系管理员!");
    }

}
