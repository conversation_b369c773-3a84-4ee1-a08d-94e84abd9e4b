package com.holderzone.member.base.service.card.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaStoreCardRuleMapper;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.card.business.TerCardCheckBusinessService;
import com.holderzone.member.base.service.card.business.TerMemberCardBusinessService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.RequestBaseInfoDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.CardStatusEnum;
import com.holderzone.member.common.enums.card.CardValidityPeriodTypeEnum;
import com.holderzone.member.common.enums.card.PhysicalCardStateEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.BindingPhysicalCardQO;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.TerLoginMemberCardQO;
import com.holderzone.member.common.qo.credit.CreditInfoListQO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.card.TerBaseLoginMemberCardVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.*;

@Service
@Slf4j
public class TerCardCheckBusinessServiceImpl implements TerCardCheckBusinessService {

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    @Resource
    private HsaCreditInfoService hsaCreditInfoService;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private TerMemberCardBusinessService terMemberCardBusinessService;

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    private static final String MEMBER_CARD_EXPIRED = "会员卡已过期";

    private static final String MEMBER_CARD_NOT = "会员卡不存在";

    @Override
    public void checkMemberCardLogin(HsaMemberInfoCard hsaMemberInfoCard, Integer state, TerLoginMemberCardQO terLoginMemberCardQO, Integer cardType) {
        if (Objects.isNull(hsaMemberInfoCard)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_NOT_MEMBER, ThreadLocalCache.getOperSubjectGuid()));
        }

        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        if (terLoginMemberCardQO.getType() == NUMBER_0 && hsaCardBaseInfo.getIsPreStored() == BooleanEnum.FALSE.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace("此会员卡不支持充值", ThreadLocalCache.getOperSubjectGuid()));
        }

        if (hsaCardBaseInfo.getCardStatus() == CardStatusEnum.FORBIDDEN.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace("会员卡已禁用，请联系管理员", ThreadLocalCache.getOperSubjectGuid()));
        }

        if (state == PhysicalCardStateEnum.ALREADY_FROZEN.getCode() || state == PhysicalCardStateEnum.TO_ALREADY_FROZEN.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_FREEZE_NEW, ThreadLocalCache.getOperSubjectGuid()));
        }

        if (hsaMemberInfoCard.getCardValidity() == 1 && LocalDate.now().isAfter(hsaMemberInfoCard.getCardValidityDate())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MEMBER_CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
        }

        //只判断实体卡
        if (cardType == NUMBER_0 && hsaMemberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.NOT_ACTIVATE.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NOT_OPEN_NEW, ThreadLocalCache.getOperSubjectGuid()));
        }

        //门店范围
        checkStore(terLoginMemberCardQO.getStoreGuid(), hsaMemberInfoCard);
    }

    private void checkStore(String storeGuid, HsaMemberInfoCard memberInfoCard) {
        if (memberInfoCard.getApplicableAllStore() == NUMBER_0) {
            List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberInfoCard.getGuid()));
            if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
                List<String> storeGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
                if (!storeGuidList.contains(storeGuid)) {
                    log.info("===========>无法在当前门店使用");
                    throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_NOT_STORE, ThreadLocalCache.getOperSubjectGuid()));
                }
            }
        }
    }

    @Override
    public void checkMemberInfo(HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (Objects.isNull(hsaOperationMemberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_MEMBER);
        }

        if (hsaOperationMemberInfo.getAccountState() == 1) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getCode(), systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getDes(), ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    @Override
    public String getCheckPhoneNum(TerLoginMemberCardQO terLoginMemberCardQO, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO, String loginNum) {
        dealCheck(terBaseLoginMemberCardVO, loginNum, terLoginMemberCardQO);
        return getPhoneNum(terBaseLoginMemberCardVO, loginNum);
    }

    private void dealCheck(TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                           String loginNum,
                           TerLoginMemberCardQO terLoginMemberCardQO) {
        if (loginNum.contains(QrcodeTypeEnum.CREDIT.getDes())) {
            CreditInfoListQO creditInfoListQO = new CreditInfoListQO();
            creditInfoListQO.setQrCode(loginNum);
            creditInfoListQO.setSource(NUMBER_2);
            creditInfoListQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            terBaseLoginMemberCardVO.setTerminalCreditVO(hsaCreditInfoService.getTerminalCreditAccount(creditInfoListQO));

            terLoginMemberCardQO.setTerminalCheckStatus(TerminalCheckStatusEnum.SCAN_QR_CODES_PAY.getCode());
        }
    }

    private static String getPhoneNum(TerBaseLoginMemberCardVO terBaseLoginMemberCardVO, String loginNum) {
        return Objects.nonNull(terBaseLoginMemberCardVO.getTerminalCreditVO()) ? terBaseLoginMemberCardVO.getTerminalCreditVO().getPhoneNum() : loginNum;
    }

    @Override
    public void checkMemberCard(RequestConfirmPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo, RequestBaseInfoDTO requestBaseInfo, HsaMemberInfoCard memberInfoCard) {
        if (Objects.nonNull(request.getRequestOrderInfo()) && Objects.nonNull(memberInfoCard)) {
            checkCardStore(request, hsaOperationMemberInfo, requestBaseInfo.getCardType(), memberInfoCard);
        } else {
            if (StringUtils.isNotBlank(requestBaseInfo.getMemberInfoCardGuid()) || StringUtils.isNotBlank(requestBaseInfo.getCardNum())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MEMBER_CARD_NOT, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
    }

    @Override
    public void checkCreditUserCondition(RequestConfirmPayVO request, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo, HsaOperationMemberInfo hsaOperationMemberInfo) {

        //挂账信息校验
        checkCreditInfo(request, hsaCreditUser, hsaCreditInfo, hsaOperationMemberInfo);

        //挂账门店校验
        checkCreditStore(request, hsaCreditInfo);
    }

    @Override
    public void checkCard(HsaCardBaseInfo hsaCardBaseInfo, HsaMemberInfoCard hsaMemberInfoCard) {
        if (hsaCardBaseInfo.getCardStatus() == CardStatusEnum.FORBIDDEN.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace("会员卡已禁用", ThreadLocalCache.getOperSubjectGuid()));
        }
        if (hsaCardBaseInfo.getCardValidity() == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode() && LocalDate.now().isAfter(hsaCardBaseInfo.getCardValidityDate())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MEMBER_CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (hsaMemberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.ALREADY_FROZEN.getCode()) {
            throw new MemberBaseException("实体卡已冻结，不可激活");
        }
        if (hsaMemberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.HAVE_EXPIRED.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MEMBER_CARD_EXPIRED, ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    @Override
    public void checkPassword(RequestConfirmPayVO requestConfirmPay, HsaMemberInfoCard memberInfoCard) {
        String payPassword = requestConfirmPay.getPayPassword();
        String pwd = SecureUtil.md5(payPassword).toUpperCase();
        if (StringUtils.isEmpty(payPassword)) {
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_MEMBER_PASSWORD_EMPTY);
        }
        if (!memberInfoCard.getCardPayPassword().equals(pwd)) {
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_MEMBER_PASSWORD_INCORRECT);
        }
    }

    @Override
    public void checkBindingAndMatching(HsaMemberInfoCard hsaMemberInfoCard, HsaPhysicalCard card, HsaPhysicalCard hsaPhysicalCard) {
        //校验卡是否匹配
        if (!hsaMemberInfoCard.getCardGuid().equals(card.getCardGuid())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_MATE_CARD);
        }
        //校验卡是否已绑定
        if (ObjectUtil.isNotNull(hsaPhysicalCard)) {
            if (hsaPhysicalCard.getMemberBindingState() == BooleanEnum.TRUE.getCode()) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_BINDING_CARD);
            }
            if (hsaPhysicalCard.getCardState() == PhysicalCardStateEnum.ALREADY_FROZEN.getCode() ||
                    hsaPhysicalCard.getCardState() == PhysicalCardStateEnum.TO_ALREADY_FROZEN.getCode() ||
                    hsaPhysicalCard.getCardState() == PhysicalCardStateEnum.HAVE_EXPIRED.getCode()) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FREEZE_CARD);
            }
        }
    }

    @Override
    public void checkPhysicalCard(HsaPhysicalCard card, BindingPhysicalCardQO bindingPhysicalCardQO) {
        if (ObjectUtil.isNull(card)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_NUM);
        }
        // 实体开卡信息
        if (ObjectUtil.notEqual(card.getCardBindingNum(), bindingPhysicalCardQO.getCardBindingNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_FIND_CARD);
        }
        if (card.getMemberBindingState() == BooleanEnum.TRUE.getCode()) {
            if (!bindingPhysicalCardQO.getMemberInfoGuid().equals(card.getMemberInfoGuid())) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_BINDING_CARD);
            } else {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_REPEAT);
            }
        }
        PhysicalCardStateEnum physicalCardStateEnum = PhysicalCardStateEnum.getByCode(card.getCardState());
        switch (Objects.requireNonNull(physicalCardStateEnum)) {
            case ALREADY_FROZEN:
            case TO_ALREADY_FROZEN:
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FROZEN_CARD);
            case HAVE_EXPIRED:
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_EXPIRE_CARD);
            default:
        }
    }


    private void checkCreditInfo(RequestConfirmPayVO request, HsaCreditUser hsaCreditUser, HsaCreditInfo hsaCreditInfo, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (hsaCreditInfo.getIsEnable() == EnableEnum.NOT_ENABLE.getCode() || hsaCreditUser.getIsEnable() == EnableEnum.NOT_ENABLE.getCode()) {
            log.error("===========>挂账账户已禁用，请联系管理员");
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_GENERAL_STATUS_DISABLED);
        }

        if (hsaCreditInfo.getAccountValidity() == NUMBER_1 && hsaCreditInfo.getAccountValidityDate().isBefore(LocalDateTime.now())) {
            log.error("===========>挂账账户已过期，不可使用");
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_USER_STATUS_DISABLED);
        }

        if (StringUtils.isNotBlank(request.getRequestBaseInfo().getMemberInfoGuid())
                && !request.getRequestBaseInfo().getMemberInfoGuid().equals(hsaCreditUser.getMemberInfoGuid())) {
            log.error("===========>挂账手机号与会员支付手机号不一致");
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CREDIT_USER_PHONE, ThreadLocalCache.getOperSubjectGuid()));
        }

        //判断当前会员是否禁用
        if (hsaOperationMemberInfo.getAccountState() == 1) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
        }
    }


    private void checkCreditStore(RequestConfirmPayVO request, HsaCreditInfo hsaCreditInfo) {
        if (hsaCreditInfo.getApplicableAllStore() == NUMBER_0) {
            List<HsaStoreRuleInfo> hsaStoreCardRuleList = hsaStoreRuleInfoMapper.selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                    .eq(HsaStoreRuleInfo::getType, NUMBER_0)
                    .eq(HsaStoreRuleInfo::getTypeGuid, hsaCreditInfo.getGuid()));
            if (CollUtil.isNotEmpty(hsaStoreCardRuleList) && (StringUtils.isNotBlank(request.getRequestBaseInfo().getStallGuid()) ||
                    StringUtils.isNotEmpty(request.getRequestBaseInfo().getStoreGuid()))) {
                List<String> storeGuid = hsaStoreCardRuleList.stream().map(HsaStoreRuleInfo::getStoreGuid).collect(Collectors.toList());
                if (!storeGuid.contains(StringUtils.isNotBlank(request.getRequestBaseInfo().getStallGuid()) ? request.getRequestBaseInfo().getStallGuid() : request.getRequestBaseInfo().getStoreGuid())) {
                    log.error("===========>挂账无法在当前门店使用");
                    throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_STORE);
                }
            }
        }
    }

    private void checkCardStore(RequestConfirmPayVO request,
                                HsaOperationMemberInfo hsaOperationMemberInfo,
                                Integer cardType,
                                HsaMemberInfoCard memberInfoCard) {
        //卡信息校验
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(memberInfoCard.getCardGuid());
        terMemberCardBusinessService.checkMemberInfoCard(memberInfoCard, cardType, hsaCardBaseInfo, hsaOperationMemberInfo);
        //门店范围判断
        if (memberInfoCard.getApplicableAllStore() == NUMBER_0) {
            List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberInfoCard.getGuid()));
            if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
                List<String> storeGuid = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
                if (!storeGuid.contains(StringUtils.isNotBlank(request.getRequestBaseInfo().getStallGuid()) ? request.getRequestBaseInfo().getStallGuid() : request.getRequestBaseInfo().getStoreGuid())) {
                    log.info("===========>无法在当前门店使用");
                    throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_NOT_STORE, ThreadLocalCache.getOperSubjectGuid()));
                }
            }
        }
    }
}
