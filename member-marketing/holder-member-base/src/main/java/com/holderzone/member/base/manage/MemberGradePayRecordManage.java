package com.holderzone.member.base.manage;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.base.service.grade.IHsaMemberGradePayRecordService;
import com.holderzone.member.base.transform.grade.GradeTransform;
import com.holderzone.member.base.util.EasyExcelUtils;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.grade.MemberGradePayRecordQO;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.excel.ExcelGradePayRecordVO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordRespVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年04月11日 上午10:26
 * @description 会员等级付费记录管理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class MemberGradePayRecordManage {

    @Lazy
    @Autowired
    private IHsaMemberGradePayRecordService memberGradePayRecordService;

    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(MemberGradePayRecordReqVO gradePayRecordReqVO) {
        memberGradePayRecordService.batchAdd(gradePayRecordReqVO);
    }

    /**
     * 等级付费记录分页列表
     */
    public PageResult pageRecord(MemberGradePayRecordQO gradePayRecordQO) {
        gradePayRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(gradePayRecordQO.getCurrentPage(), gradePayRecordQO.getPageSize());
        List<MemberGradePayRecordRespVO> gradePayRecordList = memberGradePayRecordService.pageRecord(gradePayRecordQO);
        if (CollUtil.isEmpty(gradePayRecordList)) {
            return PageUtil.getPageResult(new PageInfo<>());
        }
        return PageUtil.getPageResult(new PageInfo<>(gradePayRecordList));
    }

    /**
     * 导出等级付费记录
     */
    public void export(MemberGradePayRecordQO request, HttpServletResponse response) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), StringConstant.NUM_VALUE);
        List<MemberGradePayRecordRespVO> gradePayRecordList = memberGradePayRecordService.pageRecord(request);
        if (gradePayRecordList.size() > StringConstant.FIVE_THOUSAND) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL_5000);
        }
        //导出数据
        String tableName = getTableName(request.getRoleType());
        exportData(gradePayRecordList, response, tableName);
    }

    /**
     * 导出数据
     */
    private void exportData(List<MemberGradePayRecordRespVO> gradePayRecordList, HttpServletResponse response, String tableName) {
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = String.format(tableName, timeStr);
        try {
            response.setContentType(StringConstant.CONTENT_TYPE);
            response.setCharacterEncoding(StringConstant.UTF_8);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader(StringConstant.HEADER, StringConstant.ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            List<ExcelGradePayRecordVO> excelDataList = GradeTransform.INSTANCE.gradePayRecordListTOExcel(gradePayRecordList);
            EasyExcelUtils.writeExcelWithModel(response.getOutputStream(), excelDataList, ExcelGradePayRecordVO.class, "sheet1");
        } catch (IOException e) {
            log.error("等级付费记录导出错误异常!" + e.getMessage());
        }
    }

    private String getTableName(String roleType) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(roleType)) {
            sb.append(RoleTypeEnum.MEMBER.getGrade());
        } else {
            sb.append(RoleTypeEnum.getGrade(roleType));
        }
        sb.append("付费记录");
        sb.append("%s.xlsx");
        return sb.toString();
    }
}
