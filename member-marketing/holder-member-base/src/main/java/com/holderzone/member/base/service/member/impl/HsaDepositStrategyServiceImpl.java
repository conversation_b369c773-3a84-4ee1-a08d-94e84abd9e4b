package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.base.entity.member.HsaDepositStrategy;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.HsaCardOpenRuleMapper;
import com.holderzone.member.base.mapper.member.HsaDepositStrategyMapper;
import com.holderzone.member.base.service.member.HsaDepositStrategyService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.transform.member.MemberCardTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.DeleteEnum;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.qo.member.HsaDepositStrategyQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.member.HsaDepositStrategyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaDepositStrategy
 * @Author: 张林
 * @Description: 会员卡押金策瑜service
 * @Version: 1.0.1
 */
@Slf4j
@Service
public class HsaDepositStrategyServiceImpl extends HolderBaseServiceImpl<HsaDepositStrategyMapper, HsaDepositStrategy> implements HsaDepositStrategyService {

    @Resource
    private HsaDepositStrategyMapper hsaDepositStrategyMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private HsaCardOpenRuleMapper cardOpenRuleMapper;

    @Override
    public int addDepositStrategy(HsaDepositStrategyQO depositStrategyQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        //校验押金策略信息
        validate(depositStrategyQO);

        HsaDepositStrategy hsaDepositStrategy = new HsaDepositStrategy();
        BeanUtil.copyProperties(depositStrategyQO, hsaDepositStrategy);

        String guid = guidGeneratorUtil.getStringGuid(HsaOperationMemberInfo.class.getSimpleName());
        hsaDepositStrategy.setGuid(guid);
        hsaDepositStrategy.setDepositStrategyCode(guidGeneratorUtil.getCode(String.valueOf(guid)));  //押金编码
        hsaDepositStrategy.setEnterpriseGuid(Optional.ofNullable(headerUserInfo.getEnterpriseGuid()).orElse(""));  //企业名称
        hsaDepositStrategy.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid()); //运营主体guid
        hsaDepositStrategy.setIsDelete(DeleteEnum.NOT_DELETE.getCode());
        return hsaDepositStrategyMapper.insert(hsaDepositStrategy);
    }

    @Override
    public int updateDepositStrategy(HsaDepositStrategyQO depositStrategyQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //校验押金策略信息
        validate(depositStrategyQO);
        HsaDepositStrategy hsaDepositStrategy = new HsaDepositStrategy();
        BeanUtil.copyProperties(depositStrategyQO, hsaDepositStrategy);

        hsaDepositStrategy.setEnterpriseGuid(Optional.ofNullable(headerUserInfo.getEnterpriseGuid()).orElse(""));  //企业名称
        hsaDepositStrategy.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid()); //运营主体guid
        return hsaDepositStrategyMapper.updateById(hsaDepositStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByGuid(long guid) {
        final int delete = hsaDepositStrategyMapper.delete(
                new LambdaQueryWrapper<HsaDepositStrategy>()
                        .eq(HsaDepositStrategy::getGuid, guid));
        //删除其他已关联的卡
        cardOpenRuleMapper.removeOpenPhysicalCardStrategyGuid(ThreadLocalCache.getOperSubjectGuid(),guid);
        return delete;
    }

    @Override
    public HsaDepositStrategyVO getByGuid(long guid) {
        HsaDepositStrategyVO hsaDepositStrategyVO = new HsaDepositStrategyVO();
        HsaDepositStrategy hsaDepositStrategy = hsaDepositStrategyMapper.selectOne(
                new LambdaQueryWrapper<HsaDepositStrategy>()
                        .eq(HsaDepositStrategy::getGuid, guid));
        BeanUtil.copyProperties(hsaDepositStrategy, hsaDepositStrategyVO);
        return hsaDepositStrategyVO;
    }

    @Override
    public List<HsaDepositStrategyVO> list() {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaDepositStrategy> hsaDepositStrategies = hsaDepositStrategyMapper.selectList(
                new LambdaQueryWrapper<HsaDepositStrategy>()
                        .eq(HsaDepositStrategy::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .orderByDesc(HsaDepositStrategy::getGmtModified)
                        .eq(HsaDepositStrategy::getIsDelete, DeleteEnum.NOT_DELETE.getCode()));
        return MemberCardTransform.INSTANCE.hsaDepositStrategyToVo(hsaDepositStrategies);
    }

    @Override
    public void refreshLabel(List<String> memberGuid, List<HsaLabelSetting> hsaLabelSettings, int assignLabel) {
        hsaLabelSettingService.refreshLabel(memberGuid, hsaLabelSettings, assignLabel,null, LabelTriggerTypeEnum.ALL.getCode());
    }

    /**
     * 校验押金策略信息
     *
     * @param hsaDepositStrategyQO 请求参数对象
     */
    public void validate(HsaDepositStrategyQO hsaDepositStrategyQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 可退金额需≤押金金额
        if (!ObjectUtils.isEmpty(hsaDepositStrategyQO.getRefundAmount()) &&
                BigDecimalUtil.lessThan(BigDecimalUtil.nonNullValue(hsaDepositStrategyQO.getDepositAmount()), hsaDepositStrategyQO.getRefundAmount())) {
            throw new BusinessException(MemberAccountExceptionEnum.ERROR_REFUND_LESS_THAN_DEPOSIT.getDes());
        }
        // 重名校验
        HsaDepositStrategy depositStrategy = hsaDepositStrategyMapper.selectOne(
                new LambdaQueryWrapper<HsaDepositStrategy>()
                        .eq(HsaDepositStrategy::getDepositStrategyName, hsaDepositStrategyQO.getDepositStrategyName())
                        .eq(HsaDepositStrategy::getIsDelete, DeleteEnum.NOT_DELETE.getCode())
                        .eq(HsaDepositStrategy::getEnterpriseGuid, headerUserInfo.getEnterpriseGuid())
                        .eq(HsaDepositStrategy::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .ne(!ObjectUtils.isEmpty(hsaDepositStrategyQO.getGuid()), HsaDepositStrategy::getGuid, hsaDepositStrategyQO.getGuid()));
        if (!ObjectUtils.isEmpty(depositStrategy)) {
            throw new BusinessException(MemberAccountExceptionEnum.ERROR_DEPOSIT_NAME_EXISTS.getDes());
        }
    }
}
