package com.holderzone.member.base.service.send.event;

import com.holderzone.member.base.service.send.event.BinderChannel;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.PushSettlementDiscountEvent;
import com.holderzone.member.common.dto.event.PushShortMessageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;

/**
 * 推送
 *
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class ShortMessageSendEvent {

    @Resource
    private BinderChannel binderChannel;

    /**
     * 推送
     */
    public boolean send(PushShortMessageEvent request) {
        request.setUserInfo(ThreadLocalCache.getHeaderUserInfo());
        Message<PushShortMessageEvent> build = MessageBuilder.withPayload(request).build();
        return binderChannel.outputPushShortMessageSend().send(build);
    }
}
