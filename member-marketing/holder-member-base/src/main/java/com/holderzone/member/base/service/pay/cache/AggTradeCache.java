package com.holderzone.member.base.service.pay.cache;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.pay.OrderPollingDTO;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class AggTradeCache {

    private final StringRedisTemplate stringRedisTemplate;

    private final ExternalSupport externalSupport;

    private static final String AGG_ACCOUNT = "AGG_ACCOUNT:";

    private static final String PRE_PAY_SUCCESS = "PRE_PAY_SUCCESS:";

    private static final String POLLING_PAY = "POLLING_PAY:";

    public void setMallOrderCache(String key, String orderNum, Integer value) {
        stringRedisTemplate.opsForValue().set(key, orderNum, value, TimeUnit.MINUTES);
    }

    public PaySettingBaseRes getAggAccountSet(PaySettingDTO paySettingDTO) {
        if (StringUtils.isBlank(paySettingDTO.getAppId())) {
            throw new MallBaseException("小程序appId不能为空");
        }
        final String aggAccountKey = AGG_ACCOUNT +
                paySettingDTO.getAppId() + StringConstant.COLON + paySettingDTO.getOrDefaultStoreId();
        String accountSet = stringRedisTemplate.opsForValue().get(aggAccountKey);
        if (accountSet == null) {
            log.info("等级聚合支付远程获取门店配置入参：{}", JSONObject.toJSONString(paySettingDTO));
            PaySettingBaseRes setting = externalSupport.storeServer(ThreadLocalCache.getSystem()).getPaySetting(paySettingDTO);
            stringRedisTemplate.opsForValue().set(aggAccountKey, JSONObject.toJSONString(setting));
            log.info("等级聚合支付远程获取门店配置返参：{}", JSONObject.toJSONString(setting));
            stringRedisTemplate.expire(aggAccountKey, 8L, TimeUnit.HOURS);
            return setting;
        }
        return JSONObject.parseObject(accountSet, PaySettingBaseRes.class);

    }

    public void putPrePaySuccess(String orderGuid, String payGuid) {
        //预下单成功结果存入redis
        stringRedisTemplate.opsForValue().set(PRE_PAY_SUCCESS + orderGuid, payGuid);
        stringRedisTemplate.expire(PRE_PAY_SUCCESS + orderGuid, 8L, TimeUnit.HOURS);
    }

    public String getPrePaySuccess(String orderGuid) {
        //返回预下单成功单号
        return stringRedisTemplate.opsForValue().get(PRE_PAY_SUCCESS + orderGuid);
    }

    public boolean existPaySuccess(String orderGuid) {
        //判断是否预下单成功
        Boolean hasKey = stringRedisTemplate.hasKey(PRE_PAY_SUCCESS + orderGuid);
        return hasKey != null && hasKey;
    }

    public void deletePrePaySuccess(String orderGuid) {
        stringRedisTemplate.delete(PRE_PAY_SUCCESS + orderGuid);
    }

    public void putPayPolling(String orderGuid, OrderPollingDTO convertPolling, int code) {
        convertPolling.setState(code);
        //支付信息存入redis
        stringRedisTemplate.opsForValue().set(POLLING_PAY + orderGuid, JSONObject.toJSONString(convertPolling));
        stringRedisTemplate.expire(POLLING_PAY + orderGuid, 8L, TimeUnit.HOURS);
    }

    public void updatePayPolling(String orderGuid, int code) {
        String str = stringRedisTemplate.opsForValue().get(POLLING_PAY + orderGuid);
        if (str == null) {
            return;
        }
        OrderPollingDTO convertPolling = JSONObject.parseObject(str, OrderPollingDTO.class);
        putPayPolling(orderGuid, convertPolling, code);
    }

    public OrderPollingDTO getPayPolling(String orderGuid) {
        String str = stringRedisTemplate.opsForValue().get(POLLING_PAY + orderGuid);
        if (str == null) {
            return null;
        }
        return JSONObject.parseObject(str, OrderPollingDTO.class);
    }
}
