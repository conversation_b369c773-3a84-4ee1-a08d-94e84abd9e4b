package com.holderzone.member.base.entity.purchase;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.holderzone.member.common.module.base.purchase.enums.ConsumptionOrderStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动-订单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@Accessors(chain = true)
@ApiModel(value="HsaPurchaseOrder对象", description="限量抢购活动-订单")
public class HsaPurchaseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "限购活动id")
    private String purchaseId;

    @ApiModelProperty(value = "限购活动guid")
    private String purchaseGuid;

    @ApiModelProperty(value = "活动名称")
    private String purchaseName;

    @ApiModelProperty(value = "订单编号（外部订单编号）")
    private String orderNumber;

    /**
     * 订单状态：0下单 1支付 2退款
     * @see ConsumptionOrderStateEnum
     */
    @ApiModelProperty(value = "订单状态：0下单 1支付 2退款")
    private Integer orderState;

    /**
     * 下单时为空
     * 支付时通过orderNumber关联 {@link com.holderzone.member.base.entity.member.HsaMemberConsumption#guid}
     */
    @ApiModelProperty(value = "消费订单guid")
    private String consumptionOrderGuid;

    @ApiModelProperty(value = "下单手机号")
    private String phone;

    @ApiModelProperty(value = "下单用户")
    private String userName;

    @ApiModelProperty(value = "memberInfoGuid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * business
     */
    @ApiModelProperty(value = "订单业务")
    private String orderType;

    @ApiModelProperty(value = "订单时间")
    private LocalDateTime orderTime;

    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
    private Integer isDelete;


    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
