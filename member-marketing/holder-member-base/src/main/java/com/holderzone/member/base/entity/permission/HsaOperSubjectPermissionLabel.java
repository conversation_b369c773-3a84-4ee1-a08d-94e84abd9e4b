package com.holderzone.member.base.entity.permission;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 运营主体权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@Accessors(chain = true)
@ApiModel(value="HsaOperSubjectPermissionLabel对象", description="运营主体权限表")
public class HsaOperSubjectPermissionLabel  {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "岗位id和角色id")
    private String positionGuid;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "1：会员管理   2：营销中心")
    private Integer sourceType;

    @ApiModelProperty(value = "权限类型（0：岗位  1：角色）")
    private Integer isRole;

    private String labelGuid;

    private String labelName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
