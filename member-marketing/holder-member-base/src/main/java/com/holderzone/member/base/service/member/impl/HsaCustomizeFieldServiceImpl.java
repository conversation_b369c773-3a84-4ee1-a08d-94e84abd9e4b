package com.holderzone.member.base.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.member.HsaCustomizeField;
import com.holderzone.member.base.mapper.member.HsaCustomizeFieldMapper;
import com.holderzone.member.base.service.member.HsaCustomizeFieldService;
import com.holderzone.member.base.transform.member.MemberBusinessTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.business.CustomizeFieldDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaCustomizeFieldServiceImpl extends HolderBaseServiceImpl<HsaCustomizeFieldMapper, HsaCustomizeField> implements HsaCustomizeFieldService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealData(CustomizeFieldDTO customize, String memberGuid) {
        if (!customize.validate()) {
            return;
        }

        HsaCustomizeField customizeField = this.getOne(new LambdaQueryWrapper<HsaCustomizeField>()
                .eq(HsaCustomizeField::getMemberGuid, memberGuid)
                .eq(HsaCustomizeField::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaCustomizeField::getField, customize.getField()));
        if (customizeField == null) {
            HsaCustomizeField field = new HsaCustomizeField();
            field.setMemberGuid(memberGuid);
            field.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            field.setContent(customize.getContent());
            field.setField(customize.getField());
            field.setInfoFormat(customize.getInfoFormat());
            this.save(field);
            return;
        }
        customizeField.setContent(customize.getContent());
        customizeField.setInfoFormat(customize.getInfoFormat());
        this.updateById(customizeField);
    }

    @Override
    public List<CustomizeFieldDTO> listByMemberGuid(String memberGuid, String operSubjectGuid) {
        return MemberBusinessTransform.INSTANCE.toCustomizeList(this.list(new LambdaQueryWrapper<HsaCustomizeField>()
                .eq(HsaCustomizeField::getMemberGuid, memberGuid)
                .eq(HsaCustomizeField::getOperSubjectGuid, operSubjectGuid)));
    }

    @Override
    public void deleteByFiled(List<String> fileds, String operSubjectGuid) {
        if (CollectionUtils.isEmpty(fileds)) {
            return;
        }
        log.info("{} 删除了主体 {} 的自定义字段，对应数据也删除:,fileds:{}", ThreadLocalCache.getUserName(), operSubjectGuid, JacksonUtils.writeValueAsString(fileds));
        //配置字段被删，对应的数据也删
        this.remove(new LambdaQueryWrapper<HsaCustomizeField>()
                .eq(HsaCustomizeField::getOperSubjectGuid, operSubjectGuid)
                .in(HsaCustomizeField::getField, fileds)
        );
    }
}
