package com.holderzone.member.base.service.growth;

import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.growth.wechat.AppletGrowthTaskDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.growth.*;
import com.holderzone.member.common.vo.growth.AppletGrowthLevelVO;
import com.holderzone.member.common.vo.growth.AppletGrowthValueDetailVO;
import com.holderzone.member.common.vo.growth.AppletOwnLevelVO;
import com.holderzone.member.common.vo.growth.AppletsGrowthCommodityVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 小程序成长值相关
 * @date 2021/11/24
 */
public interface AppletGrowthService {

    AppletGrowthTaskDTO getMemberGrowthTaskDetail(String guid);

    /**
     * 功能描述：根据会员guid查询等级信息
     * @date 2021/11/24
     * @param qo 会员guid
     * @return com.holderzone.member.common.vo.growth.AppletOwnLevelVO
     */
    AppletOwnLevelVO getOwnLevel(AppletGrowthQO qo);

    /**
     * 功能描述：根据会员guid查询等级和成长值详细
     * @date 2021/11/25
     * @param qo 会员guid
     * @return com.holderzone.member.common.vo.growth.AppletGrowthLevelVO
     */
    AppletGrowthLevelVO getGrowthLevelDetail(AppletGrowthQO qo);

    /**
     * 功能描述：小程序成长值列表
     * @date 2021/11/25
     * @param qo 分页查询数据
     * @return java.util.List<com.holderzone.member.common.vo.growth.AppletGrowthValueDetailVO>
     */
    List<AppletGrowthValueDetailVO> listGrowthValueDetail(AppletGrowthDetailPageQO qo);

    /**
     * 功能描述：小程序获取成长值任务指定门店
     * @date 2021/12/1
     * @param qo 查询条件
     * @return java.util.List
     */
    PageResult<StoreBaseInfo> listGrowthValueStore(AppletGrowthStorePageQO qo);
    /**
     * 功能描述：小程序获取成长值任务指定商品
     * @date 2021/12/9
     * @param request 查询条件
     * @return java.util.List
     */
    AppletsGrowthCommodityVO listGrowthDesignatedGoods(AppletGrowthGoodsPageQO request);

    /**
     * 功能描述：小程序通过商品id或者策略单id查询门店信息
     * @date 2021/12/9
     * @param request 查询条件
     * @return java.util.List
     */
    PageResult listGrowthStoreByStrategyId(AppletGrowthStoreQO request);
}
