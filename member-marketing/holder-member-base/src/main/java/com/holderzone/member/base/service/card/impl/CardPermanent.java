package com.holderzone.member.base.service.card.impl;

import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.service.card.CardValidityType;
import com.holderzone.member.common.dto.card.ValidTime;
import com.holderzone.member.common.enums.card.CardValidityPeriodTypeEnum;

/**
 * 永久有效期
 */
public class CardPermanent extends ValidTime implements CardValidityType {
    @Override
    public String cardTimeHandler(HsaCardBaseInfo hsaCardBaseInfo, HsaElectronicCard hsaElectronicCard, HsaPhysicalCard hsaPhysicalCard) {
        return CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getDes();
    }
}
