package com.holderzone.member.base.entity.member;

/**
 * @Author: rw
 * @Description: 会员标签:历史操作记录
 * @Version: 1.0
 */

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberLabelRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GUID
     */
    private String guid;

    /**
     * 标签设置GUID
     */
    private String labelSettingGuid;

    /**
     * labelName
     */
    private String labelName;

    /**
     * 标签类型(0-手动标签， 1-自动标签)
     */
    private Integer labelType;

    /**
     * 关联类型 0 手动关联 1 自动关联
     */
    private Integer connectionType;

    /**
     * 关联时间
     */
    private LocalDateTime connectionTime;

    /**
     * 取关时间
     */
    private LocalDateTime cancelConnectionTime;

    /**
     * 是否关联 1 关联中 0 已取关
     */
    private Integer isConnection;

    /**
     * 关联操作员
     */
    private String connectionOperator;

    /**
     * 取关操作员
     */
    private String cancelConnectionOperator;

    /**
     * 取关类型 0 手动关 1 自动关
     */
    private Integer cancelConnectionType;

    /**
     * member_label_guid
     */
    private String memberLabelGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 运营主体会员GUID
     */
    private String memberInfoGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
