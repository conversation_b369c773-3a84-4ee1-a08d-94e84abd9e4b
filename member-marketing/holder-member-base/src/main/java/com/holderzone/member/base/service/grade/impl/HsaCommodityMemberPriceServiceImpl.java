package com.holderzone.member.base.service.grade.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.commodity.HsaCommodityInfo;
import com.holderzone.member.base.entity.grade.HsaCommodityMemberPrice;
import com.holderzone.member.base.mapper.commodity.HsaCommodityInfoMapper;
import com.holderzone.member.base.mapper.grade.HsaCommodityMemberPriceMapper;
import com.holderzone.member.base.service.commodity.HsaCommodityInfoService;
import com.holderzone.member.base.service.grade.HsaCommodityMemberPriceService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.commodity.callback.CallbackDTO;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.grade.DiscountTypeEnum;
import com.holderzone.member.common.enums.grade.ExceptionCodeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.RelationCommodityPriceQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.CommodityDeleteDataVO;
import com.holderzone.member.common.vo.grade.CommodityInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: member-marketing
 * @description: 商品会员价关联service
 * @author: pan tao
 * @create: 2022-07-07 14:39
 */
@Service
@Slf4j
public class HsaCommodityMemberPriceServiceImpl extends HolderBaseServiceImpl<HsaCommodityMemberPriceMapper, HsaCommodityMemberPrice>
        implements HsaCommodityMemberPriceService {

    @Resource
    private HsaCommodityMemberPriceMapper hsaCommodityMemberPriceMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaCommodityInfoService hsaCommodityInfoService;

    @Resource
    private HsaCommodityInfoMapper hsaCommodityInfoMapper;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private MemberCommodityFeign memberCommodityFeign;

    private static final String STORE = "store";

    private static final String STALL = "stall";

    @Override
    public PageResult queryCommodityInfo(CommodityInfoQO commodityInfoQO) {
        CrmFeignModel<CommodityInfoDTO> feignModel = externalSupport.itemServer(ThreadLocalCache.getSystem()).listCommodityHasCount(commodityInfoQO);
        PageResult pageResult = new PageResult();
        pageResult.setRecords(feignModel.getDataList());
        pageResult.setTotal(ObjectUtil.objToInt(((Map) feignModel.getData()).get("count")));
        return pageResult;
    }


    @Override
    public Integer saveCustomCommodityPrice(RelationCommodityPriceQO request) {

        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        hsaCommodityMemberPriceMapper.delete(new LambdaQueryWrapper<HsaCommodityMemberPrice>()
                .eq(HsaCommodityMemberPrice::getOperSubjectGuid, operSubjectGuid));
        if (CollectionUtils.isEmpty(request.getCommodityInfoDTOList())) {
            return 0;
        }
        List<HsaCommodityMemberPrice> hsaCommodityMemberPrices = new ArrayList<>();
        for (CommodityInfoDTO commodityInfoDTO : request.getCommodityInfoDTOList()) {
            HsaCommodityMemberPrice hsaCommodityMemberPrice = new HsaCommodityMemberPrice();
            hsaCommodityMemberPrice.setGuid(guidGeneratorUtil.getStringGuid(HsaCommodityMemberPrice.class.getSimpleName()));
            hsaCommodityMemberPrice.setOperSubjectGuid(operSubjectGuid);
            hsaCommodityMemberPrice.setChannel(commodityInfoDTO.getChannel());
            hsaCommodityMemberPrice.setCommoditySource(commodityInfoDTO.getCommodity_source());
            hsaCommodityMemberPrice.setCommodityId(commodityInfoDTO.getCommodity_id());
            hsaCommodityMemberPrice.setDiscountType(commodityInfoDTO.getDiscountType());
            hsaCommodityMemberPrice.setDiscountValue(commodityInfoDTO.getDiscountValue());
            hsaCommodityMemberPrice.setLimitedType(commodityInfoDTO.getLimitedType());
            hsaCommodityMemberPrice.setLimitedNumber(commodityInfoDTO.getLimitedNumber());
            hsaCommodityMemberPrice.setGmtCreate(LocalDateTime.now());
            hsaCommodityMemberPrice.setGmtModified(LocalDateTime.now());
            hsaCommodityMemberPrices.add(hsaCommodityMemberPrice);
        }
        this.saveBatch(hsaCommodityMemberPrices);
        return hsaCommodityMemberPrices.size();
    }

    @Override
    public List<CommodityInfoVO> queryCustomCommodityPrice() {

        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        List<HsaCommodityMemberPrice> hsaCommodityMemberPriceList = hsaCommodityMemberPriceMapper
                .selectList(new LambdaQueryWrapper<HsaCommodityMemberPrice>()
                        .eq(HsaCommodityMemberPrice::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaCommodityMemberPrice::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (CollectionUtils.isEmpty(hsaCommodityMemberPriceList)) {
            return Lists.newArrayList();
        }
        List<Long> stallId = hsaCommodityMemberPriceList.stream().filter(o -> STALL.equals(o.getCommoditySource()))
                .map(HsaCommodityMemberPrice::getCommodityId).collect(Collectors.toList());
        List<Long> storeId = hsaCommodityMemberPriceList.stream().filter(o -> STORE.equals(o.getCommoditySource()))
                .map(HsaCommodityMemberPrice::getCommodityId).collect(Collectors.toList());
        CommodityInfoQO commodityInfoQO = new CommodityInfoQO();
        commodityInfoQO.setStore_data(storeId);
        commodityInfoQO.setStall_data(stallId);
        log.info("commodityIdDTO===========>" + JSONObject.toJSONString(commodityInfoQO));
        List<CommodityInfoVO> commodityInfoVOList = Lists.newArrayList();
        List<CommodityInfoDTO> commodityInfoDTOList = externalSupport.itemServer(ThreadLocalCache.getSystem()).listCommodity(commodityInfoQO);
        for (HsaCommodityMemberPrice hsaCommodityMemberPrice : hsaCommodityMemberPriceList) {
            List<CommodityInfoDTO> list = commodityInfoDTOList
                    .stream()
                    .filter(item -> Objects.equals(item.getCommodity_source(), hsaCommodityMemberPrice.getCommoditySource())
                            &&
                            Objects.equals(item.getCommodity_id(), hsaCommodityMemberPrice.getCommodityId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            commodityInfoVOList.add(toCommodityInfoVO(list.get(0), hsaCommodityMemberPrice));
        }
        return commodityInfoVOList;
    }

    private CommodityInfoVO toCommodityInfoVO(CommodityInfoDTO commodityInfoDTO, HsaCommodityMemberPrice hsaCommodityMemberPrice) {
        CommodityInfoVO commodityInfoVO = new CommodityInfoVO();
        BeanUtils.copyProperties(commodityInfoDTO, commodityInfoVO);
        commodityInfoVO.setId(hsaCommodityMemberPrice.getId());
        commodityInfoVO.setGuid(hsaCommodityMemberPrice.getGuid());
        commodityInfoVO.setDiscountType(hsaCommodityMemberPrice.getDiscountType());
        commodityInfoVO.setDiscountValue(hsaCommodityMemberPrice.getDiscountValue());
        commodityInfoVO.setLimitedType(hsaCommodityMemberPrice.getLimitedType());
        commodityInfoVO.setLimitedNumber(hsaCommodityMemberPrice.getLimitedNumber());
        commodityInfoVO.setExceptionCode(getExceptionCode(hsaCommodityMemberPrice, commodityInfoVO));
        return commodityInfoVO;
    }

    private int getExceptionCode(HsaCommodityMemberPrice hsaCommodityMemberPrice, CommodityInfoVO commodityInfoVO) {

        if (hsaCommodityMemberPrice.getDiscountType() == DiscountTypeEnum.DISCOUNT.getCode()) {
            return ExceptionCodeEnum.NORMAL.getCode();
        }
        BigDecimal commodityPrice;
        if (commodityInfoVO.getCommodity_price().contains(StringConstant.STR_BIAS_TWO)) {
            String commodity_price = commodityInfoVO.getCommodity_price().split(StringConstant.STR_BIAS_TWO)[0];
            commodityPrice = new BigDecimal(commodity_price);
        } else {
            commodityPrice = new BigDecimal(commodityInfoVO.getCommodity_price());
        }
        if (hsaCommodityMemberPrice.getDiscountType() == DiscountTypeEnum.REDUCE.getCode()) {
            if (BigDecimalUtil.lessThan(commodityPrice, commodityInfoVO.getDiscountValue())) {
                return ExceptionCodeEnum.DISCOUNT_AMOUNT_ERROR.getCode();
            }
        }
        if (hsaCommodityMemberPrice.getDiscountType() == DiscountTypeEnum.ASSIGN_AMOUNT.getCode()) {
            if (BigDecimalUtil.lessEqual(commodityPrice, commodityInfoVO.getDiscountValue())) {
                return ExceptionCodeEnum.ASSIGN_AMOUNT_ERROR.getCode();
            }
        }
        return ExceptionCodeEnum.NORMAL.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean commodityDataSynchronization(List<CommodityInfoDTO> commodityInfoDTOList) {
        if (CollectionUtils.isEmpty(commodityInfoDTOList)) {
            return true;
        }
        try {
            CallbackDTO callbackDTO = new CallbackDTO();
            callbackDTO.setCommodityIds(commodityInfoDTOList
                    .stream()
                    .map(CommodityInfoDTO::getCommodity_id)
                    .collect(Collectors.toList()));
            memberCommodityFeign.commodityCallback(callbackDTO);
        } catch (Exception e) {
            log.error("回调商品数据失败，原因:{}", e.getMessage());
        }
        List<HsaCommodityMemberPrice> deleteCommodityData = Lists.newArrayList();
        List<HsaCommodityInfo> hsaCommodityInfos = Lists.newArrayList();
        for (CommodityInfoDTO commodityInfoDTO : commodityInfoDTOList) {
            HsaCommodityMemberPrice hsaCommodityMemberPrice = hsaCommodityMemberPriceMapper
                    .selectOne(new LambdaQueryWrapper<HsaCommodityMemberPrice>()
                            .eq(HsaCommodityMemberPrice::getCommodityId, commodityInfoDTO.getCommodity_id())
                            .eq(HsaCommodityMemberPrice::getCommoditySource, commodityInfoDTO.getCommodity_source()));
            if (Objects.isNull(hsaCommodityMemberPrice)) {
                continue;
            }
            hsaCommodityMemberPrice.setIsDelete(BooleanEnum.TRUE.getCode());
            deleteCommodityData.add(hsaCommodityMemberPrice);
            hsaCommodityInfos.add(toHsaCommodityInfo(commodityInfoDTO, hsaCommodityMemberPrice.getId()));
        }
        if (hsaCommodityInfos.size() == 0) {
            return true;
        }
        hsaCommodityInfoService.saveBatch(hsaCommodityInfos);
        this.saveOrUpdateBatch(deleteCommodityData);
        return true;
    }

    private HsaCommodityInfo toHsaCommodityInfo(CommodityInfoDTO commodityInfoDTO, Long id) {
        HsaCommodityInfo hsaCommodityInfo = new HsaCommodityInfo();
        hsaCommodityInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaCommodityInfo.class.getSimpleName()));
        hsaCommodityInfo.setCommodityId(commodityInfoDTO.getCommodity_id());
        hsaCommodityInfo.setChannel(commodityInfoDTO.getChannel());
        hsaCommodityInfo.setStoreName(commodityInfoDTO.getStore_name());
        hsaCommodityInfo.setStoreId(commodityInfoDTO.getStore_id());
        hsaCommodityInfo.setStallName(commodityInfoDTO.getStall_name());
        hsaCommodityInfo.setStallId(commodityInfoDTO.getStall_id());
        hsaCommodityInfo.setStrategyMenu(commodityInfoDTO.getStrategy_menu());
        hsaCommodityInfo.setCommodityCategory(commodityInfoDTO.getCommodity_category());
        hsaCommodityInfo.setCommodityType(commodityInfoDTO.getCommodity_type());
        hsaCommodityInfo.setCommodityCode(commodityInfoDTO.getCommodity_code());
        hsaCommodityInfo.setCommodityName(commodityInfoDTO.getCommodity_name());
        hsaCommodityInfo.setCommodityPrice(commodityInfoDTO.getCommodity_price());
        hsaCommodityInfo.setCommoditySource(commodityInfoDTO.getCommodity_source());
        hsaCommodityInfo.setStrategyName(commodityInfoDTO.getStrategy_name());
        hsaCommodityInfo.setStrategyCode(commodityInfoDTO.getStrategy_code());
        hsaCommodityInfo.setIsDelete(BooleanEnum.TRUE.getCode());
        hsaCommodityInfo.setGmtCreate(LocalDateTime.now());
        hsaCommodityInfo.setGmtModified(LocalDateTime.now());
        hsaCommodityInfo.setCommodityMemberPriceId(id);
        return hsaCommodityInfo;
    }

    @Override
    public List<StoreInfoVO> getStoreStall(StoreInfoDTO storeInfoDTO) {

        return externalSupport.storeServer(ThreadLocalCache.getSystem()).listStoreStall(storeInfoDTO);
    }


    @Override
    public List<StrategyInfoVO> getStrategyInfo(StrategyInfoDTO strategyInfoDTO) {

        return externalSupport.itemServer(ThreadLocalCache.getSystem()).listStrategyInfo(strategyInfoDTO);
    }

    @Override
    public List<CommodityDeleteDataVO> queryDeleteData() {

        return hsaCommodityMemberPriceMapper.queryDeleteData(ThreadLocalCache.getOperSubjectGuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disposeDeleteData(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        hsaCommodityInfoMapper.delete(new LambdaQueryWrapper<HsaCommodityInfo>()
                .in(HsaCommodityInfo::getCommodityMemberPriceId, ids));
        hsaCommodityMemberPriceMapper.deleteBatchIds(ids);
        return true;
    }

}
