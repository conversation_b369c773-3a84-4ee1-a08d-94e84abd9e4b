package com.holderzone.member.base.service.card;


import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.card.ECardCreateCountDTO;

import java.util.List;

/**
 * <p>
 * 会员卡开卡规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface HsaCardOpenRuleService extends IHolderBaseService<HsaCardOpenRule> {

    void updatePhysicalCreateCount(String cardGuid, Integer createCount);

    void updateElectronicCreateCount(List<ECardCreateCountDTO> countList);
}
