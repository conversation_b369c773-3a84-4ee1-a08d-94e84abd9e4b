package com.holderzone.member.base.service.grade.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.grade.HsaMemberApplyGradeRecord;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberApplyGradeRecordMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.HsaMemberApplyGradeRecordService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.excel.MemberApplyGradeRecordExcelVO;
import com.holderzone.member.common.enums.member.GradeApplyStateEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.grade.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * @program: member-marketing
 * @description: 会员等级关联信息service
 * @author: rw
 * @create: 2023-04-06 18:29
 */
@Slf4j
@Service
public class HsaMemberApplyGradeRecordServiceImpl extends ServiceImpl<HsaMemberApplyGradeRecordMapper, HsaMemberApplyGradeRecord> implements HsaMemberApplyGradeRecordService {

    @Resource
    private HsaMemberApplyGradeRecordMapper hsaMemberApplyGradeRecordMapper;

    @Resource
    private HsaOperationMemberInfoMapper operationMemberInfoMapper;

    @Resource
    @Lazy
    private IHsaMemberGradeRelationService hsaMemberGradeRelationService;

    @Resource
    private IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public boolean memberGradeUpgradeApply(GradeUpgradeApplyVO gradeUpgradeApplyVO) {
        HsaMemberApplyGradeRecord hsaMemberApplyGradeRecord;
        HsaOperationMemberInfo operationMemberInfo = operationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getGuid, gradeUpgradeApplyVO.getMemberInfoGuid()));
        HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoService.getHsaMemberGradeInfo(gradeUpgradeApplyVO.getRoleType(), gradeUpgradeApplyVO.getMemberInfoGradeGuid());
        if (Objects.isNull(hsaMemberGradeInfo)) {
            throw new MemberBaseException("等级不存在");
        }
        MemberGradeDTO memberGradeDTO = hsaMemberGradeRelationService.getUserCurrentGrade(gradeUpgradeApplyVO.getMemberInfoGuid(), gradeUpgradeApplyVO.getRoleType());

        hsaMemberApplyGradeRecord = new HsaMemberApplyGradeRecord();
        hsaMemberApplyGradeRecord.setApplyState(GradeApplyStateEnum.AWAIT_AUDIT.getCode())
                .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberApplyGradeRecord.class.getSimpleName()))
                .setMemberPhone(operationMemberInfo.getPhoneNum())
                .setMemberGuid(operationMemberInfo.getGuid())
                .setMemberName(operationMemberInfo.getUserName())
                .setApplyGradeGuid(hsaMemberGradeInfo.getGuid())
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setUnit(gradeUpgradeApplyVO.getUnit())
                .setNum(gradeUpgradeApplyVO.getNum())
                .setRoleType(gradeUpgradeApplyVO.getRoleType())
                .setApplyGradeName(hsaMemberGradeInfo.getName());
        setCurrentGrade(gradeUpgradeApplyVO, hsaMemberApplyGradeRecord, memberGradeDTO);
        return save(hsaMemberApplyGradeRecord);

    }

    private void setCurrentGrade(GradeUpgradeApplyVO gradeUpgradeApplyVO, HsaMemberApplyGradeRecord hsaMemberApplyGradeRecord, MemberGradeDTO memberGradeDTO) {
        if (Objects.nonNull(memberGradeDTO)) {
            HsaMemberGradeInfo memberGrade = hsaMemberGradeInfoService.getHsaMemberGradeInfo(gradeUpgradeApplyVO.getRoleType(), memberGradeDTO.getMemberInfoGradeGuid());
            hsaMemberApplyGradeRecord.setCurrentGradeGuid(memberGrade.getGuid())
                    .setCurrentGradeName(memberGrade.getName());
        }
    }

    @Override
    public GradeUpgradeApplyPageDTO getGradeUpgradeApplyPage(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO) {
        GradeUpgradeApplyPageDTO pageDTO = new GradeUpgradeApplyPageDTO();
        List<MemberApplyGradeRecordVO> memberApplyGradeRecordVOList = Lists.newArrayList();
        gradeUpgradeApplyPageVO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        gradeUpgradeApplyPageVO.setCurrentPage((gradeUpgradeApplyPageVO.getCurrentPage() - 1) * gradeUpgradeApplyPageVO.getPageSize());
        Integer num = hsaMemberApplyGradeRecordMapper.getRecordCount(gradeUpgradeApplyPageVO);
        if (num == 0) {
            return pageDTO.setPageResult(PageUtil.getPageResult(new PageInfo<>(memberApplyGradeRecordVOList)));
        }
        List<HsaMemberApplyGradeRecord> hsaMemberApplyGradeRecords = hsaMemberApplyGradeRecordMapper.getMemberApplyGradeRecordPage(gradeUpgradeApplyPageVO);
        getMemberApplyGradeRecordVOList(memberApplyGradeRecordVOList, hsaMemberApplyGradeRecords);
        for (MemberApplyGradeRecordVO hsaMemberApplyGradeRecord : memberApplyGradeRecordVOList) {
            MemberGradeDTO memberGradeDTO = hsaMemberGradeRelationService.getUserCurrentGrade(hsaMemberApplyGradeRecord.getMemberGuid(), gradeUpgradeApplyPageVO.getRoleType());

            if (Objects.nonNull(memberGradeDTO)) {
                HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeInfoService.getHsaMemberGradeInfo(gradeUpgradeApplyPageVO.getRoleType(),memberGradeDTO.getMemberInfoGradeGuid());
                hsaMemberApplyGradeRecord.setCurrentGradeName(memberGradeInfo.getName());
            }
        }
        pageDTO.setPageResult(PageUtil.getPageResult(new PageInfo<>(memberApplyGradeRecordVOList)));
        gradeUpgradeApplyPageVO.setApplyState(GradeApplyStateEnum.AWAIT_AUDIT.getCode());
        pageDTO.setApplyCount(getRecordCount(gradeUpgradeApplyPageVO));

        pageDTO.getPageResult().setTotal(num);
        return pageDTO;
    }

    private void getMemberApplyGradeRecordVOList(List<MemberApplyGradeRecordVO> memberApplyGradeRecordVOList, List<HsaMemberApplyGradeRecord> hsaMemberApplyGradeRecords) {
        if (CollectionUtils.isNotEmpty(hsaMemberApplyGradeRecords)) {
            for (HsaMemberApplyGradeRecord hsaMemberApplyGradeRecord : hsaMemberApplyGradeRecords) {
                MemberApplyGradeRecordVO memberApplyGradeRecordVO = new MemberApplyGradeRecordVO();
                BeanUtils.copyProperties(hsaMemberApplyGradeRecord, memberApplyGradeRecordVO);
                if (StringUtils.isEmpty(hsaMemberApplyGradeRecord.getCurrentGradeName())) {
                    memberApplyGradeRecordVO.setCurrentGradeName("无等级");
                }
                memberApplyGradeRecordVOList.add(memberApplyGradeRecordVO);
            }
        }
    }


    @Override
    public List<MemberApplyGradeRecordExcelVO> queryExportRecord(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO) {
        List<MemberApplyGradeRecordExcelVO> memberApplyGradeRecordVOList = Lists.newArrayList();
        int count = getRecordCount(gradeUpgradeApplyPageVO);
        if (count > 5000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL_5000);
        }
        List<HsaMemberApplyGradeRecord> hsaMemberApplyGradeRecords = hsaMemberApplyGradeRecordMapper.getMemberApplyGradeRecordPage(gradeUpgradeApplyPageVO);
        if (CollUtil.isEmpty(hsaMemberApplyGradeRecords)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        for (HsaMemberApplyGradeRecord hsaMemberApplyGradeRecord : hsaMemberApplyGradeRecords) {
            MemberApplyGradeRecordExcelVO recordExcelVO = new MemberApplyGradeRecordExcelVO();
            recordExcelVO.setMemberName(hsaMemberApplyGradeRecord.getMemberName())
                    .setCurrentGradeName(StringUtils.isNotEmpty(hsaMemberApplyGradeRecord.getCurrentGradeName()) ? hsaMemberApplyGradeRecord.getCurrentGradeName() : "无等级")
                    .setMemberPhone(hsaMemberApplyGradeRecord.getMemberPhone())
                    .setGmtModified(DateUtil.formatLocalDateTime(hsaMemberApplyGradeRecord.getGmtModified(),
                            DateUtil.PATTERN_DATETIME))
                    .setApplyGradeName(hsaMemberApplyGradeRecord.getApplyGradeName())
                    .setApplyState(GradeApplyStateEnum.getNameByCode(hsaMemberApplyGradeRecord.getApplyState()));
            recordExcelVO.setApplyTime(hsaMemberApplyGradeRecord.getNum() + (hsaMemberApplyGradeRecord.getUnit() == 3 ? "个月" : "年"));
            memberApplyGradeRecordVOList.add(recordExcelVO);
        }

        return memberApplyGradeRecordVOList;
    }

    @Override
    public int getRecordCount(GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO) {
        gradeUpgradeApplyPageVO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        gradeUpgradeApplyPageVO.setCurrentPage(NumberConstant.NUMBER_0);
        gradeUpgradeApplyPageVO.setPageSize(NumberConstant.NUMBER_9999);
        return hsaMemberApplyGradeRecordMapper.getRecordCount(gradeUpgradeApplyPageVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGradeUpgradeApply(UpdateGradeApplyVO updateGradeAp) {
        HsaMemberApplyGradeRecord applyGradeRecord = hsaMemberApplyGradeRecordMapper.selectOne(new LambdaQueryWrapper<HsaMemberApplyGradeRecord>()
                .eq(HsaMemberApplyGradeRecord::getGuid, updateGradeAp.getGuid()));
        applyGradeRecord.setApplyState(updateGradeAp.getApplyState());
        return this.updateById(applyGradeRecord);
    }
}
