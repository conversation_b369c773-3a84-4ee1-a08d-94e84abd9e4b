package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员认证学生信息
 */
@Data
@TableName(value = "hsa_member_certificate_student_info")
public class HsaMemberCertificateStudentInfo implements Serializable {

    private static final long serialVersionUID = -5421936981741721674L;

    private Long id;

    /**
     * GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 支付宝用户id
     */
    private String userId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学校内标
     */
    private String schoolId;

    /**
     * 会员状态，1 校园会员；0 新用户
     */
    private String memberShipStatus;

    /**
     * 身份类型，1-学生；2-教师；3-校友；4-考生；5-家长
     */
    private String type;

    /**
     * 入学时间，格式yyyy-mm-dd
     */
    private String enrollDate;

    /**
     * 学历，枚举类型：专科0，本科1，研究生2，博士3，本硕连读4，硕博连读5，本硕博连读6
     */
    private String degree;

    /**
     * 是否是学生，1是；0否
     */
    private String collegeOnlineTag;

    /**
     * 审核状态，0-审核中，2-审核不通过，3-审核通过，5-无数据
     */
    private String examineStatus;
}
