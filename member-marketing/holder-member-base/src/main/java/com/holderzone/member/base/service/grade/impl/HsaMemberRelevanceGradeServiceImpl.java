package com.holderzone.member.base.service.grade.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.grade.HsaMemberRelevanceGrade;
import com.holderzone.member.base.mapper.grade.HsaMemberRelevanceGradeMapper;
import com.holderzone.member.base.service.grade.HsaMemberRelevanceGradeService;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.vo.grade.MemberRelevanceGradeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * @program: member-marketing
 * @description: 会员等级关联信息service
 * @author: rw
 * @create: 2023-04-06 18:29
 */
@Slf4j
@Service
public class HsaMemberRelevanceGradeServiceImpl extends ServiceImpl<HsaMemberRelevanceGradeMapper, HsaMemberRelevanceGrade> implements HsaMemberRelevanceGradeService {

    private HsaMemberRelevanceGradeMapper memberRelevanceGradeMapper;

    @Override
    public MemberRelevanceGradeVO getUserCurrentGrade(String memberGuid, Integer roleType) {
        MemberRelevanceGradeVO gradeVO = new MemberRelevanceGradeVO();
        HsaMemberRelevanceGrade hsaMemberRelevanceGrade = memberRelevanceGradeMapper.selectOne(new LambdaQueryWrapper<HsaMemberRelevanceGrade>()
                .eq(HsaMemberRelevanceGrade::getMemberGuid, memberGuid)
                .eq(HsaMemberRelevanceGrade::getRoleType, roleType)
                .eq(HsaMemberRelevanceGrade::getIsCurrent, BooleanEnum.TRUE.getCode()));
        if (Objects.nonNull(hsaMemberRelevanceGrade)) {
            BeanUtils.copyProperties(hsaMemberRelevanceGrade, gradeVO);
        }
        return gradeVO;
    }

    @Override
    public List<MemberRelevanceGradeVO> getUserGradeAll(String memberGuid, Integer roleType) {
        List<MemberRelevanceGradeVO> memberRelevanceGradeVO = Lists.newArrayList();
        List<HsaMemberRelevanceGrade> hsaMemberRelevanceGradeList = memberRelevanceGradeMapper.selectList(new LambdaQueryWrapper<HsaMemberRelevanceGrade>()
                .eq(HsaMemberRelevanceGrade::getMemberGuid, memberGuid)
                .eq(HsaMemberRelevanceGrade::getRoleType, roleType)
                .eq(HsaMemberRelevanceGrade::getEffective, BooleanEnum.TRUE.getCode()));
        if (CollectionUtils.isNotEmpty(hsaMemberRelevanceGradeList)){
            for (HsaMemberRelevanceGrade hsaMemberRelevanceGrade : hsaMemberRelevanceGradeList) {
                MemberRelevanceGradeVO relevanceGrade = new MemberRelevanceGradeVO();
                BeanUtils.copyProperties(hsaMemberRelevanceGrade, relevanceGrade);
                memberRelevanceGradeVO.add(relevanceGrade);
            }
        }
        return memberRelevanceGradeVO;
    }
}
