package com.holderzone.member.base.entity.growth;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 额外奖励规则
 * author: pantao
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaExtraAwardRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则guid
     */
    private String guid;

    /**
     * 成长值任务guid
     */
    private String growthValueTaskGuid;

    /**
     * 充值、消费金额
     */
    private BigDecimal consumptionAmount;

    /**
     * 成长值
     */
    private int growthValue;

    /**
     * 消费笔数
     */
    private int consumptionFrequency;

}
