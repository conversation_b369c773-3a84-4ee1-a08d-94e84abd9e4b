package com.holderzone.member.base.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.base.dto.SettlementUnLockedDiscountDTO;
import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.mapper.member.HsaMemberInfoWeChatMapper;
import com.holderzone.member.base.service.assembler.MemberCouponApplyAssembler;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.cache.RedisService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponUseService;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.service.send.WechatSendService;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.MultipleEquitiesDTO;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.card.AppletBalanceRecordDTO;
import com.holderzone.member.common.dto.coupon.MemberCouponLinkDTO;
import com.holderzone.member.common.dto.integral.DeductCommodityDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.enums.exception.MemberMarketExceptionEnum;
import com.holderzone.member.common.enums.member.QrcodeTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponActivityDTO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.coupon.CouponDtlQO;
import com.holderzone.member.common.qo.member.MemberInfoCouponQO;
import com.holderzone.member.common.qo.member.MemberSendCouponQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qrcode.QrCodeSupport;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员优惠券 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
@Slf4j
public class HsaMemberCouponLinkServiceImpl extends HolderBaseServiceImpl<HsaMemberCouponLinkMapper, HsaMemberCouponLink>
        implements IHsaMemberCouponLinkService {

    @Resource
    private QrCodeSupport qrCodeSupport;

    @Resource
    private WechatSendService wechatSendService;

    @Resource
    private HsaMemberInfoWeChatMapper hsaMemberInfoWeChatMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private ShortMessageSendService sendService;

    @Resource
    private HsaMemberCouponLinkMapper hsaMemberCouponLinkMapper;

    /**
     * 优惠券使用记录
     */
    @Resource
    private IHsaMemberCouponUseService memberCouponUseService;

    @Resource
    private HsaMemberCouponUseMapper hsaMemberCouponUseMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    @Resource
    private MemberCouponApplyAssembler memberCouponApplyAssembler;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    //优惠券锁定信息
    private static final String LOCK_COUPON_DISCOUNT = "LOCK_COUPON_DISCOUNT:";

    @Override
    public CouponQrCodeVO getQrCode(String memberCouponGuid) {
        final HsaMemberCouponLink memberCouponLink = queryByGuid(memberCouponGuid);
        if (ObjectUtil.isNull(memberCouponLink) || memberCouponLink.deleted()) {
            throw new MemberBaseException(MemberMarketExceptionEnum.MEMBER_COUPON_NULL);
        }
        //状态判断
        if (!CouponMemberStateEnum.canUse(memberCouponLink.getState())) {
            throw new MemberBaseException(MemberMarketExceptionEnum.MEMBER_COUPON_NOT_USE);
        }
        CouponQrCodeVO vo = new CouponQrCodeVO();
        vo.setCode(memberCouponLink.getCode());
        //生成二维码
        vo.setQrCode(qrCodeSupport.getQrStr(memberCouponLink.getCode(), QrcodeTypeEnum.COUPON.getDes()));
        vo.setCouponName(memberCouponLink.getCouponName());
        vo.setThresholdType(memberCouponLink.getThresholdType());
        vo.setThresholdAmount(memberCouponLink.getThresholdAmount());
        vo.setDiscountAmount(memberCouponLink.getDiscountAmount());
        return vo;
    }

    @Override
    public void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponLinks) {

        log.info("优惠券到账推送参数:{}", JSON.toJSONString(memberCouponLinks));

        List<String> getMemberGuidList =
                memberCouponLinks.stream().map(MemberCouponPackageVO::getMemberGuid).collect(Collectors.toList());


        log.info("实际推送OpenId:{}", JSON.toJSONString(getMemberGuidList));
        for (MemberCouponPackageVO memberOpenIdVO : memberCouponLinks) {
            Map<String, String> prams = new HashMap<>();
            String couponName;


            if (memberOpenIdVO.getCouponPackageNum() > 1) {
                couponName = memberOpenIdVO.getCouponName();
            } else {
                couponName = memberOpenIdVO.getCouponPackageName();
            }
            LocalDateTime couponEffectiveEndTime = memberOpenIdVO.getCouponEffectiveEndTime();
            String minutes = couponEffectiveEndTime.getMinute() == 0 ? "00" : couponEffectiveEndTime.getMinute() + "";
            prams.put("thing5", couponName);
            prams.put("number6", memberOpenIdVO.getCouponNum() + "");
            prams.put("thing8", CouponTypeEnum.getDesByCode(memberOpenIdVO.getCouponType()));
            prams.put("time3", couponEffectiveEndTime.getYear() + "年"
                    + couponEffectiveEndTime.getMonthValue() + "月"
                    + couponEffectiveEndTime.getDayOfMonth() + "日" + " "
                    + couponEffectiveEndTime.getHour() + ":" + minutes);

            MessagesSendQO qo = new MessagesSendQO();

            qo.setPrams(prams);

            qo.setOperSubjectGuid(memberOpenIdVO.getOperSubjectGuid());
            qo.setPhone(memberOpenIdVO.getPhoneNum());
            qo.setEnterpriseGuid(memberOpenIdVO.getEnterpriseGuid());
            qo.setMemberGuid(memberOpenIdVO.getMemberGuid());
            qo.setTemplateName("优惠券到账通知");
            ThreadLocalCache.put(JSON.toJSONString(new HeaderUserInfo()));

            wechatSendService.send(qo);
        }


    }

    @Override
    public void sendMemberCouponExpireNotice() {
        log.info("定时触发三天内过期优惠券提醒，当前执行时间：{}", LocalDateTime.now());
        String current = DateUtil.getTmpDate(new Date(), StringConstant.FORMAT);
        if (Boolean.TRUE.equals(cacheService.setLock(StringConstant.XXL_JOB_COUPON_NOTICE + current))) {
            log.info("执行时间重复跳出，当前时间：{}", current);
            return;
        }
        LocalDateTime now = LocalDateTime.now().plusDays(3);
        List<MemberCouponOpenIdVO> memberCouponOpenIdVOList = hsaMemberInfoWeChatMapper.getMemberCouponOpenId(now);

        if (CollUtil.isNotEmpty(memberCouponOpenIdVOList)) {

            List<MemberCouponOpenIdVO> couponOpenIdVOList = Lists.newArrayList();

            for (MemberCouponOpenIdVO memberCouponOpenIdVO : memberCouponOpenIdVOList) {
                if (Duration.between(memberCouponOpenIdVO.getReachTime(), memberCouponOpenIdVO.getCouponEffectiveEndTime()).toHours() >= 72) {
                    couponOpenIdVOList.add(memberCouponOpenIdVO);
                }
            }
            if (CollUtil.isEmpty(couponOpenIdVOList)) {
                cacheService.delete(StringConstant.XXL_JOB_COUPON_NOTICE + current);
                return;
            }

            //短信推送
            sendService.sendMemberCouponExpireNotice(couponOpenIdVOList);

            Map<String, List<MemberCouponOpenIdVO>> memberCouponLinkMap =
                    couponOpenIdVOList.stream().collect(Collectors.groupingBy(MemberCouponOpenIdVO::getMemberGuid));


            List<String> couponGuid = Lists.newArrayList();
            Set<String> memberGuid = couponOpenIdVOList.stream().map(MemberCouponOpenIdVO::getMemberGuid).collect(Collectors.toSet());
            for (String guid : memberGuid) {
                List<MemberCouponOpenIdVO> memberCouponOpenIdVOS = memberCouponLinkMap.get(guid);
                memberCouponOpenIdVOS = memberCouponOpenIdVOS.stream()
                        .sorted(Comparator.comparing(MemberCouponOpenIdVO::getCouponEffectiveEndTime))
                        .collect(Collectors.toList());
                Map<String, String> prams = new HashMap<>();

                prams.put("thing1", memberCouponOpenIdVOS.get(0).getCouponName());
                prams.put("thing2", CouponTypeEnum.getDesByCode(memberCouponOpenIdVOS.get(0).getCouponType()));
                prams.put("thing5", memberCouponOpenIdVOS.size() + "");
                prams.put("thing4", "您有" + memberCouponOpenIdVOS.size() + "张优惠券即将过期，请尽快使用");

                MessagesSendQO qo = new MessagesSendQO();

                qo.setPrams(prams);
                qo.setOperSubjectGuid(memberCouponOpenIdVOS.get(0).getOperSubjectGuid());
                qo.setEnterpriseGuid(memberCouponOpenIdVOS.get(0).getEnterpriseGuid());
                qo.setMemberGuid(memberCouponOpenIdVOS.get(0).getMemberGuid());
                qo.setOpenId(memberCouponOpenIdVOS.get(0).getOpenId());
                qo.setTemplateName("优惠券过期提醒");
                ThreadLocalCache.put(JSON.toJSONString(new HeaderUserInfo()));

                couponGuid.addAll(memberCouponOpenIdVOS.stream().map(MemberCouponOpenIdVO::getCouponGuid)
                        .collect(Collectors.toList()));
                wechatSendService.send(qo);
            }
            baseMapper.updateIsExpireRemind(couponGuid);
        }
        cacheService.delete(StringConstant.XXL_JOB_COUPON_NOTICE + current);
    }

    @Override
    public boolean updateStateByApply(String memberInfoGuid, String couponCode, List<String> codes) {
        int size = hsaMemberCouponLinkMapper.updateStateByUse(memberInfoGuid, couponCode, codes, CouponMemberStateEnum.APPLY.getCode());
        return size > 0;
    }

    @Override
    public List<String> list(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<String> guidList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<String> guids = hsaMemberCouponLinkMapper.listGuid(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(guids)) {
                continue;
            }
            guidList.addAll(guids);
        }
        return guidList;
    }

    @Override
    public List<String> listGuid(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<String> guidList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<String> guids = hsaMemberCouponLinkMapper.listGuid(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(guids)) {
                continue;
            }
            guidList.addAll(guids);
        }
        return guidList;
    }

    @Override
    public List<HsaMemberCouponLink> listAllByCode(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<HsaMemberCouponLink> couponList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<HsaMemberCouponLink> couponLinks = hsaMemberCouponLinkMapper.listAllByCode(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(couponLinks)) {
                continue;
            }
            couponList.addAll(couponLinks);
        }
        return couponList;
    }

    @Override
    public void locked(SettlementOrderLockDTO lockedDiscount) {
        String couponKey = LOCK_COUPON_DISCOUNT + lockedDiscount.getCodeType() + lockedDiscount.getOrderInfo().getOrderNumber();
        stringRedisTemplate.opsForValue().set(couponKey, JSON.toJSONString(lockedDiscount));
    }

    @Override
    @Transactional
    @RedissonLock(lockName = "COUPON_MARK_USE", tryLock = true, leaseTime = 10)
    public void markUse(CouponMarkUseQO qo) {
        final List<String> memberCouponGuids = Collections.singletonList(qo.getCouponGuid());
        //券
        final HsaMemberCouponLink couponLink = this.queryByGuid(qo.getCouponGuid());
        MemberCouponLinkDTO memberCouponLinkDTO = new MemberCouponLinkDTO();
        BeanUtils.copyProperties(couponLink, memberCouponLinkDTO);
        qo.setMemberCouponLinkDTO(memberCouponLinkDTO);
        if (couponLink.getCouponType() == CouponTypeEnum.COUPON_EXCHANGE.getCode()) {
            int num = hsaMemberCouponUseMapper.selectCount(new LambdaQueryWrapper<HsaMemberCouponUse>()
                    .eq(HsaMemberCouponUse::getMemberCouponLinkGuid, couponLink.getGuid()));
            CouponActivityDTO couponActivityDTO = qo.getCouponActivityDTO();
            if (couponActivityDTO.getExchangeLimit() == BooleanEnum.TRUE.getCode()
                    && couponActivityDTO.getExchangeTimes() - (num + 1) == 0) {
                //下单、券改为已使用
                hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());

            }
            //人工锁定优惠券
            memberCouponUseService.markUse(qo);
            return;
        } else if (couponLink.getCouponType() == CouponTypeEnum.COUPON_DISCOUNT.getCode()) {
            qo.setDiscountAmount(BigDecimal.ZERO);
            couponLink.setDiscountAmount(BigDecimal.ZERO);
            hsaMemberCouponLinkMapper.updateByGuid(couponLink);
        } else {
            qo.setDiscountAmount(couponLink.getDiscountAmount());
        }
        //下单、券改为已使用
        hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());
        //人工锁定优惠券
        memberCouponUseService.markUse(qo);
    }

    @Override
    @Transactional
    @RedissonLock(lockName = "COUPON_INVALID", tryLock = true, leaseTime = 10)
    public void invalid(CouponMarkInvalidQO qo) {
        //券
        final HsaMemberCouponLink couponLink = this.queryByGuid(qo.getCouponGuid());
        if (Objects.isNull(couponLink)) {
            throw new MemberBaseException("优惠券不存在");
        }
        //未过期才能作废
        if (couponLink.getState() != CouponMemberStateEnum.UN_EXPIRE.getCode()) {
            throw new MemberBaseException("优惠券不可作废");
        }
        final List<String> memberCouponGuids = Collections.singletonList(qo.getCouponGuid());
        //下单、券改为已使用
        hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.OVER.getCode());
    }

    @Override
    public void unlocked(SettlementUnLockedDiscountDTO discountDTO) {
        //优惠券状态回退
        final List<HsaMemberCouponUse> couponUseList = memberCouponUseService.listByOrderNumber(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo());
        if (CollUtil.isEmpty(couponUseList)) {
            log.info("优惠释放：优惠券未使用，订单号：{}", discountDTO.getOrderNo());
            return;
        }
        //回退使用的优惠券状态
        rollbackApplyCouponState(couponUseList, discountDTO);
        //删除使用记录
        final List<Long> couponUseIds = couponUseList.stream().map(HsaBaseEntity::getId).collect(Collectors.toList());
        memberCouponUseService.removeByIds(couponUseIds);
    }

    /**
     * 回退优惠券状态
     *
     * @param couponUseList 使用券
     */
    private void rollbackApplyCouponState(List<HsaMemberCouponUse> couponUseList, SettlementUnLockedDiscountDTO discountDTO) {
        //查询已使用优惠券
        final List<String> memberCouponGuids = couponUseList.stream().map(HsaMemberCouponUse::getMemberCouponLinkGuid).collect(Collectors.toList());
        final LambdaQueryWrapper<HsaMemberCouponLink> couponWrapper = new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaBaseEntity::getGuid, memberCouponGuids)
                .eq(HsaMemberCouponLink::getState, CouponMemberStateEnum.APPLY.getCode());
        //查询优惠券
        final List<HsaMemberCouponLink> couponLinks = this.list(couponWrapper);
        if (CollUtil.isEmpty(couponLinks)) {
            return;
        }

        //过滤可退的
        List<String> discountIdList = discountDTO.getDiscountIdList();
        log.info("可退发券码：{}", discountIdList);

        LocalDateTime now = LocalDateTime.now();
        couponLinks.forEach(couponLink -> {
            //判断可退
            if (CollUtil.isNotEmpty(discountIdList)
                    && (discountIdList.contains(couponLink.getCode()))) {
                setState(couponLink, now);
            }
        });
        //券状态修改
        this.updateBatchById(couponLinks);
    }

    private static void setState(HsaMemberCouponLink couponLink, LocalDateTime now) {
        //判断是否过期
        if (couponLink.getCouponEffectiveEndTime().isAfter(now)) {
            //未过期
            couponLink.setState(CouponMemberStateEnum.UN_EXPIRE.getCode());
        } else {
            //已过期
            couponLink.setState(CouponMemberStateEnum.EXPIRE.getCode());
        }
    }

    @Override
    public void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO) {
        SettlementOrderLockDTO lockedDiscount = getSettlementOrderLockDTO(discountDTO);
        log.info("获取折扣券占用记录：{}", JSON.toJSONString(lockedDiscount));
        if (Objects.isNull(lockedDiscount)) {
            log.info("未获取到折扣券占用记录，订单号：{}", discountDTO.getOrderNo());
            return;
        }

        List<SettlementLockedDiscountReqDTO> checkDiscountList = lockedDiscount.getCheckDiscountList()
                .stream().filter(in -> Objects.equals(discountDTO.getCodeType(), in.getDiscountOption()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(checkDiscountList)) {
            log.info("未获取到折扣券占用记录，订单号：{}", discountDTO.getOrderNo());
        }
        SettlementLockedOrderInfoDTO orderInfo = lockedDiscount.getOrderInfo();
        final Map<String, List<String>> couponMap = checkDiscountList.stream()
                .collect(Collectors.groupingBy(SettlementLockedDiscountReqDTO::getDiscountGuid,
                        Collectors.mapping(SettlementLockedDiscountReqDTO::getDiscountOptionId, Collectors.toList())));

        final List<HsaMemberCouponLink> couponLinks = listAllByCode(orderInfo.getOperSubjectGuid(), couponMap);
        if (CollUtil.isEmpty(couponLinks)) {
            return;
        }

        if (discountDTO.getCodeType() == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode()) {
            dealExchangeDiscount(discountDTO, checkDiscountList, orderInfo, couponLinks, lockedDiscount);
        } else {
            final List<String> memberCouponGuids = couponLinks.stream().map(HsaBaseEntity::getGuid).collect(Collectors.toList());
            //下单、券改为已使用
            hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());
            //下单：时间为下单时间。支付后，时间更新为已支付时间
            memberCouponUseService.lockedCoupon(lockedDiscount, couponLinks);

            //支付后操作
            memberCouponUseService.afterPayCoupon(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo(), discountDTO.getPayTime());
        }

    }

    private void dealExchangeDiscount(SettlementPayAfterDiscountDTO discountDTO,
                                      List<SettlementLockedDiscountReqDTO> checkDiscountList,
                                      SettlementLockedOrderInfoDTO orderInfo,
                                      List<HsaMemberCouponLink> couponLinks,
                                      SettlementOrderLockDTO lockedDiscount) {
        List<String> discountOptionId = checkDiscountList.stream().map(SettlementLockedDiscountReqDTO::getDiscountOptionId).collect(Collectors.toList());

        //占用的次数
        Map<String, Integer> codeMap = hsaMemberOrderDiscountService.getUsedCouponNum(
                discountOptionId,
                BooleanEnum.FALSE.getCode(),
                discountDTO.getCodeType(),
                discountDTO.getOrderNo());

        List<HsaMemberCouponLink> hsaMemberCouponLinks = hsaMemberCouponLinkMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaMemberCouponLink::getCode, discountOptionId));

        //已使用的次数
        List<String> memberCouponLinkGuids = hsaMemberCouponLinks.stream().map(HsaMemberCouponLink::getGuid).collect(Collectors.toList());
        Map<String, HsaMemberCouponLink> hsaMemberCouponLinkMap = hsaMemberCouponLinks.stream()
                .collect(Collectors.toMap(HsaMemberCouponLink::getCode, Function.identity(), (entity1, entity2) -> entity1));
        List<HsaMemberCouponUse> hsaMemberCouponUseList = hsaMemberCouponUseMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponUse>()
                .in(HsaMemberCouponUse::getMemberCouponLinkGuid, memberCouponLinkGuids));
        Map<String, List<HsaMemberCouponUse>> hsaMemberCouponUseMap = hsaMemberCouponUseList.stream()
                .collect(Collectors.groupingBy(HsaMemberCouponUse::getMemberCouponLinkGuid));

        //选中优惠map
        final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = getCheckDiscountMap(lockedDiscount, couponLinks);
        List<HsaMemberCouponUse> recordList = new ArrayList<>();
        //需要修改状态的券
        List<HsaMemberCouponLink> hsaMemberCouponLinkList = Lists.newArrayList();
        for (SettlementLockedDiscountReqDTO settlementLockedDiscountReqDTO : checkDiscountList) {

            HsaMemberCouponLink hsaMemberCouponLink = hsaMemberCouponLinkMap.get(settlementLockedDiscountReqDTO.getDiscountOptionId());

            //核销记录
            addMemberCouponUse(orderInfo, settlementLockedDiscountReqDTO, hsaMemberCouponLink, checkDiscountMap, recordList);

            if (hsaMemberCouponLink.getExchangeLimit() == 0) {
                log.info("券：{}，总次数为不限制，无需占用", hsaMemberCouponLink.getCode());
                continue;
            }
            //校验券使用次数
            checkLimitNumber(settlementLockedDiscountReqDTO, hsaMemberCouponLink, codeMap, hsaMemberCouponUseMap, hsaMemberCouponLinkList);
        }

        if (CollUtil.isNotEmpty(hsaMemberCouponLinkList)) {
            final List<String> memberCouponGuids = hsaMemberCouponLinkList.stream().map(HsaBaseEntity::getGuid).collect(Collectors.toList());
            //下单、券改为已使用
            hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());
        }

        memberCouponUseService.saveBatch(recordList);

        //支付后操作
        memberCouponUseService.afterPayCoupon(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo(), discountDTO.getPayTime());
    }

    private static void checkLimitNumber(SettlementLockedDiscountReqDTO settlementLockedDiscountReqDTO,
                                         HsaMemberCouponLink hsaMemberCouponLink,
                                         Map<String, Integer> codeMap,
                                         Map<String, List<HsaMemberCouponUse>> hsaMemberCouponUseMap,
                                         List<HsaMemberCouponLink> hsaMemberCouponLinkList) {
        //限制次数
        int limitNumber = hsaMemberCouponLink.getExchangeTimes();
        log.info("券：{}，限制次数为：{}", hsaMemberCouponLink.getCode(), limitNumber);
        //已使用总次数
        int totalUsedNumber = settlementLockedDiscountReqDTO.getUsedTimes();
        log.info("券：{}，当前使用次数为：{}", hsaMemberCouponLink.getCode(), totalUsedNumber);

        //占用次数
        if (codeMap.containsKey(settlementLockedDiscountReqDTO.getDiscountOptionId())) {
            log.info("券：{}，占用次数为：{}", hsaMemberCouponLink.getCode(), codeMap.get(settlementLockedDiscountReqDTO.getDiscountOptionId()));
            totalUsedNumber = totalUsedNumber + codeMap.get(settlementLockedDiscountReqDTO.getDiscountOptionId());
        }
        //已使用次数
        if (hsaMemberCouponUseMap.containsKey(hsaMemberCouponLink.getGuid())) {
            log.info("券：{}，已使用次数为：{}", hsaMemberCouponLink.getCode(), hsaMemberCouponUseMap.get(hsaMemberCouponLink.getGuid()).size());
            List<HsaMemberCouponUse> hsaMemberCouponUses = hsaMemberCouponUseMap.get(hsaMemberCouponLink.getGuid());
            totalUsedNumber = totalUsedNumber + hsaMemberCouponUses.size();
        }
        log.info("券：{}，已消耗总次数为：{}", hsaMemberCouponLink.getCode(), totalUsedNumber);
        if (limitNumber <= totalUsedNumber) {
            hsaMemberCouponLinkList.add(hsaMemberCouponLink);
        }
    }

    private void addMemberCouponUse(SettlementLockedOrderInfoDTO orderInfo,
                                    SettlementLockedDiscountReqDTO settlementLockedDiscountReqDTO,
                                    HsaMemberCouponLink hsaMemberCouponLink,
                                    Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap,
                                    List<HsaMemberCouponUse> recordList) {
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaMemberCouponUse.class.getSimpleName(), settlementLockedDiscountReqDTO.getUsedTimes());
        //当前次数记录
        for (int i = 0; i < settlementLockedDiscountReqDTO.getUsedTimes(); i++) {
            HsaMemberCouponUse hsaMemberCouponUse = new HsaMemberCouponUse();

            final String memberCouponLinkGuid = hsaMemberCouponLink.getGuid();
            final String guid = guids.get(i);
            hsaMemberCouponUse.setOperSubjectGuid(orderInfo.getOperSubjectGuid());
            hsaMemberCouponUse.setOrderNumber(orderInfo.getOrderNumber());
            //优惠券guid
            hsaMemberCouponUse.setMemberCouponLinkGuid(memberCouponLinkGuid);
            //下单
            hsaMemberCouponUse.setLockTime(orderInfo.getOrderTime());
            hsaMemberCouponUse.setStoreGuid(orderInfo.getStoreGuid());
            hsaMemberCouponUse.setStoreName(orderInfo.getStoreName());
            hsaMemberCouponUse.setGuid(guid);
            //来源
            hsaMemberCouponUse.setSource(orderInfo.getSource());
            //优惠金额
            Optional.ofNullable(checkDiscountMap.get(memberCouponLinkGuid))
                    .ifPresent(req -> hsaMemberCouponUse.setDiscountAmount(req.getDiscountAmount()));
            //实付
            hsaMemberCouponUse.setOrderPaidAmount(orderInfo.getOrderPaidAmount());
            //操作人
            hsaMemberCouponUse.setOperatorAccountName(orderInfo.getOperatorAccountName());

            hsaMemberCouponUse.setCouponName(hsaMemberCouponLink.getCouponName());
            hsaMemberCouponUse.setCouponCode(hsaMemberCouponLink.getCouponCode());
            hsaMemberCouponUse.setMemberPhone(hsaMemberCouponLink.getMemberPhone());
            hsaMemberCouponUse.setUserName(hsaMemberCouponLink.getUserName());
            hsaMemberCouponUse.setMemberGuid(hsaMemberCouponLink.getMemberGuid());
            hsaMemberCouponUse.setCode(hsaMemberCouponLink.getCode());
            hsaMemberCouponUse.setCouponPackageCode(hsaMemberCouponLink.getCouponPackageCode());
            hsaMemberCouponUse.setCouponPackageName(hsaMemberCouponLink.getCouponPackageName());
            recordList.add(hsaMemberCouponUse);
        }
    }

    /**
     * 构造选中map
     *
     * @param lockedDiscount 锁定参数
     * @param memberCoupons  优惠券
     * @return couponGuid, req
     */
    private Map<String, SettlementLockedDiscountReqDTO> getCheckDiscountMap(SettlementOrderLockDTO lockedDiscount,
                                                                            List<HsaMemberCouponLink> memberCoupons) {
        final List<SettlementLockedDiscountReqDTO> checkDiscountList = lockedDiscount.getCheckDiscountList();
        final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = new HashMap<>();
        for (HsaMemberCouponLink c : memberCoupons) {
            for (SettlementLockedDiscountReqDTO d : checkDiscountList) {
                if (d.getDiscountGuid().equals(c.getCouponCode())
                        && d.getDiscountOptionId().equals(c.getCode())) {
                    checkDiscountMap.put(c.getGuid(), d);
                    break;
                }
            }
        }
        return checkDiscountMap;
    }

    /**
     * 获取锁定优惠信息
     */
    private SettlementOrderLockDTO getSettlementOrderLockDTO(SettlementPayAfterDiscountDTO discountDTO) {
        String couponKey = LOCK_COUPON_DISCOUNT + discountDTO.getCodeType() + discountDTO.getOrderNo();
        String value = stringRedisTemplate.opsForValue().get(couponKey);
        return JSON.parseObject(value, SettlementOrderLockDTO.class);
    }

    @Override
    public Map<String, Integer> listByCodeRecord(CouponDtlQO couponDtlQO) {
        List<String> dtlList = couponDtlQO.getDtlList();
        log.info("查询券核销数量：{}", dtlList);
        Map<String, Integer> map = new HashMap<>();
        List<HsaMemberCouponLink> hsaMemberCouponLinks = hsaMemberCouponLinkMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaMemberCouponLink::getDtlGuid, dtlList));

        if (CollUtil.isNotEmpty(hsaMemberCouponLinks)) {
            Map<String, List<HsaMemberCouponLink>> hashMap = hsaMemberCouponLinks.stream().collect(Collectors.groupingBy(HsaMemberCouponLink::getDtlGuid));
            for (String i : dtlList) {
                List<HsaMemberCouponLink> childLinks = hashMap.get(i);
                if (CollUtil.isNotEmpty(childLinks)) {
                    List<String> list = childLinks.stream().map(HsaBaseEntity::getGuid).collect(Collectors.toList());
                    Integer num = hsaMemberCouponUseMapper
                            .selectCount(new LambdaQueryWrapper<HsaMemberCouponUse>().in(HsaMemberCouponUse::getMemberCouponLinkGuid, list));

                    num = num == null ? 0 : num;
                    map.put(i, num);
                }
            }
        }
        return map;
    }

    @Override
    public void sendMemberCoupon(MemberSendCouponQO memberSendCouponQO) {
        log.info("手动发送优惠券：{}", JSON.toJSONString(memberSendCouponQO));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        for (MemberInfoCouponQO memberInfoCouponQO : memberSendCouponQO.getMemberInfoCouponQOS()) {
            memberSendCouponQO.setCouponPackageType(CouponPackageTypeEnum.COUPON_ADMIN_SEND.getCode());
            memberSendCouponQO.setCouponPackageCode(headerUserInfo.getUserName() + "-" + headerUserInfo.getTel());
            memberSendCouponQO.setActivityName(CouponPackageTypeEnum.COUPON_ADMIN_SEND.getDes());
            memberSendCouponQO.setMemberName(memberInfoCouponQO.getMemberName());
            memberSendCouponQO.setMemberGuid(memberInfoCouponQO.getMemberGuid());
            memberSendCouponQO.setMemberPhone(memberInfoCouponQO.getMemberPhone());
            List<HsaMemberCouponLink> hsaMemberCouponLinks = memberCouponApplyAssembler.formMemberCouponLink(memberSendCouponQO);
            if (CollUtil.isNotEmpty(hsaMemberCouponLinks) && StringUtils.isNotEmpty(memberSendCouponQO.getMemberGuid())) {
                memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));

                List<MemberCouponPackageVO> memberCouponPackageVOS = Lists.newArrayList();
                for (HsaMemberCouponLink hsaMemberCouponLink : hsaMemberCouponLinks) {
                    MemberCouponPackageVO memberCouponPackageVO = memberCouponApplyAssembler.getMemberCouponPackageVO(hsaMemberCouponLink, memberSendCouponQO);
                    memberCouponPackageVOS.add(memberCouponPackageVO);
                }
                sendService.sendMemberCouponNotice(memberCouponPackageVOS);
            }
        }
        }
}
