package com.holderzone.member.base.service.grade.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.ShopBaseService;
import com.holderzone.member.base.dto.CardBusinessEquitiesDTO;
import com.holderzone.member.base.dto.CardGrantEquitiesDTO;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.equities.HsaEquitiesInfo;
import com.holderzone.member.base.entity.equities.HsaEquitiesStoreRule;
import com.holderzone.member.base.entity.grade.*;
import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.handler.AbstractHandler;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.equities.HsaEquitiesInfoMapper;
import com.holderzone.member.base.mapper.grade.*;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.equities.HsaEquitiesStoreRuleService;
import com.holderzone.member.base.service.grade.HsaBusinessEquitiesService;
import com.holderzone.member.base.service.grade.HsaGradeRightsCommodityRuleService;
import com.holderzone.member.base.service.grade.HsaMemberEquitiesReceiveRecordService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.base.service.integral.HsaIntegralDetailService;
import com.holderzone.member.base.support.SettlementMemberSupport;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.GrantMemberCouponPackageEvent;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.CardStatusEnum;
import com.holderzone.member.common.enums.card.EletronicCardStateEnum;
import com.holderzone.member.common.enums.card.PhysicalCardStateEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesReceiveTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.grade.MemberGradeStateEnum;
import com.holderzone.member.common.enums.growth.ApplyBusinessEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.growth.GrowthValueValidityTypeEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.business.BusinessCardQO;
import com.holderzone.member.common.qo.card.CardEquitiesQO;
import com.holderzone.member.common.qo.card.SendMemberEquityCard;
import com.holderzone.member.common.qo.equities.EquitiesStoreRuleQO;
import com.holderzone.member.common.qo.equities.GradeEquitiesQO;
import com.holderzone.member.common.qo.equities.MemberEquitiesQO;
import com.holderzone.member.common.qo.equities.SendCardEquitiesQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBaseQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.equities.EquitiesDetailVO;
import com.holderzone.member.common.vo.grade.DoubleValueRequest;
import com.holderzone.member.common.vo.grade.GradeEquitiesPreviewVO;
import com.holderzone.member.common.vo.grade.MemberGradeRelationVO;
import com.holderzone.member.common.vo.grade.PeriodDiscountVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityPageVO;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: member-marketing
 * @description: 等级权益service
 * @author: pan tao
 * @create: 2022-01-18 18:22
 */
@Slf4j
@Service
public class HsaBusinessEquitiesServiceImpl extends HolderBaseServiceImpl<HsaBusinessEquitiesMapper, HsaBusinessEquities>
        implements HsaBusinessEquitiesService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    @Resource
    private ShopBaseService shopBaseService;

    @Lazy
    @Resource
    private RequestGoalgoService hsaRequestGoalgoService;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaCommodityMemberPriceMapper hsaCommodityMemberPriceMapper;

    @Lazy
    @Resource
    private HsaGradeRightsCommodityRuleService hsaGradeRightsCommodityRuleService;

    @Resource
    private HsaEquitiesInfoMapper hsaEquitiesInfoMapper;

    @Lazy
    @Resource
    private IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    @Resource
    private CacheService cacheService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private List<AbstractHandler> abstractHandleList;

    @Lazy
    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Lazy
    @Resource
    private HsaEquitiesStoreRuleService hsaEquitiesStoreRuleService;

    @Lazy
    @Resource
    private SettlementMemberSupport settlementMemberSupport;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Lazy
    @Resource
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Lazy
    @Resource
    private HsaMemberEquitiesReceiveRecordService hsaMemberEquitiesReceiveRecordService;

    @Lazy
    @Resource
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Resource
    private HsaGradeRightsCommodityRuleMapper hsaGradeRightsCommodityRuleMapper;

    private AbstractHandler abstractHandler;

    @PostConstruct
    public void initializeChainFilter() {

        for (int i = 0; i < abstractHandleList.size(); i++) {
            if (i == 0) {
                abstractHandler = abstractHandleList.get(0);
            } else {
                AbstractHandler currentHander = abstractHandleList.get(i - 1);
                AbstractHandler nextHander = abstractHandleList.get(i);
                currentHander.setNextHandler(nextHander);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEquities(MemberEquitiesQO memberEquitiesQO) {
        List<GradeEquitiesQO> gradeEquitiesQOS = memberEquitiesQO.getGradeEquitiesQOS();
        BusinessCardQO businessCardQO = new BusinessCardQO();
        List<HsaBusinessEquities> list = new ArrayList<>();
        //获取卡以往数据
        List<HsaBusinessEquities> cardBusinessEquities = getCardBusinessEquities(memberEquitiesQO);
        //删除以往卡权益门店商品数据
        dealCardEquities(memberEquitiesQO, cardBusinessEquities);
        forEquities(memberEquitiesQO, gradeEquitiesQOS, businessCardQO, list, cardBusinessEquities);
        //删除以往卡权益
        dealCardBusinessEquities(memberEquitiesQO, cardBusinessEquities);
        if (CollUtil.isNotEmpty(list)) {
            this.saveBatch(list);
        }
        //推送结算台
        cardProcessing(memberEquitiesQO, businessCardQO, list);
    }

    private void cardProcessing(MemberEquitiesQO memberEquitiesQO, BusinessCardQO businessCardQO, List<HsaBusinessEquities> list) {
        if (memberEquitiesQO.getBusinessType() == BusinessTypeEnum.CARD_EQUITIES.getCode()) {
            businessCardQO.setCardName(memberEquitiesQO.getCardName());
            settlementMemberSupport.sendCardDiscount(businessCardQO);

            //赠送一次性权益
            if (memberEquitiesQO.getCardStatus() == CardStatusEnum.ENABLE.getCode() && CollUtil.isNotEmpty(list)) {

                List<HsaBusinessEquities> hsaBusinessEquities = list.stream().filter(in -> Objects.nonNull(in.getSetPeriod())
                                && in.getSetPeriod() == DataUnitEnum.FOREVER.getCode())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(hsaBusinessEquities)) {
                    CardGrantEquitiesDTO cardGrantEquitiesDTO = new CardGrantEquitiesDTO();
                    cardGrantEquitiesDTO.setCardGuid(memberEquitiesQO.getBusinessGuid());
                    cardGrantEquitiesDTO.setHsaBusinessEquitiesList(hsaBusinessEquities);
                    memberBaseThreadExecutor.execute(() -> grantCardRights(cardGrantEquitiesDTO));
                }
            }
        }
    }

    private void forEquities(MemberEquitiesQO memberEquitiesQO, List<GradeEquitiesQO> gradeEquitiesQOS, BusinessCardQO businessCardQO, List<HsaBusinessEquities> list, List<HsaBusinessEquities> cardBusinessEquities) {
        if (CollUtil.isNotEmpty(gradeEquitiesQOS)) {
            for (GradeEquitiesQO gradeEquitiesQO : gradeEquitiesQOS) {
                //业务类型
                gradeEquitiesQO.setBusinessType(memberEquitiesQO.getBusinessType());
                HsaBusinessEquities hsaBusinessEquities = getHsaBusinessEquities(memberEquitiesQO, gradeEquitiesQO);
                int equitiesRuleType = gradeEquitiesQO.getEquitiesRuleType();
                if (EquitiesRuleTypeEnum.GOODS_MEMBER.getCode() == equitiesRuleType) {
                    // 设置商品会员价使用次数数据
                    setPeriodDiscountType(gradeEquitiesQO, hsaBusinessEquities);
                }
                list.add(hsaBusinessEquities);

                //权益商品
                saveHsaGrowthCommodityRule(gradeEquitiesQO, hsaBusinessEquities);

                //权益门店
                saveHsaStoreRule(gradeEquitiesQO, hsaBusinessEquities);
            }
            //处理会员卡更新与删除推送权益数据
            cardSettlement(memberEquitiesQO, businessCardQO, list);
        } else {
            //处理结算台权益删除数据
            cardSettlementDelete(memberEquitiesQO, businessCardQO, cardBusinessEquities);
        }
    }

    private void cardSettlement(MemberEquitiesQO memberEquitiesQO, BusinessCardQO businessCardQO, List<HsaBusinessEquities> list) {
        if (memberEquitiesQO.getBusinessType() == BusinessTypeEnum.GRADE_EQUITIES.getCode()) {
            return;
        }

        //分离
        List<String> hsaBusinessEquitiesMap = getCardBusinessEquities(memberEquitiesQO)
                .stream()
                .map(HsaBusinessEquities::getGuid).collect(Collectors.toList());

        List<String> cardEquitiesGuid = list.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(hsaBusinessEquitiesMap)) {
            //待删除的权益
            List<String> guid = hsaBusinessEquitiesMap.stream().filter(in -> !cardEquitiesGuid.contains(in)).collect(Collectors.toList());

            businessCardQO.setDeleteEquitiesGuid(guid);
        }

        businessCardQO.setCardGuid(memberEquitiesQO.getBusinessGuid());
        businessCardQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

        List<GradeEquitiesQO> equitiesQOS = Lists.newArrayList();
        for (HsaBusinessEquities hsaBusinessEquities : list) {
            if (hsaBusinessEquities.getEquitiesRuleType() != EquitiesRuleTypeEnum.MEMBER_PRICE.getCode()
                    || memberEquitiesQO.getCardStatus() == CardStatusEnum.DRAFT.getCode()
                    || memberEquitiesQO.getCardStatus() == CardStatusEnum.FINISH.getCode()) {
                continue;
            }
            GradeEquitiesQO gradeEquitiesQO = new GradeEquitiesQO();
            BeanUtils.copyProperties(hsaBusinessEquities, gradeEquitiesQO);
            gradeEquitiesQO.setGuid(hsaBusinessEquities.getGuid());
            equitiesQOS.add(gradeEquitiesQO);
        }
        businessCardQO.setGradeEquitiesQOS(equitiesQOS);
    }

    private void cardSettlementDelete(MemberEquitiesQO memberEquitiesQO, BusinessCardQO businessCardQO, List<HsaBusinessEquities> cardBusinessEquities) {
        if (memberEquitiesQO.getBusinessType() == BusinessTypeEnum.GRADE_EQUITIES.getCode()) {
            return;
        }
        if (CollUtil.isNotEmpty(cardBusinessEquities)) {
            businessCardQO.setDeleteEquitiesGuid(cardBusinessEquities.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList()));
        }
        businessCardQO.setCardGuid(memberEquitiesQO.getBusinessGuid());
        businessCardQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
    }

    private void dealCardEquities(MemberEquitiesQO memberEquitiesQO, List<HsaBusinessEquities> hsaBusinessEquities) {
        if (memberEquitiesQO.getBusinessType() == BusinessTypeEnum.CARD_EQUITIES.getCode()
                && (CollUtil.isNotEmpty(hsaBusinessEquities))) {
            List<String> gradeEquitieGuidList = hsaBusinessEquities.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList());
            //删除老权益商品
            hsaGradeRightsCommodityRuleMapper.deleteByEquitiesCommodityCardGuid(gradeEquitieGuidList);
            //删除老权益门店
            hsaGradeRightsCommodityRuleMapper.deleteByEquitiesStoreCardGuid(gradeEquitieGuidList);

        }
    }

    private void dealCardBusinessEquities(MemberEquitiesQO memberEquitiesQO, List<HsaBusinessEquities> hsaBusinessEquities) {
        if (memberEquitiesQO.getBusinessType() == BusinessTypeEnum.CARD_EQUITIES.getCode()
                && (CollUtil.isNotEmpty(hsaBusinessEquities))) {
            List<String> gradeEquitieGuidList = hsaBusinessEquities.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList());
            //删除老权益
            hsaGradeRightsCommodityRuleMapper.deleteByEquitiesCardGuid(gradeEquitieGuidList);
        }
    }

    private List<HsaBusinessEquities> getCardBusinessEquities(MemberEquitiesQO memberEquitiesQO) {
        return hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.CARD_EQUITIES.getCode())
                .in(HsaBusinessEquities::getMemberGradeInfoGuid, memberEquitiesQO.getBusinessGuid())
                .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode())
        );
    }

    private HsaBusinessEquities getHsaBusinessEquities(MemberEquitiesQO memberEquitiesQO, GradeEquitiesQO gradeEquitiesQO) {
        HsaBusinessEquities hsaBusinessEquities = new HsaBusinessEquities();
        //编辑权益
        if (StringUtils.isNotEmpty(gradeEquitiesQO.getGuid())) {
            hsaBusinessEquities.setGuid(gradeEquitiesQO.getGuid());
        } else {
            hsaBusinessEquities.setGuid(guidGeneratorUtil.getStringGuid(HsaBusinessEquities.class.getSimpleName()));
        }
        //卡权益，直接生效
        final boolean card = Objects.equals(memberEquitiesQO.getBusinessType(), BusinessTypeEnum.CARD_EQUITIES.getCode());
        int effective = card ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode();
        hsaBusinessEquities.setEffective(effective);

        hsaBusinessEquities.setMemberGradeInfoGuid(memberEquitiesQO.getBusinessGuid());
        hsaBusinessEquities.setEquitiesType(gradeEquitiesQO.getEquitiesType());
        hsaBusinessEquities.setEquitiesRuleType(gradeEquitiesQO.getEquitiesRuleType());
        hsaBusinessEquities.setEquitiesGuid(gradeEquitiesQO.getEquitiesGuid());
        HsaEquitiesInfo hsaEquitiesInfo = hsaEquitiesInfoMapper.queryByGuid(gradeEquitiesQO.getEquitiesGuid());
        hsaBusinessEquities.setEquitiesName(hsaEquitiesInfo.getEquitiesName());
        hsaBusinessEquities.setEquitiesNumber(hsaEquitiesInfo.getEquitiesNumber());
        hsaBusinessEquities.setGradeValueDoubleNumber(gradeEquitiesQO.getGradeValueDoubleNumber());
        hsaBusinessEquities.setDoubleCountLimited(gradeEquitiesQO.getDoubleCountLimited());
        hsaBusinessEquities.setDoubleCountUpperLimit(gradeEquitiesQO.getDoubleCountUpperLimit());
        hsaBusinessEquities.setSingleDoubleLimited(gradeEquitiesQO.getSingleDoubleLimited());
        hsaBusinessEquities.setSingleDoubleUpperLimit(gradeEquitiesQO.getSingleDoubleUpperLimit());
        hsaBusinessEquities.setTotalDoubleCountLimited(gradeEquitiesQO.getTotalDoubleCountLimited());
        hsaBusinessEquities.setTotalDoubleCountUpperLimit(gradeEquitiesQO.getTotalDoubleCountUpperLimit());
        hsaBusinessEquities.setTotalGiveNumber(gradeEquitiesQO.getTotalGiveNumber());
        hsaBusinessEquities.setSetPeriod(gradeEquitiesQO.getSetPeriod());
        hsaBusinessEquities.setPeriodGiveNumber(gradeEquitiesQO.getPeriodGiveNumber());
        hsaBusinessEquities.setDiscountDynamics(gradeEquitiesQO.getDiscountDynamics());
        hsaBusinessEquities.setSingleDiscountsLimited(gradeEquitiesQO.getSingleDiscountsLimited());
        hsaBusinessEquities.setSingleDiscountsLimitedAmount(gradeEquitiesQO.getSingleDiscountsLimitedAmount());
        hsaBusinessEquities.setPeriodDiscountLimited(gradeEquitiesQO.getPeriodDiscountLimited());
        hsaBusinessEquities.setPeriodDiscountType(gradeEquitiesQO.getPeriodDiscountType());
        hsaBusinessEquities.setTotalDiscountLimited(gradeEquitiesQO.getTotalDiscountLimited());
        hsaBusinessEquities.setTotalDiscountLimitedAmount(gradeEquitiesQO.getTotalDiscountLimitedAmount());
        hsaBusinessEquities.setDiscountsSuperposition(gradeEquitiesQO.getDiscountsSuperposition());
        hsaBusinessEquities.setEquitiesEffectiveDateLimited(gradeEquitiesQO.getEquitiesEffectiveDateLimited());
        hsaBusinessEquities.setEquitiesTimeLimitedType(gradeEquitiesQO.getEquitiesTimeLimitedType());
        hsaBusinessEquities.setEquitiesTimeLimitedJson(gradeEquitiesQO.getEquitiesTimeLimitedJson());
        hsaBusinessEquities.setApplyGoodsType(gradeEquitiesQO.getApplyGoodsType());
        hsaBusinessEquities.setApplyTerminal(gradeEquitiesQO.getApplyTerminal());
        hsaBusinessEquities.setApplyTerminalJson(gradeEquitiesQO.getApplyTerminalJson());
        hsaBusinessEquities.setApplyBusiness(gradeEquitiesQO.getApplyBusiness());
        hsaBusinessEquities.setApplyBusinessJson(gradeEquitiesQO.getApplyBusinessJson());
        hsaBusinessEquities.setApplyChannel(gradeEquitiesQO.getApplyChannel());
        hsaBusinessEquities.setApplyChannelJson(gradeEquitiesQO.getApplyChannelJson());
        hsaBusinessEquities.setGmtCreate(LocalDateTime.now());
        hsaBusinessEquities.setGmtModified(LocalDateTime.now());
        hsaBusinessEquities.setIsDelete(BooleanEnum.FALSE.getCode());
        hsaBusinessEquities.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

        hsaBusinessEquities.setBusinessType(memberEquitiesQO.getBusinessType());
        hsaBusinessEquities.setApplicableAllStore(gradeEquitiesQO.getApplicableAllStore());
        return hsaBusinessEquities;
    }

    private void saveHsaStoreRule(GradeEquitiesQO gradeEquitiesQO, HsaBusinessEquities hsaBusinessEquities) {
        if (CollUtil.isNotEmpty(gradeEquitiesQO.getEquitiesStoreRuleQOList())) {
            List<HsaEquitiesStoreRule> equitiesStoreRuleInfoList = Lists.newArrayList();
            //卡权益，直接生效
            final boolean card = Objects.equals(gradeEquitiesQO.getBusinessType(), BusinessTypeEnum.CARD_EQUITIES.getCode());
            int effective = card ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode();
            for (EquitiesStoreRuleQO equitiesStoreRuleQO : gradeEquitiesQO.getEquitiesStoreRuleQOList()) {
                HsaEquitiesStoreRule equitiesStoreRuleInfo = new HsaEquitiesStoreRule();
                equitiesStoreRuleInfo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                        .setEquitiesGuid(hsaBusinessEquities.getGuid())
                        .setEquitiesVersionId(hsaBusinessEquities.getVersionId())
                        .setTime(equitiesStoreRuleQO.getTime())
                        .setAddress(equitiesStoreRuleQO.getAddress())
                        .setStoreGuid(equitiesStoreRuleQO.getStoreGuid())
                        .setStoreName(equitiesStoreRuleQO.getStoreName())
                        .setStoreLogo(equitiesStoreRuleQO.getStoreLogo())
                        .setAddressPoint(equitiesStoreRuleInfo.getAddressPoint())
                        .setIsDelete(BooleanEnum.FALSE.getCode())
                        .setEffective(effective)
                        .setSystem(equitiesStoreRuleQO.getSystem())
                        .setStoreNumber(equitiesStoreRuleQO.getStoreNumber());
                equitiesStoreRuleInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaEquitiesStoreRule.class.getSimpleName()));

                equitiesStoreRuleInfoList.add(equitiesStoreRuleInfo);
            }

            hsaEquitiesStoreRuleService.saveBatch(equitiesStoreRuleInfoList);
        }
    }

    private void setPeriodDiscountType(GradeEquitiesQO gradeEquitiesQO, HsaBusinessEquities hsaBusinessEquities) {
        hsaBusinessEquities.setPeriodDiscountLimited(gradeEquitiesQO.getPeriodUseCountLimited());
        if (Objects.isNull(gradeEquitiesQO.getPeriodUseCountType()) || Objects.isNull(gradeEquitiesQO.getPeriodUseCount())) {
            return;
        }
        List<PeriodDiscountVO> periodDiscountList = new ArrayList<>();

        PeriodDiscountVO periodDiscountVO = new PeriodDiscountVO();
        periodDiscountVO.setType(gradeEquitiesQO.getPeriodUseCountType().toString());
        periodDiscountVO.setValue(gradeEquitiesQO.getPeriodUseCount().toString());
        periodDiscountList.add(periodDiscountVO);

        hsaBusinessEquities.setPeriodDiscountType(JSON.toJSONString(periodDiscountList));
    }

    @Override
    public Long isDoubleGrowthValue(DoubleValueRequest request) {
        log.info("翻倍成长值权益请求参数：{}", JSON.toJSONString(request));
        Long doubleValue = null;

        //历史问题 暂时方案
        if (Objects.isNull(request.getBusinessType())) {
            request.setBusinessType(BusinessTypeEnum.GRADE_EQUITIES.getCode());
        }
        //查询当前会员是否拥有翻倍成长值权益
        HsaBusinessEquities hsaBusinessEquities;
        if (request.getBusinessType() == BusinessTypeEnum.GRADE_EQUITIES.getCode()) {
            hsaBusinessEquities = hsaBusinessEquitiesMapper.selectOne(
                    new LambdaQueryWrapper<HsaBusinessEquities>()
                            .eq(HsaBusinessEquities::getGuid, request.getGradeEquitiesGuid())
                            .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_INTEGRAL.getCode())
                            .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
                            .eq(HsaBusinessEquities::getBusinessType, request.getBusinessType())
                            .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1));
        } else {
            hsaBusinessEquities = hsaBusinessEquitiesMapper.selectOne(
                    new LambdaQueryWrapper<HsaBusinessEquities>()
                            .eq(HsaBusinessEquities::getGuid, request.getGradeEquitiesGuid())
                            .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_INTEGRAL.getCode())
                            .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
                            .eq(HsaBusinessEquities::getBusinessType, request.getBusinessType()));
        }

        log.info("当前会员翻倍成长值权益hsaGradeEquities:{}", JSON.toJSONString(hsaBusinessEquities));
        if (Objects.isNull(hsaBusinessEquities)) {
            return doubleValue;
        }
        //查询当前会员是否使用过翻倍成长值记录
        HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = hsaMemberEquitiesReceiveRecordMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getOperSubjectGuid, request.getOperSubjectGuid())
                        .eq(HsaMemberEquitiesReceiveRecord::getMemberInfoGuid, request.getMemberInfoGuid())
                        .isNull(HsaMemberEquitiesReceiveRecord::getGiveGrowthValueNumber)
                        .eq(HsaMemberEquitiesReceiveRecord::getType, BooleanEnum.TRUE.getCode())
                        .eq(HsaMemberEquitiesReceiveRecord::getGradeEquitiesGuid, request.getGradeEquitiesGuid()));
        doubleValue = this.exec(request, hsaBusinessEquities, equitiesReceiveRecord);
        if (Objects.isNull(doubleValue)) {
            return doubleValue;
        }
        log.info("equitiesReceiveRecord:{}", JSON.toJSONString(equitiesReceiveRecord));
        if (Objects.nonNull(equitiesReceiveRecord)) {
            equitiesReceiveRecord.setDoubleValueCount(equitiesReceiveRecord.getDoubleValueCount() + NumberConstant.NUMBER_1);

            Integer totalDoubleUpperValue = equitiesReceiveRecord.getTotalDoubleUpperValue();
            equitiesReceiveRecord.setTotalDoubleUpperValue((int) (totalDoubleUpperValue + doubleValue));
            hsaMemberEquitiesReceiveRecordMapper.updateByGuid(equitiesReceiveRecord);
        } else {
            HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
            String guid = guidGeneratorUtil.getStringGuid(HsaMemberGradeChangeDetail.class.getSimpleName());
            hsaMemberEquitiesReceiveRecord.setGuid(guid);
            hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid(hsaBusinessEquities.getGuid());
            hsaMemberEquitiesReceiveRecord.setMemberGradeGuid(hsaBusinessEquities.getMemberGradeInfoGuid());
            hsaMemberEquitiesReceiveRecord.setMemberInfoGuid(request.getMemberInfoGuid());
            hsaMemberEquitiesReceiveRecord.setDoubleValueCount(NumberConstant.NUMBER_1);
            hsaMemberEquitiesReceiveRecord.setTotalDoubleUpperValue(Math.toIntExact(doubleValue));
            hsaMemberEquitiesReceiveRecord.setOperSubjectGuid(request.getOperSubjectGuid());
            hsaMemberEquitiesReceiveRecord.setType(BooleanEnum.TRUE.getCode());
            hsaMemberEquitiesReceiveRecordMapper.insert(hsaMemberEquitiesReceiveRecord);
            log.info("hsaMemberEquitiesReceiveRecord：{}", hsaMemberEquitiesReceiveRecord);
        }
        return doubleValue;

    }

    /**
     * 执行可以翻倍的成长值
     *
     * @param request               请求参数
     * @param hsaBusinessEquities   权益信息
     * @param equitiesReceiveRecord 会员使用的权益记录
     * @return 可以翻倍的成长值
     */
    public Long exec(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                     HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        return abstractHandler.filter(request, hsaBusinessEquities, equitiesReceiveRecord);
    }

    /**
     * 保存适用商品信息
     *
     * @param gradeEquitiesQO growthValueTaskQO
     */
    private void saveHsaGrowthCommodityRule(GradeEquitiesQO gradeEquitiesQO, HsaBusinessEquities hsaBusinessEquities) {
        List<HsaGradeRightsCommodityRule> hsaGrowthValueCommodityRules = Lists.newArrayList();
        //卡权益，直接生效
        final boolean card = Objects.equals(gradeEquitiesQO.getBusinessType(), BusinessTypeEnum.CARD_EQUITIES.getCode());
        int effective = card ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode();
        if (gradeEquitiesQO.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode() && (CollUtil.isNotEmpty(gradeEquitiesQO.getGradeCommodityBaseQOList()))) {
            for (GradeCommodityBaseQO gradeCommodityBaseQO : gradeEquitiesQO.getGradeCommodityBaseQOList()) {
                HsaGradeRightsCommodityRule rule = new HsaGradeRightsCommodityRule();
                rule.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueCommodityRule.class.getSimpleName()))
                        .setCommodityId(gradeCommodityBaseQO.getCommodityId())
                        .setGradeEquitiesGuid(hsaBusinessEquities.getGuid())
                        .setGradeEquitiesVersionId(hsaBusinessEquities.getVersionId())
                        .setCommodityName(gradeCommodityBaseQO.getCommodityName())
                        .setCommodityCode(gradeCommodityBaseQO.getCommodityCode())
                        .setCommodityPrice(gradeCommodityBaseQO.getBasePrice())
                        .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                        .setComboType(gradeCommodityBaseQO.getCommodityComboType())
                        .setBusinessType(gradeCommodityBaseQO.getBusinessType())
                        .setIsDelete(BooleanEnum.FALSE.getCode())
                        .setEffective(effective);
                hsaGrowthValueCommodityRules.add(rule);
            }

        }
        if (CollUtil.isNotEmpty(hsaGrowthValueCommodityRules)) {
            hsaGradeRightsCommodityRuleService.saveBatch(hsaGrowthValueCommodityRules);
        }
    }


    /**
     * 获取权益信息
     *
     * @param guid
     * @return
     */
    @Override
    public List<EquitiesDetailVO> getEquitiesVOList(String guid) {

        List<HsaBusinessEquities> hsaBusinessEquitiesList = hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getMemberGradeInfoGuid, guid)
                .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode()));

        if (CollUtil.isEmpty(hsaBusinessEquitiesList)) {
            return new ArrayList<>();
        }

        // 目前所有商品会员价权益公用一个商品配置信息,所以判断商品会员价权益是否已经配置只需要判断表中是否有数据
        // 后续需求变更为每一个商品会员价独立配置商品，那么这里逻辑需要改变
        Boolean isSetCommodityPrice = Optional.ofNullable(hsaCommodityMemberPriceMapper.selectCount(null)).orElse(NumberConstant.NUMBER_0) > 0;
        List<EquitiesDetailVO> equitiesDetailVOS = new ArrayList<>();
        List<GradeCommodityBaseQO> gradeCommodityBaseQOList = new ArrayList<>();
        for (HsaBusinessEquities hsaBusinessEquities : hsaBusinessEquitiesList) {
            EquitiesDetailVO gradeEquitiesVO = new EquitiesDetailVO();
            gradeEquitiesVO.setGuid(hsaBusinessEquities.getGuid());
            gradeEquitiesVO.setMemberGradeInfoGuid(hsaBusinessEquities.getMemberGradeInfoGuid());
            gradeEquitiesVO.setEquitiesType(hsaBusinessEquities.getEquitiesType());
            gradeEquitiesVO.setEquitiesRuleType(hsaBusinessEquities.getEquitiesRuleType());
            gradeEquitiesVO.setEquitiesGuid(hsaBusinessEquities.getEquitiesGuid());
            gradeEquitiesVO.setGradeValueDoubleNumber(hsaBusinessEquities.getGradeValueDoubleNumber());
            gradeEquitiesVO.setDoubleCountLimited(hsaBusinessEquities.getDoubleCountLimited());
            gradeEquitiesVO.setDoubleCountUpperLimit(hsaBusinessEquities.getDoubleCountUpperLimit());
            gradeEquitiesVO.setSingleDoubleLimited(hsaBusinessEquities.getSingleDoubleLimited());
            gradeEquitiesVO.setSingleDoubleUpperLimit(hsaBusinessEquities.getSingleDoubleUpperLimit());
            gradeEquitiesVO.setTotalDoubleCountLimited(hsaBusinessEquities.getTotalDoubleCountLimited());
            gradeEquitiesVO.setTotalDoubleCountUpperLimit(hsaBusinessEquities.getTotalDoubleCountUpperLimit());
            gradeEquitiesVO.setTotalGiveNumber(hsaBusinessEquities.getTotalGiveNumber());
            gradeEquitiesVO.setSetPeriod(hsaBusinessEquities.getSetPeriod());
            gradeEquitiesVO.setPeriodGiveNumber(hsaBusinessEquities.getPeriodGiveNumber());
            gradeEquitiesVO.setDiscountDynamics(hsaBusinessEquities.getDiscountDynamics());
            gradeEquitiesVO.setSingleDiscountsLimited(hsaBusinessEquities.getSingleDiscountsLimited());
            gradeEquitiesVO.setSingleDiscountsLimitedAmount(hsaBusinessEquities.getSingleDiscountsLimitedAmount());
            gradeEquitiesVO.setPeriodDiscountLimited(hsaBusinessEquities.getPeriodDiscountLimited());
            gradeEquitiesVO.setPeriodDiscountType(hsaBusinessEquities.getPeriodDiscountType());
            gradeEquitiesVO.setTotalDiscountLimited(hsaBusinessEquities.getTotalDiscountLimited());
            gradeEquitiesVO.setTotalDiscountLimitedAmount(hsaBusinessEquities.getTotalDiscountLimitedAmount());
            gradeEquitiesVO.setDiscountsSuperposition(hsaBusinessEquities.getDiscountsSuperposition());
            gradeEquitiesVO.setEquitiesEffectiveDateLimited(hsaBusinessEquities.getEquitiesEffectiveDateLimited());
            gradeEquitiesVO.setEquitiesTimeLimitedType(hsaBusinessEquities.getEquitiesTimeLimitedType());
            gradeEquitiesVO.setEquitiesTimeLimitedJson(hsaBusinessEquities.getEquitiesTimeLimitedJson());
            gradeEquitiesVO.setApplyGoodsType(hsaBusinessEquities.getApplyGoodsType());
            gradeEquitiesVO.setApplyTerminal(hsaBusinessEquities.getApplyTerminal());
            gradeEquitiesVO.setApplyTerminalJson(hsaBusinessEquities.getApplyTerminalJson());
            gradeEquitiesVO.setApplyBusiness(hsaBusinessEquities.getApplyBusiness());
            gradeEquitiesVO.setApplyBusinessJson(hsaBusinessEquities.getApplyBusinessJson());
            gradeEquitiesVO.setApplyChannel(hsaBusinessEquities.getApplyChannel());
            gradeEquitiesVO.setApplyChannelJson(hsaBusinessEquities.getApplyChannelJson());
            gradeEquitiesVO.setApplicableAllStore(hsaBusinessEquities.getApplicableAllStore());

            gradeEquitiesVO.setProductShowSet(isSetCommodityPrice);
            gradeEquitiesVO.setProductShowSetCopy(isSetCommodityPrice);

            setPeriodUseCount(hsaBusinessEquities, gradeEquitiesVO);
            getGradeRightCommodityRule(gradeCommodityBaseQOList, gradeEquitiesVO);

            gradeEquitiesVO.setEquitiesStoreRuleVOList(hsaEquitiesStoreRuleService.getEquitiesStoreRule(gradeEquitiesVO.getGuid(), BooleanEnum.FALSE.getCode()));
            equitiesDetailVOS.add(gradeEquitiesVO);
        }
        return equitiesDetailVOS;
    }

    @Override
    public List<String> getEquitiesName(String businessGuid) {
        List<HsaBusinessEquities> hsaBusinessEquities = getHsaBusinessEquities(businessGuid);
        if (CollUtil.isEmpty(hsaBusinessEquities)) {
            return Collections.emptyList();
        }
        return hsaBusinessEquities.stream().map(HsaBusinessEquities::getEquitiesName).collect(Collectors.toList());
    }

    private List<HsaBusinessEquities> getHsaBusinessEquities(String businessGuid) {
        return hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .in(HsaBusinessEquities::getMemberGradeInfoGuid, businessGuid)
                .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    @Override
    public List<GradeEquitiesPreviewVO> getEquitiesCard(String businessGuid, String memberInfoGuid) {
        List<GradeEquitiesPreviewVO> gradeEquitiesPreviewList = new ArrayList<>();
        List<HsaBusinessEquities> hsaBusinessEquities = getHsaBusinessEquities(businessGuid);
        hsaMemberGradeInfoService.getEquities(memberInfoGuid, gradeEquitiesPreviewList, BooleanEnum.FALSE.getCode(), hsaBusinessEquities);
        return gradeEquitiesPreviewList;
    }

    @Override
    public void sendExpireCardEquities(SendCardEquitiesQO sendCardEquitiesQO) {

        List<HsaBusinessEquities> hsaBusinessEquities = baseMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.MEMBER_PRICE.getCode())
                .in(HsaBusinessEquities::getMemberGradeInfoGuid, sendCardEquitiesQO.getCardGuid()));
        log.info("过期卡删除权益：{}", JSON.toJSONString(hsaBusinessEquities));
        //待删除的权益
        if (CollUtil.isNotEmpty(hsaBusinessEquities)) {
            List<String> guid = hsaBusinessEquities.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList());
            BusinessCardQO businessCardQO = new BusinessCardQO();
            businessCardQO.setDeleteEquitiesGuid(guid);
            settlementMemberSupport.sendCardDiscount(businessCardQO);
        }
    }

    /**
     * 定时周期触发会员卡权益赠送
     *
     * @param giveCardRightsJob giveCardRightsJob
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giveCardRightsJob(GrantMemberCouponPackageEvent giveCardRightsJob) {

        String current = DateUtil.getTmpDate(giveCardRightsJob.getDate(), StringConstant.FORMAT);

        if (Boolean.TRUE.equals(cacheService.setCardLock(StringConstant.XXL_JOB_CARD_NOTICE + current))) {
            log.info("执行时间重复跳出，当前时间：{}", current);
            return;
        }

        grantCardRights(new CardGrantEquitiesDTO().setCardGuid(StringConstant.CARD_JOB));

        cacheService.delete(StringConstant.XXL_JOB_CARD_NOTICE + current);
    }

    /**
     * 处理发放周期赠送
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantCardRights(CardGrantEquitiesDTO cardGrantEquitiesDTO) {
        //获取锁
        RLock lock = redissonClient.getLock(StringConstant.CARD_RIGHTS + cardGrantEquitiesDTO.getCardGuid());
        try {
            //30秒等待
            if (!lock.tryLock(30, 30, TimeUnit.SECONDS)) {
                throw new MemberBaseException("会员卡发放周期赠送锁请求超时参数：" + JSON.toJSONString(cardGrantEquitiesDTO));
            }
            processor(cardGrantEquitiesDTO);
        } catch (Exception e) {
            log.error("会员卡发放周期赠送锁请求发生异常，请求参数{},error:{}", JSON.toJSONString(cardGrantEquitiesDTO), e.getMessage());
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantFeignCardRights(CardEquitiesQO cardGrantEquitiesDTO) {

        log.info("指定用户时间开卡赠送权益，会员持卡数据：{}", JSON.toJSONString(cardGrantEquitiesDTO));
        if (CollUtil.isNotEmpty(cardGrantEquitiesDTO.getSendMemberEquityCard())) {
            List<HsaMemberInfoCard> hsaMemberInfoCardList = Lists.newArrayList();
            for (SendMemberEquityCard sendMemberEquityCard : cardGrantEquitiesDTO.getSendMemberEquityCard()) {
                HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
                BeanUtils.copyProperties(sendMemberEquityCard, hsaMemberInfoCard);
                hsaMemberInfoCardList.add(hsaMemberInfoCard);
            }

            Map<String, List<HsaMemberInfoCard>> hsaMap = hsaMemberInfoCardList.stream()
                    .collect(Collectors.groupingBy(HsaMemberInfoCard::getCardGuid));
            hsaMap.forEach((key, value) -> {
                CardGrantEquitiesDTO equitiesDTO = new CardGrantEquitiesDTO();
                equitiesDTO.setCardGuid(key);
                equitiesDTO.setHsaMemberInfoCardList(value);
                this.grantCardRights(equitiesDTO);
            });
        }


    }

    private void processor(CardGrantEquitiesDTO cardGrantEquitiesDTO) {
        CardBusinessEquitiesDTO cardBusinessEquitiesDTO = new CardBusinessEquitiesDTO();
        List<HsaBusinessEquities> hsaBusinessEquitiesList;
        String cardGuid = cardGrantEquitiesDTO.getCardGuid();

        List<HsaCardBaseInfo> hsaCardBaseInfoList;
        List<HsaMemberInfoCard> hsaMemberInfoCards = cardGrantEquitiesDTO.getHsaMemberInfoCardList();
        //获取正常的会员卡
        if (StringUtils.isEmpty(cardGuid) || cardGuid.equals(StringConstant.CARD_JOB)) {
            hsaCardBaseInfoList = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                    .eq(HsaCardBaseInfo::getCardStatus, CardStatusEnum.ENABLE.getCode()));

            cardBusinessEquitiesDTO.setIsDisposable(BooleanEnum.FALSE.getCode());
        } else {
            hsaCardBaseInfoList = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                    .eq(HsaCardBaseInfo::getCardStatus, CardStatusEnum.ENABLE.getCode())
                    .eq(HsaCardBaseInfo::getGuid, cardGuid));

            cardBusinessEquitiesDTO.setIsDisposable(BooleanEnum.TRUE.getCode());
        }

        if (CollUtil.isEmpty(hsaCardBaseInfoList)) {
            log.info("未获取到卡数据");
            return;
        }
        List<String> cardGuidList = hsaCardBaseInfoList.stream().map(HsaCardBaseInfo::getGuid).collect(Collectors.toList());

        //获取全表权益
        if (cardBusinessEquitiesDTO.getIsDisposable() == BooleanEnum.TRUE.getCode()) {
            hsaBusinessEquitiesList = hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                    .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.CARD_EQUITIES.getCode())
                    .in(HsaBusinessEquities::getSetPeriod, DataUnitEnum.FOREVER.getCode())
                    .in(HsaBusinessEquities::getMemberGradeInfoGuid, cardGuidList)
                    .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode()));
        } else {
            hsaBusinessEquitiesList = hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                    .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.CARD_EQUITIES.getCode())
                    .in(HsaBusinessEquities::getSetPeriod, DateUtil.getPeriodType())
                    .in(HsaBusinessEquities::getMemberGradeInfoGuid, cardGuidList)
                    .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode()));
        }

        if (CollUtil.isEmpty(hsaBusinessEquitiesList)) {
            log.info("未获取到卡权益数据");
            return;
        }
        List<String> memberCardInfoGuid = hsaBusinessEquitiesList.stream().map(HsaBusinessEquities::getMemberGradeInfoGuid).collect(Collectors.toList());

        //获取持卡数据
        if (CollUtil.isEmpty(hsaMemberInfoCards)) {
            hsaMemberInfoCards = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getCardGuid, memberCardInfoGuid)
                    .and(wq -> wq.eq(HsaMemberInfoCard::getPhysicalCardState, PhysicalCardStateEnum.NORMAL.getCode())
                            .or().eq(HsaMemberInfoCard::getElectronicCardState, EletronicCardStateEnum.NORMAL.getCode())));
        }

        if (CollUtil.isEmpty(hsaMemberInfoCards)) {
            log.info("未获取到持卡数据");
            return;
        }

        //分组卡信息
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoList.stream().filter(in -> memberCardInfoGuid.contains(in.getGuid()))
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        //分组持卡人
        Map<String, List<HsaMemberInfoCard>> hsaMemberInfoMap = hsaMemberInfoCards.stream()
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getCardGuid));

        Set<String> memberInfoGuidList = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getMemberInfoGuid).collect(Collectors.toSet());

        //获取对应的会员等级
        setGradeInfoAndCardBaseInfo(cardBusinessEquitiesDTO, hsaCardBaseInfoMap, memberInfoGuidList);

        //获取会员已有成长值和积分
        setMemberGradeRelation(cardBusinessEquitiesDTO, memberInfoGuidList);

        //处理权益赠送
        dealForBusinessEquities(hsaBusinessEquitiesList, cardBusinessEquitiesDTO, hsaMemberInfoMap);

        //持久化记录
        saveDetailedRecords(cardBusinessEquitiesDTO);

        //更新成长值/积分
        updateMemberValue(cardBusinessEquitiesDTO.getIntegralRealityValueTotalMap(), EquitiesReceiveTypeEnum.GIVE_INTEGRAL.getCode());
        updateMemberValue(cardBusinessEquitiesDTO.getGrowthRealityValueTotalMap(), EquitiesReceiveTypeEnum.GIVE_GROWTH_VALUE.getCode());
    }


    private void updateMemberValue(Map<String, Integer> valueTotalMap, Integer type) {
        if (CollUtil.isEmpty(valueTotalMap)) {
            return;
        }
        List<MemberGrowthValueRelationVO> growthValueRelationVOS = Lists.newArrayList();
        Set<Map.Entry<String, Integer>> entrySet = valueTotalMap.entrySet();
        for (Map.Entry<String, Integer> entry : entrySet) {
            String memberGuid = entry.getKey();
            MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
            memberGrowthValueRelationVO.setMemberGuid(memberGuid);
            memberGrowthValueRelationVO.setGrowthValue(valueTotalMap.get(memberGuid));
            growthValueRelationVOS.add(memberGrowthValueRelationVO);
        }
        if (type == EquitiesReceiveTypeEnum.GIVE_GROWTH_VALUE.getCode()) {
            hsaOperationMemberInfoMapper.batchCardUpdateGrowthValue(growthValueRelationVOS);
        } else {
            hsaOperationMemberInfoMapper.batchCardUpdateIntegralValue(growthValueRelationVOS);
        }


    }


    private void saveDetailedRecords(CardBusinessEquitiesDTO cardBusinessEquitiesDTO) {
        log.info("赠送成长值明细==============>" + JSON.toJSONString(cardBusinessEquitiesDTO.getHsaGrowthValueDetails()));
        if (CollUtil.isNotEmpty(cardBusinessEquitiesDTO.getHsaGrowthValueDetails())) {
            hsaGrowthValueDetailService.saveBatch(cardBusinessEquitiesDTO.getHsaGrowthValueDetails());
        }

        log.info("记录会员已经使用的权益信息==============>" + JSON.toJSONString(cardBusinessEquitiesDTO.getHsaMemberEquitiesReceiveRecords()));
        if (CollUtil.isNotEmpty(cardBusinessEquitiesDTO.getHsaMemberEquitiesReceiveRecords())) {
            hsaMemberEquitiesReceiveRecordService.saveBatch(cardBusinessEquitiesDTO.getHsaMemberEquitiesReceiveRecords());
        }

        log.info("赠送积分值明细==============>" + JSON.toJSONString(cardBusinessEquitiesDTO.getHsaIntegralDetails()));
        if (CollUtil.isNotEmpty(cardBusinessEquitiesDTO.getHsaIntegralDetails())) {
            hsaIntegralDetailService.saveBatch(cardBusinessEquitiesDTO.getHsaIntegralDetails());
        }
    }

    private void dealForBusinessEquities(List<HsaBusinessEquities> hsaBusinessEquitiesList, CardBusinessEquitiesDTO cardBusinessEquitiesDTO, Map<String, List<HsaMemberInfoCard>> hsaMemberInfoMap) {
        for (HsaBusinessEquities hsaBusinessEquities : hsaBusinessEquitiesList) {
            //适配数据库类型
            int type = hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode() ?
                    EquitiesReceiveTypeEnum.GIVE_INTEGRAL.getCode() : EquitiesReceiveTypeEnum.GIVE_GROWTH_VALUE.getCode();

            //获取历史权益记录
            Map<String, Integer> memberGrowthValueMap = getEquitiesReceiveRecord(hsaBusinessEquities, type);

            //持有此会员卡集合
            List<HsaMemberInfoCard> memberInfoCards = hsaMemberInfoMap.get(hsaBusinessEquities.getMemberGradeInfoGuid());

            //循环处理会员赠送
            if (CollUtil.isNotEmpty(memberInfoCards)) {
                List<String> memberGuidList = memberInfoCards.stream().
                        map(HsaMemberInfoCard::getMemberInfoGuid).
                        filter(StringUtils::isNotBlank).collect(Collectors.toList());
                forMemberGuidList(cardBusinessEquitiesDTO, hsaBusinessEquities, type, memberGrowthValueMap, memberGuidList);
            }
        }
    }

    private void setMemberGradeRelation(CardBusinessEquitiesDTO cardBusinessEquitiesDTO, Set<String> memberInfoGuidList) {
        List<MemberGradeRelationVO> gradeRelationVOList = hsaOperationMemberInfoMapper.selectMemberGuidByGuid(memberInfoGuidList);
        if (CollUtil.isNotEmpty(gradeRelationVOList)) {
            Map<String, MemberGradeRelationVO> memberGradeRelationVOMap = gradeRelationVOList.stream()
                    .collect(Collectors.toMap(MemberGradeRelationVO::getMemberGuid, Function.identity(), (entity1, entity2) -> entity1));
            cardBusinessEquitiesDTO.setMemberGradeRelationVOMap(memberGradeRelationVOMap);

            Map<String, Integer> integralValueTotalMap = gradeRelationVOList.stream().collect(Collectors.toMap(MemberGradeRelationVO::getMemberGuid,
                    MemberGradeRelationVO::getIntegralValue));

            Map<String, Integer> growthValueTotalMap = gradeRelationVOList.stream().collect(Collectors.toMap(MemberGradeRelationVO::getMemberGuid,
                    MemberGradeRelationVO::getGrowthValue));

            cardBusinessEquitiesDTO.setGrowthValueTotalMap(growthValueTotalMap);
            cardBusinessEquitiesDTO.setIntegralValueTotalMap(integralValueTotalMap);
        }
    }

    private void setGradeInfoAndCardBaseInfo(CardBusinessEquitiesDTO cardBusinessEquitiesDTO, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, Set<String> memberInfoGuidList) {
        LambdaQueryWrapper<HsaOperationMemberInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(HsaOperationMemberInfo::getMemberGradeInfoGuid, HsaOperationMemberInfo::getGuid)
                .in(HsaOperationMemberInfo::getGuid, memberInfoGuidList);


        List<HsaOperationMemberInfo> hsaOperationMembers = hsaOperationMemberInfoMapper.selectList(queryWrapper);
        Map<String, String> memberGradeInfoGuidMap = hsaOperationMembers.stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, HsaOperationMemberInfo::getMemberGradeInfoGuid, (entity1, entity2) -> entity1));
        cardBusinessEquitiesDTO.setMemberGradeInfoGuidMap(memberGradeInfoGuidMap);

        //获取对应等级信息
        Set<String> memberGradeInfoGuidSet = hsaOperationMembers.stream().map(HsaOperationMemberInfo::getMemberGradeInfoGuid).collect(Collectors.toSet());
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .in(HsaMemberGradeInfo::getGuid, memberGradeInfoGuidSet)
                        .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), MemberGradeStateEnum.REMOVE_BUT_VALID.getCode()))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        cardBusinessEquitiesDTO.setHsaMemberGradeInfoMap(hsaMemberGradeInfoMap);


        cardBusinessEquitiesDTO.setHsaCardBaseInfoMap(hsaCardBaseInfoMap);
    }

    private Map<String, Integer> getEquitiesReceiveRecord(HsaBusinessEquities hsaBusinessEquities, Integer type) {
        List<MemberGrowthValueRelationVO> valueRelationList = hsaMemberEquitiesReceiveRecordMapper
                .selectNewAllGrowthValue(
                        hsaBusinessEquities.getGuid(), type);

        return valueRelationList
                .stream()
                .collect(Collectors.toMap(MemberGrowthValueRelationVO::getMemberGuid, MemberGrowthValueRelationVO::getGrowthValue));
    }


    private void forMemberGuidList(CardBusinessEquitiesDTO cardBusinessEquitiesDTO,
                                   HsaBusinessEquities hsaGradeEquities,
                                   int type,
                                   Map<String, Integer> memberGrowthValueMap,
                                   List<String> memberGuidList) {

        if (CollUtil.isNotEmpty(memberGuidList)) {
            for (String memberGuid : memberGuidList) {
                int giveGrowthValue = BigDecimal.ROUND_UP;
                //获取当前会员当前权益下已经赠送的成长值
                int growthValue = ObjectUtil.objToInt(memberGrowthValueMap.get(memberGuid));
                if (cardBusinessEquitiesDTO.getIsDisposable() == BooleanEnum.TRUE.getCode()) {
                    //若存在则已赠送过
                    giveGrowthValue = getGrowthValue(hsaGradeEquities, growthValue, giveGrowthValue);
                } else {
                    //已经送超过了就不赠送了
                    if (growthValue >= hsaGradeEquities.getTotalGiveNumber()) {
                        continue;
                    }
                    giveGrowthValue = hsaGradeEquities.getPeriodGiveNumber();
                    //累计赠送加上本次赠送 超过了设置的最多累计
                    giveGrowthValue = getGiveGrowthValue(hsaGradeEquities, growthValue, giveGrowthValue);
                }


                //组装记录明细
                if (giveGrowthValue > BigDecimal.ROUND_UP) {
                    dealDetailedRecords(cardBusinessEquitiesDTO, hsaGradeEquities, type, memberGuid, giveGrowthValue);
                }
            }
        }
    }

    private static int getGrowthValue(HsaBusinessEquities hsaGradeEquities, int growthValue, int giveGrowthValue) {
        if (growthValue == BigDecimal.ROUND_UP) {
            giveGrowthValue = hsaGradeEquities.getTotalGiveNumber();
        }
        return giveGrowthValue;
    }

    private static int getGiveGrowthValue(HsaBusinessEquities hsaGradeEquities, int growthValue, int giveGrowthValue) {
        if ((growthValue + giveGrowthValue) > hsaGradeEquities.getTotalGiveNumber()) {
            //计算出本次赠送数量
            giveGrowthValue = giveGrowthValue - (growthValue + giveGrowthValue - hsaGradeEquities.getTotalGiveNumber());
        }
        return giveGrowthValue;
    }

    private void dealDetailedRecords(CardBusinessEquitiesDTO cardBusinessEquitiesDTO,
                                     HsaBusinessEquities hsaBusinessEquities,
                                     int type,
                                     String memberGuid,
                                     int giveGrowthValue) {
        Map<String, HsaCardBaseInfo> hsaMemberGradeInfoMap = cardBusinessEquitiesDTO.getHsaCardBaseInfoMap();

        //记录更新数据
        Map<String, Integer> growthValueTotalMap = cardBusinessEquitiesDTO.getGrowthValueTotalMap();
        Map<String, Integer> integralValueTotalMap = cardBusinessEquitiesDTO.getIntegralValueTotalMap();


        //新增实际获得赠送值
        Map<String, Integer> integralRealityValueTotalMap = cardBusinessEquitiesDTO.getIntegralRealityValueTotalMap();
        Map<String, Integer> growthRealityValueTotalMap = cardBusinessEquitiesDTO.getGrowthRealityValueTotalMap();

        //记录保存明细
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = cardBusinessEquitiesDTO.getHsaGrowthValueDetails();
        List<HsaIntegralDetail> hsaIntegralDetailList = cardBusinessEquitiesDTO.getHsaIntegralDetails();
        List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords = cardBusinessEquitiesDTO.getHsaMemberEquitiesReceiveRecords();

        HsaCardBaseInfo hsaCardBaseInfo = hsaMemberGradeInfoMap.get(hsaBusinessEquities.getMemberGradeInfoGuid());

        //获取等级
        String gradeGuid = cardBusinessEquitiesDTO.getMemberGradeInfoGuidMap().get(memberGuid);
        HsaMemberGradeInfo hsaMemberGradeInfo = cardBusinessEquitiesDTO.getHsaMemberGradeInfoMap().get(gradeGuid);
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode()) {
            //获取会员赠送积分总额
            int totalGiveIntegralValue = ObjectUtil.objToInt(integralValueTotalMap.get(memberGuid)) + giveGrowthValue;
            integralValueTotalMap.put(memberGuid, totalGiveIntegralValue);
            hsaIntegralDetailList.add(addIntegralValueDetail(memberGuid, hsaCardBaseInfo, hsaBusinessEquities, hsaMemberGradeInfo, giveGrowthValue, totalGiveIntegralValue));

            //实际获得累计值
            int realityTotalGiveIntegralValue = ObjectUtil.objToInt(integralRealityValueTotalMap.get(memberGuid)) + giveGrowthValue;
            integralRealityValueTotalMap.put(memberGuid, realityTotalGiveIntegralValue);
        } else {
            //获取会员赠送成长值总额
            int totalGiveGrowthValue = ObjectUtil.objToInt(growthValueTotalMap.get(memberGuid)) + giveGrowthValue;
            growthValueTotalMap.put(memberGuid, totalGiveGrowthValue);
            //添加成长值明细记录
            hsaGrowthValueDetails.add(addGrowthValueDetail(memberGuid, hsaCardBaseInfo, hsaBusinessEquities, hsaMemberGradeInfo, giveGrowthValue, totalGiveGrowthValue));

            //实际获得累计值
            int realityTotalGiveGrowthValue = ObjectUtil.objToInt(growthRealityValueTotalMap.get(memberGuid)) + giveGrowthValue;
            growthRealityValueTotalMap.put(memberGuid, realityTotalGiveGrowthValue);
        }


        hsaMemberEquitiesReceiveRecords.add(getHsaMemberEquitiesReceiveRecord(hsaBusinessEquities.getOperSubjectGuid(),
                memberGuid,
                hsaBusinessEquities.getMemberGradeInfoGuid(),
                hsaBusinessEquities.getGuid(),
                giveGrowthValue,
                type));
    }


    private HsaMemberEquitiesReceiveRecord getHsaMemberEquitiesReceiveRecord(String operSubjectGuid,
                                                                             String memberGuid,
                                                                             String memberGradeGuid,
                                                                             String gradeEquitiesGuid,
                                                                             Integer giveGrowthValueNumber,
                                                                             Integer type) {
        HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaMemberEquitiesReceiveRecord.setGiveGrowthValueNumber(giveGrowthValueNumber);
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid(operSubjectGuid);
        hsaMemberEquitiesReceiveRecord.setGmtCreate(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setGmtModified(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid(memberGradeGuid);
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid(memberGuid);
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid(gradeEquitiesGuid);
        hsaMemberEquitiesReceiveRecord.setType(type);
        return hsaMemberEquitiesReceiveRecord;
    }


    private HsaGrowthValueDetail addGrowthValueDetail(String memberGuid,
                                                      HsaCardBaseInfo hsaCardBaseInfo,
                                                      HsaBusinessEquities hsaBusinessEquities,
                                                      HsaMemberGradeInfo hsaMemberGradeInfo,
                                                      int growthValue,
                                                      int totalGrowthValue) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaGrowthValueDetail.setOperatorAccountName(hsaCardBaseInfo.getOperatorName());
        hsaGrowthValueDetail.setOperSubjectGuid(hsaCardBaseInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setCurrentMemberLevel(
                hsaMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        hsaMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaGrowthValueDetail.setRecordDeclaration(hsaCardBaseInfo.getCardName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaGrowthValueDetail.setGrowthValue(growthValue);
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaGrowthValueDetail.setMemberInfoGuid(memberGuid);
        hsaGrowthValueDetail.setGrowthValidity(GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode());
        hsaGrowthValueDetail.setGrowthValueType(GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode());
        hsaGrowthValueDetail.setRemainGrowthValue(totalGrowthValue);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(growthValue);
        return hsaGrowthValueDetail;
    }

    private HsaIntegralDetail addIntegralValueDetail(String memberGuid,
                                                     HsaCardBaseInfo hsaCardBaseInfo,
                                                     HsaBusinessEquities hsaBusinessEquities,
                                                     HsaMemberGradeInfo hsaMemberGradeInfo,
                                                     int integralValue,
                                                     int totalGrowthValue) {
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaIntegralDetail.class.getSimpleName()));
        hsaIntegralDetail.setIntegralSourceType(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaIntegralDetail.setOperatorAccountName(hsaCardBaseInfo.getOperatorName());
        hsaIntegralDetail.setOperSubjectGuid(hsaMemberGradeInfo.getOperSubjectGuid());
        hsaIntegralDetail.setCurrentMemberLevel(
                hsaMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        hsaMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaIntegralDetail.setRecordDeclaration(hsaCardBaseInfo.getCardName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setDeclaration(hsaCardBaseInfo.getCardName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setIntegral(integralValue);
        hsaIntegralDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaIntegralDetail.setMemberInfoGuid(memberGuid);
        hsaIntegralDetail.setIntegralValidity(GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode());
        hsaIntegralDetail.setIntegralType(GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode());
        hsaIntegralDetail.setCurrentIntegral(totalGrowthValue);
        hsaIntegralDetail.setRecordRemainIntegral(integralValue);
        return hsaIntegralDetail;
    }

    private void setPeriodUseCount(HsaBusinessEquities hsaBusinessEquities, EquitiesDetailVO gradeEquitiesVO) {
        int equitiesRuleType = hsaBusinessEquities.getEquitiesRuleType();
        if (EquitiesRuleTypeEnum.GOODS_MEMBER.getCode() == equitiesRuleType) {
            gradeEquitiesVO.setPeriodUseCountLimited(hsaBusinessEquities.getPeriodDiscountLimited());

            PeriodDiscountVO periodDiscount = getPeriodDiscount(hsaBusinessEquities);
            if (Objects.nonNull(periodDiscount)) {
                gradeEquitiesVO.setPeriodUseCountType(Integer.valueOf(periodDiscount.getType()));
                gradeEquitiesVO.setPeriodUseCount(Integer.valueOf(periodDiscount.getValue()));
            }
        }
    }

    private void getGradeRightCommodityRule(List<GradeCommodityBaseQO> gradeCommodityBaseQOList, EquitiesDetailVO gradeEquitiesVO) {
        if (gradeEquitiesVO.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode()) {
            List<HsaGradeRightsCommodityRule> hsaGradeRightsCommodityRules = hsaGradeRightsCommodityRuleMapper.selectList(new LambdaQueryWrapper<HsaGradeRightsCommodityRule>()
                    .eq(HsaGradeRightsCommodityRule::getGradeEquitiesGuid, gradeEquitiesVO.getGuid())
                    .eq(HsaGradeRightsCommodityRule::getIsDelete, BooleanEnum.FALSE.getCode()));

            //分业务获取商品信息
            List<GrowthCommodityBaseVO> growthCommodityBaseVOS = getGrowthCommodityBaseVOS(hsaGradeRightsCommodityRules);

            Map<String, Map<String, GrowthCommodityBaseVO>> commodityBaseMap = new HashMap<>();


            if (CollUtil.isNotEmpty(growthCommodityBaseVOS)) {
                putCommodityMap(growthCommodityBaseVOS, StringConstant.POS, commodityBaseMap);

                putCommodityMap(growthCommodityBaseVOS, StringConstant.MALL, commodityBaseMap);

                putCommodityMap(growthCommodityBaseVOS, StringConstant.TAKEAWAY, commodityBaseMap);

                putCommodityMap(growthCommodityBaseVOS, StringConstant.S2B2C, commodityBaseMap);
            }

            //判断商品信息是否存在
            getCommodityBaseQOList(gradeCommodityBaseQOList, hsaGradeRightsCommodityRules, commodityBaseMap);

        }
        gradeEquitiesVO.setGradeCommodityBaseQOList(gradeCommodityBaseQOList);
    }

    private static void getCommodityBaseQOList(List<GradeCommodityBaseQO> gradeCommodityBaseQOList,
                                               List<HsaGradeRightsCommodityRule> hsaGradeRightsCommodityRules,
                                               Map<String, Map<String, GrowthCommodityBaseVO>> commodityBaseMap) {
        for (HsaGradeRightsCommodityRule hsaGradeRightsCommodityRule : hsaGradeRightsCommodityRules) {
            GradeCommodityBaseQO gradeCommodityBaseQO = new GradeCommodityBaseQO();
            // 1. 检查商品渠道是否存在
            if (CollUtil.isEmpty(commodityBaseMap) ||
                    !commodityBaseMap.containsKey(getChannel(hsaGradeRightsCommodityRule.getBusinessType()))) {
                dealNotCommodity(hsaGradeRightsCommodityRule, gradeCommodityBaseQO);
                continue;
            }
            // 2. 获取对应渠道的商品映射
            Map<String, GrowthCommodityBaseVO> commodityBaseVOMap = commodityBaseMap.get(getChannel(hsaGradeRightsCommodityRule.getBusinessType()));
            // 3. 获取具体商品信息
            GrowthCommodityBaseVO growthCommodityBaseVO = commodityBaseVOMap.get(hsaGradeRightsCommodityRule.getCommodityId());

            // 4. 判断商品是否存在且渠道匹配
            if (Objects.nonNull(growthCommodityBaseVO)
                    && getChannel(hsaGradeRightsCommodityRule.getBusinessType())
                    .equals(getChannel(growthCommodityBaseVO.getBusinessType()))) {
                gradeCommodityBaseQO.setBasePrice(growthCommodityBaseVO.getBasePrice())
                        .setCommodityCode(growthCommodityBaseVO.getCommodityCode())
                        .setCommodityComboType(growthCommodityBaseVO.getCommodityComboType())
                        .setCommodityId(growthCommodityBaseVO.getCommodityId())
                        .setCommodityName(growthCommodityBaseVO.getCommodityName())
                        .setBusinessType(hsaGradeRightsCommodityRule.getBusinessType())
                        .setPhoto(growthCommodityBaseVO.getPhoto())
                        .setStoreState(growthCommodityBaseVO.getStoreState())
                        .setSystem(growthCommodityBaseVO.getSystem())
                        .setIsExist(BooleanEnum.TRUE.getCode());
            } else {
                dealNotCommodity(hsaGradeRightsCommodityRule, gradeCommodityBaseQO);
            }
            gradeCommodityBaseQOList.add(gradeCommodityBaseQO);
        }
    }

    private static void dealNotCommodity(HsaGradeRightsCommodityRule hsaGradeRightsCommodityRule, GradeCommodityBaseQO gradeCommodityBaseQO) {
        gradeCommodityBaseQO.setBasePrice(hsaGradeRightsCommodityRule.getCommodityPrice())
                .setCommodityCode(hsaGradeRightsCommodityRule.getCommodityCode())
                .setCommodityComboType(hsaGradeRightsCommodityRule.getComboType())
                .setCommodityId(hsaGradeRightsCommodityRule.getCommodityId())
                .setCommodityName(hsaGradeRightsCommodityRule.getCommodityName())
                .setBusinessType(hsaGradeRightsCommodityRule.getBusinessType())
                .setIsExist(BooleanEnum.FALSE.getCode());
    }

    public static String getChannel(Integer businessType) {
        if (businessType == null) {
            return "";
        }

        if (businessType == 0) {
            return "pos";
        }

        if (businessType == 1) {
            return "pos";
        }

        if (businessType == 2) {
            return "pos";
        }

        if (businessType == 13) {
            return "会员商城";
        }

        if (businessType == 4) {
            return "自营外卖";
        }

        if (businessType == NumberConstant.NUMBER_15) {
            return ApplyBusinessEnum.S2B2C.getDes();
        }
        return "";
    }


    private void putCommodityMap(List<GrowthCommodityBaseVO> commodityBaseVOS,
                                 String type,
                                 Map<String, Map<String, GrowthCommodityBaseVO>> commodityBaseMap) {
        Map<String, GrowthCommodityBaseVO> commodityMap =
                commodityBaseVOS
                        .stream()
                        .filter(in -> in.getChannel().equals(type))
                        .collect(Collectors.toList())
                        .stream()
                        .collect(Collectors.toMap(GrowthCommodityBaseVO::getCommodityId, Function.identity(), (oldValue, newValue) -> newValue));

        if (CollUtil.isNotEmpty(commodityMap))
            commodityBaseMap.put(type, commodityMap);
    }

    /**
     * 商品会员价,周期使用次数和类型对象
     *
     * @param gradeEquities 等级权益
     * @return 使用次数信息
     */
    private PeriodDiscountVO getPeriodDiscount(HsaBusinessEquities gradeEquities) {
        String periodDiscountType = gradeEquities.getPeriodDiscountType();
        if (StringUtils.isEmpty(periodDiscountType)) {
            return null;
        }
        List<PeriodDiscountVO> periodDiscountVOS = JSON.parseArray(periodDiscountType, PeriodDiscountVO.class);
        return periodDiscountVOS.stream().findFirst().orElse(null);
    }

    private List<GrowthCommodityBaseVO> getGrowthCommodityBaseVOS(List<HsaGradeRightsCommodityRule> hsaGradeRightsCommodityRules) {
        List<GrowthCommodityBaseVO> growthCommodityBaseVOS = Lists.newArrayList();
        if (CollUtil.isEmpty(hsaGradeRightsCommodityRules)) {
            return growthCommodityBaseVOS;
        }
        Map<Integer, List<HsaGradeRightsCommodityRule>> gradeRightsCommodityMap = hsaGradeRightsCommodityRules.stream()
                .collect(Collectors.groupingBy(HsaGradeRightsCommodityRule::getBusinessType));

        Set<Integer> channelSet = hsaGradeRightsCommodityRules.stream().map(HsaGradeRightsCommodityRule::getBusinessType).collect(Collectors.toSet());


        for (Integer businessType : channelSet) {
            List<HsaGradeRightsCommodityRule> hsaGradeRightsCommodityList = gradeRightsCommodityMap.get(businessType);

            List<String> commodityId = hsaGradeRightsCommodityList.stream()
                    .map(in -> String.valueOf(in.getCommodityId())).collect(Collectors.toList());

            GradeCommodityBasePageQO qo = new GradeCommodityBasePageQO();
            qo.setBusinessType(businessType).setCommityIds(commodityId);
            // 转换下渠道不同的系统
            qo.setSystemEnum(GradeCommodityBasePageQO.mapBusinessTypeToSystem(businessType));
            GrowthCommodityPageVO growthCommodityPageVO = shopBaseService.queryGradeCommodityPage(qo);
            if (Objects.nonNull(growthCommodityPageVO) && CollUtil.isNotEmpty(growthCommodityPageVO.getGrowthCommodityBaseVOS())) {
                growthCommodityBaseVOS.addAll(growthCommodityPageVO.getGrowthCommodityBaseVOS());
            }
        }
        return growthCommodityBaseVOS;
    }

    @Override
    public List<HsaBusinessEquities> listByCard(List<String> memberCards) {
        final LambdaQueryWrapper<HsaBusinessEquities> wrapper = new LambdaQueryWrapper<HsaBusinessEquities>()
                .in(HsaBusinessEquities::getMemberGradeInfoGuid, memberCards)
                //卡权益
                .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.CARD_EQUITIES.getCode())
                .in(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.MEMBER_PRICE.getCode())
                .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode());
        //todo  暂时只查询折扣
        return hsaBusinessEquitiesMapper.selectList(wrapper);
    }
}
