package com.holderzone.member.base.service.card;

import com.holderzone.member.base.entity.card.HsaCardPayRecord;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.common.dto.card.*;
import com.holderzone.member.common.qo.card.CreatePhysicalCardSecretQO;
import com.holderzone.member.common.qo.card.FreezeOrThawQO;
import com.holderzone.member.common.qo.card.OpenElectronicCardQO;
import com.holderzone.member.common.vo.card.AppletOpenCardVO;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 电子卡业务
 * @date 2021/9/2
 */
public interface HsaElectronicCardService {
    /**
     * 功能描述：生成卡密同时去开通会员卡
     *
     * @param qo 传递数据
     * @date 2021/9/2
     */
    void openCardWithPhysicalCreate(CreatePhysicalCardSecretQO qo, LocalDateTime now, ProduceSecretDTO produceSecretDTO);

    /**
     * 功能描述：批量开通电子卡
     *
     * @param qo 前端传递数据
     * @date 2021/9/3
     */
    String batchOpen(OpenElectronicCardQO qo) throws IOException;

    /**
     * 功能描述：查询电子卡信息和excel导入得会员信息中得会员卡做对比
     *
     * @param operSubjectGuid 主体guid
     * @return java.util.List<com.holderzone.member.common.dto.card.ECardCompareExcelDTO>
     * @date 2021/9/4
     */
    List<ECardCompareExcelDTO> listAllECard(String operSubjectGuid);

    /**
     * 功能描述：批量开通excel导入得会员信息
     *
     * @param cardOpenList 需要开通得会员卡关联会员信息
     * @date 2021/9/4
     */
    void batchOpenWithExcelMember(List<MemberCardOpenDTO> cardOpenList);

    /**
     * 小程序开通会员卡
     *
     * @param dto 开卡卡信息
     */
    AppletOpenCardVO miniProgramOpenCard(MemberCardOpenDTO dto);


    HsaMemberInfoCard getMemberPayOpenCard(MemberCardOpenDTO dto, CardInfoDetailDTO available, HsaCardPayRecord hsaCardPayRecord );

    /**
     * 门店save
     *
     * @param memberInfoCardList
     */
    void saveStoreCardRule(List<HsaMemberInfoCardDTO> memberInfoCardList);

    /**
     * 功能描述：冻结或者解冻
     *
     * @param qo 冻结或者解冻传递参数
     * @date 2021/9/6
     */
    void freezeOrThawCard(FreezeOrThawQO qo);

    /**
     * 功能描述：后台开通电子卡
     *
     * @param ownGuid 关联guid
     * @date 2021/9/6
     */
    void openCardByBackStage(String ownGuid);

    void addOpenCardBalanceRecord(List<HsaMemberInfoCard> hsaMemberInfoCards, Map<String, BigDecimal> cardMoney,
                                  Integer source, Integer cardType);

    void openCardByMemberLabel(List<String> memberInfoGuid, Integer sourceType);

    String terminalOpenCard(MemberCardOpenDTO dto);
}
