package com.holderzone.member.base.mapper.purchase;

import com.holderzone.member.base.entity.purchase.HsaPurchaseOrderCommodity;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseMemberCommodityQo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseApplyCommodityVo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseReserveCommodityVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 限量抢购活动-订单商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface HsaPurchaseOrderCommodityMapper extends HolderBaseMapper<HsaPurchaseOrderCommodity> {

    /**
     * 查询会员限购商品
     *
     * @param qo 查询条件
     * @return 限购商品
     */
    List<PurchaseApplyCommodityVo> listMemberPurchaseCommodity(@Param("qo") PurchaseMemberCommodityQo qo);
}
