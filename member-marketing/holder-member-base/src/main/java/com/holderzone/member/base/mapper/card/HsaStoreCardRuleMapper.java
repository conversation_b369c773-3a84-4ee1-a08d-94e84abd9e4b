package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.vo.card.MemberCardStoreDataQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡门店适用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaStoreCardRuleMapper extends HolderBaseMapper<HsaStoreCardRule> {


    /**
     * 根据会员卡获取适用门店
     *
     * @param request 请求参数
     * @return 返回结果
     */
    List<StoreBaseInfo> findStoreCardRule(@Param("request") MemberCardStoreDataQO request);


    void updateMemberInfoCardGuid(@Param("storeRuleList") List<HsaStoreCardRule> storeCardRuleList);
}
