package com.holderzone.member.base.entity.card;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡余额总计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Data
@Accessors(chain = true)
@ApiModel(value="HsaMemberInfoCardTotal对象", description="会员卡余额历史")
public class HsaMemberInfoCardTotal implements Serializable {

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 0未删除，时间戳：删除
     */
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
    private Long isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "日期")
    private String recordDate;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "补贴余额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty(value = "充值余额")
    private BigDecimal cardAmount;

    @ApiModelProperty(value = "赠送余额")
    private BigDecimal giftAmount;


}
