package com.holderzone.member.base.entity.equities;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.annotation.FieldLabel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 适用字典 （适用业务、适用终端、适用渠道）
 * @author: pan tao
 * @create: 2022-01-18 15:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaApplyDictionaries implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 所属模块 （适用业务、适用终端、适用渠道）
     *
     * @see com.holderzone.member.common.enums.equities.ApplyModuleEnum
     */
    private String module;

    /**
     * 所属类型
     */
    private String type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 新/旧 系统
     *
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private Integer system;

    /**
     * 是否展示
     */
    private Integer isShow;
}
