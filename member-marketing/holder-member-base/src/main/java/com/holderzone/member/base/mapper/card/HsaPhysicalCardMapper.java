package com.holderzone.member.base.mapper.card;

import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.card.BindMemberAccountDTO;
import com.holderzone.member.common.dto.card.MemberBindPhysicalCardSecretDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface HsaPhysicalCardMapper extends HolderBaseMapper<HsaPhysicalCard> {


    List<MemberBindPhysicalCardSecretDTO> listBindMemberByCardGuidAndCardNum(@Param("recordGuid") String recordGuid, @Param("bindCardNumList") List<String> bindCardNumList);

    List<MemberBindPhysicalCardSecretDTO> listBindMemberByCardGuidPage(@Param("cardGuid") String cardGuid);

    void freezeOrThawCard(@Param("guidList") List<String> guidList, @Param("status") Integer status);

    void inactiveCardFreezeOrThawCard(@Param("guid") String guid, @Param("status") Integer status);

    void updateMemberBind(@Param("ownGuid") String ownGuid, @Param("guid") String guid);

    void updateBindAccount(BindMemberAccountDTO memberAccountDTO);

    /**
     * 通过memberInfoCardGuid 查询当前实体卡状态
     *
     * @param guid
     */
    Integer findCardStatus(@Param("guid") String guid);

    Integer getCardNumCountLike(@Param("cardNum") String cardNum, @Param("operSubjectGuid") String operSubjectGuid);

    HsaPhysicalCard getByCardNum(@Param("cardNum") String cardNum, @Param("operSubjectGuid") String operSubjectGuid);

    void updateBatchMemberInfoGuid(@Param("list") List<HsaPhysicalCard> hsaPhysicalCards, @Param("memberInfoGuid") String memberInfoGuid);

    void batchUpdatePhone(@Param("memberInfoGuid") String memberInfoGuid,@Param("phone") String phone);
}
