package com.holderzone.member.base.entity.grade;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 会员等级关联表
 * @author: rw
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberRelevanceGrade implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 等级guid
     */
    private String gradeGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 等级名称
     */
    private String name;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 等级到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime overdueExceed;

    /**
     * 是否过期 0:无效 1:有效
     */
    private Integer effective;

    /**
     * 等级角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;

    /**
     * 开通类型：1成长值升级 2付费升级
     *
     * @see com.holderzone.member.common.enums.grade.UpgradeTypeEnum
     */
    private Integer upgradeType;

    /**
     * 临时等级
     */
    private Integer temporaryVipGrade;

    /**
     * 是否当前生效等级
     */
    private Integer isCurrent;

}
