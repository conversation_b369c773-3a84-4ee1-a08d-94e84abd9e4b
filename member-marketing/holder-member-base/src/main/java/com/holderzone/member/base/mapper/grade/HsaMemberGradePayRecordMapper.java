package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaMemberGradePayRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.member.common.qo.grade.MemberGradePayRecordQO;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员等级付费记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface HsaMemberGradePayRecordMapper extends BaseMapper<HsaMemberGradePayRecord> {

    List<MemberGradePayRecordRespVO> pageRecord(@Param("request") MemberGradePayRecordQO gradePayRecordQO);
}
