package com.holderzone.member.base.service.grade.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.grade.*;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.event.MemberGradeChangeEvent;
import com.holderzone.member.base.mapper.equities.HsaEquitiesInfoMapper;
import com.holderzone.member.base.mapper.grade.*;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDetailMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.assembler.MemberGradeAssembler;
import com.holderzone.member.base.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.base.service.grade.HsaMemberRelevanceGradeService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.grade.ClaimGradeEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.GradeChangeTypeEnum;
import com.holderzone.member.common.enums.member.GradeRightsTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.equities.BusinessEquitiesQO;
import com.holderzone.member.common.qo.grade.ReceiveMemberGradeBagQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.grade.*;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员等级变化明细表
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
@Slf4j
public class HsaMemberGradeChangeDetailServiceImpl extends HolderBaseServiceImpl<HsaMemberGradeChangeDetailMapper,
        HsaMemberGradeChangeDetail> implements HsaMemberGradeChangeDetailService {

    @Resource
    private HsaMemberGradeChangeDetailMapper hsaMemberGradeChangeDetailMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaGradeGiftBagMapper hsaGradeGiftBagMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberRelevanceGradeService hsaMemberRelevanceGradeService;

    @Resource
    private HsaMemberRelevanceGradeMapper hsaMemberRelevanceGradeMapper;

    @Resource
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;

    @Resource
    private HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private MemberGradeAssembler memberGradeAssembler;

    @Resource
    private HsaIntegralDetailMapper hsaIntegralDetailMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private MemberGradeChangeEvent memberGradeChangeEvent;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Resource
    private IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    @Resource
    private HsaEquitiesInfoMapper hsaEquitiesInfoMapper;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;


    /**
     * 会员小程序升级礼包领取页面
     *
     * @param memberInfoGuid memberInfoGuid
     * @return AppletMemberGradeInfoBagBaseVO
     */
    @Override
    public AppletMemberGradeInfoBagBaseVO getAppletMemberGradeInfoBag(String memberInfoGuid) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        AppletMemberGradeInfoBagBaseVO bagBaseVO = new AppletMemberGradeInfoBagBaseVO();
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberInfoGuid);
        //获取会员所有等级
        List<HsaMemberGradeInfo> hsaMemberGradeInfos = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                .orderByAsc(HsaMemberGradeInfo::getVipGrade));
        if (CollectionUtil.isEmpty(hsaMemberGradeInfos)) {
            log.info("会员等级不存在>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>主体{}", headerUserInfo.getOperSubjectGuid());
            return bagBaseVO;
        }

        //获取权益规则
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        checkGiftBagBaseInfo(headerUserInfo, hsaGiftBagBaseInfo);

        //获取会员所有升级礼包
        Map<String, List<HsaGradeGiftBag>> hsaGradeGiftBagsMap = hsaGradeGiftBagMapper.selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                        .eq(HsaGradeGiftBag::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaGradeGiftBag::getEffective, NumberConstant.NUMBER_1))
                .stream()
                .collect(Collectors.groupingBy(HsaGradeGiftBag::getMemberGradeInfoGuid));

        if (CollectionUtil.isEmpty(hsaGradeGiftBagsMap)) {
            log.info("升级礼包不存在>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>主体{}", headerUserInfo.getOperSubjectGuid());
            return bagBaseVO;
        }

        //获取会员领取记录
        Map<String, HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecordsMap = hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, memberInfoGuid)
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode()))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeRightsRecord::getMemberGradeGuid, Function.identity(), (obj1, obj2) -> obj1));
        bagBaseVO.setEquitiesDescribe(hsaGiftBagBaseInfo.getEquitiesDescribe())
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setEquitiesIcon(JSON.parseArray(hsaGiftBagBaseInfo.getEquitiesIcon(), String.class));

        List<AppletMemberGradeInfoBagVO> appletMemberGradeInfoBagVOS = new ArrayList<>();

        memberGradeAssembler.fromAppletMemberGradeInfoBagVOS(hsaOperationMemberInfo, hsaMemberGradeInfos, hsaGradeGiftBagsMap, hsaMemberGradeRightsRecordsMap, appletMemberGradeInfoBagVOS);
        bagBaseVO.setAppletMemberGradeInfoBagVOS(appletMemberGradeInfoBagVOS);
        return bagBaseVO;
    }

    private static void checkGiftBagBaseInfo(HeaderUserInfo headerUserInfo, HsaGiftBagBaseInfo hsaGiftBagBaseInfo) {
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            log.info("获取权益规则不存在>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>主体{}", headerUserInfo.getOperSubjectGuid());
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_NULL_CARD_INFO);
        }
    }


    @Override
    public List<GradeEquitiesPreviewVO> getAppletMemberGradeEquities(String memberInfoGuid,
                                                                     String memberGradeGuid,
                                                                     String equitiesRuleType,
                                                                     Integer effective) {
        List<GradeEquitiesPreviewVO> gradeEquitiesPreviewList = new ArrayList<>();

        BusinessEquitiesQO baseQO = new BusinessEquitiesQO();
        baseQO.setMemberGradeGuid(memberGradeGuid);
        baseQO.setMemberInfoGuid(memberInfoGuid);
        baseQO.setEquitiesRuleType(equitiesRuleType);
        baseQO.setEffective(effective);
        hsaMemberGradeInfoService.addGradeEquities(baseQO, gradeEquitiesPreviewList);
        return gradeEquitiesPreviewList;
    }


    @Override
    public Boolean receiveMemberGradeInfoChangeBag(ReceiveMemberGradeBagQO receiveMemberGradeBagQO) {
        HsaOperationMemberInfo hsaOperationMemberInfo = receiveMemberGradeInfoBag(receiveMemberGradeBagQO);
        if (Objects.nonNull(hsaOperationMemberInfo)) {
            memberBaseThreadExecutor.execute(() -> sendMemberGradeChangeEvent(hsaOperationMemberInfo));
        }
        return true;
    }

    /**
     * 微信小程序升级礼包领取
     *
     * @param receiveMemberGradeBagQO receiveMemberGradeBagQO
     * @return Boolean
     */
//    @Override
    @Transactional(rollbackFor = Exception.class)
    public HsaOperationMemberInfo receiveMemberGradeInfoBag(ReceiveMemberGradeBagQO receiveMemberGradeBagQO) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(receiveMemberGradeBagQO.getMemberInfoGuid());
        List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecord = hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                .eq(HsaMemberGradeRightsRecord::getMemberGradeGuid, receiveMemberGradeBagQO.getGradeGuid())
                .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, receiveMemberGradeBagQO.getMemberInfoGuid())
                .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode()));
        if (CollectionUtil.isNotEmpty(hsaMemberGradeRightsRecord)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.MEMBER_GRADE_RECEIVE_BAG);
        }
        List<HsaGradeGiftBag> hsaGradeGiftBag = hsaGradeGiftBagMapper.selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                .eq(HsaGradeGiftBag::getMemberGradeInfoGuid, receiveMemberGradeBagQO.getGradeGuid())
                .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaGradeGiftBag::getEffective, NumberConstant.NUMBER_1));
        if (CollectionUtil.isEmpty(hsaGradeGiftBag)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.MEMBER_GRADE_BAG_NOT_EXIST, ThreadLocalCache.getOperSubjectGuid()));
        }
        HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, receiveMemberGradeBagQO.getGradeGuid())
                .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (Objects.isNull(hsaMemberGradeInfo)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.MEMBER_GRADE_NOT_EXIST, ThreadLocalCache.getOperSubjectGuid()));
        }
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        for (HsaGradeGiftBag gradeGiftBag : hsaGradeGiftBag) {
            HsaMemberGradeRightsRecord memberGradeRightsRecord = new HsaMemberGradeRightsRecord();
            memberGradeRightsRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRightsRecord.class.getSimpleName()))
                    .setMemberInfoGuid(receiveMemberGradeBagQO.getMemberInfoGuid())
                    .setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid())
                    .setMemberGradeGuid(hsaMemberGradeInfo.getGuid())
                    .setRightsGuid(gradeGiftBag.getGuid())
                    .setRightsType(GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode());

            if (gradeGiftBag.getType() == NumberConstant.NUMBER_0) {
                hsaOperationMemberInfo.setMemberGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue() + gradeGiftBag.getValue());
                //会员成长值明细记录
                HsaGrowthValueDetail hsaGrowthValueDetail = memberGradeAssembler.getHsaGrowthValueDetail(hsaOperationMemberInfo, SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode(), hsaOperationMemberInfo.getOperSubjectGuid(), gradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo);
                hsaGrowthValueDetailMapper.insert(hsaGrowthValueDetail);
            } else {
                //todo 积分待定
                hsaOperationMemberInfo.setMemberIntegral(hsaOperationMemberInfo.getMemberIntegral() + gradeGiftBag.getValue());
                //会员成长值明细记录
                HsaIntegralDetail hsaIntegralDetail = memberGradeAssembler.getHsaIntegralValueDetail(hsaOperationMemberInfo, SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode(), hsaOperationMemberInfo.getOperSubjectGuid(), gradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo);
                hsaIntegralDetailMapper.insert(hsaIntegralDetail);
            }
            hsaMemberGradeRightsRecordMapper.insert(memberGradeRightsRecord);
            hsaOperationMemberInfoMapper.updateByGuid(hsaOperationMemberInfo);
        }
        return hsaOperationMemberInfo;
    }

    /**
     * 会员等级变更弹窗
     *
     * @param memberInfoGuid memberInfoGuid
     * @return AppletMemberGradeChangeVO
     */
    @Override
    public AppletMemberGradeChangeVO showMemberGradeChange(String memberInfoGuid) {
        AppletMemberGradeChangeVO appletMemberGradeChangeVO = new AppletMemberGradeChangeVO();
        List<MemberGradeEquitiesVO> memberGradeEquitiesVOS = new ArrayList<>();
        List<HsaMemberGradeChangeDetail> hsaMemberGradeChangeDetails = hsaMemberGradeChangeDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeChangeDetail>()
                .eq(HsaMemberGradeChangeDetail::getMemberInfoGuid, memberInfoGuid)
                .orderByDesc(HsaMemberGradeChangeDetail::getGmtCreate));
        if (CollectionUtil.isEmpty(hsaMemberGradeChangeDetails)) {
            return null;
        }
        if (hsaMemberGradeChangeDetails.get(0).getIsShowApplet() == BooleanEnum.TRUE.getCode()) {
            return null;
        }

        //获取权益规则
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            throw new MemberBaseException("升级礼包基础规则未初始化");
        }


        appletMemberGradeChangeVO.setEquitiesDescribe(hsaGiftBagBaseInfo.getEquitiesDescribe());
        HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = hsaMemberGradeChangeDetails.get(0);
        appletMemberGradeChangeVO.setBeforeChangeGradeGuid(hsaMemberGradeChangeDetail.getBeforeChangeGradeGuid())
                .setBeforeChangeGradeName(hsaMemberGradeChangeDetail.getBeforeChangeGradeName())
                .setBeforeChangeGradeVipGrade(hsaMemberGradeChangeDetail.getBeforeChangeGradeVipGrade())
                .setAfterChangeGradeGuid(hsaMemberGradeChangeDetail.getAfterChangeGradeGuid())
                .setAfterChangeGradeName(hsaMemberGradeChangeDetail.getAfterChangeGradeName())
                .setAfterChangeGradeVipGrade(hsaMemberGradeChangeDetail.getAfterChangeGradeVipGrade())
                .setGradeChangeType(hsaMemberGradeChangeDetail.getGradeChangeType());

        //获取会员当前等级礼包
        List<HsaGradeGiftBag> hsaGradeGiftBags = hsaGradeGiftBagMapper
                .selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                        .eq(HsaGradeGiftBag::getMemberGradeInfoGuid, hsaMemberGradeChangeDetail.getAfterChangeGradeGuid())
                        .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaGradeGiftBag::getEffective, BooleanEnum.TRUE.getCode()));
        if (CollectionUtil.isNotEmpty(hsaGradeGiftBags)) {
            appletMemberGradeChangeVO.setGiftNum(appletMemberGradeChangeVO.getGiftNum() + 1);
            MemberGradeEquitiesVO memberGradeEquitiesVO = new MemberGradeEquitiesVO();
            memberGradeEquitiesVO.setEquitiesIcon(StringUtils.isEmpty(hsaGiftBagBaseInfo.getEquitiesIcon()) ? null : JSONArray.parseArray(hsaGiftBagBaseInfo.getEquitiesIcon(), String.class));
            memberGradeEquitiesVO.setEquitiesName(hsaGiftBagBaseInfo.getEquitiesName());
            memberGradeEquitiesVOS.add(memberGradeEquitiesVO);
        }
        //todo 其他权益待定
        //查询当前会员等级所有权益
        List<GradeEquitiesVO> gradeEquitieInfos = hsaBusinessEquitiesMapper.getGradeEquitieInfo(hsaMemberGradeChangeDetail.getAfterChangeGradeGuid());
        for (GradeEquitiesVO gradeEquitieInfo : gradeEquitieInfos) {
            MemberGradeEquitiesVO equitiesVO = new MemberGradeEquitiesVO();
            //set微信小程序所需要的参数信息
            setWeChatInfo(equitiesVO, gradeEquitieInfo, memberInfoGuid);

            memberGradeEquitiesVOS.add(equitiesVO);
        }

        //获取会员领取记录
        Map<String, HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecordsMap = hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, memberInfoGuid)
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode())
                        .eq(HsaMemberGradeRightsRecord::getMemberGradeGuid, hsaMemberGradeChangeDetail.getAfterChangeGradeGuid()))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeRightsRecord::getMemberGradeGuid, Function.identity(), (obj1, obj2) -> obj1));

        if (CollectionUtil.isNotEmpty(hsaMemberGradeRightsRecordsMap) && hsaMemberGradeRightsRecordsMap.containsKey(hsaMemberGradeChangeDetail.getAfterChangeGradeGuid())) {
            appletMemberGradeChangeVO.setClaimStatus(ClaimGradeEnum.RECEIVED.getCode());
        } else {
            appletMemberGradeChangeVO.setClaimStatus(ClaimGradeEnum.NOT_RECEIVED.getCode());
        }
        appletMemberGradeChangeVO.setMemberInfoGuid(memberInfoGuid);
        HsaMemberGradeInfo beforeChangeGrade = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, appletMemberGradeChangeVO.getBeforeChangeGradeGuid())
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
        HsaMemberGradeInfo afterChangeGrade = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, appletMemberGradeChangeVO.getAfterChangeGradeGuid())
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
        appletMemberGradeChangeVO.setBeforeChangeGradeIcon(beforeChangeGrade.getGradeIcon());
        appletMemberGradeChangeVO.setAfterChangeGradeIcon(afterChangeGrade.getGradeIcon());
        //更新弹窗提示
        hsaMemberGradeChangeDetail.setIsShowApplet(BooleanEnum.TRUE.getCode());
        hsaMemberGradeChangeDetailMapper.updateByGuid(hsaMemberGradeChangeDetail);
        appletMemberGradeChangeVO.setMemberGradeEquitiesVOS(memberGradeEquitiesVOS);
        return appletMemberGradeChangeVO;
    }

    /**
     * set微信小程序所需要的参数信息
     *
     * @param equitiesVO       小程序等级变更权益弹窗
     * @param gradeEquitieInfo 权益信息
     */
    private void setWeChatInfo(MemberGradeEquitiesVO equitiesVO, GradeEquitiesVO gradeEquitieInfo, String memberInfoGuid) {
        String equitiesImg = gradeEquitieInfo.getEquitiesImg();
        if (StringUtils.isNotBlank(equitiesImg)) {
            String[] split = equitiesImg.split(StringConstant.COMMA);
            List<String> iconList = Arrays.stream(split).collect(Collectors.toList());
            // 权益图标
            equitiesVO.setEquitiesIcon(iconList);
        }
        // 权益名称
        equitiesVO.setEquitiesName(gradeEquitieInfo.getName());
        // 会员价折扣力度
        equitiesVO.setDiscountDynamics(gradeEquitieInfo.getDiscountDynamics());

        // 处理赠送成长值
        if (EquitiesRuleTypeEnum.GIVE_GROWTH_VALUE.getCode() == gradeEquitieInfo.getEquitiesRuleType()) {
            setGiveValue(memberInfoGuid, equitiesVO, gradeEquitieInfo, BooleanEnum.FALSE.getCode());
        }
        // 处理赠送积分
        if (EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode() == gradeEquitieInfo.getEquitiesRuleType()) {
            setGiveValue(memberInfoGuid, equitiesVO, gradeEquitieInfo, BooleanEnum.TRUE.getCode());
        }

        // 处理翻倍成长值,小程序展示信息
        // 主要用于判断用户翻倍成长值权益次数是否使用完了，使用完了，则不展示翻倍倍数
        if (EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode() == gradeEquitieInfo.getEquitiesRuleType()) {
            setIntegralValue(memberInfoGuid, equitiesVO, gradeEquitieInfo, BooleanEnum.FALSE.getCode());
        }
        // 处理翻倍积分,小程序展示信息
        if (EquitiesRuleTypeEnum.DOUBLE_INTEGRAL.getCode() == gradeEquitieInfo.getEquitiesRuleType()) {
            setIntegralValue(memberInfoGuid, equitiesVO, gradeEquitieInfo, BooleanEnum.TRUE.getCode());
        }
    }

    private void setIntegralValue(String memberInfoGuid, MemberGradeEquitiesVO equitiesVO, GradeEquitiesVO gradeEquitieInfo, Integer type) {
        Optional<GradeEquitiesVO> gradeEquitie = Optional.of(gradeEquitieInfo);
        //成长值翻倍次数限制 0:不限制 1:限制
        Integer doubleCountLimited = gradeEquitie.map(GradeEquitiesVO::getDoubleCountLimited)
                .orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == doubleCountLimited) {
            return;
        }
        HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = hsaMemberEquitiesReceiveRecordMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getGradeEquitiesGuid, gradeEquitieInfo.getGuid())
                        .eq(HsaMemberEquitiesReceiveRecord::getMemberInfoGuid, memberInfoGuid)
                        .eq(HsaMemberEquitiesReceiveRecord::getType, type));
        //使用次数
        Integer useCount = Optional.ofNullable(equitiesReceiveRecord)
                .map(HsaMemberEquitiesReceiveRecord::getDoubleValueCount).orElse(0);
        //翻倍成长值\积分总次数
        Integer totalCount = gradeEquitie.map(GradeEquitiesVO::getDoubleCount).orElse(0);
        //剩余使用次数
        int remainCount = totalCount - useCount;
        //用户翻倍成长值、积分权益使用完了,小程序则不展示翻倍倍数
        if (remainCount > 0) {
            equitiesVO.setMultiple(gradeEquitieInfo.getMultiple());
        }
        equitiesVO.setDoubleCount(remainCount);
    }

    private void setGiveValue(String memberInfoGuid, MemberGradeEquitiesVO equitiesVO, GradeEquitiesVO gradeEquitieInfo, Integer type) {
        MemberGrowthValueRelationVO valueRelation = hsaMemberEquitiesReceiveRecordMapper
                .selectAllGrowthValue(gradeEquitieInfo.getGuid(), memberInfoGuid, type);
        // 已经赠送成长值或积分
        Integer giveValue = Optional.ofNullable(valueRelation)
                .map(MemberGrowthValueRelationVO::getGrowthValue).orElse(0);
        // 剩余赠送成长值或积分
        Integer residue = gradeEquitieInfo.getTotalGiveNumber() - giveValue;
        equitiesVO.setTotalGiveNumber(residue > NumberConstant.NUMBER_0 ? residue : NumberConstant.NUMBER_0);
    }

    @Override
    public void encapsulationBusiness(HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo afterMemberGradeInfo, LocalDateTime localDateTime) {
        HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = memberGradeAssembler.getHsaMemberGradeChangeDetail(hsaOperationMemberInfo, afterMemberGradeInfo, localDateTime);
        hsaMemberGradeChangeDetailMapper.insert(hsaMemberGradeChangeDetail);
    }

    @Override
    public void batchUpdateGradeChange(List<HsaMemberGradeChangeDetail> request) {
        if (CollUtil.isEmpty(request)) {
            return;
        }
        hsaMemberGradeChangeDetailMapper.batchUpdateGradeChange(request);
    }

    @Override
    public void gradeChangeBusiness(String memberGuid, HsaMemberGradeInfo afterMemberGradeInfo, HsaMemberGradeInfo beforeMemberGradeInfo) {
        HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = memberGradeAssembler.getHsaMemberGradeChangeDetail(memberGuid, afterMemberGradeInfo, beforeMemberGradeInfo);
        hsaMemberGradeChangeDetailMapper.insert(hsaMemberGradeChangeDetail);
    }

    /**
     * 等级变化触发
     *
     * @param hsaOperationMemberInfo
     */
    private void sendMemberGradeChangeEvent(HsaOperationMemberInfo hsaOperationMemberInfo) {
        SendMemberGradeChangeEvent event = new SendMemberGradeChangeEvent();
        event.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        event.setMemberGuidList(Collections.singletonList(hsaOperationMemberInfo.getGuid()));
        event.setSourceType(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
        event.setIsRefresh(BooleanEnum.FALSE.getCode());
        log.info("小程序领取礼包等级变更发送>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaOperationMemberInfo);
        memberGradeChangeEvent.send(event);
    }
}
