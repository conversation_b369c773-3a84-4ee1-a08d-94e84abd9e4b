package com.holderzone.member.base.mapper.activity;


import com.holderzone.member.base.entity.activity.HsaSubsidyActivity;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.card.RequestSubsidyActivityQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员食堂卡补贴活动
 * @date 2021/10/26 9:42
 */
public interface HsaSubsidyActivityMapper extends HolderBaseMapper<HsaSubsidyActivity> {

    List<HsaSubsidyActivity> subsidyActivityList(@Param("request") RequestSubsidyActivityQO request);
}
