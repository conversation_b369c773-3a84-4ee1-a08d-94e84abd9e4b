package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.entity.grade.HsaMemberGradePurchaseHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;
import com.holderzone.member.common.vo.grade.MemberGradePurchaseHistoryVO;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * 会员付费记录服务
 * <AUTHOR>
 * @description
 * @createDate 2025-04-27 11:59:11
 */
public interface HsaMemberGradePurchaseHistoryService extends IService<HsaMemberGradePurchaseHistory> {

	/**
	 * 查询会员等级购买流水列表
	 *
	 * @param qo 查询条件
	 * @return 会员等级购买流水列表
	 */
	List<MemberGradePurchaseHistoryVO> list(MemberGradePurchaseHistoryQO qo);

	/**
	 * 根据订单号查询付费记录详情
	 * @param orderNo 订单号
	 * @return MemberGradePurchaseHistoryVO 会员等级购买流水
	 */
	MemberGradePurchaseHistoryVO getByOrderNo(String orderNo);

	/**
	 * 根据会员信息guid查询
	 * @param memberInfoGuid 会员信息guid
	 * @return 会员等级购买流水列表
	 */
	List<MemberGradePurchaseHistoryVO> listByMember(String memberInfoGuid);

	/**
	 * 根据guid查询
	 * @param guid guid
	 * @return 会员等级购买流水
	 */
	MemberGradePurchaseHistoryVO getByGuid(String guid);

	/**
	 * 新增会员等级购买流水
	 *
	 * @param qo 会员等级购买流水信息
	 */
	String add(MemberGradePurchaseHistoryQO qo);

	/**
	 * 更新会员等级购买流水
	 *
	 * @param qo 会员等级购买流水信息
	 */
	void update(MemberGradePurchaseHistoryQO qo);

	/**
	 * 根据guid删除
	 * @param guid guid
	 */
	void delete(String guid);

	/**
	 * 根据guid批量删除
	 * @param guids guid集合
	 */
	void batchDelete(List<String> guids);

	PageResult<MemberGradePurchaseHistoryVO> pageRecord (MemberGradePurchaseHistoryQO qo);

	/**
	 * 导出付费记录列表
	 * @param qo 查询条件
	 * @param response	响应
	 */
	void export (MemberGradePurchaseHistoryQO qo, HttpServletResponse response);
}
