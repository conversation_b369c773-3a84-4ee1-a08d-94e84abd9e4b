package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description: 挂账资金来往表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCreditFundingDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 挂账信息guid
     */
    private String creditInfoGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 挂账订单记录guid
     */
    private String creditOrderRecordGuid;

    /**
     * 挂账金额
     */
    private BigDecimal creditAmount;

    /**
     * 交易类型
     * 0:消费,1:消费退款,2:结算收款
     */
    private Integer transactionType;

    /**
     * 销售门店
     */
    private String saleStore;

    /**
     * 结算状态 0:未结算 1:结算中 2:已结算
     * 默认未：0
     */
    private Integer clearingStatus;

    /**
     * 变动类型 0 增加 1 减少
     */
    private Integer changeAmountType;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}
