package com.holderzone.member.base.service.cache.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.util.JwtParserUtil;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.TokenRequestBO;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员缓存
 * @date 2021/8/19 15:04
 */
@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Resource
    private JwtParserUtil jwtParserUtil;

    private static final String TOKEN_PREFIX = "token_";

    private static final String MEMBER = "member:";

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate redisTemplate;


    @Resource
    private RedisTemplate<String, Object> stringObjectRedisTemplate;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Value("${token.ttl}")
    public long JWT_REFRESH_TTL;

    public long GRADE_REFRESH_TTL = 30;

    private static final String GRADE_REFRESH = "GRADE_REFRESH";


    /**
     * 缓存token
     *
     * @param appToken
     * @param openId   openId
     */
    @Override
    public void createTokenForClient(Integer source, String appToken, String openId) {
        log.info("保存token到redis,来源= {},appToken = {}", source, appToken);
        String cacheKey = TOKEN_PREFIX + openId + ":" + source;
        long ttl = JWT_REFRESH_TTL;
        stringRedisTemplate.opsForValue().set(cacheKey, appToken, ttl, TimeUnit.DAYS);
    }

    @Override
    public void deleteToken(String openId) {
        String source = SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode() + "";
        String cacheKey = TOKEN_PREFIX + openId + ":" + source;
        redisTemplate.delete(cacheKey);
    }

    @Override
    public void setGradeCache(String key, String orderNum, Integer value) {
        if (Objects.isNull(value)) {
            stringRedisTemplate.opsForValue().set(key, orderNum);
        } else {
            stringRedisTemplate.opsForValue().set(key, orderNum, value, TimeUnit.MINUTES);
        }

    }

    @Override
    public void setOrderCache(String orderKey, String value) {
        redisTemplate.opsForValue().set(orderKey, value);
    }

    @Override
    public String getCache(String key) {
        return (String) redisTemplate.opsForValue().get(key);
    }

    /**
     * @param memberGuid
     * @param userInfo
     */
    @Override
    public void createMember(String memberGuid, HeaderUserInfo userInfo) {
        log.info("保存member HeaderUserInfo到redis,HeaderUserInfo = {}", JSONUtil.toJsonStr(userInfo));
        String cacheKey = MEMBER + memberGuid;
        redisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(userInfo), JWT_REFRESH_TTL, TimeUnit.DAYS);
    }

    @Override
    public HeaderUserInfo getTokenMember(String memberGuid) {
        String cacheKey = MEMBER + memberGuid;
        return Optional.ofNullable(redisTemplate.opsForValue().get(cacheKey))
                .map(v -> JSONUtil.toBean(v.toString(), HeaderUserInfo.class))
                .orElse(new HeaderUserInfo());
    }

    @Override
    public void createGradeRefresh(String cacheKey, String operSubjectGuid, String roleType) {
        String key = cacheKey + StringConstant.COLON + operSubjectGuid + StringConstant.COLON + roleType;
        if (cacheKey.equals(GRADE_REFRESH)) {
            //自动释放，不允许频繁操作
            redisTemplate.opsForValue().set(key, operSubjectGuid, GRADE_REFRESH_TTL, TimeUnit.MINUTES);
        } else {
            redisTemplate.opsForValue().set(key, operSubjectGuid);
        }
    }

    @Override
    public boolean getGradeRefresh(String cacheKey) {
        Object serverToken = redisTemplate.opsForValue().get(cacheKey);
        return Objects.isNull(serverToken);
    }

    @Override
    public boolean cleanToken(String cacheKey, String operSubjectGuid) {
        TokenRequestBO tokenRequestBO = jwtParserUtil.acquirePayload(cacheKey);
        String openId = tokenRequestBO.getOpenId();

        String key = TOKEN_PREFIX + openId + ":" + operSubjectGuid;
        Object object = stringRedisTemplate.opsForValue().get(key);
        if (Objects.nonNull(object)){
            return Boolean.TRUE.equals(stringRedisTemplate.delete(key));
        }
        return false;
    }


    @Override
    public boolean cleanToken(String cacheKey, String operSubjectGuid, String roleType) {
        String key = cacheKey + StringConstant.COLON + operSubjectGuid + StringConstant.COLON + roleType;
        if (!getGradeRefresh(key)) {
            return redisTemplate.delete(key);
        }
        return false;
    }

    @Override
    public boolean delete(String cacheKey) {
        return redisTemplate.delete(cacheKey);
    }

    /**
     * 是否存在
     *
     * @param token  token
     * @param source source
     * @return boolean
     */
    public boolean isServerExistToken(String token, String source) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        try {
            String openId = jwtParserUtil.acquireUserFromToken(token);
            String cacheKey = TOKEN_PREFIX + openId + ":" + source;
            Object serverToken = redisTemplate.opsForValue().get(cacheKey);
            return serverToken != null && serverToken.toString().split(StringConstant.POUND)[0].equals(token);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void updateInventoryNum(String key, String code) {

        RLock lock = redissonClient.getLock(StringConstant.COUPON_PACKAGE_INVENTORY + code);
        try {
            if (lock.isLocked()) {
                updateInventoryNum(key, code);
            }
            lock.lock();
            stringRedisTemplate.opsForValue().decrement(key, 1L);
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("更新券包库存失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    @Override
    public int getInventoryNum(String key, String code) {
        RLock lock = redissonClient.getLock(StringConstant.COUPON_PACKAGE_INVENTORY + code);
        try {
            if (lock.isLocked()) {
                getInventoryNum(key, code);
            }
            lock.lock();
            return Integer.parseInt(stringObjectRedisTemplate.opsForValue().get(key) + "");
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("更新券包库存失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    /**
     * 定时器防重锁
     *
     * @return
     */
    @Override
    public Boolean setLock(String key) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_XXL_JOB_COUPON_NOTICE);
        try {
            if (lock.isLocked()) {
                return true;
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.isNull(object)) {
                stringObjectRedisTemplate.opsForValue().set(key, -1, 1, TimeUnit.MINUTES);
                return false;
            } else {
                log.info("XXL-JOB防重拦截，重复时间：{}", JSON.toJSONString(object));
                return true;
            }

        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("XXL-JOB防重锁失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }


    /**
     * 定时器防重锁
     *
     * @return
     */
    @Override
    public Boolean setCardLock(String key) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_XXL_JOB_CARD_NOTICE);
        try {
            if (lock.isLocked()) {
                return true;
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.isNull(object)) {
                stringObjectRedisTemplate.opsForValue().set(key, -1, 1, TimeUnit.MINUTES);
                return false;
            } else {
                log.info("XXL-JOB防重拦截，重复时间：{}", JSON.toJSONString(object));
                return true;
            }

        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("XXL-JOB防重锁失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    @Override
    public void setOldGradeEquitiesList(String operSubjectGuid, String finalRoleType, List<String> gradeInfoList) {
        final String key = String.format(RedisKeyConstant.GRADE_EQUITIE_OLD_LIST, operSubjectGuid, finalRoleType);
        stringRedisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(gradeInfoList), 1, TimeUnit.HOURS);
    }

    @Override
    public List<String> getOldGradeEquitieList(String operSubjectGuid, String finalRoleType) {
        final String key = String.format(RedisKeyConstant.GRADE_EQUITIE_OLD_LIST, operSubjectGuid, finalRoleType);
        final String value = stringRedisTemplate.opsForValue().get(key);
        return Optional.ofNullable(value)
                .map(s -> JacksonUtils.toObjectList(String.class, s))
                .orElse(Collections.emptyList());
    }


}
