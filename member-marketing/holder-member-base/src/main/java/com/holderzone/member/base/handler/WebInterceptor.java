package com.holderzone.member.base.handler;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.holderzone.feign.spring.boot.util.TraceidUtils;
import com.holderzone.member.common.annotation.FunctionLabel;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.util.thead.SnowflakeKeyGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @date 2018/09/10 16:26
 * @description 接口拦截
 */
@Slf4j
@Component
public class WebInterceptor implements HandlerInterceptor {

    /**
     * 白名单url，不需要校验userInfo信息
     */
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/hsm-member-statistics/querySexAndConsume",
            "/hsm-member-statistics/ageDistribution",
            "/hsm-member-statistics/memberGrowth",
            "/hsmdc/store/business/getStoreByPayWay",
            "/getOperSubjectInfo",
            "/store/sync",
            "/member-store/querySubjects",
            "/hsa-LabelSetting/initializeAutomaticLabel",
            "/hsa-LabelSetting/refreshLabel",
            "/applets/auth",
            "/hsa-base/getOperatingSubjectInfo",
            "/file/member/uploadImage",
            "/ter-card/order_rights_callback",
            "/goalgo_callback/initialize_subject_data",
            "/goalgo_callback/initialize_old_subject_data",
            "/applets/card/aggregate_pay_order",
            "/commodity/commodity_data_synchronization",
            "ter-card/getMemberUploadPage",
            "/data_item/init_update_info",
            "/applets/card/order_generate_callback_processing",
            "/applets/card/accumulation_discount_release",
            "/applets/occupy_order_deduct_detail",
            "/applets/release_order_deduct_detail",
            "/hsa-member-coupon-package-link/xxlJobCouponPackageGrant",
            "/base/hsa-member-coupon-package-link/xxlJobCouponPackageGrant",
            "/applets/coupon/sendMemberCouponExpireNotice",
            "/base/applets/coupon/sendMemberCouponExpireNotice",
            "/equities_center/grantFeignCardRights",
            "/applets/release_order_deduct_detail",
            "/hsa-member/initMemberPinyin",
            "/label/listOperGuid",
            "/hsa-base/autoInit",
            "/hsa-member/get/member/label/grade",
            "/hsa-member/getMemberInfo",
            "/hsa-member/addMemberInfo"
    );

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) {
        ThreadLocalCache.remove();
        MDC.remove(FilterConstant.TRACE_ID);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {

    }

    @Override
    @FunctionLabel
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {

        //链路：若是有上层调用就用上层的ID
        String baseTraceId = request.getHeader(TraceidUtils.TRACEID_KEY);
        if (baseTraceId == null) {
            SnowflakeKeyGeneratorUtil snowflakeKeyGeneratorUtil = new SnowflakeKeyGeneratorUtil();
            baseTraceId = snowflakeKeyGeneratorUtil.generateKey().toString();
        }
        TraceidUtils.setTraceid(baseTraceId);

        String userInfo = request.getHeader(FilterConstant.USER_INFO);
        //只要有就存
        if (StringUtils.isNotEmpty(userInfo)) {
            userInfo = URLDecoder.decode(userInfo, "UTF-8");
            log.info("userInfo信息初始化：{}", userInfo);
            ThreadLocalCache.put(userInfo);
        }
        String url = request.getRequestURI();

        log.info("url：{}", url);

        if (checkUrl(url)) {
            return true;
        }

        final String xxlJob = request.getHeader(FilterConstant.XXL_JOB_TYPE);
        //定时任务执行放行
        if (FilterConstant.XXL_JOB_VALUE.equals(xxlJob)) {
            HeaderUserInfo headerUserInfo = new HeaderUserInfo();
            final String operSubjectGuid = request.getHeader(FilterConstant.OPER_SUBJECT_GUID);
            headerUserInfo.setOperSubjectGuid(operSubjectGuid);
            log.info("headerUserInfo请求入参 => url:{},{}", request.getRequestURI(), JSONObject.toJSONString(headerUserInfo));
            userInfo = JSONUtil.toJsonStr(headerUserInfo);
            ThreadLocalCache.put(URLDecoder.decode(userInfo, "UTF-8"));
            log.info("base定时任务放行");
            return Boolean.TRUE;
        }
        if (StringUtils.isEmpty(userInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_HEADER_OPERATION_GUID);
        }
        try {

            userInfo = userInfo.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
            userInfo = userInfo.replace("\\+", "%2B");
            userInfo = URLDecoder.decode(userInfo, "UTF-8");
            log.info("userInfo信息初始化：{}", userInfo);
            ThreadLocalCache.put(userInfo);
            return true;
        } catch (Exception e) {
            errorMsg(response);
            return false;
        }

    }

    private boolean checkUrl(String url) {
        if (filterUrl(url)) {
            return true;
        }
        // 校验头部信息
        if (Pattern.matches("/applets/.*", url) && !Pattern.matches("/applets/card.*", url)) {
            return true;
        }

        if (Pattern.matches("/statement/.*", url)) {
            return true;
        }

        // 白名单
        return WHITE_LIST.contains(url);
    }

    private boolean filterUrl(String url) {
        for (String systemUrl : FilterConstant.SYSTEM_URL) {
            if (url.startsWith(systemUrl)) {
                return true;
            }
        }
        return false;
    }

    private void errorMsg(HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.reset();
        PrintWriter printWriter = response.getWriter();
        printWriter.write(JSONObject.toJSONString(Result.error(CommonEnum.HEADER_ILLEGAL)));
        printWriter.flush();
        printWriter.close();
    }


}
