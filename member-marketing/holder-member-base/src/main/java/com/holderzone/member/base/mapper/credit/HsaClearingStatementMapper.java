package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaClearingStatement;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.credit.AuditClearingStatementQO;
import com.holderzone.member.common.qo.credit.ClearingStatementQO;
import com.holderzone.member.common.qo.credit.ReconciliationStatusQO;
import com.holderzone.member.common.vo.credit.ClearingStatementVO;
import com.holderzone.member.common.vo.credit.CreditClearingVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-06-14 11:18
 */
public interface HsaClearingStatementMapper extends HolderBaseMapper<HsaClearingStatement> {

    /**
     * 查询小程序挂账结算单列表
     *
     * @param request 请求参数
     * @return 操作结果
     */
    List<CreditClearingVO> queryClearingStatementList(@Param("request") AuditClearingStatementQO request);

    /**
     * 查询统计结算数量
     *
     * @param operSubjectGuid 运营主体
     * @param accountPhone    账户手机号
     * @param status          结账单状态
     * @return 统计数量
     */
    Integer queryCountClearingStatement(@Param("operSubjectGuid") String operSubjectGuid,
                                        @Param("accountPhone") String accountPhone,
                                        @Param("status") Integer status);

    /**
     * 修改结算单对账状态
     *
     * @param request 请求参数
     * @return 操作结果 操作结果
     */
    int updateReconciliationStatus(@Param("request") ReconciliationStatusQO request);

    /**
     * 结算单应收调整修改
     *
     * @param adjustAmount            调整金额
     * @param clearingStatementNumber 结算单
     * @return 操作结果
     */
    int receivableAmountAdjust(@Param("adjustAmount") BigDecimal adjustAmount,
                               @Param("clearingStatementNumber") String clearingStatementNumber);

    /**
     * 查询结算单列表
     *
     * @param request 查询结算单列表请求qo
     * @return 操作结果
     */
    List<ClearingStatementVO> queryClearingStatement(@Param("request") ClearingStatementQO request);
}
