package com.holderzone.member.base.entity.permission;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 商家权限
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_partner_merchant_permission")
public class HsaPartnerMerchantPermission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 岗位id
     */
    private String positionGuid;

    /**
     * 商家名称guid
     */
    private String merchantGuid;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 是否选择（1：选中，0未选中）
     * 默认选中
     */
    private Integer isChecked = 1;

    /**
     * 1：会员管理
     * 2：营销中心
     * @see com.holderzone.member.common.enums.SystemPermissionEnum
     */
    private Integer sourceType;

    /**
     * 0：岗位
     * 1:角色
     */
    private Integer isRole;
}
