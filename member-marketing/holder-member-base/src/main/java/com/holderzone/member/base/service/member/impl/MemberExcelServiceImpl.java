package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.handler.ExportFieldPermissionHandler;
import com.holderzone.member.base.listener.CustomerImportRelationLabelListener;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDetailMapper;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.service.card.HsaMemberInfoCardService;
import com.holderzone.member.base.service.grade.HsaMemberApplyGradeRecordService;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.base.service.integral.HsaIntegralDetailService;
import com.holderzone.member.base.service.member.HsaMemberFundingDetailService;
import com.holderzone.member.base.service.member.MemberExcelService;
import com.holderzone.member.base.service.permission.impl.HsaOperSubjectPermissionServiceImpl;
import com.holderzone.member.base.util.EasyExcelUtils;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.ExcelImportConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.excel.ExportDataDTO;
import com.holderzone.member.common.dto.excel.ExportField;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.integral.MemberIntegralDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.permission.HsaListScopePermissionDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.RegisterChannelEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.card.CardTypeEnum;
import com.holderzone.member.common.enums.excel.CustomExportTypeEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.card.QueryMemberInfoCardQO;
import com.holderzone.member.common.qo.excel.CustomExportQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailRequest;
import com.holderzone.member.common.qo.integral.IntegralDetailQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.excel.ExcelResultVO;
import com.holderzone.member.common.vo.grade.GradeUpgradeApplyPageVO;
import com.holderzone.member.common.vo.member.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.*;

/**
 * @ProjectName: member-marketing
 * @ClassName: MemberExcelServiceImpl
 * @Author: pantao
 * @Description:
 * @Date: 2021/8/17 15:43
 * @Version: 1.0
 */
@Slf4j
@Service
public class MemberExcelServiceImpl implements MemberExcelService {

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaLabelSettingMapper hsaLabelSettingMapper;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private FileOssService fileOssService;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private HsaMemberFundingDetailService hsaMemberFundingDetailService;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private HsaOperSubjectPermissionServiceImpl hsaOperSubjectPermissionService;

    @Resource
    private RequestGoalgoService operatingSubjectService;

    @Resource
    private HsaIntegralDetailMapper hsaIntegralDetailMapper;

    @Autowired
    @Lazy
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Autowired
    @Lazy
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Autowired
    @Lazy
    private HsaMemberApplyGradeRecordService hsaMemberApplyGradeRecordService;

    @Autowired
    @Lazy
    private HsaMemberInfoCardService hsaMemberInfoCardService;

    @Resource
    private ExternalSupport externalSupport;

    private static final String EXCEL_PREFIX = "会员账户";

    private static final String DEFAULT_SHEET = "sheet1";

    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

    private static final String UTF_8 = "utf-8";

    private static final String HEADER = "Content-disposition";

    private static final String ATTACHMENT = "attachment;filename=";

    private static final String LABEL_RELATION_MEMBER = "标签关联会员";

    private static final String BALANCE_CHANGE_RECORD = "会员卡余额变动记录";

    private static final String GROWTH_VALUE_SHEET = "成长值完成记录";


    private static final int ADD = 0;

    @Resource
    private HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    private static final String RAIL = "-";


    @Override
    public void exportMemberList(MemberListQO memberListQO, HttpServletResponse response) throws IOException {
        //设置运营主体
        memberListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        int count = hsaOperationMemberInfoMapper.countMemberInfo(memberListQO);
        if (CollectionUtils.isEmpty(memberListQO.getMemberGuids()) && count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<MemberInfoVO> list = hsaOperationMemberInfoMapper.findAllMemberInfo(memberListQO);
        if (CollectionUtils.isEmpty(list)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        List<String> memberGuids = list.stream().map(MemberInfoVO::getMemberGuid).collect(Collectors.toList());
        //会员卡余额
        Map<String, BigDecimal> memberAmountMap = hsaMemberInfoCardMapper.findAmountMemberCard(memberGuids)
                .stream().collect(Collectors.toMap(MemberCardAmountDTO::getMemberGuid,
                        MemberCardAmountDTO::getMemberCardAmount));
        //所有标签
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));
        //所有消费信息
        List<MemberConsumptionInfoDTO> memberConsumptions = hsaMemberConsumptionMapper.
                getConsumptionDetailByMemberGuid(memberGuids);

        List<MemberConsumptionInfoDTO> memberConsumptionsCount = hsaMemberConsumptionMapper.getConsumptionCountByMemberGuid(memberGuids);
        Map<String, MemberConsumptionInfoDTO> consumptionMap = memberConsumptions.stream().collect(Collectors.
                toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));

        for (MemberConsumptionInfoDTO memberConsumptionInfoDTO : memberConsumptionsCount) {
            if (consumptionMap.containsKey(memberConsumptionInfoDTO.getMemberInfoGuid())) {
                consumptionMap.get(memberConsumptionInfoDTO.getMemberInfoGuid())
                        .setConsumptionCount(memberConsumptionInfoDTO.getConsumptionCount());
            }
        }

        //所有充值信息
        Map<String, MemberConsumptionInfoDTO> rechargeMap = hsaMemberConsumptionMapper.
                getRechargeDetailByMemberGuid(memberGuids).stream().collect(Collectors.
                        toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        //累计积分
        Map<String, Integer> integralMap = hsaIntegralDetailMapper.sumIntegralByMemberGuids(memberGuids)
                .stream().collect(Collectors.toMap(MemberIntegralDTO::getMemberInfoGuid,
                        MemberIntegralDTO::getTotalIntegral, (entity1, entity2) -> entity1));
        String fileName = systemRoleHelper.getReplace(EXCEL_PREFIX, ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now()
                , DateUtil.DATE_PATTERN);
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding(UTF_8);
        fileName = URLEncoder.encode(fileName, UTF_8);
        response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        String userGuid = Optional.ofNullable(headerUserInfo).map(HeaderUserInfo::getUserGuid).orElse(null);
        UserPermissionDTO userPermissionDTO = hsaOperSubjectPermissionService.executeRequest(MemberPermissionTypeEnum.MEMBER_MARKET.getDes(), userGuid, null);
        List<String> head = hsaOperSubjectPermissionService.getDataScopePermissionVO(userPermissionDTO, null).stream()
                .filter(x -> Objects.equals(x.getIsChecked(), 1)).map(HsaListScopePermissionDTO::getListField).collect(Collectors.toList());
        List<Integer> fields = new ArrayList<>();
        if (CollUtil.isEmpty(head)) {
            return;
        }
        fields.add(0);
        for (String field : head) {
            fields.add(MemberInfoListEnum.getIndexByField(field));
        }
        ExportFieldPermissionHandler exportFieldPermissionHandler = new ExportFieldPermissionHandler(fields);
        EasyExcel.write(response.getOutputStream(), ExportExcelMemberVO.class).sheet(DEFAULT_SHEET).registerWriteHandler(exportFieldPermissionHandler).
                doWrite(toExportExcelMemberVOList(list, allLabelMap, allMemberMap, consumptionMap, rechargeMap, integralMap, memberAmountMap));
    }

    private List<ExportExcelMemberVO> queryMemberExportData(MemberListQO memberListQO) {

        memberListQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        int count = hsaOperationMemberInfoMapper.countMemberInfo(memberListQO);
        if (CollectionUtils.isEmpty(memberListQO.getMemberGuids()) && count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<MemberInfoVO> list = hsaOperationMemberInfoMapper.findAllMemberInfo(memberListQO);
        if (CollectionUtils.isEmpty(list)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        List<String> memberGuids = list.stream().map(MemberInfoVO::getMemberGuid).collect(Collectors.toList());
        //所有标签
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));
        //所有消费信息
        List<MemberConsumptionInfoDTO> memberConsumptions = hsaMemberConsumptionMapper.
                getConsumptionDetailByMemberGuid(memberGuids);
        Map<String, MemberConsumptionInfoDTO> consumptionMap = memberConsumptions.stream().collect(Collectors.
                toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        List<MemberConsumptionInfoDTO> memberConsumptionsCount = hsaMemberConsumptionMapper.getConsumptionCountByMemberGuid(memberGuids);
        for (MemberConsumptionInfoDTO memberConsumptionInfoDTO : memberConsumptionsCount) {
            if (consumptionMap.containsKey(memberConsumptionInfoDTO.getMemberInfoGuid())) {
                consumptionMap.get(memberConsumptionInfoDTO.getMemberInfoGuid())
                        .setConsumptionCount(memberConsumptionInfoDTO.getConsumptionCount());
            }
        }

        //所有充值信息
        Map<String, MemberConsumptionInfoDTO> rechargeMap = hsaMemberConsumptionMapper.
                getRechargeDetailByMemberGuid(memberGuids).stream().collect(Collectors.
                        toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        //累计积分
        Map<String, Integer> integralMap = hsaIntegralDetailMapper.sumIntegralByMemberGuids(memberGuids)
                .stream().collect(Collectors.toMap(MemberIntegralDTO::getMemberInfoGuid,
                        MemberIntegralDTO::getTotalIntegral, (entity1, entity2) -> entity1));

        //会员卡余额
        Map<String, BigDecimal> memberAmountMap = hsaMemberInfoCardMapper.findAmountMemberCard(memberGuids)
                .stream().collect(Collectors.toMap(MemberCardAmountDTO::getMemberGuid,
                        MemberCardAmountDTO::getMemberCardAmount));

        return toExportExcelMemberVOList(list, allLabelMap, allMemberMap, consumptionMap, rechargeMap, integralMap, memberAmountMap);
    }


    /**
     * 封装ExportExcelMemberVO 集合
     *
     * @param memberInfoVOList 会员列表返回参数
     * @return 操作结果
     */
    private List<ExportExcelMemberVO> toExportExcelMemberVOList(List<MemberInfoVO> memberInfoVOList, Map<String, String> allLabelMap,
                                                                Map<String, String> allMemberMap,
                                                                Map<String, MemberConsumptionInfoDTO> consumptionMap,
                                                                Map<String, MemberConsumptionInfoDTO> rechargeMap,
                                                                Map<String, Integer> integralMap,
                                                                Map<String, BigDecimal> memberAmountMap) {

        if (CollectionUtils.isEmpty(memberInfoVOList)) {
            return new ArrayList<>();
        }
        List<ExportExcelMemberVO> excelMemberVOList = new ArrayList<>();
        int index = 1;
        boolean phonePermission = isPhonePermission();
        for (MemberInfoVO memberInfoVO : memberInfoVOList) {
            excelMemberVOList.add(toExportExcelMemberVO(memberInfoVO, index, allMemberMap.get(memberInfoVO.getMemberGuid()),
                    allLabelMap.get(memberInfoVO.getMemberGuid()), consumptionMap.get(memberInfoVO.getMemberGuid()),
                    rechargeMap.get(memberInfoVO.getMemberGuid()), phonePermission, integralMap.get(memberInfoVO.getMemberGuid())
                    , memberAmountMap.get(memberInfoVO.getMemberGuid())));
            index++;
        }

        return excelMemberVOList;
    }

    /**
     * 是否拥有查看完整手机号权限
     *
     * @return
     */
    private boolean isPhonePermission() {
        boolean phonePermission;  //是否有查看完整手机号权限
        try {
            phonePermission = operatingSubjectService.phonePermission(SystemPermissionEnum.MEMBER_PERMISSION.getDes());
        } catch (Exception e) {
            phonePermission = false;
        }
        return phonePermission;
    }

    /**
     * 封装ExportExcelMemberVO
     *
     * @param memberInfoVO 会员列表返回参数
     * @return 操作结果
     */
    private ExportExcelMemberVO toExportExcelMemberVO(MemberInfoVO memberInfoVO, int index, String memberCard,
                                                      String memberLabel, MemberConsumptionInfoDTO consumptionInfo,
                                                      MemberConsumptionInfoDTO rechargeInfo, boolean isTrue,
                                                      Integer integral,
                                                      BigDecimal amount) {


        Optional<MemberInfoVO> memberInfoVO1 = Optional.ofNullable(memberInfoVO);
        String codePhoneNum = null;
        if (memberInfoVO1.isPresent()) {  //给手机号+区号
            String phoneCountryCode = memberInfoVO1.map(MemberInfoVO::getPhoneCountryCode).orElse(null);
            String phoneNum = memberInfoVO1.map(MemberInfoVO::getPhoneNum).orElse(null);
            codePhoneNum = StringBaseHandlerUtil.phoneCountryCodeHandler(phoneCountryCode, phoneNum, isTrue);
        }
        ExportExcelMemberVO exportExcelMemberVO = new ExportExcelMemberVO()
                .setNumber(index)
                .setAccountState(AccountStateEnum.getNameByCode(memberInfoVO.getAccountState()))
                .setMemberAccount(memberInfoVO.getMemberAccount())
                .setRoleType(RoleTypeEnum.convertName(memberInfoVO.getRoleType()))
                .setUserName(memberInfoVO.getUserName())
                .setSex(SexEnum.getSex(memberInfoVO.getSex()))
                .setPhoneNum(codePhoneNum)
                .setBirthday(DateUtil.formatLocalDateTime(memberInfoVO.getBirthday()
                        , DateUtil.PATTERN_DATE))
                .setMemberGrowthValue(memberInfoVO.getMemberGrowthValue())
                .setMemberLevel(ObjectUtil.objToString(memberInfoVO.getMemberLevel()))
                .setMemberCard(memberCard)
                .setMemberLabel(memberLabel)

                .setMemberIntegral(memberInfoVO.getMemberIntegral())
                .setTotalMemberIntegral(integral)
                .setAccountMoney(Objects.isNull(amount) ? BigDecimal.ZERO : amount)
                .setGmtCreate(DateUtil.formatLocalDateTime(memberInfoVO.getGmtCreate()
                        , DateUtil.PATTERN_DATE))
                .setSourceType(RegisterChannelEnum.getNameByCode(memberInfoVO.getSourceType()))
                .setBelongStore(memberInfoVO.getBelongStore());
        if (Objects.nonNull(consumptionInfo)) {
            exportExcelMemberVO.setConsumptionMoney(consumptionInfo.getConsumptionAmount());
            exportExcelMemberVO.setLastConsumptionTime(DateUtil.formatLocalDateTime(consumptionInfo.getConsumptionTime(),
                    DateUtil.PATTERN_DATE));
            exportExcelMemberVO.setConsumptionCount(consumptionInfo.getConsumptionCount());
        }
        if (Objects.nonNull(rechargeInfo)) {
            exportExcelMemberVO.setRechargeMoney(rechargeInfo.getRechargeAmount());
            exportExcelMemberVO.setRechargeCount(rechargeInfo.getRechargeCount());
        }
        return exportExcelMemberVO;
    }

    @Override
    public void exportLabelList(MemberLabelListQO memberLabelListQO, HttpServletResponse response) throws IOException {

        //设置运营主体
        memberLabelListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        List<MemberLabelListVO> list = hsaLabelSettingMapper.findAllMemberLabel(memberLabelListQO);
        if (CollectionUtils.isEmpty(list)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }

        String fileName = systemRoleHelper.getReplace("会员标签", ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now()
                , DateUtil.DATE_PATTERN);
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding(UTF_8);
        fileName = URLEncoder.encode(fileName, UTF_8);
        //response.setHeader(HEADER, "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName + ExcelTypeEnum.XLSX.getValue());

        response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExportExcelLabelVO.class).sheet(DEFAULT_SHEET).doWrite(toExportExcelLabelVOList(list));
    }

    /**
     * 封装ExportExcelLabelVO集合
     *
     * @param list 查询会员标签列表返vo集合
     * @return 操作结果
     */
    private List<ExportExcelLabelVO> toExportExcelLabelVOList(List<MemberLabelListVO> list) {
        //序号
        int number = 1;
        List<ExportExcelLabelVO> exportExcelLabelVOList = new ArrayList<>();
        for (MemberLabelListVO memberLabelListVO : list) {
            exportExcelLabelVOList.add(toExportExcelLabelVO(memberLabelListVO, number));
            number++;
        }
        return exportExcelLabelVOList;
    }

    /**
     * 封装ExportExcelLabelVO
     *
     * @param memberLabelListVO 查询会员标签列表返vo
     * @param number            序号
     * @return 操作结果
     */
    private ExportExcelLabelVO toExportExcelLabelVO(MemberLabelListVO memberLabelListVO, int number) {

        return new ExportExcelLabelVO()
                .setNumber(number)
                .setLabelName(memberLabelListVO.getLabelName())
                .setLabelType(LabelTypeEnum.getDesByCode(memberLabelListVO.getLabelType()))
                .setRelationCount(memberLabelListVO.getRelationCount())
                .setGmtCreate(DateUtil.formatLocalDate(memberLabelListVO.getGmtCreate()
                        , DateUtil.PATTERN_DATE))
                .setGmtModified(DateUtil.formatLocalDate(memberLabelListVO.getGmtModified()
                        , DateUtil.PATTERN_DATE))
                .setIsEnable(EnableEnum.getDesByCode(memberLabelListVO.getIsEnable()))
                .setOperatorName(memberLabelListVO.getOperatorName() + "/" + memberLabelListVO.getOperatorAccount());
    }


    @Override
    public void exportBalanceRecord(BalanceRecordQO qo, HttpServletResponse response) throws IOException {

        //设置运营主体
        qo.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        int count = hsaMemberFundingDetailMapper.queryBalanceRecordCount(qo);
        if (count == 0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<BalanceRecordVO> list = hsaMemberFundingDetailMapper.queryBalanceRecordList(qo);
        String fileName = systemRoleHelper.getReplace(BALANCE_CHANGE_RECORD, ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now()
                , DateUtil.DATE_PATTERN);
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding(UTF_8);
        fileName = URLEncoder.encode(fileName, UTF_8);
        //response.setHeader(HEADER, "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName + ExcelTypeEnum.XLSX.getValue());

        response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), BalanceRecordExcelVO.class).sheet(DEFAULT_SHEET).doWrite(toBalanceRecordExcelVOList(list));
    }

    private List<BalanceRecordExcelVO> toBalanceRecordExcelVOList(List<BalanceRecordVO> balanceRecordVOS) {
        int number = 1;
        boolean phonePermission = isPhonePermission();
        List<BalanceRecordExcelVO> balanceRecordExcelVOS = new ArrayList<>();
        for (BalanceRecordVO balanceRecord : balanceRecordVOS) {
            balanceRecordExcelVOS.add(toBalanceRecordExcelVO(balanceRecord, number, phonePermission));
            number++;
        }
        return balanceRecordExcelVOS;
    }


    private BalanceRecordExcelVO toBalanceRecordExcelVO(BalanceRecordVO balanceRecordVO, int number, Boolean isTrue) {

        String phoneNum = StringBaseHandlerUtil.phoneCountryCodeHandler(balanceRecordVO.getPhoneCountryCode(), balanceRecordVO.getPhoneNum(), isTrue);
        return new BalanceRecordExcelVO()
                .setNumber(number)
                .setCardName(balanceRecordVO.getCardName())
                .setCardNum(balanceRecordVO.getCardNum())
                .setCardType(CardTypeEnum.getDesByCode(balanceRecordVO.getCardType()))
                .setPhoneNum(StringUtils.isEmpty(phoneNum) ? RAIL : phoneNum)
                .setChangeTime(DateUtil.formatLocalDateTime(balanceRecordVO.getChangeTime(), DateUtil.PATTERN_DATETIME))
                .setAmountSourceType(AmountSourceTypeEnum.getDesByCode(balanceRecordVO.getAmountSourceType()))
                .setRechargeAmount(getChangeBalanceByType(balanceRecordVO, BalanceChangeTypeEnum.RECHARGE.getCode()))
                .setCardRechargeResidualBalance(BigDecimalUtil.nonNullValue(balanceRecordVO.getCardRechargeResidualBalance()))
                .setGiftAmount(getChangeBalanceByType(balanceRecordVO, BalanceChangeTypeEnum.GIFT.getCode()))
                .setCardGiftResidualBalance(BigDecimalUtil.nonNullValue(balanceRecordVO.getCardGiftResidualBalance()))
                .setSubsidyAmount(getChangeBalanceByType(balanceRecordVO, BalanceChangeTypeEnum.SUBSIDY.getCode()))
                .setCardSubsidyResidualBalance(BigDecimalUtil.nonNullValue(balanceRecordVO.getCardSubsidyResidualBalance()))
                .setChangeTotalMoney(hsaMemberFundingDetailService.computeChangeTotalMoney(balanceRecordVO))
                .setChangeSource(externalSupport.baseServer(ThreadLocalCache.getSystem()).getSourceTypeEnum(balanceRecordVO.getChangeSource()))
                .setSourceStore(StringUtils.isEmpty(balanceRecordVO.getSourceStore()) ? RAIL : balanceRecordVO.getSourceStore())
                .setRemark(balanceRecordVO.getRemark())
                .setOperatorTelName(StringUtils.isEmpty(balanceRecordVO.getOperatorTelName()) || "null/null".equals(balanceRecordVO.
                        getOperatorTelName()) ? RAIL : balanceRecordVO.getOperatorTelName())
                .setResidualTotal(balanceRecordVO.getResidualTotal())
                ;

    }

    private BigDecimal getChangeBalanceByType(BalanceRecordVO balanceRecordVO, int type) {

        BalanceChangeTypeEnum balanceChangeTypeEnum = BalanceChangeTypeEnum.getEnumByCode(type);
        switch (balanceChangeTypeEnum) {
            case RECHARGE:
                if (Objects.isNull(balanceRecordVO.getAmountRechargeFundingType())) {
                    return BigDecimal.ZERO;
                }
                if (balanceRecordVO.getAmountRechargeFundingType() == ADD) {
                    return balanceRecordVO.getRechargeAmount();
                } else {
                    return BigDecimal.ZERO.subtract(balanceRecordVO.getRechargeAmount());
                }
            case GIFT:
                if (Objects.isNull(balanceRecordVO.getAmountGiftFundingType())) {
                    return BigDecimal.ZERO;
                }
                if (balanceRecordVO.getAmountGiftFundingType() == ADD) {
                    return balanceRecordVO.getGiftAmount();
                } else {
                    return BigDecimal.ZERO.subtract(balanceRecordVO.getGiftAmount());
                }
            case SUBSIDY:
                if (Objects.isNull(balanceRecordVO.getAmountSubsidyFundingType())) {
                    return BigDecimal.ZERO;
                }
                if (balanceRecordVO.getAmountSubsidyFundingType() == ADD) {
                    return balanceRecordVO.getSubsidyAmount();
                } else {
                    return BigDecimal.ZERO.subtract(balanceRecordVO.getSubsidyAmount());
                }
            default:
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void exportRelationLabelMember(RelationLabelListQO relationLabelListQO, HttpServletResponse response) throws IOException {

        Integer count = hsaMemberLabelMapper.countRelationLabel(relationLabelListQO);
        if (count == NumberConstant.NUMBER_0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<RelationLabelListVO> list = hsaMemberLabelMapper.findAllRelationLabel(relationLabelListQO);
        List<String> memberGuids = list.stream().map(RelationLabelListVO::getMemberGuid).collect(Collectors.toList());
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));
        String fileName = systemRoleHelper.getReplace(LABEL_RELATION_MEMBER, ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.DATE_PATTERN);
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding(UTF_8);
        fileName = URLEncoder.encode(fileName, UTF_8);
        response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExportRelationLabelMemberVO.class).sheet(DEFAULT_SHEET).
                doWrite(toExportRelationLabelMemberVOList(list, allLabelMap, allMemberMap));

    }

    private List<ExportRelationLabelMemberVO> queryExportRelationLabelData(RelationLabelListQO relationLabelListQO) {
        Integer count = hsaMemberLabelMapper.countRelationLabel(relationLabelListQO);
        if (count == NumberConstant.NUMBER_0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<RelationLabelListVO> list = hsaMemberLabelMapper.findAllRelationLabel(relationLabelListQO);
        List<String> memberGuids = list.stream().map(RelationLabelListVO::getMemberGuid).collect(Collectors.toList());
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));
        return toExportRelationLabelMemberVOList(list, allLabelMap, allMemberMap);
    }

    /**
     * 封装导出关联标签
     *
     * @param relationLabelList 关联标签返回vo
     * @return 操作结果
     */
    private List<ExportRelationLabelMemberVO> toExportRelationLabelMemberVOList(List<RelationLabelListVO> relationLabelList,
                                                                                Map<String, String> allLabelMap,
                                                                                Map<String, String> allMemberMap) {
        //序号
        int number = 1;
        boolean phonePermission = isPhonePermission();
        List<ExportRelationLabelMemberVO> exportRelationLabelMemberVOS = new ArrayList<>();
        for (RelationLabelListVO relationLabelListVO : relationLabelList) {
            exportRelationLabelMemberVOS.add(toExportRelationLabelMemberVO(relationLabelListVO, number,
                    allLabelMap.get(relationLabelListVO.getMemberGuid()), allMemberMap.get(relationLabelListVO.getMemberGuid()), phonePermission));
            number++;
        }
        return exportRelationLabelMemberVOS;
    }

    /**
     * 封装导出关联标签
     *
     * @param relationLabel 关联标签返回vo
     * @param number        序号
     * @return 操作结果
     */
    private ExportRelationLabelMemberVO toExportRelationLabelMemberVO(RelationLabelListVO relationLabel, int number,
                                                                      String memberLabel, String memberCard, boolean isTrue) {
        String phoneCountryCode = Optional.ofNullable(relationLabel).map(RelationLabelListVO::getPhoneCountryCode).orElse(null);
        String phone = Optional.ofNullable(relationLabel).map(RelationLabelListVO::getPhoneNum).orElse(null);
        String phoneNum = StringBaseHandlerUtil.phoneCountryCodeHandler(phoneCountryCode, phone, isTrue);
        return new ExportRelationLabelMemberVO().setNumber(number)
                .setMemberAccount(relationLabel.getMemberAccount())
                .setUserName(relationLabel.getUserName())
                .setPhoneNum(phoneNum)
                .setSex(SexEnum.getSex(relationLabel.getSex()))
                .setMemberGrowthValue(relationLabel.getMemberGrowthValue())
                .setMemberGradeInfoName(relationLabel.getMemberGradeInfoName())
                .setMemberCard(memberCard)
                .setSourceType(RegisterChannelEnum.getNameByCode(relationLabel.getSourceType()))
                .setGmtCreate(DateUtil.formatLocalDateTime(relationLabel.getGmtCreate()
                        , DateUtil.PATTERN_DATE))
                .setConnectType(LabelConnectTypeEnum.getDesByCode(relationLabel.getConnectType()))
                .setRelationTime(DateUtil.formatLocalDateTime(relationLabel.getRelationTime()
                        , DateUtil.PATTERN_DATETIME))
                .setAccountState(AccountStateEnum.getNameByCode(relationLabel.getAccountState()))
                .setMemberLabel(memberLabel);
    }


    @Override
    public void importRechargeAmount(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        //实例化实现了AnalysisEventListener接口的类
        CustomerImportRelationLabelListener listener = new CustomerImportRelationLabelListener();
        //传入参数
        ExcelReader excelReader = new ExcelReader(inputStream, ExcelTypeEnum.XLSX, null, listener);
        //读取信息
        excelReader.read(new Sheet(NumberConstant.NUMBER_1, NumberConstant.NUMBER_0, ImportRechargeAmountQO.class));
        //获取数据
        List<Object> list = listener.getDatas();
        list.remove(0);
        list.remove(0);
        List<ImportRechargeAmountQO> importMemberInfoQOS = JSONArray.parseArray(JSONArray.toJSONString(list), ImportRechargeAmountQO.class);
        log.info("=======>" + JSONObject.toJSONString(importMemberInfoQOS));
        //hsaMemberInfoCardService.updateMemberRechargeAmount(importMemberInfoQOS);
        if (CollectionUtils.isEmpty(importMemberInfoQOS)) {
            log.info("修改数据为空");
        }


    }

    @Override
    public ExcelResultVO importMemberInfo(MultipartFile file, List<String> havePhoneList, String cacheKey) {
        if (ObjectUtil.checkParam(file)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        try {
            //List<String> phoneList = new ArrayList<>();
            //系统中已经关联的手机号
            InputStream inputStream = file.getInputStream();
            //实例化实现了AnalysisEventListener接口的类
            CustomerImportRelationLabelListener listener = new CustomerImportRelationLabelListener();
            //传入参数
            ExcelReader excelReader = new ExcelReader(inputStream, ExcelTypeEnum.XLSX, null, listener);
            //读取信息
            excelReader.read(new Sheet(NumberConstant.NUMBER_1, NumberConstant.NUMBER_0, ImportMemberInfoQO.class));
            //获取数据
            List<Object> list = listener.getDatas();
            List<MemberInfoUploadError> errorDataList = new ArrayList<>();
            //返回数据
            List<String> returnPhoneList = new ArrayList<>();
            //判断数据大小
            if (list.size() == NUMBER_0 || list.size() == NUMBER_1 || list.size() == NUMBER_2) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
            }
            if (list.size() >= NUMBER_5003) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_UPLOAD_EXCEL);
            }
            ImportMemberInfoQO heard = (ImportMemberInfoQO) list.get(0);
            if (!heard.getPhoneNum().contains(ExcelImportConstant.HEADER_MEMBER_PHONE)
                    || cn.hutool.core.util.ObjectUtil.notEqual(heard.getUserName(), ExcelImportConstant.HEADER_MEMBER_NAME)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
            }
            list.remove(0);
            list.remove(0);
            List<ImportMemberInfoQO> importMemberInfoQOS = JSONArray.parseArray(JSONArray.toJSONString(list), ImportMemberInfoQO.class);
            List<String> importPhoneNum = importMemberInfoQOS.stream().map(ImportMemberInfoQO::getPhoneNum).collect(Collectors.toList());
            List<String> totalPhoneNum = queryRegisterMember(importPhoneNum, ThreadLocalCache.getOperSubjectGuid());
            totalPhoneNum = ObjectUtil.listDistinct(totalPhoneNum);
            //错误数据序号
            int number = 1;
            for (ImportMemberInfoQO memberInfo : importMemberInfoQOS) {
                if (ObjectUtil.isEmpty(memberInfo.getPhoneNum())) {
                    errorDataList.add(toMemberInfoUploadError(memberInfo, number, ExcelImportConstant.
                            IMPORT_ERROR_PHONE_NULL));
                    number++;
                    continue;
                }
                if (!NumberUtil.isPhoneNum11(memberInfo.getPhoneNum())) {
                    errorDataList.add(toMemberInfoUploadError(memberInfo, number, ExcelImportConstant.
                            IMPORT_ERROR_PHONE_ILLEGAL));
                    number++;
                    continue;
                }
                if (havePhoneList.contains(memberInfo.getPhoneNum())) {
                    errorDataList.add(toMemberInfoUploadError(memberInfo, number, ExcelImportConstant.
                            IMPORT_ERROR_PHONE_REPETITION));
                    number++;
                    continue;
                }
                if (!totalPhoneNum.contains(memberInfo.getPhoneNum())) {
                    errorDataList.add(toMemberInfoUploadError(memberInfo, number, ExcelImportConstant.IMPORT_ERROR_PHONE_UNREGISTERED));
                    number++;
                    continue;
                }
                havePhoneList.add(memberInfo.getPhoneNum());
                returnPhoneList.add(memberInfo.getPhoneNum());
            }
            ExcelResultVO excelResultVO = new ExcelResultVO();
            excelResultVO.setErrorSize(0);
            excelResultVO.setSuccessSize(0);
            if (CollectionUtil.isNotEmpty(returnPhoneList)) {
                //根据手机号去查询数据
                MemberListExcelQO qo = new MemberListExcelQO();
                qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                qo.setPhoneList(returnPhoneList);
                List<MemberInfoExcelVO> excelMemberInfo = hsaOperationMemberInfoMapper.findExcelMemberInfo(qo);
                excelMemberInfo = sortMemberInfoExcelVO(excelMemberInfo, returnPhoneList);
                excelResultVO.setSuccessDataList(excelMemberInfo);
                excelResultVO.setSuccessSize(excelMemberInfo.size());
            }
            if (CollectionUtil.isNotEmpty(errorDataList)) {
                excelResultVO.setErrorDataUrl(getUploadUrl(errorDataList).replaceAll("http", "https"));
                excelResultVO.setErrorSize(errorDataList.size());
            }
            if (StringUtils.isNotBlank(cacheKey)) {
                stringRedisTemplate.opsForValue().set(RedisKeyConstant.EXCEL_CACHE_KEY + cacheKey, JSON.toJSONString(havePhoneList), 30L, TimeUnit.MINUTES);
            }
            return excelResultVO;
        } catch (Exception e) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FILE_UPLOAD);
        }
    }

    private List<MemberInfoExcelVO> sortMemberInfoExcelVO(List<MemberInfoExcelVO> excelMemberInfo, List<String> returnPhoneList) {
        Map<String, MemberInfoExcelVO> map = excelMemberInfo.stream().collect(Collectors.toMap(MemberInfoExcelVO::getPhoneNum, Function.identity(),
                (entity1, entity2) -> entity1));
        List<MemberInfoExcelVO> newExcelMemberInfoList = new ArrayList<>();
        for (String returnPhone : returnPhoneList) {
            if (Objects.isNull(map.get(returnPhone))) {
                continue;
            }
            MemberInfoExcelVO memberInfoExcelVO = map.get(returnPhone);
            memberInfoExcelVO.setPhoneNum(memberInfoExcelVO.getPhoneNum());
            memberInfoExcelVO.setPhoneCountryCode(memberInfoExcelVO.getPhoneCountryCode());
            newExcelMemberInfoList.add(memberInfoExcelVO);
        }
        return newExcelMemberInfoList;
    }

    private List<String> queryRegisterMember(List<String> importPhoneNum, String operSubjectGuid) {

        //每页查询500条
        final int pageSize = 500;
        int runThreadSize = (importPhoneNum.size() % pageSize) == 0 ? (importPhoneNum.size() / pageSize) :
                (importPhoneNum.size() / pageSize) + 1;
        List<FutureTask<List<String>>> futureTasks = new ArrayList<>();
        // ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < runThreadSize; i++) {
            List<String> phoneNums = importPhoneNum.stream().limit((i + 1) * pageSize).
                    skip(i * pageSize).collect(Collectors.toList());
            FutureTask<List<String>> futureTask = new FutureTask<>(new GetRegisterMemberThread(operSubjectGuid, phoneNums));
            futureTasks.add(futureTask);
            memberBaseThreadExecutor.execute((futureTask));
        }

        List<String> totalPhoneNum = new ArrayList<>();
        //遍历结果
        for (FutureTask<List<String>> task : futureTasks) {
            try {
                //FutureTask的get()方法会自动阻塞，5s后超时自动放弃
                totalPhoneNum.addAll(task.get(5000, TimeUnit.MILLISECONDS));
            } catch (Exception e) {
                log.error("线程执行异常:{}", e.getMessage());
            }
        }
        return totalPhoneNum;
    }

    class GetRegisterMemberThread implements Callable<List<String>> {

        private String operSubjectGuid;

        private List<String> importPhoneNum;

        public GetRegisterMemberThread(String operSubjectGuid, List<String> importPhoneNum) {
            this.operSubjectGuid = operSubjectGuid;
            this.importPhoneNum = importPhoneNum;
        }

        @Override
        public List<String> call() throws Exception {
            //获取手机号
            return hsaOperationMemberInfoMapper.findAllPhone(operSubjectGuid, importPhoneNum);
        }
    }

    /**
     * 封装会员关联标签导入失败数据详情
     *
     * @param memberInfo   会员信息
     * @param number       错误序号
     * @param errorMessage 错误信息
     * @return 操作结果
     */
    private MemberInfoUploadError toMemberInfoUploadError(ImportMemberInfoQO memberInfo, int number, String errorMessage) {

        return new MemberInfoUploadError()
                .setNumber(ObjectUtil.objToString(number))
                .setErrorMessage(errorMessage)
                .setPhoneNum(memberInfo.getPhoneNum())
                .setUserName(memberInfo.getUserName());
    }

    @Override
    public ExcelResultVO importLabelRelationMember(MultipartFile file, String labelGuid) {

        return importMemberInfo(file, hsaMemberLabelMapper.findPhoneNumByLabelGuid(labelGuid), labelGuid);
    }


    private String getUploadUrl(List<MemberInfoUploadError> errors) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formatDateTime = now.format(formatter);
        ExcelResult excelResults = com.aimilin.utils.BeanUtils.toResult(errors);
        byte[] writes = ExcelWriteUtils.write(excelResults, ExcelType.XLSX);
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(writes));
            fileDto.setFileName(systemRoleHelper.getReplace(ExcelImportConstant.TEMPLATE_CHOOSE_MEMBER, ThreadLocalCache.getOperSubjectGuid()) + formatDateTime + ExcelImportConstant.TEMPLATE_CHOOSE_MEMBER_SUFFIX + ExcelType.XLSX);
            String upload = fileOssService.upload(fileDto);
            log.info("错误信息文件下载路径->>>>>{}", upload);
            return upload;
        } catch (Exception e) {
            log.error("上传文件失败");
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FILE_UPLOAD);
        }
    }

    @SneakyThrows
    @Override
    public void export(HttpServletResponse response, CustomExportQO request) {
        //新建ExcelWriter
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        ExportDataDTO exportDataDTO = toExportData(request);
        if (Objects.isNull(exportDataDTO)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        writeData(excelWriter, DEFAULT_SHEET, exportDataDTO.getList(), request.getExportFields());
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding(UTF_8);
        String fileName = URLEncoder.encode(exportDataDTO.getFileName(), UTF_8);
        response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
        //关闭流
        excelWriter.finish();
    }

    /**
     * 写入数据
     *
     * @param excelWriter  ExcelWriter
     * @param sheetName    sheet名
     * @param list         导出数据
     * @param exportFields 字段名称
     */
    private void writeData(ExcelWriter excelWriter, String sheetName, List<?> list, List<ExportField> exportFields) {
        //获取sheet对象
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName)
                .head(EasyExcelUtils.head(exportFields))
                .needHead(true)
                .build();
        List<List<Object>> allList = new ArrayList<>();
        for (Object o : list) {
            allList.addAll(EasyExcelUtils.dataList(exportFields, o));
        }
        //向sheet写入数据
        excelWriter.write(allList, writeSheet);
    }

    /**
     * 封装导出数据
     *
     * @param request 导出qo
     * @return 操作结果
     */
    private ExportDataDTO toExportData(CustomExportQO request) {
        List<?> list;
        String fileName;
        CustomExportTypeEnum customExportTypeEnum = CustomExportTypeEnum.getEnumByCode(request.getType());
        if (Objects.isNull(customExportTypeEnum)) {
            return null;
        }
        switch (customExportTypeEnum) {
            case ACCOUNT_MANAGE:
                fileName = systemRoleHelper.getReplace(EXCEL_PREFIX, ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.DATE_PATTERN);
                list = queryMemberExportData(JSONObject.parseObject(request.getConditionJson(), MemberListQO.class));
                break;
            case RELATION_LABEL:
                fileName = systemRoleHelper.getReplace(LABEL_RELATION_MEMBER, ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.DATE_PATTERN);
                list = queryExportRelationLabelData(JSONObject.parseObject(request.getConditionJson(), RelationLabelListQO.class));
                break;
            case INTEGRAL_RECORD:
                IntegralDetailQO integralDetailQO = JSONObject.parseObject(request.getConditionJson(), IntegralDetailQO.class);
                fileName = hsaIntegralDetailService.packageFileName(integralDetailQO.getTaskGuid());
                list = hsaIntegralDetailService.queryIntegralRecordData(integralDetailQO);
                break;
            case GROWTH_VALUE_RECORD:
                fileName = GROWTH_VALUE_SHEET + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.DATE_PATTERN);
                list = hsaGrowthValueDetailService.queryExcelGrowthValueData(JSONObject.parseObject(request.getConditionJson(),
                        GrowthValueDetailRequest.class));
                break;
            case MEMBER_CARD_MANAGE:
                fileName = systemRoleHelper.getReplace("已领会员卡", ThreadLocalCache.getOperSubjectGuid()) + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATE);
                list = hsaMemberInfoCardService.queryExportMemberInfoCard(JSONObject.parseObject(request.getConditionJson(),
                        QueryMemberInfoCardQO.class));
                break;
            case MEMBER_GRADE_APPLY:
                GradeUpgradeApplyPageVO gradeUpgradeApplyPageVO = JSONObject.parseObject(request.getConditionJson(), GradeUpgradeApplyPageVO.class);
                String roleName = gradeUpgradeApplyPageVO.getRoleType() == 0 ? "会员等级" : "商家等级";
                fileName = systemRoleHelper.getReplace(roleName, ThreadLocalCache.getOperSubjectGuid()) + "申请记录" + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATE);
                list = hsaMemberApplyGradeRecordService.queryExportRecord(gradeUpgradeApplyPageVO);
                break;
            default:
                return null;
        }
        ExportDataDTO exportDataDTO = new ExportDataDTO();
        exportDataDTO.setList(list);
        exportDataDTO.setFileName(fileName);
        return exportDataDTO;
    }


}
