package com.holderzone.member.base.entity.credit;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 挂账钱包记录
 * @author: pan tao
 * @create: 2022-02-11 12:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCreditWalletLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 挂账信息guid
     */
    private String creditInfoGuid;

    /**
     * @see com.holderzone.member.common.enums.credit.WalletChangeTypeEnum
     * 变动类型 0:消费退款 1:结算结余 2:挂账结算支付 10:预存余额
     */
    private Integer changeType;

    /**
     * 变动类型 0 增加 1 减少
     */
    private Integer changeAmountType;

    /**
     * 变动金额
     */
    private BigDecimal changeAmount;

    /**
     * 挂账钱余额
     */
    private BigDecimal creditWalletBalance;

    /**
     * 变动来源
     */
    private Integer changeSource;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}
