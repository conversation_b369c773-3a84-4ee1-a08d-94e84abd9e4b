package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.entity.grade.HsaGiftBagBaseInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.grade.GiftBagBaseInfoQO;
import com.holderzone.member.common.vo.grade.GiftBagBaseInfoVO;

import java.util.List;


/**
 * @program: member-marketing
 * @description: 等级升级礼包基础信息
 * @author: z<PERSON><PERSON>
 * @create: 2022-1-4 09:48
 */
public interface HsaGiftBagBaseInfoService extends IHolderBaseService<HsaGiftBagBaseInfo> {

    /**
     * 初始化当前运营主体升级礼包基础信息
     * @param operSubjectGuids 运营主体集合
     * @return 操作结果
     */
    void addGiftBag(List<String> operSubjectGuids);

    /**
     * 修改升级礼包基础信息
     * @param giftBagBaseInfo 修改信息
     * @return 操作结果
     */
    boolean updateGiftBagInfo(GiftBagBaseInfoQO giftBagBaseInfo);

    /**
     * 通过运营主体获取升级礼包基础信息
     * @param operSubjectGuid
     * @return 升级礼包基础信息
     */
    GiftBagBaseInfoVO getGiftBagBaseInfo(String operSubjectGuid);

}
