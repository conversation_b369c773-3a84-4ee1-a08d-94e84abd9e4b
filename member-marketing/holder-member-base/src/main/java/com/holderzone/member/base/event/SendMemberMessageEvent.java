package com.holderzone.member.base.event;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.event.SendMessageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;

@Slf4j
@EnableBinding(BinderChannel.class)
public class SendMemberMessageEvent {

    @Resource
    private BinderChannel binderChannel;

    /**
     * 触发事件
     *
     * @param request 发送消息信息(BaseEvent中的userInfo信息必填)
     */
    public boolean send(SendMessageEvent request) {
        Message<SendMessageEvent> build = MessageBuilder.withPayload(request).build();
        boolean result = binderChannel.outputSendMessage().send(build);
        return result;
    }
}
