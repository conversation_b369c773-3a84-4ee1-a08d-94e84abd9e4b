package com.holderzone.member.base.util;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.common.constant.GrowthValueConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.growth.HsaGrowthValueTaskDTO;
import com.holderzone.member.common.dto.growth.wechat.AppletGrowthTaskDTO;
import com.holderzone.member.common.enums.growth.DescriptionTypeEnum;
import com.holderzone.member.common.enums.growth.TaskValidityTypeEnum;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @create 2023-04-14
 * @description 抽离服务公共代码
 */
public class ServiceCommonUtil {

    public static Boolean buildCommonCondition(AppletGrowthTaskDTO taskDTO, HsaGrowthValueTaskDTO obj) {
        if(ObjectUtil.isNull(taskDTO)){
            return true;
        }
        //若未在进行中
        if (ObjectUtil.notEqual(obj.getTaskValidityType(), TaskValidityTypeEnum.PERMANENT_VALIDITY.getCode())) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTaskDate = obj.getStartFixedTaskValidityDate();
            LocalDateTime endTaskDate = obj.getEndFixedTaskValidityDate();
            boolean underway = now.isBefore(endTaskDate)
                    && now.isAfter(startTaskDate);
            //不满足时间
            if (!underway) {
                return false;
            }
            taskDTO.setTaskValidityDate(String.format(GrowthValueConstant.GROWTH_TASK_VALIDITY_FIXED, endTaskDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            //设置结束时间
            taskDTO.setTaskSurplusDate(buildSurplusDate(now, endTaskDate));
        }
        //去设置公共属性
        taskDTO.setTaskId(obj.getId());
        taskDTO.setTaskGuid(obj.getGuid());
        taskDTO.setTaskAction(obj.getTaskAction());
        if (ObjectUtil.equal(obj.getTaskValidityType(), TaskValidityTypeEnum.PERMANENT_VALIDITY.getCode())) {
            taskDTO.setTaskValidityDate(GrowthValueConstant.GROWTH_TASK_VALIDITY_PERMANENT);
        }
        return true;
    }

    public static String buildSurplusDate(LocalDateTime now, LocalDateTime endTaskDate) {
        Duration duration = Duration.between(now, endTaskDate);
        if (duration.toDays() > NumberConstant.NUMBER_7) {
            return null;
        }
        if (duration.toDays() >= NumberConstant.NUMBER_1) {
            return String.format(GrowthValueConstant.GROWTH_TASK_SURPLUS_DAY, duration.toDays());
        }
        if (duration.toHours() >= NumberConstant.NUMBER_1) {
            return String.format(GrowthValueConstant.GROWTH_TASK_SURPLUS_HOUR, duration.toHours());
        }
        return GrowthValueConstant.GROWTH_TASK_SURPLUS_IMMEDIATELY;
    }

}
