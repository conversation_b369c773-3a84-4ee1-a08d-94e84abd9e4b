package com.holderzone.member.base.util;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.common.enums.card.PeriodTypeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;

public class ValidTimeUtils {

    private ValidTimeUtils(){

    }
    public static LocalDate validTimeStart(LocalDateTime electronic, LocalDateTime physical){

        LocalDateTime startTime;  //会员卡使用有效时间，开始
        if(ObjectUtil.isNotNull(electronic) && ObjectUtil.isNotNull(physical)){
            if(electronic.isBefore(physical)){
                startTime = electronic;
            }else{
                startTime = physical;
            }
        }else if(ObjectUtil.isNotNull(electronic)){ //只有电子卡存在
            startTime = electronic;
        }else {  //只有实体卡存在
            startTime = physical;
        }
        return startTime.toLocalDate();
    }

    public static LocalDate validTimeEnd(LocalDateTime electronic, LocalDateTime physical){

        LocalDateTime endTime;  //会员卡使用有效时间，结束
        if(ObjectUtil.isNotNull(electronic) && ObjectUtil.isNotNull(physical)){
            if(electronic.isBefore(physical)){
                endTime = physical;
            }else{
                endTime = electronic;
            }
        }else if(ObjectUtil.isNotNull(electronic)){ //只有电子卡存在
            endTime = electronic;
        }else {  //只有实体卡存在
            endTime = physical;
        }
        return endTime.toLocalDate();
    }

    public static LocalDate buildValidityDate(Integer validityUnit, Integer validityTime) {
        LocalDate now = LocalDate.now();

        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_WEEK.getCode(), validityUnit)) {
            return now.plusWeeks(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_DAY.getCode(), validityUnit)) {
            return now.plusDays(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_YEAR.getCode(), validityUnit)) {
            return now.plusYears(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_MONTH.getCode(), validityUnit)) {
            return now.plusMonths(validityTime);
        }

        return now;
    }
}
