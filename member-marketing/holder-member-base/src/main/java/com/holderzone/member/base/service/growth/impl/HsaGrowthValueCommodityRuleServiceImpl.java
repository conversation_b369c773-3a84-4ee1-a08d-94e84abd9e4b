package com.holderzone.member.base.service.growth.impl;


import com.holderzone.member.base.entity.growth.HsaGrowthValueCommodityRule;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueCommodityRuleMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueCommodityRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @description 成长值任务适用商品
 * @date 2021/12/09
 */
@Service
@Slf4j
public class HsaGrowthValueCommodityRuleServiceImpl extends HolderBaseServiceImpl<HsaGrowthValueCommodityRuleMapper, HsaGrowthValueCommodityRule> implements HsaGrowthValueCommodityRuleService {

}
