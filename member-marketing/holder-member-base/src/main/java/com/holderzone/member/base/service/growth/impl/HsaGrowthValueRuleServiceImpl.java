package com.holderzone.member.base.service.growth.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.growth.HsaGrowthValueRule;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueRuleMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.qo.growth.GrowthValueRuleQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.growth.GrowthValueRuleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class HsaGrowthValueRuleServiceImpl extends HolderBaseServiceImpl<HsaGrowthValueRuleMapper,HsaGrowthValueRule> implements HsaGrowthValueRuleService {

    @Resource
    private HsaGrowthValueRuleMapper hsaGrowthValueRuleMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    private static final String GROWTH_RULE_NAME = "成长值";

    @Override
    public String saveOrUpdate(GrowthValueRuleQO request) {
        String guid = request.getGuid();
        HsaGrowthValueRule hsaGrowthValueRule = hsaGrowthValueRuleMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueRule>()
                .eq(HsaGrowthValueRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(!StringUtils.isEmpty(guid),HsaGrowthValueRule::getGuid, guid));
        if(Objects.isNull(hsaGrowthValueRule)){  //新增
            return saveGrowthRule(null,request.getGrowthValueRule());
        }
        //更新
        hsaGrowthValueRule.setGrowthValueRule(request.getGrowthValueRule());
        hsaGrowthValueRuleMapper.updateById(hsaGrowthValueRule);
        return hsaGrowthValueRule.getGuid();
    }

    @Override
    public GrowthValueRuleVO getGrowthRuleName(String operSubjectGuid) {
        GrowthValueRuleVO growthValueRuleVO = new GrowthValueRuleVO();
        HsaGrowthValueRule hsaGrowthValueRule = hsaGrowthValueRuleMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueRule>()
                .eq(HsaGrowthValueRule::getOperSubjectGuid, StringUtils.isEmpty(operSubjectGuid)?ThreadLocalCache.getOperSubjectGuid():operSubjectGuid));
        if(Objects.isNull(hsaGrowthValueRule)){
            String guid = saveGrowthRule(operSubjectGuid,GROWTH_RULE_NAME);//给当前运营主体新增默认成长值规则名称
            growthValueRuleVO.setGrowthValueRule(GROWTH_RULE_NAME);
            growthValueRuleVO.setGuid(guid);
            return growthValueRuleVO;
        }
        BeanUtils.copyProperties(hsaGrowthValueRule,growthValueRuleVO);
        return growthValueRuleVO;
    }

    /**
     * 成长值规则数据封装
     * @param growthValueRuleName 成长值规则名称
     * @return guid
     */
    public String saveGrowthRule(String operSubjectGuid,String growthValueRuleName){
        HsaGrowthValueRule hsaGrowthValueRule = new HsaGrowthValueRule();
        String stringGuid = guidGeneratorUtil.getStringGuid(HsaGrowthValueRule.class.getSimpleName());
        hsaGrowthValueRule.setGuid(stringGuid);
        hsaGrowthValueRule.setOperSubjectGuid(StringUtils.isEmpty(operSubjectGuid)?ThreadLocalCache.getOperSubjectGuid():operSubjectGuid);
        hsaGrowthValueRule.setGrowthValueRule(growthValueRuleName);
        hsaGrowthValueRuleMapper.insert(hsaGrowthValueRule);
        return stringGuid;
    }
}
