package com.holderzone.member.base.service.credit.impl;

import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.base.mapper.credit.HsaCreditUserMapper;
import com.holderzone.member.base.service.credit.HsaCreditUserService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-02-10 10:15
 */
@Slf4j
@Service
public class HsaCreditUserServiceImpl extends HolderBaseServiceImpl<HsaCreditUserMapper, HsaCreditUser> implements
        HsaCreditUserService {


}
