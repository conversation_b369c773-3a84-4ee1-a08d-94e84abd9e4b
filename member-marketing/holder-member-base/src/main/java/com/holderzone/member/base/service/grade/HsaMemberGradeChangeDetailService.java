package com.holderzone.member.base.service.grade;


import com.holderzone.member.base.entity.grade.HsaMemberGradeChangeDetail;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.grade.ReceiveMemberGradeBagQO;
import com.holderzone.member.common.vo.grade.AppletMemberGradeChangeVO;
import com.holderzone.member.common.vo.grade.AppletMemberGradeInfoBagBaseVO;
import com.holderzone.member.common.vo.grade.GradeEquitiesPreviewVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员等级变化明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface HsaMemberGradeChangeDetailService extends IHolderBaseService<HsaMemberGradeChangeDetail> {

    /**
     * 小程序升级礼包领取页面
     *
     * @param memberInfoGuid memberInfoGuid
     * @return AppletMemberGradeInfoBagBaseVO
     */
    AppletMemberGradeInfoBagBaseVO getAppletMemberGradeInfoBag(String memberInfoGuid);

    /**
     * 获取会员等级权益规则信息
     *
     * @param memberInfoGuid   会员guid
     * @param memberGradeGuid  会员等级权益
     * @param equitiesRuleType 权益规则类型
     * @return
     */
    List<GradeEquitiesPreviewVO> getAppletMemberGradeEquities(String memberInfoGuid, String memberGradeGuid, String equitiesRuleType, Integer effective);


    /**
     * 领取升级礼包
     *
     * @param receiveMemberGradeBagQO receiveMemberGradeBagQO
     * @return Boolean
     */
    Boolean receiveMemberGradeInfoChangeBag(ReceiveMemberGradeBagQO receiveMemberGradeBagQO);

    /**
     * 小程序等级变更弹窗
     *
     * @param memberInfoGuid memberInfoGuid
     * @return AppletMemberGradeChangeVO
     */
    AppletMemberGradeChangeVO showMemberGradeChange(String memberInfoGuid);

    /**
     * 等级变更记录
     *
     * @param hsaOperationMemberInfo hsaOperationMemberInfo
     * @param afterMemberGradeInfo   afterMemberGradeInfo
     * @param localDateTime          localDateTime
     */
    void encapsulationBusiness(HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo
            afterMemberGradeInfo, LocalDateTime localDateTime);

    /**
     * 批量更会员等级变更记录
     *
     * @param request 修改参数
     */
    void batchUpdateGradeChange(List<HsaMemberGradeChangeDetail> request);

    /**
     * 等级变化记录
     *
     * @param memberGuid
     * @param afterMemberGradeInfo
     * @param beforeMemberGradeInfo
     */
    void gradeChangeBusiness(String memberGuid, HsaMemberGradeInfo afterMemberGradeInfo, HsaMemberGradeInfo beforeMemberGradeInfo);
}
