package com.holderzone.member.base.entity.growth;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGrowthValueRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    /**
     * 成长值规则guid
     */
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 成长值规则
     */
    private String growthValueRule;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}
