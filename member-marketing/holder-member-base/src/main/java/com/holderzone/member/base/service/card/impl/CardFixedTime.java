package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.service.card.CardValidityType;
import com.holderzone.member.base.util.ValidTimeUtils;
import com.holderzone.member.common.dto.card.ValidTime;

import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 处理会员卡-固定效使用时间
 * 使用有效期：显示当前会员电子卡、实体卡总使用有效期；电子卡从开通成功开始计算，实体卡从激活成功开始计算；
 * ① 仅存在会员电子卡：使用有效期=电子卡使用有效期；
 * ② 仅存在会员实体卡：使用有效期=实体卡使用有效期；
 * ③ 存在实体卡+电子卡：使用有效期=实体卡+电子卡合计覆盖的使用有效期范围；（如任一存在“永久有效”即默认为永久有效）；
 */
public class CardFixedTime extends ValidTime implements CardValidityType {
    @Override
    public String cardTimeHandler(HsaCardBaseInfo hsaCardBaseInfo, HsaElectronicCard hsaElectronicCard, HsaPhysicalCard hsaPhysicalCard) {
        //电子卡开卡时间
        LocalDateTime electronic = Optional.ofNullable(hsaElectronicCard)
                .map(HsaElectronicCard::getGmtCreate).orElse(null);
        //实体卡激活时间
        LocalDateTime physical = Optional.ofNullable(hsaPhysicalCard)
                .map(HsaPhysicalCard::getActivationTime).orElse(null);

        if(ObjectUtil.isNull(electronic) && ObjectUtil.isNull(physical)){
            return hsaCardBaseInfo.getGmtCreate().toLocalDate()+ "至" +
                    hsaCardBaseInfo.getCardValidityDate();
        }

        this.setEndTime(hsaCardBaseInfo.getCardValidityDate());
        this.setStartTime(ValidTimeUtils.validTimeStart(electronic,physical));

        StringBuilder time = new StringBuilder();
        time.append(this.getStartTime()).append("至");
        time.append(this.getEndTime());
        return time.toString();
    }
}
