package com.holderzone.member.base.mapper.integral;

import com.holderzone.member.base.entity.growth.HsaSuspendTaskTimeQuantum;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.integral.HsaIntegralDetailDTO;
import com.holderzone.member.common.dto.integral.MemberIntegralDTO;
import com.holderzone.member.common.dto.integral.RecordRemainIntegralDTO;
import com.holderzone.member.common.dto.integral.StatisticsMonthIntegralDTO;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.qo.growth.AppletGrowthDetailPageQO;
import com.holderzone.member.common.qo.integral.IntegralDetailQO;
import com.holderzone.member.common.qo.integral.MemberIntegralDetailQO;
import com.holderzone.member.common.vo.integral.IntegralCompleteCountVO;
import com.holderzone.member.common.vo.integral.IntegralDetailVO;
import com.holderzone.member.common.vo.integral.MemberIntegralDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-05-07 11:36
 */
@Mapper
public interface HsaIntegralDetailMapper extends HolderBaseMapper<HsaIntegralDetail> {

    /**
     * 积分任务完成统计
     *
     * @param guids
     * @return 操作结果
     */
    List<IntegralCompleteCountVO> findCompleteCount(@Param("guids") List<String> guids);

    /**
     * 查询积分明细记录
     *
     * @param request 查询记录请求参数
     * @return 查询结果
     */
    List<IntegralDetailVO> queryIntegralDetailRecord(@Param("request") IntegralDetailQO request);

    /**
     * 查询会员积分明细记录
     *
     * @param request 查询记录请求参数
     * @return 查询结果
     */
    List<MemberIntegralDetailVO> queryMemberIntegralDetail(@Param("request") MemberIntegralDetailQO request);


    int queryMemberIntegralDetailCount(@Param("request") MemberIntegralDetailQO request);

    /**
     * 功能描述：统计失效的积分
     *
     * @param memberInfoGuid  会员guid
     * @param operSubjectGuid 主体guid
     * @return java.lang.Integer 失效的成长值
     * @date 2021/11/23
     */
    Integer sumInvalidIntegralValue(@Param("memberInfoGuid") String memberInfoGuid, @Param("operSubjectGuid") String operSubjectGuid, @Param("changeType") Integer changeType);

    /**
     * 查询即将过期积分
     *
     * @param memberInfoGuid 会员guid
     * @param overdueDay     即将过期天数
     * @return 查询结果
     */
    Integer querySoonOutDateIntegral(@Param("memberInfoGuid") String memberInfoGuid, @Param("overdueDay") Integer overdueDay);


    List<HsaIntegralDetailDTO> listAllIntegralDetail(@Param("taskIdList") List<String> taskIdList, @Param("memberInfoGuid") String guid);

    List<HsaIntegralDetailDTO> listAppletIntegralPage(@Param("memberInfoGuid") String memberInfoGuid,
                                                      @Param("operSubjectGuid") String operSubjectGuid);

    List<HsaIntegralDetailDTO> listAppletIntegralByTaskActionPage(@Param("memberInfoGuid") String memberInfoGuid,
                                                                  @Param("operSubjectGuid") String operSubjectGuid,
                                                                  @Param("taskAction") Integer taskAction);

    List<HsaIntegralDetailDTO> listAppletIntegralByTaskActionPageNew(@Param("qo") AppletGrowthDetailPageQO qo);

    List<StatisticsMonthIntegralDTO> statisticsMonthGrowth(AppletGrowthDetailPageQO qo);

    Integer sumImmediatelyInvalidIntegral(@Param("memberInfoGuid") String memberInfoGuid,
                                          @Param("operSubjectGuid") String operSubjectGuid,
                                          @Param("overdueDay") Integer overdueDay);

    Integer sumIntegralProtectDateIntegral(@Param("memberInfoGuid") String memberInfoGuid, @Param("operSubjectGuid") String operSubjectGuid, String changeString);

    List<MemberIntegralDTO> sumIntegralByMemberGuids(@Param("memberGuids") List<String> memberGuids);

    List<RecordRemainIntegralDTO> queryByIntegralValidityDate(@Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 批量更新积分记录剩余积分
     *
     * @param list 积分记录
     */
    void batchUpdateRecordRemainIntegral(@Param("list") List<RecordRemainIntegralDTO> list);

    Set<String> queryIntegralRelationMember(@Param("query") RequestLabelQuery query,
                                            @Param("integralType") Integer integralType);


    /**
     * 查询即将过期的积分
     *
     * @param operSubjectGuid 主体
     * @param memberInfoGuid  会员
     * @return 查询结果
     */
    List<HsaIntegralDetail> listAboutExpire(@Param("operSubjectGuid") String operSubjectGuid,
                                            @Param("memberInfoGuid") String memberInfoGuid);

    int selectByMemberGuid(@Param("memberInfoGuid") String memberInfoGuid,
                           @Param("operSubjectGuid") String operSubjectGuid,
                           @Param("taskNumber") String taskNumber,
                           @Param("awardGuid") String awardGuid,
                           @Param("beginTime") LocalDateTime beginTime);
}
