package com.holderzone.member.base.mapper.system;

import com.holderzone.member.base.entity.system.HsaWelcomeMessage;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;

/**
 * <AUTHOR>
 * @program: member-marketing
 * @description: ${description}
 */
public interface HsaWelcomeMessageMapper extends HolderBaseMapper<HsaWelcomeMessage> {


    /**
     * 随机查询欢迎词
     * @return
     */
    String getRandomMessage();
}
