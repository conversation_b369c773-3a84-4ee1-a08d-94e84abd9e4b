package com.holderzone.member.base.mapper.growth;


import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO;
import com.holderzone.member.common.dto.growth.MemberGrowthDetailExportDTO;
import com.holderzone.member.common.dto.growth.PartnerMemberGrowthDetailExportDTO;
import com.holderzone.member.common.dto.growth.wechat.StatisticsMonthGrowthDTO;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.qo.growth.AppletGrowthDetailPageQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailMemberQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailRequest;
import com.holderzone.member.common.vo.excel.ExcelGrowthValueDetailVO;
import com.holderzone.member.common.vo.growth.GrowthValueCompleteCountVO;
import com.holderzone.member.common.vo.growth.GrowthValueDetailMemberVO;
import com.holderzone.member.common.vo.growth.GrowthValueDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 会员卡基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaGrowthValueDetailMapper extends HolderBaseMapper<HsaGrowthValueDetail> {

    /**
     * 查询成长值任务完成明细记录列表
     *
     * @param request 高级筛选请求参数
     * @return java.util.List<com.holderzone.member.common.vo.growth.IntegralDetailVO>
     */
    List<GrowthValueDetailVO> getGrowthValueDetailList(@Param("request") GrowthValueDetailRequest request);

    /**
     * 功能描述：会员账户查询成长值列表
     *
     * @param qo 会员账户查询成长值参数
     * @return java.util.List<com.holderzone.member.common.vo.growth.GrowthValueDetailMemberVO>
     * @date 2021/11/23
     */
    List<GrowthValueDetailMemberVO> listMemberGrowth(GrowthValueDetailMemberQO qo);

    /**
     * 功能描述：统计失效的成长值
     *
     * @param memberInfoGuid  会员guid
     * @param operSubjectGuid 主体guid
     * @return java.lang.Integer 失效的成长值
     * @date 2021/11/23
     */
    Integer sumInvalidGrowthValue(@Param("memberInfoGuid") String memberInfoGuid, @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 功能描述：会员账户查询成长值导出列表
     *
     * @param qo 会员账户查询成长值参数
     * @return java.util.List<com.holderzone.member.common.dto.growth.MemberGrowthDetailExportDTO>
     * @date 2021/11/24
     */
    List<MemberGrowthDetailExportDTO> listExportMemberGrowth(GrowthValueDetailMemberQO qo);

    /**
     * 功能描述：会员账户查询成长值导出列表统计
     *
     * @param qo 会员账户查询成长值参数
     * @return java.lang.Integer
     * @date 2021/12/2
     */
    Integer listExportMemberGrowthCount(GrowthValueDetailMemberQO qo);

    /**
     * 查询成长值任务完成明细记录列表
     *
     * @param request 高级筛选请求参数
     * @return java.util.List<com.holderzone.member.common.vo.growth.IntegralDetailVO>
     */
    List<ExcelGrowthValueDetailVO> exportGrowthValueDetailList(@Param("request") GrowthValueDetailRequest request);

    /**
     * 功能描述：查询成长值详情
     *
     * @param id             任务id
     * @param memberInfoGuid 会员guid
     * @param taskAction     任务
     * @return com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO
     * @date 2021/11/24
     */
    List<HsaGrowthValueDetailDTO> listGrowthDetailByAction(@Param("taskId") Long id, @Param("memberInfoGuid") String memberInfoGuid, @Param("taskAction") Integer taskAction);

    /**
     * 功能描述：查询所有成长值详情
     *
     * @param taskIdList 任务id列表
     * @param guid       会员guid
     * @return java.util.List<com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO>
     * @date 2021/11/24
     */
    List<HsaGrowthValueDetailDTO> listAllGrowthDetail(@Param("taskIdList") List<Long> taskIdList, @Param("memberInfoGuid") String guid);

    /**
     * 功能描述：查询即将失效的成长值
     *
     * @param memberInfoGuid  会员guid
     * @param operSubjectGuid 主体guid
     * @return java.lang.Integer
     * @date 2021/11/25
     */
    Integer sumImmediatelyInvalidGrowth(@Param("memberInfoGuid") String memberInfoGuid, @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 功能描述：小程序查询成长值明细
     *
     * @param qo 查询数据
     * @return java.util.List<com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO>
     * @date 2021/11/25
     */
    List<HsaGrowthValueDetailDTO> listAppletGrowthPage(AppletGrowthDetailPageQO qo);

    /**
     * 功能描述：小程序统计月份数据
     *
     * @param qo 查询参数
     * @return java.util.List<com.holderzone.member.common.dto.growth.wechat.StatisticsMonthGrowthDTO>
     * @date 2021/11/25
     */
    List<StatisticsMonthGrowthDTO> statisticsMonthGrowth(AppletGrowthDetailPageQO qo);

    List<GrowthValueCompleteCountVO> findCompleteCount(@Param("ids") List<Long> ids);


    List<String> findRelationLabelMemberGuid(@Param("query") RequestLabelQuery query);

    Set<String> queryGuidByGrade(@Param("query") RequestLabelQuery query);

    void batchUpdate(@Param("list") List<HsaGrowthValueDetail> activityList);

    /**
     * 更新翻倍成长值权益Guid以及翻倍的成长值
     *
     * @param growthList 成长值详情list
     */
    void batchUpdateGrowth(@Param("list") List<HsaGrowthValueDetail> growthList);

    /**
     * 好搭档会员账户查询成长值导出列表
     *
     * @param qo 会员账户查询成长值导出列表参数
     * @return 会员账户查询成长值导出列表数据
     */
    List<PartnerMemberGrowthDetailExportDTO> listPartnerExportMemberGrowth(GrowthValueDetailMemberQO qo);

    void batchUpdateFinishTime(@Param("memberInfoGuid")String memberInfoGuid, @Param("taskIdList")Set<String> taskIdList);

    void updateTouchDetailNumByConsumptionGuid(@Param("memberConsumptionGuid")String memberConsumptionGuid);
}
