package com.holderzone.member.base.service.card;


import com.holderzone.member.common.dto.card.HsaPhysicalCardCreateRecordDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.CreateRecordQO;

/**
 * <AUTHOR>
 */
public interface HsaPhysicalCardCreateRecordService{


    void saveCreateRecord(HsaPhysicalCardCreateRecordDTO recordDTO);

    PageResult listPage(CreateRecordQO qo);

}
