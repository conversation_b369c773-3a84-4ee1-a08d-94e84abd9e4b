package com.holderzone.member.base.service.card.impl;

import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.base.mapper.card.HsaCardOpenRuleMapper;
import com.holderzone.member.base.service.card.HsaCardOpenRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.dto.card.ECardCreateCountDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 会员卡开卡规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Slf4j
@Service
@AllArgsConstructor
public class HsaCardOpenRuleServiceImpl extends HolderBaseServiceImpl<HsaCardOpenRuleMapper, HsaCardOpenRule> implements HsaCardOpenRuleService {

    private final HsaCardOpenRuleMapper openRuleMapper;

    @Override
    public void updatePhysicalCreateCount(String cardGuid, Integer createCount) {
        openRuleMapper.updatePhysicalCreateCount(cardGuid,createCount);
    }

    @Override
    public void updateElectronicCreateCount(List<ECardCreateCountDTO> countList) {
        openRuleMapper.updateElectronicCreateCount(countList);
    }
}
