package com.holderzone.member.base.service.face;

import com.holderzone.member.common.qo.card.TerFaceMemberCardQO;
import com.holderzone.member.common.vo.card.TerBaseLoginMemberCardVO;
import com.holderzone.member.common.vo.card.TerCheckFaceMemberVO;

public interface TerFaceIdentifyService {

    /**
     * 通过人脸获取可支付会员卡
     * @param request
     * @return
     */
    TerBaseLoginMemberCardVO getMemberCardByFace(TerFaceMemberCardQO request);


    TerCheckFaceMemberVO checkFaceMember(String userId);


}
