package com.holderzone.member.base.handler.impl;

import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.base.handler.AbstractHandler;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.vo.grade.DoubleValueRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Order(3) //顺序排第1，最先校验
@Slf4j
public class CheckSingleLimitFilter extends AbstractHandler {

    @Override
    public boolean doFilter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                            HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        Optional<HsaBusinessEquities> gradeEquities = Optional.of(hsaBusinessEquities);
        //翻倍积分倍数
        BigDecimal doubleNumber = gradeEquities.map(HsaBusinessEquities::getGradeValueDoubleNumber).orElse(new BigDecimal(1));
        //任务积分
        Integer integralValue = request.getGrowthValue();
        //用户剩余可以的累计翻倍值
        Integer remainingValue = request.getRemainingValue();
        //翻倍积分
        int doubleValue = 0;
        doubleValue = doubleNumber.multiply(new BigDecimal(integralValue)).intValue();
        doubleValue = doubleValue - integralValue;

        //积分单次翻倍限制 0:不限制 1:限制
        Integer singleLimit = gradeEquities
                .map(HsaBusinessEquities::getSingleDoubleLimited).orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == singleLimit) {
            if (Objects.isNull(remainingValue) || remainingValue > doubleValue) {
                request.setDoubleValue((long) doubleValue);
            } else {
                request.setDoubleValue((long) remainingValue);
            }
        } else {
            //翻倍积分，单次不能超过的上限
            Integer singleValueLimit = gradeEquities.map(HsaBusinessEquities::getSingleDoubleUpperLimit).orElse(0);
            log.info("翻倍积分倍数:{},任务积分:{},用户剩余可以的累计翻倍值{},翻倍后积分:{},单倍最大可以获取的积分：{}"
                    ,doubleNumber,integralValue,remainingValue,doubleValue,singleValueLimit);
            //翻倍积分，不能超过累计翻倍积分
            if (Objects.nonNull(remainingValue) && remainingValue < singleValueLimit) {
                singleValueLimit = remainingValue;
            }
            if (singleValueLimit > doubleValue) {
                request.setDoubleValue((long) doubleValue);
            } else {
                request.setDoubleValue((long) singleValueLimit);
            }
        }
        log.info("用户：{} 使用了翻倍积分权益,翻倍了：{}积分", request.getMemberInfoGuid(),request.getDoubleValue());
        return false;
    }

}
