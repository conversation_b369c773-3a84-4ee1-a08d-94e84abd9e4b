package com.holderzone.member.base.entity.growth;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.annotation.FieldLabel;
import com.holderzone.member.common.enums.growth.GoodsTypeEnum;
import com.holderzone.member.common.enums.growth.TaskActionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成长值任务
 * author: pantao
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGrowthValueTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @FieldLabel(mark = "成长值实体")
    private Long id;

    /**
     * guid
     */
    @FieldLabel(mark = "成长值实体")
    private String guid;

    /**
     * 运营主体
     */
    @FieldLabel(mark = "成长值实体")
    private String operSubjectGuid;

    /**
     * 任务名称
     */
    @FieldLabel(mark = "成长值实体")
    private String taskName;

    /**
     * 任务编号
     */
    @FieldLabel(mark = "成长值实体")
    private String taskNumber;

    /**
     * 任务类型 0:基础任务 1:消费任务 2:充值任务
     *
     * @see com.holderzone.member.common.enums.growth.TaskTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer taskType;

    /**
     * 任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额
     *
     * @see com.holderzone.member.common.enums.growth.TaskActionEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer taskAction;

    /**
     * 任务描述类型 0：智能生成 1：自定义
     *
     * @see com.holderzone.member.common.enums.growth.DescriptionTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer descriptionType;

    /**
     * 任务描述规则（为空表示自定义）
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String description;

    /**
     * 赠送多少成长值
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer growthValue;

    /**
     * 成长值生效次数（0表示不限制）
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer limitedNumber;

    /**
     * 注册渠道多个用,隔开
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String sourceTypeJson;

    /**
     * 任务有效期类型 0：永久有限 1：固定有效期
     *
     * @see com.holderzone.member.common.enums.growth.TaskValidityTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer taskValidityType;

    /**
     * 开始任务固定有效期
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startFixedTaskValidityDate;

    /**
     * 结束任务固定有效期
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endFixedTaskValidityDate;

    /**
     * 成长值有效期类型 0：永久有效 1：动态有效期 2：固定有效期
     *
     * @see com.holderzone.member.common.enums.growth.GrowthValueValidityTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer growthValueValidityType;

    /**
     * 成长值动态有效期类型 0：天 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer dynamicValidityType;


    /**
     * 动态有效数量
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer dynamicValidityNumber;

    /**
     * 成长值固定有效期
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String fixedGrowthValueValidityDate;

    /**
     * 个人信息类型 0：性别 1;生日 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.PersonalDetailsTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String personalDetailsTypeJson;

    /**
     * 每消费多少金额赠送成长值
     * 或者
     * 每充值多少赠送多少成长值
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal amount;

    /**
     * 应用业务 0、食堂预订 1、点餐 2、自助餐 3、快速收款 4、外卖 5、卡包 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyBusinessJson;

    /**
     * 获取次数类型 0：不限次数 1：固定限制 2：每周期限制
     *
     * @see com.holderzone.member.common.enums.growth.GetCountTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer getCountType;

    /**
     * 周期限制类型 0：天 1：周 2：月
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodLimitedType;

    /**
     * 消费商品类型 0：指定商品 1：指定分类商品
     *
     * @see com.holderzone.member.common.enums.growth.GoodsTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionGoodsType;

    /**
     * 选择商品类型 0：消费指定商品 1：消费指定全部商品
     *
     * @see com.holderzone.member.common.enums.growth.ChooseGoodsTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @FieldLabel(mark = "成长值实体")
    private Integer chooseGoodsType;

    /**
     * 策略单名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @FieldLabel(mark = "成长值实体")
    private String strategyName;

    /**
     * 策略单id
     */
    private String strategyId;

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     *
     * @see com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum
     * 部分门店
     * @see HsaGrowthValueStoreRule
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @FieldLabel(mark = "成长值实体")
    private Integer applicableAllStore;

    /**
     * 指定商品赠送成长值类型 0：购买次数 1:购买数量 2：购买周期
     *
     * @see com.holderzone.member.common.enums.growth.BuyTypeEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer buyType;

    /**
     * 购买数量  单位：购买次数 次 ，购买数量 件 ，购买周期 天/周
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer buyNumber;

    /**
     * 购买周期类型 0：天 1：周
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer buyPeriodType;

    /**
     * -1：始终累计 0：按天累计 1：按周累计 2：按月累计 3：按年累计
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalPeriodType;

    /**
     * 累计周期
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String totalPeriod;

    /**
     * 累计消费多少次赠送成长值
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionCount;

    /**
     * 单笔消费不足多少，不记录奖励规则
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal consumptionIgnoreAmount;

    /**
     * 创建时间
     */
    @FieldLabel(mark = "成长值实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @FieldLabel(mark = "成长值实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 操作人员名字/账号
     */
    @FieldLabel(mark = "成长值实体")
    private String operatorTelName;

    /**
     * 是否开启 0：停止  1：开启
     */
    @FieldLabel(mark = "成长值实体")
    private Integer isEnable;

    /**
     * 商品分类json
     */
    @FieldLabel(mark = "成长值实体")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String goodsTypeJson;

    /**
     * 成长值任务位置
     */
    @FieldLabel(mark = "成长值实体")
    public int position;

    /**
     * 是否删除 0：未删除 1：已删除
     */
    @FieldLabel(mark = "成长值实体")
    private Integer isDelete;

    public boolean isAppointClassifyGoods() {
        return taskAction == TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode()
                && consumptionGoodsType == GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode();
    }

    public boolean isAppointGoods() {
        return taskAction == TaskActionEnum.CONSUMPTION_SPECIFIED_GOODS.getCode()
                && consumptionGoodsType == GoodsTypeEnum.APPOINT_GOODS.getCode();
    }
}
