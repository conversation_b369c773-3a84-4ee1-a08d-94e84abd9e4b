package com.holderzone.member.base.service.credit;

import com.holderzone.member.base.entity.credit.HsaCreditOrderRecord;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.credit.ClearingOrderQO;
import com.holderzone.member.common.qo.credit.CreditOrderRecordQO;
import com.holderzone.member.common.qo.credit.CreditRecordListQO;
import com.holderzone.member.common.vo.credit.CreditOrderRecordTotalVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 挂账订单记录service
 *
 * <AUTHOR>
 */
public interface HsaCreditOrderRecordService extends IHolderBaseService<HsaCreditOrderRecord> {


    /**
     * 查询订单记录
     *
     * @param request 查询订单记录请求qo
     * @return 操作结果
     */
    PageResult queryCreditOrderRecords(CreditOrderRecordQO request);

    /**
     * 查询挂账明细筛选列表
     *
     * @param request 筛选条件参数
     * @return 查询数据
     */
    PageResult queryListDetail(CreditRecordListQO request);

    /**
     * 查询挂账订单记录总计
     *
     * @return 查询结果
     */
    CreditOrderRecordTotalVO queryCreditOrderRecordTotal(String creditInfoGuid);

    /**
     * 查询挂账明细筛选列表
     *
     * @param request  筛选条件参数
     * @param response 响应对象
     */
    void exportListDetail(CreditRecordListQO request, HttpServletResponse response) throws IOException;

    /**
     * 获取挂账结算订单列表
     *
     * @param clearingOrderQO          结算订单列表请求参数
     * @return 操作结果
     */
    PageResult getClearingOrderList(ClearingOrderQO clearingOrderQO);

    /**
     * 导出订单记录请求
     *
     * @param request  查询订单记录请求qo
     * @param response 响应对象
     */
    void exportCreditOrderRecord(CreditOrderRecordQO request, HttpServletResponse response);

    /**
     * 导出结算单明细
     *
     * @param request  查询订单记录请求qo
     * @param response 响应对象
     */
    void exportClearingStatementDetail(CreditOrderRecordQO request, HttpServletResponse response);

}
