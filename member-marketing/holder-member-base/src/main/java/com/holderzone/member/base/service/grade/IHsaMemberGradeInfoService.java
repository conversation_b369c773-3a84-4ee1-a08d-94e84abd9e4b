package com.holderzone.member.base.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.grade.HsaGradeRightsCommodityRule;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.pay.MemberGradePerPayVO;
import com.holderzone.member.common.qo.equities.BusinessEquitiesQO;
import com.holderzone.member.common.qo.equities.HistoryEquitiesQO;
import com.holderzone.member.common.qo.grade.AdjustGradeQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBaseQO;
import com.holderzone.member.common.qo.grade.GradeEquitiesExplainQO;
import com.holderzone.member.common.qo.grade.GradeModifyQO;
import com.holderzone.member.common.qo.grade.MemberGradeQO;
import com.holderzone.member.common.vo.grade.*;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2021-12-30 18:28
 */
public interface IHsaMemberGradeInfoService extends IService<HsaMemberGradeInfo> {

    /**
     * 查询会员等级
     *
     * @param roleType 角色类型
     * @param gradeType 等级类型 0 免费会员 1付费会员
     * @return 查询结果
     */
    QueryMemberGradeVO queryMemberGradeList(String roleType,Integer gradeType);

    /**
     * 根据类型查询会员等级列表
     * @param roleType 角色
     * @param gradeType 类型
     * @return 查询结果
     */
    List<HsaMemberGradeInfoVO> queryMemberGradeInfoList (String roleType, Integer gradeType);

    /**
     * 查询会员等级：有效的
     *
     * @return 查询结果
     */
    QueryMemberEffectiveGradeVO queryEffectiveGradeList(String roleType,Integer gradeType);

    /**
     * 添加默认等级
     *
     * @param operSubjectGuids 运营主体guid
     */
    void addDefaultMemberGrade(List<String> operSubjectGuids);

    void deleteGrowthGrade(String operSubjectGuid);

    /**
     * 添加角色类型默认等级
     *
     * @param operSubjectGuid 运营主体guid
     * @param roleType        角色类型
     */
    void addDefaultMemberGrade(String operSubjectGuid, String roleType);

    /**
     * 新增、编辑会员等级
     *
     * @param memberGradeQO 新增会员等级礼包QO
     */
    boolean saveOrUpdateMemberGrade(MemberGradeQO memberGradeQO);

    /**
     * 查询成长值区间
     *
     * @param vipGrade 会员等级，插入的时候传
     * @param guid     会员guid
     * @param roleType 角色类型
     * @param type     会员等级类型 0 免费会员 1付费会员
     * @return 查询结果
     */
    MemberGradeGrowthValueVO queryGrowthValue (Integer vipGrade, String guid, String roleType, Integer type);

    /**
     * 查询会员等级详情
     *
     * @param guid 等级guid
     * @return
     */
    MemberGradeVO queryMemberGradeDetail(String guid);

    /**
     * 等级刷新
     *
     * @param businessType 业务类型 1刷新 0监听
     */
    Integer gradeRefresh(Integer businessType, String roleType);

    /**
     * 根据等级guid删除等级
     *
     * @param guid 等级guid
     * @return 操作结果
     */
    boolean deleteMemberGradeByGuid(String guid, String roleType);

    /**
     * 查询等级权益说明
     *
     * @return 等级权益说明
     */
    GradeEquitiesExplainVO queryGradeEquitiesExplain();


    /**
     * 查询等级会员价权益适用商品
     *
     * @param guid  等级guid
     * @param isNew 是否是新等级
     * @return
     */
    List<GradeCommodityBaseQO> queryGradeCommodityBaseQO(String guid, int isNew);

    /**
     * 查询可适用商品
     *
     * @return
     */
    List<GradeCommodityBaseQO> getGradeCommodityBase(List<GradeCommodityBaseQO> gradeCommodityBaseQOS, HsaBusinessEquities hsaBusinessEquities, List<HsaGradeRightsCommodityRule> hsaGradeRightsCommodityRules, List<String> commodityId);

    /**
     * 查询会员等级
     *
     * @param roleType
     * @param type     会员等级类型 0 免费会员 1付费会员
     * @return 等级信息
     */
    List<GradeVo> getGrade(String roleType,String type);

    /**
     * 查询会员等级名称集合
     *
     * @return 等级名称集合
     */
    List<GradeNameVo> getGradeList();

    /**
     * 保存等级说明
     *
     * @param qo 等级说明情趣QO
     */
    boolean saveGradeEquitiesExplain(GradeEquitiesExplainQO qo);

    /**
     * 查询会员等级权益预览信息
     *
     * @param memberGradeGuid 会员等级guid
     * @param memberInfoGuid  会员guid
     * @param sourceType      区分是否会员等级页面过来的查询
     * @param upgradeTime     会员升级时间
     * @return 权益预览信息
     */
    GradePreviewVO getGradeEquities(String memberGradeGuid,
                                    String memberInfoGuid,
                                    String sourceType,
                                    LocalDateTime upgradeTime,
                                    Integer effective);

    /**
     * 小程序查询等级详情
     *
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    List<WeChatGradeDetailVO> weChatGradeDetail(String memberGuid);

    void addGradeEquities(String memberGradeGuid, List<GradeEquitiesVO> gradeEquitiesVOList, String memberInfoGuid, Integer business);

    HsaMemberGradeInfo getNextHsaMemberGradeInfo(List<HsaMemberGradeInfo> hsaMemberGradeInfos, String currentGuid);

    /**
     * 添加会员等级权益预览信息
     *
     * @param businessEquitiesQO       权益预览信息
     * @param gradeEquitiesPreviewList 当前会员等级权益预览信息集合
     */
    void addGradeEquities(BusinessEquitiesQO businessEquitiesQO, List<GradeEquitiesPreviewVO> gradeEquitiesPreviewList);


    /**
     * 组装会员权益
     *
     * @param memberInfoGuid           会员guid
     * @param gradeEquitiesPreviewList 返参
     * @param effective                是否有效
     * @param hsaBusinessEquities      权益集合
     */
    void getEquities(String memberInfoGuid, List<GradeEquitiesPreviewVO> gradeEquitiesPreviewList, Integer effective, List<HsaBusinessEquities> hsaBusinessEquities);


    /**
     * 查询会员历史权益预览信息
     *
     * @param request 请求参数
     * @return 操作结果
     */
    GradePreviewVO getHistoryEquities(HistoryEquitiesQO request);


    /**
     * 初始化会员等级变更表旧数据
     *
     * @return 操作结果
     */
    void initializeOldChangeGrade();


    /**
     * 好搭档用户等级页面
     *
     * @param memberInfoGuid 会员guid
     * @param roleType       用户类型
     * @return
     */
    MemberGradeEquitiesPartnerVO getAppletMemberGradeEquities(String memberInfoGuid, Integer roleType);

    /**
     * 升级预下单
     *
     * @param payGradeDto
     * @return
     */
    MemberGradePerPayVO payGrade(PayMemberGradeDTO payGradeDto);


    /**
     * 小程序用户升级
     *
     * @param saveMemberGradeDTO
     * @return
     */
    MemberGradeUpgradeVO payGradeUpgrade(SaveMemberGradeDTO saveMemberGradeDTO);

    /**
     * 金额展示
     *
     * @param gradeGuid
     * @param roleType
     * @return
     */
    MemberGradePriceTagVO getMemberGradePriceTag(String gradeGuid, Integer roleType);


    void batchAdjustGrade(AdjustGradeQO request);

    List<HsaMemberGradeInfo> listByGuidList(List<String> gradeGuidList);

    /**
     * guid定位到一个等级
     *
     * @param roleType
     * @param guid
     * @return
     */
    HsaMemberGradeInfo getHsaMemberGradeInfo(Integer roleType, String guid);

    void setWeChatInfo(String memberInfoGuid, GradeEquitiesVO gradeEquitieInfo);

    /**
     * 查询会员等级详情
     * @param gradeGuid 等级guid
     * @return 查询结果
     */
    HsaMemberGradeInfoVO getMemberGradeInfoByGuid (String gradeGuid);


    /**
     * 批量查询会员等级详情
     * @param gradeGuids 会员等级guid集合
     * @return 查询结果
     */
    List<HsaMemberGradeInfoVO> batchGetMemberGradeInfoByGuid (List<String> gradeGuids);

    /**
     * 查询会员付费等级详情
     * @param memberGuid 会员guid
     * @param roleType 角色类型
     * @return 查询结果
     */
    GradeLevelVO getPaidGradeLevelByMemberGuid (String memberGuid,String roleType);

    /**
     * 查询会员免得等级详情
     * @param memberGuid 会员guid
     * @param roleType 角色类型
     * @return 查询结果
     */
    GradeLevelVO getFreeGradeLevelByMemberGuid (String memberGuid,String roleType);

    /**
     * 批量修改等级
     * @param request 修改等级请求信息
     *
     */
    void batchModifyGrade (GradeModifyQO request);


    /**
     * 获取会员信息
     * @param roleType 角色类型
     * @param gradeType 会员等级类型 0 免费会员 1付费会员
     * @param vipGrade 会员等级
     * @return 查询结果
     */
    HsaMemberGradeInfoVO queryMemberGradeInfo (String roleType, Integer gradeType, Integer vipGrade);
}