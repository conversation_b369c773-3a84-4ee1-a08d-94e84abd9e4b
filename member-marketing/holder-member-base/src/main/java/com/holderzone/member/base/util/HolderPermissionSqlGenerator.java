package com.holderzone.member.base.util;

import com.holderzone.member.common.enums.SystemPermissionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * holder 权限构造sql工具
 */
@Slf4j
public class HolderPermissionSqlGenerator {


    /**
     * 会员现有子系统
     * com.holderzone.member.common.enums.SystemPermissionEnum
     */
    private static final Map<String, String> SYSTEM_MAP = ofMap(
            SystemPermissionEnum.MEMBER_PERMISSION.getDes(), "会员管理",
            SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "营销中心",
            SystemPermissionEnum.EQUITIES_CENTER.getDes(), "权益中心",
            SystemPermissionEnum.MEMBER_TOOL.getDes(), "小程序基础配置",
            SystemPermissionEnum.MEMBER_MALL.getDes(), "会员商城",
            SystemPermissionEnum.MEMBER_PARTNER.getDes(), "好搭档平台");

    /**
     * 不支持中文路径
     */
    static String sqlFile = "settlement_center/1.结算中心权限.sql";

    /**
     * hp_permission.priority 排序字段
     * mysql unix_timestamp()时间戳
     *
     * @param args
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {
        //system, FUNCTION_GROUP , FUNCTION , PERMISSION，逗号分割
        getHolderSql(SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "会员结算台", "会员结算台", "查看会员结算台,新建结算规则,查看规则详情,编辑,复制规则,删除");
//        getHolderSql(SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "优惠劵", "发放统计", "查看发放统计,导出发放明细,标记使用,券作废");
//        getHolderSql(SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "优惠劵", "核销统计", "查看核销统计,导出核销明细");
//        getHolderSql(SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "发劵宝", "发劵宝", "发券宝列表,立即创建,发布,暂停,开启,编辑,复制,删除");
//        getHolderSql(SystemPermissionEnum.MARKETING_PERMISSION.getDes(), "发劵宝", "活动统计", "查看发放统计,导出发放明细,补发功能");
//        getHolderSql(SystemPermissionEnum.MEMBER_PERMISSION.getDes(), "会员账户管理", "会员账户详情", "查看优惠券,导出优惠券", 3);
//        getHolderSql(SystemPermissionEnum.MEMBER_PERMISSION.getDes(), "会员账户管理", "会员账户详情", "导出积分明细,导出挂账明细", 3);
        write(sb.toString());
    }

    /**
     * sequence 排序号
     */
    private static final String FUNCTION_GROUP = "INSERT INTO `gateway`.`hp_function_group`" +
            "(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) " +
            "\n VALUES " +
            "\n(now(), '%s',unix_timestamp(), now(), now(), now());";

    private static final String FUNCTION = "INSERT INTO `gateway`.`hp_function`" +
            "(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`) " +
            " \nVALUES " +
            "\n(NOW(), '%s', 1, 60, NOW(), 26,  " +
            "\n( SELECT id FROM hp_system WHERE name = '%s' AND identification = '%s' " +
            "   \n ORDER BY gmt_modified DESC LIMIT 1 ), " +
            "\n(select id from hp_function_group where name = '%s' " +
            "   \n order by gmt_modified desc limit 1)" +
            "\n,NOW(), NOW());";

    private static final String PERMISSION = "INSERT INTO `gateway`.`hp_permission`" +
            "(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`" +
            ", `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) " +
            "\nVALUES " +
            "\n(NOW(), NULL, '%s', NOW(), " +
            "\n(select id from hp_function where name = '%s' " +
            "\n and   system_id = (\n" +
            "\tSELECT id FROM hp_system " +
            "\nWHERE name = '%s' AND identification = '%s' limit 1" +
            "\n)" +
            "   \n order by gmt_modified desc limit 1)" +
            "\n, 0, NULL, '', 0, 0,NOW(), NOW());";

    static StringBuilder sb = new StringBuilder("-- holder权限 \n -- todo sql执行完请刷新holder权限redis \n");
    /**
     * 判重
     */
    static Map<String, Set<String>> map = new HashMap<>();

    public static void getHolderSql(String identification, String functionGroup, String function, String permission) {
        getHolderSql(identification, functionGroup, function, permission, 0);
    }

    /**
     * 获取模块
     *
     * @param identification
     * @param functionGroup
     * @param function
     * @param permission
     * @param module         1打印function_group、function、permission 2打印function、permission 3只打印permission
     */
    public static void getHolderSql(String identification, String functionGroup, String function, String permission, int module) {
        Set<String> value = Optional.ofNullable(map.get(functionGroup)).orElse(new HashSet<>());
        if (module <= 1 && !StringUtils.isEmpty(functionGroup) && value.isEmpty()) {
            String functionGroupSql = String.format(FUNCTION_GROUP, functionGroup).intern();
            sb.append(functionGroupSql).append("\n\n");
        }
        if (module <= 2 && !StringUtils.isEmpty(function) && !value.contains(function)) {
            String functionSql = String.format(FUNCTION, function, SYSTEM_MAP.get(identification), identification, functionGroup).intern();
            sb.append(functionSql).append("\n\n");
            value.add(function);
            map.put(functionGroup, value);
        }
        if (module <= 3 && !StringUtils.isEmpty(permission)) {
            String[] permissionList = permission.split(",");
            for (String sql : permissionList) {
                String permissionSql = String.format(PERMISSION, sql, function, SYSTEM_MAP.get(identification), identification).intern();
                sb.append(permissionSql).append("\n\n");
            }
        }
        sb.append("\n\n");
    }

    private static void write(String content) throws IOException {
        String jsonPath = System.getProperty("user.dir") + "/doc_version/" + sqlFile;
        //一次性写入新内容
        try (FileOutputStream out = new FileOutputStream(jsonPath)) {
            out.write(content.trim().getBytes(StandardCharsets.UTF_8));
            log.info("文件写入完成,{}", jsonPath);
        }
    }

    private static Map<String, String> ofMap(String... args) {
        Map<String, String> map = new HashMap<>(args.length / 2);
        for (int i = 0; i < args.length; i = i + 2) {
            map.put(args[i], args[i + 1]);
        }
        return map;
    }
}
