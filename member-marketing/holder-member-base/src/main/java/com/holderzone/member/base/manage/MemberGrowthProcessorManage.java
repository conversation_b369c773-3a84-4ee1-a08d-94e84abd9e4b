package com.holderzone.member.base.manage;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.*;

import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.growth.*;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.MultipleEquitiesDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.equities.RollReceiveRecordQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;

/**
 * 会员成长值处理
 *
 * <AUTHOR>
 * @date 2024/01/08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MemberGrowthProcessorManage {

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private HsaGrowthCommodityTouchDetailMapper growthCommodityTouchDetailMapper;

    @Resource
    private HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    @Resource
    private HsaSuspendTaskTimeQuantumMapper hsaSuspendTaskTimeQuantumMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private HsaExtraAwardRuleMapper hsaExtraAwardRuleMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;


    private static final String REFUNDED = "已退款";

    /**
     * 成长值回退处理
     *
     * @param memberConsumption 消费对象
     * @param hsaOperationMemberInfo 会员对象
     */
    public void growthValueBackNewProcessor(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (Objects.isNull(hsaOperationMemberInfo)) {
            log.info("会员信息为空，不处理成长值回退");
            return;
        }

        //记录持久化
        List<HsaGrowthValueDetail> updateGrowthDetail = Lists.newArrayList();
        List<HsaGrowthValueDetail> saveGrowthValueDetailList = Lists.newArrayList();

        int growthValue = BigDecimal.ROUND_UP;

        //处理单笔成长值的记录回退
        growthValue = dealGrowthValueSingle(memberConsumption, updateGrowthDetail, growthValue);
        log.info("非商品累计回退总成长值：{}", growthValue);

        //处理累计消费金额、消费笔数、充值金额可回退成长值
        int superpositionGrowthValue = dealBackGrowthSuperposition(memberConsumption, hsaOperationMemberInfo, updateGrowthDetail);
        log.info("累计消费金额、消费笔数、充值金额可回退成长值：{}", superpositionGrowthValue);
        growthValue = growthValue + superpositionGrowthValue;

        //处理消费指定商品使用的记录回退成长值
        Integer commodityGrowthValue = dealCommodityTouchDetailBackGrowth(memberConsumption, updateGrowthDetail);
        log.info("商品累计回退总成长值：{}", commodityGrowthValue);
        growthValue = growthValue + commodityGrowthValue;
        log.info("总累计回退总成长值：{}", growthValue);

        //回退成长值明细记录
        if (growthValue>0){
            HsaGrowthValueDetail hsaGrowthDetail = getHsaGrowthDetail(memberConsumption, hsaOperationMemberInfo, growthValue);
            log.info("成长值回退明细：{}", JSON.toJSONString(hsaGrowthDetail));
            saveGrowthValueDetailList.add(hsaGrowthDetail);
        }

        //回退翻倍权益使用次数和相关成长值
        multipleGrowthBackProcessor(updateGrowthDetail, memberConsumption.getMemberInfoGuid());

        //原逻辑字段处理
        dealFieldsProcessor(updateGrowthDetail);

        //回退成长值明细记录
        backGrowthDetailDB(hsaOperationMemberInfo, growthValue, updateGrowthDetail, saveGrowthValueDetailList);
    }

    private void backGrowthDetailDB(HsaOperationMemberInfo hsaOperationMemberInfo,
                                    int growthValue,
                                    List<HsaGrowthValueDetail> updateGrowthDetail,
                                    List<HsaGrowthValueDetail> saveGrowthValueDetailList) {
        if (growthValue > BigDecimal.ROUND_UP) {
            hsaOperationMemberInfoMapper.subtractMemberGrowth(growthValue, hsaOperationMemberInfo.getGuid());
            log.info("更新会员等级guid:{}", JacksonUtils.writeValueAsString(hsaOperationMemberInfo));
            hsaOperationMemberInfoMapper.updateMemberGrade(hsaOperationMemberInfo);
            hsaGrowthValueDetailService.saveBatch(saveGrowthValueDetailList);
        }
        if (CollUtil.isNotEmpty(updateGrowthDetail)) {
            updateGrowthDetail.forEach(in -> hsaGrowthValueDetailService.updateByGuid(in));
        }

    }

    private static void dealFieldsProcessor(List<HsaGrowthValueDetail> updateGrowthDetail) {
        if (CollUtil.isNotEmpty(updateGrowthDetail)) {
            for (HsaGrowthValueDetail growthValueDetail : updateGrowthDetail) {
                growthValueDetail.setRecordRemainGrowthValue(NUMBER_0);
                //产品要求  退款需要抹去次数
                growthValueDetail.setGrowthValueTaskId(REFUNDED);
                growthValueDetail.setTaskNumber(REFUNDED);
                Integer taskFinishTime = growthValueDetail.getTaskFinishTime();
                if (taskFinishTime > 0) {
                    taskFinishTime = taskFinishTime - 1;
                }
                growthValueDetail.setTaskFinishTime(taskFinishTime);
            }
        }
    }

    private int dealGrowthValueSingle(HsaMemberConsumption memberConsumption, List<HsaGrowthValueDetail> updateGrowthDetail, int growthValue) {
        List<HsaGrowthValueDetail> hsaGrowthDetails = hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getOrderNumber, memberConsumption.getOrderNumber())
                .eq(HsaGrowthValueDetail::getMemberConsumptionGuid, memberConsumption.getGuid())
                .in(HsaGrowthValueDetail::getTaskAction, TaskActionEnum.SINGLE_CONSUMPTION_AMOUNT.getCode(), TaskActionEnum.SINGLE_RECHARGE_AMOUNT.getCode()));

        if (CollUtil.isNotEmpty(hsaGrowthDetails)) {
            updateGrowthDetail.addAll(hsaGrowthDetails);
            //获取总可退成长值
            growthValue = hsaGrowthDetails.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
        }
        return growthValue;
    }

    /**
     * 处理成长值消费商品  回退成长值记录
     * @param memberConsumption 消费对象
     * @return 回退成长值
     */
    private Integer dealCommodityTouchDetailBackGrowth(HsaMemberConsumption memberConsumption, List<HsaGrowthValueDetail> updateGrowthDetail) {
        int growthValue = BigDecimal.ROUND_UP;

        //获取此订单成长值使用明细记录
        List<HsaGrowthCommodityTouchDetail> commodityTouchDetails = getHsaGrowthCommodityTouchDetails(memberConsumption, BooleanEnum.TRUE.getCode(), Collections.emptySet());

        if (CollUtil.isEmpty(commodityTouchDetails)) {
            log.info("commodityTouchDetails=>>>>>>>>>{}", JSON.toJSONString(commodityTouchDetails));
            log.info("成长值累计商品使用记录为空");
            return growthValue;
        }

        Set<String> growthTaskSetGuid = commodityTouchDetails.stream().map(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid).collect(Collectors.toSet());

        Map<String, HsaGrowthValueTask> hsaGrowthTaskMap = hsaGrowthValueTaskMapper.queryByGuids(growthTaskSetGuid)
                .stream()
                .collect(Collectors.toMap(HsaGrowthValueTask::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        if (CollUtil.isEmpty(hsaGrowthTaskMap)) {
            log.info("hsaGrowthTaskMap=>>>>>>>>>{}", JSON.toJSONString(growthTaskSetGuid));
            log.info("成长值任务数据为空");
            return growthValue;
        }

        //获取成长值累计使用明细记录
        List<HsaGrowthCommodityTouchDetail> touchDetailTotals = getHsaGrowthCommodityTouchDetails(memberConsumption, BooleanEnum.FALSE.getCode(), growthTaskSetGuid);


        List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails = Lists.newArrayList();

        List<String> removeGrowthCommodityTouchGuids = Lists.newArrayList();

        Map<String, HsaGrowthCommodityTouchDetail> touchDetailTotalMap = touchDetailTotals
                .stream()
                .collect(Collectors.toMap(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, Function.identity(), (entity1, entity2) -> entity1));


        Map<String, HsaGrowthCommodityTouchDetail> commodityTouchDetailsMap = commodityTouchDetails
                .stream()
                .collect(Collectors.toMap(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, Function.identity(), (entity1, entity2) -> entity1));


        //循环处理成长值任务 累计商品使用记录的回退
        for (String growthTaskGuid : growthTaskSetGuid) {
            HsaGrowthCommodityTouchDetail touchDetailTotal = touchDetailTotalMap.get(growthTaskGuid);
            HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail = commodityTouchDetailsMap.get(growthTaskGuid);

            //根据任务动作回退赠送成长值
            if (hsaGrowthCommodityTouchDetail.getBuyType() == BuyTypeEnum.BUY_COUNT.getCode()
                    && Objects.nonNull(touchDetailTotal)) {
                growthValue = backGrowthBuyCount(hsaGrowthTaskMap, updateGrowthDetail, hsaGrowthCommodityTouchDetail, updateGrowthCommodityTouchDetails, growthValue, touchDetailTotal);
                //回退次数
                touchDetailTotal.setNumber(Math.max(BigDecimal.ROUND_UP, touchDetailTotal.getNumber() - 1));
                updateGrowthCommodityTouchDetails.add(touchDetailTotal);
            } else if (hsaGrowthCommodityTouchDetail.getBuyType() == BuyTypeEnum.BUY_NUMBER.getCode()) {
                growthValue = backBuyNumberGrowthValue(memberConsumption, updateGrowthDetail, hsaGrowthTaskMap, hsaGrowthCommodityTouchDetail, growthValue);
            } else {
                growthValue = backBuyPeriodGrowthValue(memberConsumption, updateGrowthDetail, hsaGrowthTaskMap, hsaGrowthCommodityTouchDetail, growthValue);
            }
            removeGrowthCommodityTouchGuids.add(hsaGrowthCommodityTouchDetail.getGuid());
        }

        //更新累计使用记录
        if (CollUtil.isNotEmpty(updateGrowthCommodityTouchDetails)) {
            updateGrowthCommodityTouchDetails.forEach(in -> growthCommodityTouchDetailMapper.updateByGuid(in));
        }

        //周期累计需要直接删除
        if (CollUtil.isNotEmpty(removeGrowthCommodityTouchGuids)) {
            growthCommodityTouchDetailMapper.removeByGuids(removeGrowthCommodityTouchGuids);
        }
        return growthValue;
    }

    private List<HsaGrowthCommodityTouchDetail> getHsaGrowthCommodityTouchDetails(HsaMemberConsumption memberConsumption,
                                                                                  HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail,
                                                                                  int dayNum,
                                                                                  HsaGrowthValueTask hsaGrowthTask) {

        if (hsaGrowthTask.getBuyPeriodType() == DataUnitEnum.DAY.getCode()) {
            return growthCommodityTouchDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                    .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid())
                    .eq(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, hsaGrowthCommodityTouchDetail.getGrowthValueTaskGuid())
                    .gt(HsaGrowthCommodityTouchDetail::getOrderTime, DateUtil.addStartDayNum(hsaGrowthCommodityTouchDetail.getOrderTime(), dayNum))
                    .lt(HsaGrowthCommodityTouchDetail::getOrderTime, DateUtil.addEndDayNum(hsaGrowthCommodityTouchDetail.getOrderTime(), dayNum)));
        } else {
            return growthCommodityTouchDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                    .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid())
                    .eq(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, hsaGrowthCommodityTouchDetail.getGrowthValueTaskGuid())
                    .gt(HsaGrowthCommodityTouchDetail::getOrderTime, DateUtil.getWeekStartTimeToNum(hsaGrowthCommodityTouchDetail.getOrderTime(), dayNum))
                    .lt(HsaGrowthCommodityTouchDetail::getOrderTime, DateUtil.getWeekEndTimeToNum(hsaGrowthCommodityTouchDetail.getOrderTime(), dayNum)));
        }
    }

    /**
     * 获取成长值累计使用明细
     *
     * @param memberConsumption 消费记录
     * @param type              0 订单为空  1 订单不为空
     * @return List<HsaGrowthCommodityTouchDetail>
     */
    private List<HsaGrowthCommodityTouchDetail> getHsaGrowthCommodityTouchDetails(HsaMemberConsumption memberConsumption, int type, Set<String> growthTaskGuid) {
        if (type == BooleanEnum.FALSE.getCode()) {
            return growthCommodityTouchDetailMapper.selectList(
                    new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                            .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid())
                            .in(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, growthTaskGuid)
                            .isNull(HsaGrowthCommodityTouchDetail::getOrderNumber));
        } else {
            return growthCommodityTouchDetailMapper.selectList(
                    new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                            .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid())
                            .eq(HsaGrowthCommodityTouchDetail::getMemberConsumptionGuid, memberConsumption.getGuid())
                            .isNotNull(HsaGrowthCommodityTouchDetail::getOrderNumber));
        }
    }

    private HsaGrowthValueDetail getHsaGrowthDetail(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo, int growthValue) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(ThreadLocalCache.getHeaderUserInfo().getSource());
        dealOperatorAccountName(hsaOperationMemberInfo, hsaGrowthValueDetail);
        hsaGrowthValueDetail.setOperSubjectGuid(memberConsumption.getOperSubjectGuid());
        final SumValueChangeEnum sumValueChangeEnum = memberConsumption.getConsumptionType() == BooleanEnum.TRUE.getCode()
                ? SumValueChangeEnum.REFUND : SumValueChangeEnum.RECHARGE_REFUND;
        hsaGrowthValueDetail.setChangeType(sumValueChangeEnum.getCode());
        hsaGrowthValueDetail.setRecordDeclaration(sumValueChangeEnum.getDes());
        hsaGrowthValueDetail.setGrowthValue(growthValue);
        hsaGrowthValueDetail.setMemberInfoGuid(memberConsumption.getMemberInfoGuid());
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_1);
        hsaGrowthValueDetail.setStoreName(memberConsumption.getStoreName());
        hsaGrowthValueDetail.setStoreGuid(memberConsumption.getStoreGuid());
        hsaOperationMemberInfo.setMemberGrowthValue(Math.max(hsaOperationMemberInfo.getMemberGrowthValue() - growthValue, NumberConstant.NUMBER_0));
        hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(Math.max(hsaOperationMemberInfo.getMemberGrowthValue() - growthValue, NumberConstant.NUMBER_0));
        setCurrentMemberLevel(hsaOperationMemberInfo, hsaGrowthValueDetail);
        return hsaGrowthValueDetail;
    }

    private void setCurrentMemberLevel(HsaOperationMemberInfo hsaOperationMemberInfo, HsaGrowthValueDetail hsaGrowthValueDetail) {
        HsaMemberGradeInfo memberGradeInfo = this.calculateExtraAmount(hsaOperationMemberInfo.getMemberGrowthValue(), hsaOperationMemberInfo.getOperSubjectGuid());
        HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, hsaOperationMemberInfo.getMemberGradeInfoGuid())
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
        if (!memberGradeInfo.getGuid().equals(hsaOperationMemberInfo.getMemberGradeInfoGuid())) {
            hsaGrowthValueDetail.setCurrentMemberLevel(memberGradeInfo.getName() +
                    StringConstant.LEFT_BRACKET +
                    StringConstant.VIP +
                    memberGradeInfo.getVipGrade() +
                    StringConstant.RIGHT_BRACKET);
            hsaOperationMemberInfo.setMemberGradeInfoGuid(memberGradeInfo.getGuid());
            hsaOperationMemberInfo.setMemberGradeInfoName(memberGradeInfo.getName());
        } else {
            hsaGrowthValueDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() +
                    StringConstant.LEFT_BRACKET +
                    StringConstant.VIP +
                    hsaMemberGradeInfo.getVipGrade() +
                    StringConstant.RIGHT_BRACKET);
        }
    }

    /**
     * 定位当前成长值所在等级
     *
     * @param memberGrowthValue memberGrowthValue
     * @param operSubjectGuid   operSubjectGuid
     * @return HsaExtraAwardRule
     */
    public HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, String operSubjectGuid) {
        List<HsaMemberGradeInfo> hsaMemberGradeInfoList = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                .orderByAsc(HsaMemberGradeInfo::getVipGrade));
        HsaMemberGradeInfo awardRule = null;

        if (CollUtil.isNotEmpty(hsaMemberGradeInfoList)) {
            for (int i = 0; i < hsaMemberGradeInfoList.size(); i++) {
                Integer growthValue = hsaMemberGradeInfoList.get(i).getGrowthValue();
                log.info("当前成长值为：{}", growthValue);
                if (Objects.nonNull(growthValue) &&
                        memberGrowthValue < hsaMemberGradeInfoList.get(i).getGrowthValue()) {
                    if (i == 0) {
                        awardRule = hsaMemberGradeInfoList.get(i);
                    } else {
                        awardRule = hsaMemberGradeInfoList.get(i - 1);
                    }
                    break;
                }
                if (i == hsaMemberGradeInfoList.size() - 1) {
                    awardRule = hsaMemberGradeInfoList.get(i);
                }
            }
        }
        return awardRule;
    }

    private static void dealOperatorAccountName(HsaOperationMemberInfo hsaOperationMemberInfo, HsaGrowthValueDetail hsaGrowthValueDetail) {
        if (ThreadLocalCache.getHeaderUserInfo().getSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()
                || ThreadLocalCache.getHeaderUserInfo().getSource() == SourceTypeEnum.ADD_BACKGROUND.getCode()) {
            hsaGrowthValueDetail.setOperatorAccountName(ThreadLocalCache.getOperatorTelName());
        } else {
            hsaGrowthValueDetail.setOperatorAccountName(hsaOperationMemberInfo.getUserName() + StringConstant.STR_BIAS + hsaOperationMemberInfo.getPhoneNum());
        }
    }


    /**
     * 回退翻倍权益使用次数和相关成长值
     *
     * @param hsaGrowthValueDetails 成长值详情集合
     * @param memberInfoGuid        会员guid
     */
    private void multipleGrowthBackProcessor(List<HsaGrowthValueDetail> hsaGrowthValueDetails, String memberInfoGuid) {
        List<MultipleEquitiesDTO> multipleEquitiesDTOList = hsaGrowthValueDetails.stream()
                .filter(x -> Objects.nonNull(x.getMultipleEquitiesGuid()) && Objects.nonNull(x.getMultipleGrowthValue()))
                .map(x -> {
                    MultipleEquitiesDTO multipleEquitiesDTO = new MultipleEquitiesDTO();
                    multipleEquitiesDTO.setMultipleEquitiesGuid(x.getMultipleEquitiesGuid());
                    multipleEquitiesDTO.setMultipleValue(x.getMultipleGrowthValue());
                    return multipleEquitiesDTO;
                })
                .collect(Collectors.toList());
        // 卡权益成长值明细
        List<MultipleEquitiesDTO> multipleCardEquitiesDTOList = hsaGrowthValueDetails.stream()
                .filter(x -> Objects.nonNull(x.getMultipleCardEquitiesGuid()) && Objects.nonNull(x.getMultipleCardGrowthValue()))
                .map(x -> {
                    MultipleEquitiesDTO multipleEquitiesDTO = new MultipleEquitiesDTO();
                    multipleEquitiesDTO.setMultipleEquitiesGuid(x.getMultipleCardEquitiesGuid());
                    multipleEquitiesDTO.setMultipleValue(x.getMultipleCardGrowthValue());
                    return multipleEquitiesDTO;
                })
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(multipleCardEquitiesDTOList)) {
            multipleEquitiesDTOList.addAll(multipleCardEquitiesDTOList);
        }
        if (CollUtil.isEmpty(multipleEquitiesDTOList)) {
            return;
        }
        // 权益回退处理
        equitiesBackHandler(multipleEquitiesDTOList, memberInfoGuid, BooleanEnum.FALSE.getCode());
    }


    /**
     * 权益回退处理
     *
     * @param multipleEquitiesDTOList 翻倍权益信息
     * @param memberInfoGuid          会员guid
     * @param type                    0：翻倍成长权益类型 ，1：翻倍成长值权益类型
     */
    private void equitiesBackHandler(List<MultipleEquitiesDTO> multipleEquitiesDTOList, String memberInfoGuid, Integer type) {
        //成长值权益guid分组Map
        Map<String, List<MultipleEquitiesDTO>> multipleValueMap = multipleEquitiesDTOList.stream()
                .filter(x -> Objects.nonNull(x.getMultipleEquitiesGuid()) && Objects.nonNull(x.getMultipleValue()))
                .collect(Collectors.groupingBy(MultipleEquitiesDTO::getMultipleEquitiesGuid));

        //查询当前用户翻倍成长值或积分权益使用记录
        List<String> multipleEquitiesGuids = multipleEquitiesDTOList.stream()
                .map(MultipleEquitiesDTO::getMultipleEquitiesGuid).distinct().collect(Collectors.toList());
        List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords = hsaMemberEquitiesReceiveRecordMapper.selectList(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getMemberInfoGuid, memberInfoGuid)
                        .eq(HsaMemberEquitiesReceiveRecord::getType, type)
                        .in(HsaMemberEquitiesReceiveRecord::getGradeEquitiesGuid, multipleEquitiesGuids));

        for (HsaMemberEquitiesReceiveRecord equitiesReceiveRecord : hsaMemberEquitiesReceiveRecords) {
            String equitiesGuid = equitiesReceiveRecord.getGradeEquitiesGuid();
            if (CollUtil.isEmpty(multipleValueMap) || !multipleValueMap.containsKey(equitiesGuid)) {
                continue;
            }
            List<MultipleEquitiesDTO> growthValueDetails = multipleValueMap.get(equitiesGuid);
            int rollBackGrowth = growthValueDetails.stream().mapToInt(MultipleEquitiesDTO::getMultipleValue).sum();

            RollReceiveRecordQO rollReceiveRecordRequest = new RollReceiveRecordQO();
            BeanUtils.copyProperties(equitiesReceiveRecord, rollReceiveRecordRequest);
            rollReceiveRecordRequest.setRollBackCount(growthValueDetails.size());
            rollReceiveRecordRequest.setRollBackGrowth(rollBackGrowth);
            rollReceiveRecordRequest.setType(type);

            //回退翻倍成长值或积分权益时，参数校验
            paramsVerify(rollReceiveRecordRequest);
            hsaMemberEquitiesReceiveRecordMapper.rollReceiveRecord(rollReceiveRecordRequest);
        }
    }

    /**
     * 回退翻倍成长值权益时，参数校验
     *
     * @param rollReceiveRecordRequest 请求参数
     */
    private void paramsVerify(RollReceiveRecordQO rollReceiveRecordRequest) {
        if (StringUtils.isEmpty(rollReceiveRecordRequest.getGradeEquitiesGuid())) {
            log.error("回退翻倍成长值或积分，等级权益guid为空");
            throw new MemberBaseException(MemberAccountExceptionEnum.ROLL_EQUITIES_GUID_IS_NULL.getDes());
        }
        if (StringUtils.isEmpty(rollReceiveRecordRequest.getMemberInfoGuid())) {
            log.error("回退翻倍成长值或积分，会员guid为空");
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ROLL_MEMBER_GUID_IS_NULL, ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    private int backGrowthBuyCount(Map<String, HsaGrowthValueTask> hsaGrowthTaskMap,
                                   List<HsaGrowthValueDetail> updateGrowthDetail,
                                   HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail,
                                   List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails,
                                   int growthValue,
                                   HsaGrowthCommodityTouchDetail touchDetailTotal) {

        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthTaskMap.get(touchDetailTotal.getGrowthValueTaskGuid());
        if (Objects.nonNull(hsaGrowthValueTask)) {
            // 根据数量取余判断是否满足回退
            int flagSend = touchDetailTotal.getNumber() % hsaGrowthValueTask.getBuyNumber();

            //判断是否需要回退成长值
            if (flagSend == 0) {
                //计算回退成长值
                growthValue = getBuyCountBackGrowthValue(updateGrowthDetail, growthValue, touchDetailTotal, hsaGrowthValueTask);
            }
        }

        //记录已退款记录
        hsaGrowthCommodityTouchDetail.setGrowthValueTaskGuid(REFUNDED);
        updateGrowthCommodityTouchDetails.add(hsaGrowthCommodityTouchDetail);
        return growthValue;
    }

    private int getBuyCountBackGrowthValue(List<HsaGrowthValueDetail> updateGrowthDetail,
                                           int growthValue,
                                           HsaGrowthCommodityTouchDetail touchDetailTotal,
                                           HsaGrowthValueTask hsaGrowthTask) {
        //因需求改动 退款变为无序  因此寻找一条最新此任务触发过的成长值明细进行退款  已和产品确认
        List<HsaGrowthValueDetail> hsaGrowthDetail = hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthTask.getId())
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, touchDetailTotal.getMemberInfoGuid()));

        if (CollUtil.isNotEmpty(hsaGrowthDetail)) {
            HsaGrowthValueDetail growthDetail = hsaGrowthDetail.stream()
                    .sorted(Comparator.comparing(HsaGrowthValueDetail::getGmtCreate).reversed())
                    .collect(Collectors.toList()).get(0);
            //回退具体成长值明细
            growthValue = growthValue + growthDetail.getRecordRemainGrowthValue();

            updateGrowthDetail.add(growthDetail);
        }
        return growthValue;
    }

    private int backBuyNumberGrowthValue(HsaMemberConsumption memberConsumption,
                                         List<HsaGrowthValueDetail> updateGrowthDetail,
                                         Map<String, HsaGrowthValueTask> hsaGrowthTaskMap,
                                         HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail,
                                         int growthValue) {
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthTaskMap.get(hsaGrowthCommodityTouchDetail.getGrowthValueTaskGuid());

        if (Objects.nonNull(hsaGrowthValueTask)) {
            HsaGrowthValueDetail hsaGrowthDetail = hsaGrowthValueDetailMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                    .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                    .eq(HsaGrowthValueDetail::getOrderNumber, memberConsumption.getOrderNumber())
                    .eq(HsaGrowthValueDetail::getMemberConsumptionGuid, memberConsumption.getGuid())
                    .eq(HsaGrowthValueDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid()));

            if (Objects.nonNull(hsaGrowthDetail)) {
                growthValue = growthValue + hsaGrowthDetail.getRecordRemainGrowthValue();
                updateGrowthDetail.add(hsaGrowthDetail);
            }
        }

        return growthValue;
    }

    private int backBuyPeriodGrowthValue(HsaMemberConsumption memberConsumption,
                                         List<HsaGrowthValueDetail> updateGrowthDetail,
                                         Map<String, HsaGrowthValueTask> hsaGrowthTaskMap,
                                         HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail,
                                         int growthValue) {
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthTaskMap.get(hsaGrowthCommodityTouchDetail.getGrowthValueTaskGuid());
        if (Objects.nonNull(hsaGrowthValueTask)
                && Boolean.TRUE.equals(checkBack(memberConsumption, hsaGrowthCommodityTouchDetail, hsaGrowthValueTask))) {
            //因需求改动 退款变为无序  因此寻找一条最新此任务触发过的成长值明细进行退款  已和产品确认
            List<HsaGrowthValueDetail> hsaGrowthValueDetails = hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                    .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                    .eq(HsaGrowthValueDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid()));

            if (CollUtil.isNotEmpty(hsaGrowthValueDetails)) {
                HsaGrowthValueDetail growthValueDetail = hsaGrowthValueDetails.stream()
                        .sorted(Comparator.comparing(HsaGrowthValueDetail::getGmtCreate).reversed())
                        .collect(Collectors.toList()).get(0);

                growthValue = growthValue + growthValueDetail.getRecordRemainGrowthValue();
                updateGrowthDetail.add(growthValueDetail);
            }
        }
        return growthValue;
    }

    private Boolean checkBack(HsaMemberConsumption memberConsumption,
                              HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail,
                              HsaGrowthValueTask hsaGrowthValueTask) {
        //判断是否有连续消费的   若没有则回退
        boolean isThreeDay = false;
        for (int i = 0; i < hsaGrowthValueTask.getBuyNumber(); i++) {
            List<HsaGrowthCommodityTouchDetail> oneTouchDetailList = getHsaGrowthCommodityTouchDetails(memberConsumption, hsaGrowthCommodityTouchDetail, i + 1, hsaGrowthValueTask);
            if (CollUtil.isEmpty(oneTouchDetailList)) {
                isThreeDay = true;
                break;
            }
        }
        return isThreeDay;
    }

    /**
     * 计算累计消费金额、消费笔数、充值金额可回退成长值
     * @return growthValue
     */
    private int dealBackGrowthSuperposition(HsaMemberConsumption memberConsumption,
                                            HsaOperationMemberInfo hsaOperationMemberInfo,
                                            List<HsaGrowthValueDetail> updateGrowthDetail) {
        int growthValue = BigDecimal.ROUND_UP;

        //获取累计消费动作任务
        List<HsaGrowthValueTask> hsaGrowthTasks = getHsaGrowthTasks(memberConsumption);

        if (CollUtil.isEmpty(hsaGrowthTasks)) {
            log.info("没有累计成长值任务，不处理成长值回退");
            return growthValue;
        }

        List<String> growthTaskGuid = hsaGrowthTasks.stream().map(HsaGrowthValueTask::getGuid).map(String::valueOf).collect(Collectors.toList());

        List<String> growthTaskId = hsaGrowthTasks.stream().map(HsaGrowthValueTask::getId).map(String::valueOf).collect(Collectors.toList());

        //获取任务暂停时间
        Map<String, List<HsaSuspendTaskTimeQuantum>> suspendMap = getSuspendMap(growthTaskGuid);

        //获取赠送阶梯对象
        Map<String, List<HsaExtraAwardRule>> extraAwardRuleMap = getExtraAwardRuleMap(growthTaskGuid);

        //获取会员成长值明细
        List<HsaGrowthValueDetail> hsaGrowthDetailList = hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                .in(HsaGrowthValueDetail::getGrowthValueTaskId, growthTaskId));

        if (CollUtil.isEmpty(hsaGrowthDetailList)) {
            log.info("会员未参与累计成长值任务，不处理成长值回退");
            return growthValue;
        }

        Map<String, List<HsaGrowthValueDetail>> growthDetailMap = hsaGrowthDetailList
                .stream()
                .collect(Collectors.groupingBy(HsaGrowthValueDetail::getGrowthValueTaskId));

        Set<HsaGrowthValueTask> hsaGrowthTaskSet = hsaGrowthTasks.stream().filter(in -> growthDetailMap.containsKey(in.getId() + "")).collect(Collectors.toSet());

        //计算可回退成长值
        growthValue = computeBackGrowthValue(memberConsumption, updateGrowthDetail, hsaGrowthTaskSet, growthDetailMap, suspendMap, extraAwardRuleMap);

        return growthValue;
    }

    private List<HsaGrowthValueTask> getHsaGrowthTasks(HsaMemberConsumption memberConsumption) {
        return hsaGrowthValueTaskMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, memberConsumption.getOperSubjectGuid())
                .in(HsaGrowthValueTask::getTaskAction,
                        TaskActionEnum.TOTAL_CONSUMPTION_AMOUNT.getCode(),
                        TaskActionEnum.TOTAL_CONSUMPTION_COUNT.getCode(),
                        TaskActionEnum.TOTAL_RECHARGE_AMOUNT.getCode())
                .in(HsaGrowthValueTask::getTaskType, TaskTypeEnum.CONSUMPTION_TASK.getCode(), TaskTypeEnum.RECHARGE_TASK.getCode())
                .eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    /**
     * 获取任务暂停时段
     * @return
     */
    private Map<String, List<HsaSuspendTaskTimeQuantum>> getSuspendMap(List<String> growthTaskGuid) {
        return hsaSuspendTaskTimeQuantumMapper
                .selectList(new LambdaQueryWrapper<HsaSuspendTaskTimeQuantum>()
                        .in(HsaSuspendTaskTimeQuantum::getTaskGuid, growthTaskGuid)
                        .eq(HsaSuspendTaskTimeQuantum::getType, TaskTypeEnum.CONSUMPTION_TASK.getCode())
                        .eq(HsaSuspendTaskTimeQuantum::getTaskType, GrowthValueOrIntegralEnum.GROWTH_VALUE.getCode())
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendStartTime)
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendEndTime))
                .stream()
                .collect(Collectors.groupingBy(HsaSuspendTaskTimeQuantum::getTaskGuid));
    }

    private Map<String, List<HsaExtraAwardRule>> getExtraAwardRuleMap(List<String> growthTaskGuid) {
        return hsaExtraAwardRuleMapper
                .selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                        .in(HsaExtraAwardRule::getGrowthValueTaskGuid, growthTaskGuid)
                        .orderByDesc(HsaExtraAwardRule::getConsumptionAmount))
                .stream()
                .collect(Collectors.groupingBy(HsaExtraAwardRule::getGrowthValueTaskGuid));


    }

    private static LocalDateTime getEndTime(List<LocalDateTime> beginTimeList) {
        LocalDateTime endTime;
        if (beginTimeList.size() > 1) {
            endTime = beginTimeList.get(1);
        } else {
            endTime = null;
        }
        return endTime;
    }

    private int computeBackGrowthValue(HsaMemberConsumption memberConsumption,
                                       List<HsaGrowthValueDetail> updateGrowthDetail,
                                       Set<HsaGrowthValueTask> hsaGrowthTasks,
                                       Map<String, List<HsaGrowthValueDetail>> growthDetailMap,
                                       Map<String, List<HsaSuspendTaskTimeQuantum>> suspendMap,
                                       Map<String, List<HsaExtraAwardRule>> growthExtraAwardRuleMap) {
        int growthlValue = BigDecimal.ROUND_UP;
        for (HsaGrowthValueTask hsaGrowthTask : hsaGrowthTasks) {
            if (growthDetailMap.containsKey(String.valueOf(hsaGrowthTask.getId()))) {

                List<HsaGrowthValueDetail> memberGrowthDetails = growthDetailMap.get(hsaGrowthTask.getId() + "");

                List<LocalDateTime> beginTimeList = DateUtil.buildBeginPeriodTime(hsaGrowthTask.getTotalPeriodType(),
                        memberConsumption.getGmtCreate(),
                        hsaGrowthTask.getGmtCreate(),
                        hsaGrowthTask.getTotalPeriod());


                LocalDateTime beginTime = beginTimeList.get(0);
                LocalDateTime endTime = getEndTime(beginTimeList);


                memberGrowthDetails = memberGrowthDetails.stream()
                        .filter(in -> in.getGmtCreate().isAfter(beginTime) && in.getGmtCreate().isBefore(endTime))
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(memberGrowthDetails)) {
                    log.info("会员成长值任务当前周期未参与，不处理成长值回退");
                    continue;
                }
                memberGrowthDetails = memberGrowthDetails
                        .stream()
                        .sorted(Comparator.comparing(HsaGrowthValueDetail::getGmtCreate).reversed())
                        .collect(Collectors.toList());

                //暂停时段
                List<HsaSuspendTaskTimeQuantum> hsaSuspendTaskTimeQuantums = suspendMap.get(hsaGrowthTask.getGuid());

                //任务赠送阶梯对象
                List<HsaExtraAwardRule> hsaExtraAwardRules = growthExtraAwardRuleMap.get(hsaGrowthTask.getGuid());

                if (hsaGrowthTask.getTaskAction() == TaskActionEnum.TOTAL_CONSUMPTION_AMOUNT.getCode()) {
                    //适用业务
                    List<String> apply = Arrays.asList(hsaGrowthTask.getApplyBusinessJson().split(StringConstant.COMMA));
                    //之前充值金额
                    BigDecimal beforeRechargeAmount = hsaMemberConsumptionMapper.selectByMemberGuidBackAmount(memberConsumption.getMemberInfoGuid(),
                            memberConsumption.getGuid(), beginTime, endTime, apply, hsaSuspendTaskTimeQuantums);

                    //获取累计消费可回退金额
                    growthlValue = backConsumptionAmount(memberConsumption, beforeRechargeAmount, hsaExtraAwardRules, growthlValue, memberGrowthDetails, updateGrowthDetail);

                } else if (hsaGrowthTask.getTaskAction() == TaskActionEnum.TOTAL_CONSUMPTION_COUNT.getCode()) {
                    //适用业务
                    List<String> apply = Arrays.asList(hsaGrowthTask.getApplyBusinessJson().split(StringConstant.COMMA));
                    //除此订单的消费次数
                    int beforeConsumptionCount = Math.max(BigDecimal.ROUND_UP, hsaMemberConsumptionMapper.getBackConsumptionCount(
                            memberConsumption.getMemberInfoGuid(),
                            memberConsumption.getGuid(),
                            beginTime,
                            endTime,
                            hsaGrowthTask.getConsumptionIgnoreAmount(),
                            apply,
                            hsaSuspendTaskTimeQuantums));

                    //获取累计消费笔数可回退金额
                    growthlValue = backConsumptionNum(beforeConsumptionCount, hsaExtraAwardRules, growthlValue, memberGrowthDetails, updateGrowthDetail);
                } else {
                    //除此订单的总充值金额
                    BigDecimal beforeRechargeAmount = hsaMemberConsumptionMapper.selectRechargeBackAmountMemberGuid(memberConsumption.getMemberInfoGuid(),
                            memberConsumption.getGuid(), beginTime, endTime, hsaSuspendTaskTimeQuantums);

                    growthlValue = backRechargeAmount(memberConsumption, beforeRechargeAmount, hsaExtraAwardRules, growthlValue, memberGrowthDetails, updateGrowthDetail);
                }
            }
        }
        return growthlValue;
    }


    private int backConsumptionAmount(
            HsaMemberConsumption memberConsumption,
            BigDecimal consumptionAmount,
            List<HsaExtraAwardRule> hsaExtraAwardRules,
            int growthValue,
            List<HsaGrowthValueDetail> memberGrowthDetails,
            List<HsaGrowthValueDetail> backMemberGrowthDetails) {

        if (Objects.isNull(consumptionAmount)) {
            backMemberGrowthDetails.addAll(memberGrowthDetails);
            growthValue = growthValue + memberGrowthDetails.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
            return growthValue;
        }

        BigDecimal amount = consumptionAmount.add(memberConsumption.getOrderPaidAmount());

        //取出满足条件的阶梯 并以倒序排列
        hsaExtraAwardRules = hsaExtraAwardRules.stream()
                .filter(in -> in.getConsumptionAmount().compareTo(amount) <= BigDecimal.ROUND_UP)
                .sorted(Comparator.comparing(HsaExtraAwardRule::getConsumptionFrequency).reversed())
                .collect(Collectors.toList());

        for (int i = 0; i < hsaExtraAwardRules.size(); i++) {
            if (consumptionAmount.compareTo(hsaExtraAwardRules.get(i).getConsumptionAmount()) >= 0
                    || i > memberGrowthDetails.size() - 1) {
                log.info("剩余消费金额大于等于阶梯当前金额,不进行回退：{}", consumptionAmount);
                break;
            } else {
                log.info("回退当前阶梯消费金额：{}", growthValue);
                HsaGrowthValueDetail hsaGrowthValueDetail = memberGrowthDetails.get(i);
                backMemberGrowthDetails.add(hsaGrowthValueDetail);
                growthValue = growthValue + hsaGrowthValueDetail.getRecordRemainGrowthValue();
            }
        }
        return growthValue;
    }

    private static int backConsumptionNum(int beforeConsumptionCount,
                                          List<HsaExtraAwardRule> hsaExtraAwardRules,
                                          int growthValue,
                                          List<HsaGrowthValueDetail> memberGrowthDetails,
                                          List<HsaGrowthValueDetail> backMemberGrowthDetails) {

        if (beforeConsumptionCount == BigDecimal.ROUND_UP) {
            backMemberGrowthDetails.addAll(memberGrowthDetails);
            growthValue = growthValue + memberGrowthDetails.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
            return growthValue;
        }

        int count = beforeConsumptionCount + 1;

        //取出满足条件的阶梯 并以倒序排列
        hsaExtraAwardRules = hsaExtraAwardRules.stream()
                .filter(in -> in.getConsumptionFrequency() <= count)
                .sorted(Comparator.comparing(HsaExtraAwardRule::getConsumptionFrequency).reversed())
                .collect(Collectors.toList());

        for (int i = 0; i < hsaExtraAwardRules.size(); i++) {
            //若当前阶梯大于等于当前消费笔数或明细记录已回退完毕,则返回
            if (beforeConsumptionCount >= hsaExtraAwardRules.get(i).getConsumptionFrequency()
                    || i > memberGrowthDetails.size()) {
                log.info("剩余消费笔数大于等于阶梯当前笔数,不进行回退：{}", beforeConsumptionCount);
                break;
            } else {
                log.info("回退当前阶梯笔数：{}", growthValue);
                HsaGrowthValueDetail hsaGrowthValueDetail = memberGrowthDetails.get(i);
                backMemberGrowthDetails.add(hsaGrowthValueDetail);
                growthValue = growthValue + hsaGrowthValueDetail.getRecordRemainGrowthValue();
            }
        }
        return growthValue;
    }

    private static int backRechargeAmount(
            HsaMemberConsumption memberConsumption,
            BigDecimal beforeRechargeAmount,
            List<HsaExtraAwardRule> hsaExtraAwardRules,
            int growthValue,
            List<HsaGrowthValueDetail> memberGrowthDetails,
            List<HsaGrowthValueDetail> backMemberGrowthDetails) {

        if (Objects.isNull(beforeRechargeAmount)) {
            backMemberGrowthDetails.addAll(memberGrowthDetails);
            growthValue = growthValue + memberGrowthDetails.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
            return growthValue;
        }

        BigDecimal amount = beforeRechargeAmount.add(memberConsumption.getOrderPaidAmount());

        //取出满足条件的阶梯 并以倒序排列
        hsaExtraAwardRules = hsaExtraAwardRules.stream()
                .filter(in -> in.getConsumptionAmount().compareTo(amount) <= BigDecimal.ROUND_UP)
                .sorted(Comparator.comparing(HsaExtraAwardRule::getConsumptionFrequency).reversed())
                .collect(Collectors.toList());

        for (int i = 0; i < hsaExtraAwardRules.size(); i++) {
            if (beforeRechargeAmount.compareTo(hsaExtraAwardRules.get(i).getConsumptionAmount()) >= 0
                    || i > memberGrowthDetails.size() - 1) {
                log.info("剩余充值金额大于等于阶梯当前充值金额,不进行回退：{}", beforeRechargeAmount);
                break;
            } else {
                log.info("回退当前阶梯充值金额：{}", growthValue);
                HsaGrowthValueDetail hsaGrowthValueDetail = memberGrowthDetails.get(i);
                backMemberGrowthDetails.add(hsaGrowthValueDetail);
                growthValue = growthValue + hsaGrowthValueDetail.getRecordRemainGrowthValue();
            }
        }
        return growthValue;
    }
}
