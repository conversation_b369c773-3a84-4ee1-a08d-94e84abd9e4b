package com.holderzone.member.base.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.member.HsaMemberPersonal;
import com.holderzone.member.base.mapper.member.HsaMemberPersonalMapper;
import com.holderzone.member.base.service.member.HsaMemberPersonalService;
import com.holderzone.member.base.transform.member.MemberBusinessTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.business.PersonalDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaMemberPersonalServiceImpl extends HolderBaseServiceImpl<HsaMemberPersonalMapper, HsaMemberPersonal> implements HsaMemberPersonalService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public void edit(PersonalDTO personalDTO) {
        personalDTO.validatedEdit();
        this.updateByGuid(MemberBusinessTransform.INSTANCE.fromPersonalDTO(personalDTO));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchHandlePersonal(List<PersonalDTO> personalDTOList, String memberGuid) {
        if (CollectionUtils.isEmpty(personalDTOList)) {
            this.deleteByMemberGuid(memberGuid);
            return;
        }
        personalDTOList.forEach(PersonalDTO::validated);
        this.deleteByMemberGuid(memberGuid);
        List<HsaMemberPersonal> personalList = MemberBusinessTransform.INSTANCE.fromPersonalDTOList(personalDTOList);
        personalList.forEach(p -> {
            p.setMemberGuid(memberGuid);
            p.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            p.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberPersonalService.class.getSimpleName()));
        });
        this.saveBatch(personalList);
    }

    @Override
    public void deleteByMemberGuid(String memberGuid) {
        this.remove(new LambdaQueryWrapper<HsaMemberPersonal>().eq(HsaMemberPersonal::getMemberGuid, memberGuid));
    }

    @Override
    public void deleteByGuid(String guid) {
        this.removeByGuid(guid);
    }

    @Override
    public void add(PersonalDTO personalDTO) {
        personalDTO.validatedAdd();
        HsaMemberPersonal hsaMemberPersonal = MemberBusinessTransform.INSTANCE.fromPersonalDTO(personalDTO);
        hsaMemberPersonal.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaMemberPersonal.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberPersonalService.class.getSimpleName()));
        this.save(hsaMemberPersonal);
    }

    @Override
    public List<PersonalDTO> listByMemberGuid(String memberGuid, String operSubjectGuid) {
        return MemberBusinessTransform.INSTANCE.toPersonalList(this.list(new LambdaQueryWrapper<HsaMemberPersonal>()
                .eq(HsaMemberPersonal::getMemberGuid, memberGuid)
                .eq(HsaMemberPersonal::getOperSubjectGuid, operSubjectGuid)));
    }
}
