package com.holderzone.member.base.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.grade.PayRuleDTO;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.grade.EffectiveDurationTypeEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class StringBaseHandlerUtil {

    private StringBaseHandlerUtil() {
    }
    /**
     * 给手机号+区号信息
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneCountryCodeHandler(String phoneCountryCode, String phone) {
        if (StringUtils.isEmpty(phone)) {
            return "";
        }
        String phoneNum = Optional.of(phone).orElse("");
        String countryCode = Objects.nonNull(phoneCountryCode) ?
                ("+" + phoneCountryCode + " ") : "+86 ";
        return countryCode + phoneNum;
    }

    /**
     * 给手机号+区号信息
     * 判断用户是否有完整手机号权限
     * 没有权限脱敏展示
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneCountryCodeHandler(String phoneCountryCode, String phone, Boolean isTrue) {
        String phoneNum = "";
        if (StringUtils.isEmpty(phone)) {
            return phoneNum;
        }
        if (Boolean.TRUE.equals(isTrue)) {
            phoneNum = phoneCountryCodeHandler(phoneCountryCode, phone);
        } else {
            phoneNum = phoneDesensitizationHandler(phoneCountryCode, phone);
        }
        return phoneNum;
    }

    /**
     * 给手机号设置脱敏保护
     * 给手机号+区号信息
     *
     * @param phoneCountryCode 区号
     * @param phone            手机号
     * @return +区号+手机号
     */
    public static String phoneDesensitizationHandler(String phoneCountryCode, String phone) {
        if (StringUtils.isEmpty(phone)) {
            return "";
        }
        String phoneNum = phone.substring(0, 3) + "****" + phone.substring(7);
        String countryCode = Objects.nonNull(phoneCountryCode) ?
                ("+" + phoneCountryCode + " ") : "+86 ";
        return countryCode + phoneNum;
    }

    /**
     * 集合转换成字符串，每个字符用 、号分割
     *
     * @return
     */
    public static String arrayConvert(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            String string = list.get(i);
            if (i != size) {
                sb.append(string).append("、");
            } else {
                sb.append(string);
            }
        }
        return sb.toString();
    }

    public static List<String> StringConvertList(String string) {
        return JSONArray.parseArray(string, String.class);
    }

    public static String StringConvertList(List<String> stringList) {
        return JSONObject.toJSONString(stringList);
    }

    public static String mysqlJsonValue(Object source) {
        return "\"" + source + "\"";
    }

    public static List<String> strConvertList(String str) {
        if (StringUtils.isEmpty(str)) {
            return Collections.emptyList();
        }
        return Arrays.stream(str.split("、")).collect(Collectors.toList());
    }

    public static String getRuleStr(List<PayRuleDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            PayRuleDTO ruleDTO = list.get(i);
            if (i != size) {
                sb.append(ruleDTO.getNum()).append(DateUnitEnum.getCustomizedNameByCode(ruleDTO.getUnit()))
                        .append("￥").append(ruleDTO.getPrice()).append("、");
            } else {
                sb.append(ruleDTO.getNum()).append(DateUnitEnum.getCustomizedNameByCode(ruleDTO.getUnit()))
                        .append("￥").append(ruleDTO.getPrice());
            }
        }
        return sb.toString();
    }

    public static String getNewRuleStr(List<PayRuleDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            PayRuleDTO ruleDTO = list.get(i);
            if (i != size) {
                sb.append(EffectiveDurationTypeEnum.fromValue(ruleDTO.getUnit()).getMsg())
                        .append("￥").append(ruleDTO.getPrice()).append("、");
            } else {
                sb.append(EffectiveDurationTypeEnum.fromValue(ruleDTO.getUnit()).getMsg())
                        .append("￥").append(ruleDTO.getPrice());
            }
        }
        return sb.toString();
    }
}
