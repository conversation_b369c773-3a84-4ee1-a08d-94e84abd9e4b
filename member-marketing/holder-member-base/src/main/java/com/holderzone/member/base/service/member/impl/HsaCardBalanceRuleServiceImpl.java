package com.holderzone.member.base.service.member.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.base.entity.member.HsaCardBalanceRule;
import com.holderzone.member.base.mapper.member.HsaCardBalanceRuleMapper;
import com.holderzone.member.base.service.member.HsaCardBalanceRuleService;
import com.holderzone.member.base.transform.member.MemberCardTransform;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.qo.member.HsaCardBalanceRuleQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.member.HsaCardBalanceRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaCardBalanceRule
 * @Author: 张林
 * @Description: 会员卡余额规则service
 * @Version: 1.0.1
 */
@Slf4j
@Service
public class HsaCardBalanceRuleServiceImpl extends HolderBaseServiceImpl<HsaCardBalanceRuleMapper, HsaCardBalanceRule> implements HsaCardBalanceRuleService {

    @Resource
    private HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    public static final String CHARACTER = ",";

    public static final String ORDER_OF_DEDUCTION = "1,2,3"; // 扣款顺序1,2,3，代表顺序：实充余额＞赠送余额＞补贴余额

    public static final String NOTIFICATION_TYPE = "0,1,2"; // 余额不足通知方式：0=短信通知、1=微信公众号通知、2=小程序通知。 默认为全选

    @Override
    public int addCardBalanceRule(HsaCardBalanceRuleQO depositStrategyQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        //验证余额规则
        verify(depositStrategyQO);

        HsaCardBalanceRule hsaCardBalanceRule = new HsaCardBalanceRule();
        BeanUtil.copyProperties(depositStrategyQO, hsaCardBalanceRule);
        String guid = guidGeneratorUtil.getStringGuid(HsaCardBalanceRule.class.getSimpleName());
        hsaCardBalanceRule.setGuid(String.valueOf(guid));
        hsaCardBalanceRule.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        return hsaCardBalanceRuleMapper.insert(hsaCardBalanceRule);
    }

    @Override
    public int updateCardBalanceRule(HsaCardBalanceRuleQO depositStrategyQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        //验证余额规则
        verify(depositStrategyQO);

        HsaCardBalanceRule hsaCardBalanceRule = new HsaCardBalanceRule();
        BeanUtil.copyProperties(depositStrategyQO, hsaCardBalanceRule);
        //将集合转换成字符串存入数据库
        hsaCardBalanceRule.setDeductionBalanceOrder(StringUtils.join(depositStrategyQO.getDeductionBalanceOrderList(), CHARACTER));
        hsaCardBalanceRule.setNotificationType(StringUtils.join(depositStrategyQO.getNotificationTypeList(), CHARACTER));
        hsaCardBalanceRule.setTerminalCheckStatus(StringUtils.join(depositStrategyQO.getTerminalCheckStatusList(), CHARACTER));

        if (CollUtil.isNotEmpty(depositStrategyQO.getTerminalCheckStatusList())) {
            if (depositStrategyQO.getTerminalCheckStatusList().contains(PayWayEnum.WE_CHANT_PAY.getCode())) {
                hsaCardBalanceRule.setAppletsCheckState(BooleanEnum.TRUE.getCode());
            } else {
                hsaCardBalanceRule.setAppletsCheckState(BooleanEnum.FALSE.getCode());
            }
        }

        return hsaCardBalanceRuleMapper.update(hsaCardBalanceRule,
                new LambdaQueryWrapper<HsaCardBalanceRule>()
                        .eq(HsaCardBalanceRule::getGuid, depositStrategyQO.getGuid())
                        .eq(HsaCardBalanceRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
    }

    @Override
    public HsaCardBalanceRuleVO getBalanceRule() {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardBalanceRule>()
                        .eq(HsaCardBalanceRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        if (ObjectUtils.isEmpty(hsaCardBalanceRule)) {
            hsaCardBalanceRule = new HsaCardBalanceRule();
            String guid = guidGeneratorUtil.getStringGuid(HsaCardBalanceRule.class.getSimpleName());
            hsaCardBalanceRule.setGuid(guid);
            hsaCardBalanceRule.setUseCheck(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRule.setAppletsCheckState(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRule.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
            //扣款顺序根据1＞2＞3，默认顺序实充余额＞赠送余额＞补贴余额
            hsaCardBalanceRule.setDeductionBalanceOrder(ORDER_OF_DEDUCTION);
            hsaCardBalanceRule.setUseCheck(BooleanEnum.FALSE.getCode());
            // 余额不足通知方式：0=短信通知、1=微信公众号通知、2=小程序通知。 默认为全选
            hsaCardBalanceRule.setNotificationType(NOTIFICATION_TYPE);
            hsaCardBalanceRule.setBalanceNotification(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRule.setRefundCardRule(0);
            hsaCardBalanceRuleMapper.insert(hsaCardBalanceRule);
        }
        HsaCardBalanceRuleVO hsaCardBalanceRuleVO = MemberCardTransform.INSTANCE.hsaCardBalanceRuleToVO(hsaCardBalanceRule);
        // 参数转换
        hsaCardBalanceRuleVO.setDeductionBalanceOrderList(conversion(hsaCardBalanceRule.getDeductionBalanceOrder()));
        hsaCardBalanceRuleVO.setNotificationTypeList(conversion(hsaCardBalanceRule.getNotificationType()));
        hsaCardBalanceRuleVO.setTerminalCheckStatusList(conversion(hsaCardBalanceRule.getTerminalCheckStatus()));
        return hsaCardBalanceRuleVO;
    }

    @Override
    public List<HsaCardBalanceRuleVO> list() {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaCardBalanceRule> hsaCardBalanceRules = hsaCardBalanceRuleMapper.selectList(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        return MemberCardTransform.INSTANCE.hsaCardBalanceRuleToVO(hsaCardBalanceRules);
    }

    /**
     * 校验参数
     * @param depositStrategyQO
     */
    public void verify(HsaCardBalanceRuleQO depositStrategyQO) {
        if (depositStrategyQO.getBalanceNotification() == BooleanEnum.TRUE.getCode()) {
            //开启余额不足通知,金额不能为空
            if (ObjectUtils.isEmpty(depositStrategyQO.getBalanceThreshold())) {
                throw new BusinessException(MemberAccountExceptionEnum.ERROR_AMOUNT_INSUFFICIENT_BALANCE_NOTIFICATION.getDes());
            }

            //开启余额不足通知,通知类型至少选择一个 短信通知、微信公众号通知、小程序通知
            if (CollectionUtils.isEmpty(depositStrategyQO.getNotificationTypeList())) {
                throw new BusinessException(MemberAccountExceptionEnum.ERROR_TYPE_INSUFFICIENT_BALANCE_NOTIFICATION.getDes());
            }
        }
        //开启卡支付密码,那么小程序端校验状态不能为空
        if (depositStrategyQO.getUseCheck() == BooleanEnum.TRUE.getCode() &&
                ObjectUtils.isEmpty(depositStrategyQO.getAppletsCheckState())) {
            throw new BusinessException(MemberAccountExceptionEnum.ERROR_CARD_INSUFFICIENT_BALANCE_NOTIFICATION.getDes());
        }
    }

    /**
     * 将字符串转换成集合
     * @param message 字符串
     * @return 操作结果
     */
    public List<Integer> conversion(String message) {
        List<Integer> list = new ArrayList<>();
        if (org.apache.commons.lang.StringUtils.isEmpty(message)) {
            return list;
        }
        String[] messageList = message.split(CHARACTER);
        return Arrays.stream(messageList)
                .filter(x -> !StringUtils.isEmpty(x)).map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 判断当前运营主体是否初始化了余额扣款顺序
     * 如果没有则新增余额扣款舒心
     * @param subjectInfoList 运营主体集合
     */
    @Override
    public void isSaveBalanceRule(List<String> subjectInfoList) {
        if (CollUtil.isEmpty(subjectInfoList)) {
            return;
        }
        //需要新增的运营主体guid集合：saveSubjectGuidList
        List<String> saveSubjectGuidList;
        //数据库余额规则表已存有的运营主体guid集合：subjectGuidList
        List<String> subjectGuidList = this.list(
                        new LambdaQueryWrapper<HsaCardBalanceRule>()
                                .in(HsaCardBalanceRule::getOperSubjectGuid, subjectInfoList))
                .stream().map(HsaCardBalanceRule::getOperSubjectGuid).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(subjectGuidList)) {
            saveSubjectGuidList = subjectInfoList.stream()
                    .filter(x -> !subjectGuidList.contains(x)).collect(Collectors.toList());
        } else {
            saveSubjectGuidList = subjectInfoList;
        }
        if (CollUtil.isEmpty(saveSubjectGuidList)) {
            return;
        }
        saveBalanceRule(saveSubjectGuidList);
    }

    /**
     * 新增余额运营主体余额扣款顺序
     * @param saveSubjectGuidList 运营主体guids
     */
    private void saveBalanceRule(List<String> saveSubjectGuidList) {
        if (CollUtil.isEmpty(saveSubjectGuidList)) {
            return;
        }
        List<HsaCardBalanceRule> hsaCardBalanceRuleList = new ArrayList<>();
        for (String subjectGuid : saveSubjectGuidList) {
            HsaCardBalanceRule hsaCardBalanceRule = new HsaCardBalanceRule();
            String guid = guidGeneratorUtil.getStringGuid(HsaCardBalanceRule.class.getSimpleName());
            hsaCardBalanceRule.setGuid(guid);
            hsaCardBalanceRule.setUseCheck(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRule.setAppletsCheckState(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRule.setOperSubjectGuid(subjectGuid);
            //扣款顺序根据1＞2＞3，默认顺序实充余额＞赠送余额＞补贴余额
            hsaCardBalanceRule.setDeductionBalanceOrder(ORDER_OF_DEDUCTION);
            hsaCardBalanceRule.setUseCheck(BooleanEnum.FALSE.getCode());
            // 余额不足通知方式：0=短信通知、1=微信公众号通知、2=小程序通知。 默认为全选
            hsaCardBalanceRule.setNotificationType(NOTIFICATION_TYPE);
            hsaCardBalanceRule.setBalanceNotification(BooleanEnum.FALSE.getCode());
            hsaCardBalanceRuleList.add(hsaCardBalanceRule);
        }
        this.saveBatch(hsaCardBalanceRuleList);
    }


}
