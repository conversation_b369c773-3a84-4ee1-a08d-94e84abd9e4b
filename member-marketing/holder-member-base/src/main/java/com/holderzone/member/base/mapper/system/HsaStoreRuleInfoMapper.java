package com.holderzone.member.base.mapper.system;

import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.qo.growth.AppletGrowthStorePageQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-11 10:53
 */
public interface HsaStoreRuleInfoMapper extends HolderBaseMapper<HsaStoreRuleInfo> {

    /**
     * 功能描述：统计成长值门店数量
     * @date 2021/11/30
     * @param taskGuid 成长值任务guid
     * @return java.lang.Integer
     */
    Integer countStore(@Param("taskGuid") String taskGuid);

    /**
     * 功能描述：小程序获取成长值详细列表
     * @date 2021/12/1
     * @param qo 查询条件
     * @return java.util.List<com.holderzone.member.common.dto.base.StoreBaseInfo>
     */
    List<StoreBaseInfo> listGrowthValueStore(@Param("request") AppletGrowthStorePageQO qo);

}
