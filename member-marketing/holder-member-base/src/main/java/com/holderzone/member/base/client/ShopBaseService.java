package com.holderzone.member.base.client;


import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.growth.CommodityComboTypeEnum;
import com.holderzone.member.common.enums.growth.CommodityStatusEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.commodity.StoreStrategyQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.growth.GrowthCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityPageVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品数据
 * @date 2021/12/7 15:39
 */
@Service
@Slf4j
public class ShopBaseService {

    @Lazy
    @Resource
    private StoreBaseService storeBaseService;

    @Resource
    private ExternalSupport externalSupport;

    /**
     * 会员等级权益条件获取商品数据
     *
     * @param qo qo
     */
    public GrowthCommodityPageVO queryGradeCommodityPage(GradeCommodityBasePageQO qo) {
        GrowthCommodityPageVO growthCommodityBaseVOS = new GrowthCommodityPageVO();
        List<GrowthCommodityBaseVO> growthCommodityBaseVOList;
        qo.setStoreId(Lists.newArrayList());
        qo.initPage();
        qo.initChanel();

        // 如果有手动赋值的code，就用传入的
        Integer systemCode =
                Optional.ofNullable(qo.getSystemEnum()).map(SystemEnum::getCode).orElseGet(ThreadLocalCache::getSystem);

        log.info("条件获取商品数据 入参>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", qo);
        Pair<Integer, List<ResGradeCommodityBase>> storeCommodityPagePair = externalSupport.itemServer(systemCode).listStoreCommodityPage(qo);
        log.info("条件获取商品数据 返回>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", storeCommodityPagePair);

        List<ResGradeCommodityBase> resCommodityBases = storeCommodityPagePair.getValue();
        if (CollUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }

        final boolean isBack = Objects.equals(ThreadLocalCache.getHeaderUserInfo().getSource(), SourceTypeEnum.ADD_BACKGROUND.getCode());

        growthCommodityBaseVOList = resCommodityBases.stream()
                .filter(in -> isCommodityDown(isBack, in)
                        && !in.getState().equals(CommodityStatusEnum.COMMODITY_LOSE.getDes())
                        && !StringUtils.isEmpty(in.getPrice())
                )
                .map(in -> getGrowthCommodityBaseVO(qo, in))
                .collect(Collectors.toList());
        growthCommodityBaseVOS.setGrowthCommodityBaseVOS(growthCommodityBaseVOList);
        growthCommodityBaseVOS.setLength(storeCommodityPagePair.getKey());
        return growthCommodityBaseVOS;
    }

    private static GrowthCommodityBaseVO getGrowthCommodityBaseVO(GradeCommodityBasePageQO qo, ResGradeCommodityBase in) {
        GrowthCommodityBaseVO growthCommodityBaseVO = new GrowthCommodityBaseVO();
        growthCommodityBaseVO
                .setBasePrice(in.getPrice())
                .setStoreState(CommodityStatusEnum.getCodeByName(in.getState()))
                .setCommodityId(Optional.ofNullable(in.getId())
                                        .map(String::valueOf)
                                        .orElse(in.getCommodityId()))
                .setCommodityCode(in.getCode())
                .setCommodityName(in.getName())
                .setBusinessType(qo.getBusinessType())
                //渠道
                .setChannel(qo.getChannel())
                .setPhoto(in.getPhoto())
                .setSystem(SystemEnum.MALL.name())
                .setCommodityComboType(CommodityComboTypeEnum.getNameByCode(in.getType()));
        return growthCommodityBaseVO;
    }

    /**
     * 后台不过滤下架状态
     *
     * @param isBack
     * @param in
     * @return
     */
    private static boolean isCommodityDown(boolean isBack, ResGradeCommodityBase in) {
        return isBack || !in.getState().equals(CommodityStatusEnum.COMMODITY_DOWN.getDes());
    }

    /**
     * 成长值任务条件获取商品数据
     *
     * @param qo qo
     */
    public GrowthCommodityPageVO queryGrowthCommodityPage(GrowthCommodityBasePageQO qo) {
        GrowthCommodityPageVO growthCommodityBaseVOS = new GrowthCommodityPageVO();
        List<GrowthCommodityBaseVO> growthCommodityBaseVOList;

        QueryCommodityBasePage commodityBasePage = getQueryCommodityBasePage(qo);
        List<StoreBaseInfo> storeBaseInfoList = storeBaseService.queryStore(null, null);
        List<Integer> idList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(storeBaseInfoList)) {
            idList = storeBaseInfoList.stream().map(in -> Integer.parseInt(in.getId())).collect(Collectors.toList());
            commodityBasePage.setStore_id(idList);
        } else {
            commodityBasePage.setStore_id(idList);
        }

        log.info("条件获取商品数据 入参>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", commodityBasePage);
        Pair<Integer, List<ResCommodityBase>> pageCommodityPair = externalSupport.itemServer(ThreadLocalCache.getSystem()).pageStrategyCommodity(commodityBasePage);
        List<ResCommodityBase> resCommodityBases = pageCommodityPair.getValue();
        if (CollUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }

        // 组合商品返回的 commodityComboType为：false
        growthCommodityBaseVOList = resCommodityBases.stream().filter(
                x -> StringUtils.isNotEmpty(x.getCommodityComboType()) &&
                        !x.getCommodityComboType().equalsIgnoreCase(BooleanEnum.FALSE.getDes()))
                .map(ShopBaseService::getGrowthCommodityBaseVO)
                .collect(Collectors.toList());
        growthCommodityBaseVOS.setGrowthCommodityBaseVOS(growthCommodityBaseVOList);
        growthCommodityBaseVOS.setLength(pageCommodityPair.getKey());
        return growthCommodityBaseVOS;
    }

    private static QueryCommodityBasePage getQueryCommodityBasePage(GrowthCommodityBasePageQO qo) {
        QueryCommodityBasePage commodityBasePage = new QueryCommodityBasePage();
        commodityBasePage.setCommodity(qo.getCommodity());
        commodityBasePage.setCommodity_combo_type(qo.getCommodityComboType());
        commodityBasePage.setPage(qo.getCurrentPage());
        commodityBasePage.setPageSize(qo.getPageSize());
        commodityBasePage.setStrategy_id(qo.getStrategyId());
        commodityBasePage.setStrategy_name(qo.getStrategyName());
        return commodityBasePage;
    }

    private static GrowthCommodityBaseVO getGrowthCommodityBaseVO(ResCommodityBase in) {
        GrowthCommodityBaseVO growthCommodityBaseVO = new GrowthCommodityBaseVO();
        growthCommodityBaseVO
                .setBasePrice(in.getBasePrice())
                .setCategoryId(String.valueOf(in.getCategoryId()))
                .setCategoryName(in.getCategoryName())
                .setCommodityCode(in.getCommodityCode())
                .setCommodityId(String.valueOf(in.getId()))
                .setStoreState(Integer.parseInt(in.getStoreState()))
                .setCommodityComboType(Integer.parseInt(in.getCommodityComboType()))
                .setCommodityName(in.getName())
                .setStrategyCode(String.valueOf(in.getStrategyCode()))
                .setStrategyId(String.valueOf(in.getStrategyId()))
                .setStrategyName(in.getStrategyName());
        return growthCommodityBaseVO;
    }


    /**
     * 主体获取策略单
     */
    public List<ResArrayStrategyBase> queryStrategy(StoreStrategyQO storeStrategyQO) {

        QueryArrayShopBase queryStoreBasePage = new QueryArrayShopBase();
        queryStoreBasePage.setStore_id(Collections.singletonList(storeStrategyQO.getStoreId()));
        queryStoreBasePage.setChannel(storeStrategyQO.getChanel());
        return externalSupport.itemServer(ThreadLocalCache.getSystem()).getOperatingStrategy(queryStoreBasePage);

    }

    /**
     * 策略单获取分类
     *
     * @param strategyId strategyId
     * @return List<ResArrayStrategyBase>
     */
    public List<ResCategoryBase> queryCategoryByStrategy(Integer strategyId) {
        QueryArrayShopBase queryStoreBasePage = new QueryArrayShopBase();
        queryStoreBasePage.setStrategy_id(strategyId);
        return externalSupport.itemServer(ThreadLocalCache.getSystem()).queryCategoryByStrategy(queryStoreBasePage);
    }

    /**
     * 批量商品id获取商品
     *
     * @return List<ResArrayStrategyBase>
     */
    public List<GrowthCommodityBaseVO> queryCommodityList(List<Integer> detailsList) {
        List<GrowthCommodityBaseVO> growthCommodityBaseVOS = Lists.newArrayList();
        QueryArrayShopBase queryStoreBasePage = new QueryArrayShopBase();
        queryStoreBasePage.setDetails_list(detailsList);

        List<ResCommodityBase> resCommodityBases = externalSupport.itemServer(ThreadLocalCache.getSystem()).listCommodityByDetail(queryStoreBasePage);
        if (CollUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }
        growthCommodityBaseVOS = getGrowthCommodityBaseVOS(resCommodityBases);
        return growthCommodityBaseVOS;
    }

    /**
     * 策略单、分类id获取商品
     *
     * @return List<ResArrayStrategyBase>
     */
    public List<GrowthCommodityBaseVO> queryCommodityCategoryIdList(Integer strategyId, List<Integer> categoryId) {
        List<GrowthCommodityBaseVO> growthCommodityBaseVOS = Lists.newArrayList();
        List<ResCommodityBase> resCommodityBases = getResCommodityBaseList(strategyId, categoryId);
        if (CollUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }
        growthCommodityBaseVOS = getGrowthCommodityBaseVOS(resCommodityBases);
        return growthCommodityBaseVOS;
    }

    public List<ResCommodityBase> getResCommodityBaseList(Integer strategyId, List<Integer> categoryId) {
        QueryArrayShopBase queryStoreBasePage = new QueryArrayShopBase();
        queryStoreBasePage.setStrategy_id(strategyId);
        queryStoreBasePage.setCategory_id(categoryId);
        return externalSupport.itemServer(ThreadLocalCache.getSystem()).listCommodityBase(queryStoreBasePage);
    }

    /**
     * 重组数据
     *
     * @param resCommodityBases resCommodityBases
     * @return List<GrowthCommodityBaseVO>
     */
    private List<GrowthCommodityBaseVO> getGrowthCommodityBaseVOS(List<ResCommodityBase> resCommodityBases) {
        List<GrowthCommodityBaseVO> growthCommodityBaseVOS = Lists.newArrayList();
        if (CollUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }
        growthCommodityBaseVOS = resCommodityBases.stream()
                .map(ShopBaseService::getCommodityBaseVO)
                .collect(Collectors.toList());
        return growthCommodityBaseVOS;
    }

    private static GrowthCommodityBaseVO getCommodityBaseVO(ResCommodityBase in) {
        GrowthCommodityBaseVO growthCommodityBaseVO = new GrowthCommodityBaseVO();
        growthCommodityBaseVO
                .setBasePrice(in.getBasePrice())
                .setCategoryId(String.valueOf(in.getCategoryId()))
                .setCategoryName(in.getCategoryName())
                .setCommodityCode(in.getCommodityCode())
                .setCommodityId(String.valueOf(in.getId()))
                .setStoreState(Integer.parseInt(in.getStoreState()))
                .setCommodityComboType(Integer.parseInt(in.getCommodityComboType()))
                .setStrategyCode(String.valueOf(in.getStrategyCode()))
                .setCommodityName(in.getName())
                .setStrategyId(String.valueOf(in.getStrategyId()))
                .setCommodityImg(in.getCommodityImg())
                .setStrategyName(in.getStrategyName());
        return growthCommodityBaseVO;
    }

    /**
     * 查询商品分类
     */
    public List<ProductCrmCategoryVO> queryCommodityCategory() {
        QueryProductCategoryCrmQO categoryQO = new QueryProductCategoryCrmQO();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (!com.holderzone.framework.util.StringUtils.isEmpty(enterpriseGuid)) {
            categoryQO.setEnterpriseGuid(enterpriseGuid);
        }
        categoryQO.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
        List<ProductCrmCategoryVO> dataList = externalSupport.itemServer(ThreadLocalCache.getSystem()).getCommodityCategory(categoryQO);
        log.info("[查询商品分类]categoryQO={},Result={}", JacksonUtils.writeValueAsString(categoryQO),
                JacksonUtils.writeValueAsString(dataList));
        return dataList;
    }

    public Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO) {
        log.info("[分页获取商品基础数据]入参>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JacksonUtils.writeValueAsString(pageQO));
        int systemCode = ThreadLocalCache.getSystem();
        // 改造了下 如果传了系统 就查对应的
        if (StringUtils.isNotEmpty(pageQO.getSystem())) {
            SystemEnum systemEnum = SystemEnum.valueOf(pageQO.getSystem());
            systemCode = systemEnum.getCode();
        }
        return externalSupport.itemServer(systemCode).pageCommodityBase(pageQO);
    }

    public List<CommodityBaseTypeVO> getStoreGoodsComboType() {
        return externalSupport.itemServer(ThreadLocalCache.getSystem()).getStoreGoodsComboType();
    }
}
