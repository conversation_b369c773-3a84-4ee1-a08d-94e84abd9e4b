package com.holderzone.member.base.timer;


import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;


@Service
@Scope("prototype")
@Data
@Slf4j
public class LabelRefreshForThread implements Runnable {

    @Resource
    HsaLabelSettingService settingService;

    @Resource
    HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;
    private volatile int pageSize = 10;
    private int pageNum = 2000;
    private HsaLabelSetting label;
    private HeaderUserInfo headerUserInfo;
    private CountDownLatch countDownLatch;


    @Override
    public void run() {
        while (pageSize > -1) {
            int currentPage = 0;
            synchronized (this) {
                if (pageSize > -1) {
                    currentPage = pageSize--;
                }
            }
            Set<String> memberInfoGuids = hsaOperationMemberInfoMapper.listByPage(currentPage * pageNum, pageNum,
                    label.getOperSubjectGuid()).stream().collect(Collectors.toSet());
            settingService.refreshLabelHandle(memberInfoGuids, label, headerUserInfo, LabelTriggerTypeEnum.ALL.getCode());
            countDownLatch.countDown();
        }
    }
}
