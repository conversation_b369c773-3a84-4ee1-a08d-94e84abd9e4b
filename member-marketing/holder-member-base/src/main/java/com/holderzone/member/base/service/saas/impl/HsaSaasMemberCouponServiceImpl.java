package com.holderzone.member.base.service.saas.impl;


import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.base.service.saas.HsaSaasMemberCouponService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.saas.RequestMemberInfoVolumeQuery;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.coupon.AppletsMemberCouponVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.saas.ResponseVolumeInfo;
import com.holderzone.member.common.vo.saas.ResponseVolumeStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 老门店 会员优惠券接口实现类
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HsaSaasMemberCouponServiceImpl implements HsaSaasMemberCouponService {

    private final SaasStoreFeign sassStoreFeign;

    private final MemberMallToolFeign memberMallToolFeign;

    @Override
    public Page<AppletsMemberCouponVO> queryListByMember(RequestMemberInfoVolumeQuery query) {
        supplyRequestMemberInfoVolumeQuery(query);
        FeignModel<Page<ResponseVolumeInfo>> feignModel = sassStoreFeign.queryCouponListByMember(query);
        Page<ResponseVolumeInfo> pageData = feignModel.getData();
        List<ResponseVolumeInfo> data = pageData.getData();
        return new Page<>(pageData.getCurrentPage(), pageData.getPageSize(), pageData.getTotalCount(), transform(data));
    }

    @Override
    public AppletsMemberCouponVO queryUseConditions(String volumeInfoGuid) {
        FeignModel<ResponseVolumeInfo> feignModel = sassStoreFeign.queryCouponUseConditions(volumeInfoGuid);
        ResponseVolumeInfo responseVolumeInfo = feignModel.getData();
        return transform(responseVolumeInfo);
    }

    @Override
    public Long queryUnUserCountByMember(RequestMemberInfoVolumeQuery query) {
        supplyRequestMemberInfoVolumeQuery(query);
        FeignModel<Long> feignModel = sassStoreFeign.queryCouponUnUseCountByMember(query);
        return feignModel.getData();
    }


    /**
     * 封装请求参数
     * 追加 enterpriseGuid operSubjectGuid
     */
    private void supplyRequestMemberInfoVolumeQuery(RequestMemberInfoVolumeQuery query) {
        OperSubjectCloudVO operSubjectCloudVO = memberMallToolFeign.queryByOperSubiectGuid(ThreadLocalCache.getOperSubjectGuid());
        if (Objects.isNull(operSubjectCloudVO)) {
            log.error("未关联餐饮云, operSubjectGuid:{}", ThreadLocalCache.getOperSubjectGuid());
            throw new MemberBaseException("未关联餐饮云");
        }
        log.info("查询关联餐饮云：{}", JacksonUtils.writeValueAsString(operSubjectCloudVO));
        query.setEnterpriseGuid(operSubjectCloudVO.getMultiEnterpriseGuid());
        query.setOperSubjectGuid(operSubjectCloudVO.getMultiOperSubiectGuid());
    }

    private AppletsMemberCouponVO transform(ResponseVolumeInfo responseVolumeInfo) {
        if (Objects.isNull(responseVolumeInfo)) {
            return null;
        }
        AppletsMemberCouponVO appletsMemberCouponVO = new AppletsMemberCouponVO();
        appletsMemberCouponVO.setGuid(responseVolumeInfo.getGuid());
        appletsMemberCouponVO.setCouponGuid(responseVolumeInfo.getVolumeInfoGuid());
        appletsMemberCouponVO.setUseType(responseVolumeInfo.getVolumeType());
        appletsMemberCouponVO.setCouponName(responseVolumeInfo.getVolumeName());
        appletsMemberCouponVO.setAmountOfCoupon(responseVolumeInfo.getDenomination());
        appletsMemberCouponVO.setDemandPrice(responseVolumeInfo.getUseThresholdFull());
        appletsMemberCouponVO.setStartDate(responseVolumeInfo.getStartValidityPeriod());
        appletsMemberCouponVO.setEndDate(responseVolumeInfo.getEndValidityPeriod());
        appletsMemberCouponVO.setInsertDate(responseVolumeInfo.getPickUpTime());
        appletsMemberCouponVO.setDetails(responseVolumeInfo.getDescription());
        appletsMemberCouponVO.setCouponState(responseVolumeInfo.getVolumeState());
        appletsMemberCouponVO.setAllType(responseVolumeInfo.getAllType());
        appletsMemberCouponVO.setStoreList(Lists.newArrayList());
        // 适用门店
        if (CollectionUtils.isNotEmpty(responseVolumeInfo.getResponseVolumeStoreList())) {
            appletsMemberCouponVO.setStoreList(responseVolumeInfo.getResponseVolumeStoreList()
                    .stream().map(ResponseVolumeStore::getStoreName).distinct().collect(Collectors.toList()));
        }
        return appletsMemberCouponVO;
    }

    private List<AppletsMemberCouponVO> transform(List<ResponseVolumeInfo> responseVolumeInfoList) {
        if (CollectionUtils.isEmpty(responseVolumeInfoList)) {
            return Collections.emptyList();
        }
        return responseVolumeInfoList.stream().map(this::transform).collect(Collectors.toList());
    }
}
