package com.holderzone.member.base.entity.grade;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员和等级关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HsaMemberGradeRelation对象", description = "会员和等级关联表")
public class HsaMemberGradeRelation implements Serializable {

    private static final long serialVersionUID = 5492214337690891196L;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "付费时间")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "会员GUID")
    private String memberInfoGuid;

    @ApiModelProperty(value = "角色类型")
    private String roleType;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

    /**
     * "是否启用：0未启用 1启用中
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Boolean isEnable;

    /**
     * 会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，53 小程序)
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer sourceType;

    /**
     * 后台等级默认序号
     */
    private Integer temporaryVipGrade;
}
