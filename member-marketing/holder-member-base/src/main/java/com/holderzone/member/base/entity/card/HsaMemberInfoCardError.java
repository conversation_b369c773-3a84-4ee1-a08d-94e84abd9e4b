package com.holderzone.member.base.entity.card;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description 会员关联会员卡实体充值异常
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hsa_member_info_card_error")
public class HsaMemberInfoCardError extends HsaMemberInfoCard {

}
