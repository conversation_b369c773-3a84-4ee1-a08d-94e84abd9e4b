package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaMemberInfoWeChat;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.coupon.MemberCouponOpenIdVO;
import com.holderzone.member.common.vo.coupon.MemberOpenIdVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 运营主体会员微信表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface HsaMemberInfoWeChatMapper extends HolderBaseMapper<HsaMemberInfoWeChat> {

    List<HsaMemberInfoWeChat> getMemberInfoOpenIds(@Param("operSubjectGuid") String operSubjectGuid, @Param("phones") List<String> phones);

    String getMemberInfoOpenId(@Param("operSubjectGuid") String operSubjectGuid, @Param("memberInfoGuid") String memberInfoGuid);

    List<MemberOpenIdVO> getMemberOpenIdList(@Param("memberInfoGuid") List<String> memberInfoGuid);

    List<MemberCouponOpenIdVO> getMemberCouponOpenId(LocalDateTime now);
}
