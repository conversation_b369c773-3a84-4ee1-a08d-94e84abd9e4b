package com.holderzone.member.base.entity.member;


import com.aimilin.annotation.ExlColumn;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 会员导入记录错误表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberImportRecordError implements Serializable {

    private static final long serialVersionUID = 1L;

    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 导入记录表guid
     */
    private String memberImportRecordGuid;

    /**
     * 错误原因
     */
    private String errorCauseJson;

    /**
     * 会员卡
     */
    private String memberCardJson;

    /**
     * 会员标签
     */
    private String memberLabelJson;

    /**
     * 手机国家编码
     */
    private String phoneCountryCode;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 顾客姓名
     */
    private String userName;

    /**
     * 性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 证件类型，0身份证，1军人证，2护照，3港澳台通行证，4其它
     */
    private String certificateType;

    /**
     * 证件号码
     */
    private String certificateNum;

    /**
     * 所属单位
     */
    private String workName;

    /**
     * 所属部门
     */
    private String departmentName;

    /**
     * 省名字
     */
    private String provinceName;

    /**
     * 市名字
     */
    private String cityName;

    /**
     * 区名字
     */
    private String areaName;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 会员积分
     */
    private String memberIntegral;

    /**
     * 会员成长值
     */
    private String memberGrowthValue;

    /**
     * 实体卡、电子卡补贴总金额
     */
    private String subsidyAmount;

    /**
     * 会员账户余额
     */
    private String memberAccountMoney;

    /**
     * 会员账户赠送余额
     */
    private String memberGiftAccountMoney;

    /**
     * 编号
     */
    private String memberNum;
}
