package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.base.mapper.card.HsaStoreCardRuleMapper;
import com.holderzone.member.base.service.card.HsaStoreCardRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员卡门店适用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Slf4j
@Service
public class HsaStoreCardRuleServiceImpl extends HolderBaseServiceImpl<HsaStoreCardRuleMapper, HsaStoreCardRule> implements HsaStoreCardRuleService {

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public Integer deleteByCardGuid(String cardGuid) {
        return hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, cardGuid));
    }

    /**
     * 实体卡退卡 复制出一份新门店范围
     *
     * @param cardGuid
     * @param newMemberCardGuid
     * @param memberCardGuid
     */
    @Override
    public void saveStoreCardRule(String cardGuid, String newMemberCardGuid, String memberCardGuid) {
        List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Lists.newArrayList();
        // 去保存适用门店信息
        List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberCardGuid).isNull(HsaStoreCardRule::getParentGuid));
        //档口
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = getStoreCardRuleMap(hsaStoreCardRuleList);
        addStoreCardRule(cardGuid, newMemberCardGuid, hsaStoreCardRuleList, hsaStoreCardRuleMap, hsaStoreCardRuleArrayList);

        if (CollUtil.isNotEmpty(hsaStoreCardRuleArrayList)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleArrayList);
        }
    }

    private void addStoreCardRule(String cardGuid, String newMemberCardGuid, List<HsaStoreCardRule> hsaStoreCardRuleList, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            hsaStoreCardRuleList.forEach(in -> {
                HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
                BeanUtils.copyProperties(in, hsaStoreCardRule);
                hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                        .setMemberInfoCardGuid(newMemberCardGuid)
                        .setAddress(in.getAddress())
                        .setTime(in.getTime())
                        .setAddressPoint(in.getAddressPoint())
                        .setCardGuid(cardGuid)
                        .setGmtCreate(LocalDateTime.now())
                        .setGmtModified(LocalDateTime.now());
                addStallCardRule(cardGuid, newMemberCardGuid, in, hsaStoreCardRuleMap, hsaStoreCardRule, hsaStoreCardRuleArrayList);
                hsaStoreCardRuleArrayList.add(hsaStoreCardRule);
            });
        }
    }

    private void addStallCardRule(String cardGuid, String newMemberCardGuid, HsaStoreCardRule in, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, HsaStoreCardRule hsaStoreCardRule, List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleMap) && hsaStoreCardRuleMap.containsKey(in.getGuid())) {
            List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap.get(in.getGuid());
            storeCardRule.forEach(on -> {
                HsaStoreCardRule rule = new HsaStoreCardRule();
                BeanUtils.copyProperties(on, rule);
                rule.setParentGuid(hsaStoreCardRule.getGuid())
                        .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                        .setMemberInfoCardGuid(newMemberCardGuid)
                        .setAddress(on.getAddress())
                        .setTime(on.getTime())
                        .setAddressPoint(on.getAddressPoint())
                        .setCardGuid(cardGuid);
                hsaStoreCardRuleArrayList.add(rule);
            });
        }
    }

    private Map<String, List<HsaStoreCardRule>> getStoreCardRuleMap(List<HsaStoreCardRule> hsaStoreCardRuleList) {
        return hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList()))
                .isNotNull(HsaStoreCardRule::getParentGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
    }

    /**
     * 门店合并处理
     *
     * @param hsaMemberInfoCard
     * @param memberPhysicalCard
     */
    @Override
    public void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberInfoCard memberPhysicalCard) {
        //电子卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList = (hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid)));
        List<String> parentGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());

        //实体卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList1 = (hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberPhysicalCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid)));
        List<String> parentGuidList1 = hsaStoreCardRuleList1.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());
        parentGuidList1.addAll(parentGuidList);
        //档口级
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, parentGuidList1))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));

        hsaStoreCardRuleList.addAll(hsaStoreCardRuleList1);

        //delete合并之前的数据
        List<String> memberInfoGuidList = new ArrayList<>();
        memberInfoGuidList.add(hsaMemberInfoCard.getGuid());
        memberInfoGuidList.add(memberPhysicalCard.getGuid());
        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getMemberInfoCardGuid, memberInfoGuidList));

        Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            fullStoreCardRuleSet(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleMap1, hsaStoreCardRuleSet, map);
        }
        if (CollUtil.isNotEmpty(hsaStoreCardRuleSet)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleSet);
        }
    }

    private void fullStoreCardRuleSet(HsaMemberInfoCard hsaMemberInfoCard,
                                      List<HsaStoreCardRule> hsaStoreCardRuleList, 
                                      Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1,
                                      Set<HsaStoreCardRule> hsaStoreCardRuleSet,
                                      Map<String, String> map) {
        for (HsaStoreCardRule rule : hsaStoreCardRuleList) {
            List<String> checkStoreGuid = hsaStoreCardRuleSet.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
            if (!checkStoreGuid.contains(rule.getStoreGuid())) {
                seStore(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule);
            }
            if (CollUtil.isNotEmpty(hsaStoreCardRuleMap1) && hsaStoreCardRuleMap1.containsKey(rule.getGuid())) {
                List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap1.get(rule.getGuid());
                storeCardRule.forEach(in -> {
                    if (!checkStoreGuid.contains(in.getStoreGuid())) {
                        setStall(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule, storeCardRule, in);
                    }
                });
            }
        }
    }

    /**
     * 会员卡合并已领卡门店范围
     *
     * @param hsaMemberInfoCard
     * @param cardGuid
     */
    @Override
    public void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, String cardGuid) {
        //电子卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid));

        //会员卡门店范围
        List<HsaStoreCardRule> hsaStoreCardRuleList1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, cardGuid)
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                .isNull(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isEmpty(hsaStoreCardRuleList1)) {
            return;
        }
        handlerMergeStore(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleList1);
    }

    @Override
    public void mergeMemberCardStore(HsaMemberInfoCard currentMemberCard, HsaMemberInfoCard delMemberInfoCard) {
        if (Objects.isNull(currentMemberCard) || Objects.isNull(delMemberInfoCard)) {
            return;
        }
        //新卡门店
        List<HsaStoreCardRule> storeCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, delMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isEmpty(storeCardRuleList)) {
            return;
        }
        //旧卡门店
        List<HsaStoreCardRule> currentStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, currentMemberCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid));
        if(!CollUtil.isEmpty(currentStoreCardRuleList)){
            //去除交集
            final List<String> storeGuidList = currentStoreCardRuleList.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
            final List<HsaStoreCardRule> delStoreCardRule = storeCardRuleList.stream().filter(o -> storeGuidList.contains(o.getStoreGuid())).collect(Collectors.toList());
            if(!CollUtil.isEmpty(delStoreCardRule)) {
                removeByGuids(delStoreCardRule.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList()));
                storeCardRuleList.removeAll(delStoreCardRule);
            }
        }
        if(CollUtil.isEmpty(storeCardRuleList)){
            return;
        }
        //更新剩下的会员卡
        storeCardRuleList.forEach(cr -> cr.setMemberInfoCardGuid(currentMemberCard.getGuid()));
        hsaStoreCardRuleMapper.updateMemberInfoCardGuid(storeCardRuleList);
    }

    private void handlerMergeStore(HsaMemberInfoCard hsaMemberInfoCard,
                                   List<HsaStoreCardRule> hsaStoreCardRuleList,
                                   List<HsaStoreCardRule> delHsaStoreCardRuleList) {
        List<String> parentGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());
        List<String> delParentGuidList = delHsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());
        delParentGuidList.addAll(parentGuidList);
        //档口级
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, delParentGuidList))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));

        hsaStoreCardRuleList.addAll(delHsaStoreCardRuleList);

        //delete合并之前的数据
        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid()));

        Set<HsaStoreCardRule> hsaStoreCardRuleSet = getHsaStoreCardRules(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleMap1);
        if (CollUtil.isNotEmpty(hsaStoreCardRuleSet)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleSet);
        }
    }

    private Set<HsaStoreCardRule> getHsaStoreCardRules(HsaMemberInfoCard hsaMemberInfoCard,
                                                       List<HsaStoreCardRule> hsaStoreCardRuleList,
                                                       Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1) {
        Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            for (HsaStoreCardRule rule : hsaStoreCardRuleList) {
                List<String> checkStoreGuid = hsaStoreCardRuleSet.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
                if (!checkStoreGuid.contains(rule.getStoreGuid())) {
                    seStore(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule);
                }
                if (CollUtil.isNotEmpty(hsaStoreCardRuleMap1) && hsaStoreCardRuleMap1.containsKey(rule.getGuid())) {
                    List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap1.get(rule.getGuid());
                    storeCardRule.forEach(in -> {
                        if (!checkStoreGuid.contains(in.getStoreGuid())) {
                            setStall(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule, storeCardRule, in);
                        }
                    });
                }
            }
        }
        return hsaStoreCardRuleSet;
    }

    private void setStall(HsaMemberInfoCard hsaMemberInfoCard, Set<HsaStoreCardRule> hsaStoreCardRuleSet, Map<String, String> map, HsaStoreCardRule rule, List<HsaStoreCardRule> storeCardRule, HsaStoreCardRule in) {
        HsaStoreCardRule storeCardRule1 = new HsaStoreCardRule();
        BeanUtils.copyProperties(storeCardRule, storeCardRule1);
        storeCardRule1.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()));
        storeCardRule1.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        storeCardRule1.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        storeCardRule1.setParentGuid(map.get(rule.getStoreGuid()));
        storeCardRule1.setStoreGuid(in.getStoreGuid());
        storeCardRule1.setStoreNumber(in.getStoreNumber());
        storeCardRule1.setAddressPoint(in.getAddressPoint());
        storeCardRule1.setStoreName(in.getStoreName());
        storeCardRule1.setAddress(in.getAddress());
        storeCardRule1.setTime(in.getTime());
        hsaStoreCardRuleSet.add(storeCardRule1);
    }

    private void seStore(HsaMemberInfoCard hsaMemberInfoCard, Set<HsaStoreCardRule> hsaStoreCardRuleSet, Map<String, String> map, HsaStoreCardRule rule) {
        HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        BeanUtils.copyProperties(rule, hsaStoreCardRule);
        hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()));
        hsaStoreCardRule.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        hsaStoreCardRule.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaStoreCardRule.setStoreGuid(rule.getStoreGuid());
        hsaStoreCardRule.setStoreNumber(rule.getStoreNumber());
        hsaStoreCardRule.setStoreName(rule.getStoreName());
        hsaStoreCardRule.setAddressPoint(rule.getAddressPoint());
        hsaStoreCardRule.setAddress(rule.getAddress());
        hsaStoreCardRule.setTime(rule.getTime());
        hsaStoreCardRuleSet.add(hsaStoreCardRule);
        map.put(hsaStoreCardRule.getStoreGuid(), hsaStoreCardRule.getGuid());
    }
}
