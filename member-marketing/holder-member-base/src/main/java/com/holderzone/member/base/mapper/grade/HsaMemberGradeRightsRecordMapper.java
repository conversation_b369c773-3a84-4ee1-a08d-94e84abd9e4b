package com.holderzone.member.base.mapper.grade;


import com.holderzone.member.base.entity.grade.HsaMemberGradeRightsRecord;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.equities.MemberEquitiesRecordQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员等级权益记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface HsaMemberGradeRightsRecordMapper extends HolderBaseMapper<HsaMemberGradeRightsRecord> {

    /**
     * 获取等级权益名称
     * @param request 会员权益请求参数
     * @return 权益名称集合
     */
    List<String> getGradeEquitiesName(@Param("request") MemberEquitiesRecordQO request);

    /**
     * 获取当前等级权益名称
     * @param operSubjectGuid 运营主体guid
     * @param memberGradeGuid 会员等级guid
     * @return 权益名称集合
     */
    List<String> getCurrentEquitiesName(@Param("operSubjectGuid") String operSubjectGuid,@Param("memberGradeGuid") String memberGradeGuid);
}
