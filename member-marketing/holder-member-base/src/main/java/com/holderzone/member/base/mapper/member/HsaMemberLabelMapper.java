package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaMemberLabel;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.label.LabelRelationTypeDTO;
import com.holderzone.member.common.dto.member.MemberRelationLabelDTO;
import com.holderzone.member.common.qo.member.RelationLabelListQO;
import com.holderzone.member.common.qo.member.UnRelationLabelListQO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import com.holderzone.member.common.vo.member.RelationLabelListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaMemberLabelMapper
 * @Author: pantao
 * @Description:
 * @Date: 2021/8/24 20:15
 * @Version: 1.0
 */
public interface HsaMemberLabelMapper extends HolderBaseMapper<HsaMemberLabel> {

    void batchSave(@Param("hsaMemberLabels") List<HsaMemberLabel> hsaMemberLabels);

    /**
     * 分页查询会员标签
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<RelationLabelListVO> listRelationLabel(@Param("request") RelationLabelListQO request);

    /**
     * 查询标签数量
     *
     * @param request 会员标签列表请求vo
     * @return 查询结果
     */
    Integer countRelationLabel(@Param("request") RelationLabelListQO request);

    /**
     * 查询所有会员标签
     *
     * @param request 会员标签列表请求vo
     * @return 查询结果
     */
    List<RelationLabelListVO> findAllRelationLabel(@Param("request") RelationLabelListQO request);

    /**
     * 根据会员guid查询会员对应所有标签
     *
     * @param memberGuid 会员guid集合
     * @return 查询结果
     */
    List<MemberRelationLabelDTO> findMemberAllLabel(@Param("memberGuid") List<String> memberGuid);

    /**
     * 查询未绑定标签会员信息
     *
     * @param request 未绑定标签会员请求qo
     * @return 查询结果
     */
    List<RelationLabelListVO> findUnRelationLabelMember(@Param("request") UnRelationLabelListQO request);

    /**
     * 查询未绑定标签会员数量
     *
     * @param request 未绑定标签会员请求qo
     * @return 查询结果
     */
    Integer countUnRelationLabelMember(@Param("request") UnRelationLabelListQO request);


    List<String> findPhoneNumByLabelGuid(@Param("labelGuid") String labelGuid);

    /**
     * 查询未绑定标签会员信息
     *
     * @return 查询结果
     */
    List<MemberLabelVO> listMemberLabel(@Param("memberInfoGuid") String memberInfoGuid);


    List<LabelRelationTypeDTO> queryConnectionType(@Param("labelSettingGuid") String labelSettingGuid,
                                                   @Param("operSubjectGuid") String operSubjectGuid,
                                                   @Param("memberInfoGuids") Set<String> memberInfoGuids);

    void updateCorrelationStatus(@Param("guids") List<String> guids, @Param("status") Integer status);

    void deleteCorrelation(@Param("guids") List<String> guids);
}
