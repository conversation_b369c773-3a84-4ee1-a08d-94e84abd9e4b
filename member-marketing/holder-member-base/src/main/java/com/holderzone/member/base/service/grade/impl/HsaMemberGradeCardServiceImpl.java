package com.holderzone.member.base.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.base.entity.grade.HsaMemberGradeCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeCardMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.HsaMemberGradeCardService;
import com.holderzone.member.base.service.grade.HsaMemberGradePurchaseHistoryService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.grade.GradeCardOpenTypeEnum;
import com.holderzone.member.common.enums.grade.GradeCardStatusEnum;
import com.holderzone.member.common.enums.grade.GradeHisChangeTypeEnum;
import com.holderzone.member.common.enums.member.GradeTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.qo.grade.MemberGradeCardQO;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradeCardVO;
import com.holderzone.member.common.exception.MemberBaseException;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员等级卡Service实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class HsaMemberGradeCardServiceImpl extends ServiceImpl<HsaMemberGradeCardMapper, HsaMemberGradeCard> implements HsaMemberGradeCardService {

    @Resource
    private HsaMemberGradeCardMapper hsaMemberGradeCardMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private HsaMemberGradePurchaseHistoryService gradePurchaseHistoryService;


    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Override
    public List<MemberGradeCardVO> list(MemberGradeCardQO qo) {
        LambdaQueryWrapper<HsaMemberGradeCard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(qo.getMemberInfoGuid()), HsaMemberGradeCard::getMemberInfoGuid, qo.getMemberInfoGuid())
                .eq(StringUtils.hasText(qo.getOperSubjectGuid()), HsaMemberGradeCard::getOperSubjectGuid, qo.getOperSubjectGuid())
                .eq(StringUtils.hasText(qo.getGradeGuid()), HsaMemberGradeCard::getGradeGuid, qo.getGradeGuid())
                .eq(qo.getVipGrade() != null, HsaMemberGradeCard::getVipGrade, qo.getVipGrade())
                .eq(qo.getGradeType() != null, HsaMemberGradeCard::getGradeType, qo.getGradeType())
                .eq(qo.getMemberCardStatus() != null, HsaMemberGradeCard::getMemberCardStatus, qo.getMemberCardStatus())
                .eq(qo.getOpenType() != null, HsaMemberGradeCard::getOpenType, qo.getOpenType())
                .in(CollUtil.isNotEmpty(qo.getMemberInfoGuids()), HsaMemberGradeCard::getMemberInfoGuid, qo.getMemberInfoGuids())
                .eq(HsaMemberGradeCard::getIsDelete, 0L);

        List<HsaMemberGradeCard> list = this.list(wrapper);
        return convertToVOList(list);
    }

    @Override
    public MemberGradeCardVO getByGuid(String guid) {
        LambdaQueryWrapper<HsaMemberGradeCard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HsaMemberGradeCard::getGuid, guid);
        HsaMemberGradeCard entity = this.getOne(wrapper);
        return convertToVO(entity);
    }

    @Override
    public List<MemberGradeCardVO> listByMember(String memberInfoGuid) {
        LambdaQueryWrapper<HsaMemberGradeCard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HsaMemberGradeCard::getMemberInfoGuid, memberInfoGuid);
        List<HsaMemberGradeCard> list = this.list(wrapper);
        return convertToVOList(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(MemberGradeCardQO qo) {
        // 获取当前最高等级卡
        HsaMemberGradeCard currentGradeCard =
                hsaMemberGradeCardMapper.queryMaxGradeByMemberGuidAndGradeType(qo.getMemberInfoGuid(), qo.getGradeType() == null ? GradeTypeEnum.FREE.getCode() : qo.getGradeType());

        HsaMemberGradeCard entity = new HsaMemberGradeCard();
        BeanUtils.copyProperties(qo, entity);
        entity.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeCardService.class.getSimpleName()));
        entity.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        entity.setIsDelete(0L);
        this.save(entity);

        // 这里加个判断是因为私域商城没有传这个值，只有openType,做个转换
        if (qo.getOpenType() != null
                    && qo.getOpenType().equals(GradeCardOpenTypeEnum.PAID_BUY.getValue())) {
            qo.setChangeType(GradeHisChangeTypeEnum.PAID_MEMBER_OPEN.getValue());
        }
        this.checkAndUpdateOperationMemberInfo(qo, currentGradeCard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCardAndSavePurchaseHis (MemberGradeCardQO qo) {
        this.add(qo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MemberGradeCardQO qo) {
        LambdaQueryWrapper<HsaMemberGradeCard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HsaMemberGradeCard::getGuid, qo.getGuid());
        HsaMemberGradeCard entity = this.getOne(wrapper);
        entity.setMemberCardValidTime(qo.getMemberCardValidTime());
        // 获取当前最高等级卡
        HsaMemberGradeCard currentGradeCard =
                hsaMemberGradeCardMapper.queryMaxGradeByMemberGuidAndGradeType(qo.getMemberInfoGuid(), qo.getGradeType() == null ? GradeTypeEnum.FREE.getCode() : qo.getGradeType());

        // 如果是过期
        if (GradeCardStatusEnum.EXPIRE.getCode().equals(qo.getMemberCardStatus())
                && StrUtil.isNotEmpty(qo.getMemberInfoGuid())) {
            // 生成过期记录
            gradePurchaseHistoryService.add(new MemberGradePurchaseHistoryQO()
                                                    .setMemberInfoGuid(qo.getMemberInfoGuid())
                                                    .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                                                    .setType(GradeHisChangeTypeEnum.PAID_MEMBER_OVERDUE.getValue())
                                                    .setSource(SourceTypeEnum.ADD_BACKGROUND.getCode())
                                                    .setRecordDesc("付费等级过期"));
        }
        this.updateById(entity);
        this.checkAndUpdateOperationMemberInfo(qo, currentGradeCard);
    }

    private void checkAndUpdateOperationMemberInfo(MemberGradeCardQO qo,HsaMemberGradeCard currentGradeCard) {
        // 查询会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(qo.getMemberInfoGuid());
        if (memberInfo == null) {
            return;
        }

        // 若用户当前无付费等级或等级与此次相同，直接更新会员信息
        if (memberInfo.getMemberPaidGradeInfoGuid() == null || memberInfo.getMemberPaidGradeInfoGuid().equals(qo.getGradeGuid())) {
            updateMemberInfo(memberInfo, qo);
            return;
        }
        log.info("===>>当前拥有的会员卡级别：{}, 想要调整的会员卡级别：{}", currentGradeCard.getVipGrade(), qo.getVipGrade());
        // 当前无等级或当前等级低于此次等级时，更新会员信息
        if (shouldUpdateGrade(currentGradeCard, qo)) {
            updateMemberInfo(memberInfo, qo);
        }
    }

    /**
     * 判断是否需要更新会员等级
     * @param currentGradeCard 当前最高等级卡
     * @param qo 会员等级卡查询对象
     * @return 需要更新返回true，否则返回false
     */
    private boolean shouldUpdateGrade(HsaMemberGradeCard currentGradeCard, MemberGradeCardQO qo) {
        return currentGradeCard == null || currentGradeCard.getVipGrade() < qo.getVipGrade();
    }

    /**
     * 更新会员信息
     * @param memberInfo 会员信息对象
     * @param qo 会员等级卡查询对象
     */
    private void updateMemberInfo(HsaOperationMemberInfo memberInfo, MemberGradeCardQO qo) {
        memberInfo.setMemberPaidGradeInfoGuid(qo.getGradeGuid());
        memberInfo.setMemberPaidGradeInfoName(qo.getGradeName());
        memberInfo.setUpgradeTime(LocalDateTime.now());
        memberInfo.setExpireTime(qo.getMemberCardValidTime().atTime(23, 59, 59));
        hsaOperationMemberInfoMapper.updateByGuid(memberInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String guid) {
        HsaMemberGradeCard card = hsaMemberGradeCardMapper.queryByGuid(guid);
        if (card == null) {
            throw new MemberBaseException("会员等级卡不存在");
        }
        hsaMemberGradeCardMapper.removeByGuid(guid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> guids) {
        if (!CollectionUtils.isEmpty(guids)) {
            for (String guid : guids) {
                delete(guid);
            }
        }
    }

    /**
     * 将实体转换为VO
     */
    private MemberGradeCardVO convertToVO(HsaMemberGradeCard entity) {
        if (entity == null) {
            return null;
        }
        MemberGradeCardVO vo = new MemberGradeCardVO();
        BeanUtils.copyProperties(entity, vo);
        // TODO: 设置gradeTypeName、memberCardStatusName和openTypeName
        return vo;
    }

    /**
     * 将实体列表转换为VO列表
     */
    private List<MemberGradeCardVO> convertToVOList(List<HsaMemberGradeCard> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
}




