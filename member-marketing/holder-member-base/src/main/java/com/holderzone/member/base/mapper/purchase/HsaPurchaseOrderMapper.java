package com.holderzone.member.base.mapper.purchase;

import com.holderzone.member.base.entity.purchase.HsaPurchaseOrder;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseApplyPageQo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseApplyPageVo;
import com.holderzone.member.common.qo.member.BalanceRecordQO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 限量抢购活动-订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface HsaPurchaseOrderMapper extends HolderBaseMapper<HsaPurchaseOrder> {

    /**
     * 查询限购订单明细
     * @param request
     * @return
     */
    List<PurchaseApplyPageVo> selectPurchaseOrderPage(@Param("request") PurchaseApplyPageQo request);

    /**
     * 查询活动订单数量
     * @param request
     * @return
     */
   Integer selectPurchaseOrderNum(@Param("request") PurchaseApplyPageQo request);

    /**
     * 查询活动订单实付金额
     * @param request
     * @return
     */
    BigDecimal selectPurchaseOrderAmountPaid(@Param("request") PurchaseApplyPageQo request);

    /**
     * 查询下单用户数
     * @param request
     * @return
     */
    int selectPurchaseOrderMemberNum(@Param("request") PurchaseApplyPageQo request);
}
