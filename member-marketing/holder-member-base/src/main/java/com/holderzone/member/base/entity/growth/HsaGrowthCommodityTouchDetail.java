package com.holderzone.member.base.entity.growth;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单任务商品触发次数、数量累计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGrowthCommodityTouchDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * growthValueTaskGuid
     */
    private String growthValueTaskGuid;

    /**
     * memberInfoGuid
     */
    private String memberInfoGuid;

    /**
     * 指定商品赠送成长值类型 0：购买次数 1:购买数量 2：购买周期
     *
     * @see com.holderzone.member.common.enums.growth.BuyTypeEnum
     */
    private Integer buyType;

    /**
     * 已完成数量  单位：购买次数 次 ，购买数量 件 ，购买周期 天/周
     */
    private Integer number;

    /**
     * 消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单消费时间
     */
    private LocalDateTime orderTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 此记录是否已经触发过 1 是 0 否
     */
    private Integer isDelete;
}
