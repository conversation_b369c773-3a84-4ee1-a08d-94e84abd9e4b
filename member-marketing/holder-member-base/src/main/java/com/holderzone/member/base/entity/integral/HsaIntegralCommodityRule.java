package com.holderzone.member.base.entity.integral;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 成长值任务适用商品
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaIntegralCommodityRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * growthValueTaskGuid
     */
    private String growthValueTaskGuid;

    /**
     * 商品策略单id
     */
    private String strategyId;

    /**
     * 商品策略单名称
     */
    private String strategyName;

    /**
     * 策略单code
     */
    private String strategyCode;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 单品1 固定2 可选3
     *
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    private Integer comboType;

    /**
     * 商品状态 ('1', '告罄'),<br> ('2', '未上架'),<br> ('3', '已上架'),<br> ('4', '已失效')
     *
     * @see com.holderzone.member.common.enums.growth.CommodityStatusEnum
     */
    private Integer storeState;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 商品分类id
     */
    private String categoryId;

    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
