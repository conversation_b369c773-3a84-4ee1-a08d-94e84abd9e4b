package com.holderzone.member.base.service.saas;


import com.holderzone.framework.util.Page;
import com.holderzone.member.common.qo.saas.RequestMemberInfoVolumeQuery;
import com.holderzone.member.common.vo.coupon.AppletsMemberCouponVO;

/**
 * <p>
 * 老门店 会员优惠券接口
 * </p>
 */
public interface HsaSaasMemberCouponService {

    /**
     * 查询会员优惠券列表(未适用、已使用、已过期)
     */
    Page<AppletsMemberCouponVO> queryListByMember(RequestMemberInfoVolumeQuery query);

    /**
     * 查询会员优惠券适用商品和适用门店
     */
    AppletsMemberCouponVO queryUseConditions(String volumeInfoGuid);

    /**
     * 查询会员未使用优惠券总数
     */
    Long queryUnUserCountByMember(RequestMemberInfoVolumeQuery query);
}
