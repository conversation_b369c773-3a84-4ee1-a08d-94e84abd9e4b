package com.holderzone.member.base.manage;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.grade.HsaMemberGradeRelation;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.base.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.feign.PartnerPlatformFeign;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.GradeVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年04月04日 下午2:17
 * @description 会员和等级关联管理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class MemberGradeRelationManage {

    private IHsaMemberGradeRelationService memberGradeRelationService;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final IHsaMemberGradeInfoService memberGradeInfoService;

    private final HsaOperationMemberInfoService operationMemberInfoService;

    private HsaControlledGradeStateService gradeStateService;

    private final PartnerPlatformFeign partnerPlatformFeign;

    public List<GradeVo> listMemberGrade(String memberInfoGuid) {
        HsaOperationMemberInfo hsaOperationMemberInfo = operationMemberInfoService.queryByGuid(memberInfoGuid);
        if (ObjectUtils.isEmpty(hsaOperationMemberInfo)) {
            return Collections.emptyList();
        }
        return listMemberGrade(hsaOperationMemberInfo);
    }

    public List<GradeVo> listMemberGrade(HsaOperationMemberInfo hsaOperationMemberInfo) {
        List<GradeVo> gradeVoList = new ArrayList<>();
        List<String> roleTypeToVO = RoleTypeEnum.getRoleTypeToVO(hsaOperationMemberInfo.getRoleType());
        //启用的等级
        List<String> roleTypeList = gradeStateService.getEnableRoleType(hsaOperationMemberInfo.getOperSubjectGuid());
        roleTypeToVO.retainAll(roleTypeList);
        if (roleTypeToVO.isEmpty()) {
            return gradeVoList;
        }
        if (StringUtils.isEmpty(ThreadLocalCache.getOperSubjectGuid())) {
            HeaderUserInfo headerUserInfo = new HeaderUserInfo();
            headerUserInfo.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
            headerUserInfo.setEnterpriseGuid(hsaOperationMemberInfo.getEnterpriseGuid());
            String userInfo;
            try {
                userInfo = URLDecoder.decode(JSON.toJSONString(headerUserInfo), "utf-8");
            } catch (UnsupportedEncodingException e) {
                throw new MallBaseException(e.getMessage());
            }
            log.info("userInfo信息：{}", userInfo);
            ThreadLocalCache.put(userInfo);
        }
        List<String> phoneNum = partnerPlatformFeign.getAvailablePhones(Collections.singletonList(hsaOperationMemberInfo.getPhoneNum()));
        List<HsaMemberGradeRelation> relationList = memberGradeRelationService.list(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberGradeRelation::getIsEnable, BooleanEnum.TRUE.getCode())
                .notIn(CollectionUtils.isEmpty(phoneNum), HsaMemberGradeRelation::getRoleType, "MERCHANT")
                .eq(HsaMemberGradeRelation::getOperSubjectGuid, hsaOperationMemberInfo.getOperSubjectGuid()));
        if (CollectionUtils.isEmpty(relationList)) {
            roleTypeToVO.forEach(roleType -> {
                GradeVo gradeVo = new GradeVo();
                gradeVo.setRoleType(roleType);
                gradeVoList.add(gradeVo);
            });
            return gradeVoList;
        }
        List<String> gradeGuidList = relationList.stream()
                .map(HsaMemberGradeRelation::getMemberInfoGradeGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaMemberGradeInfo> gradeInfoList = memberGradeInfoService.listByGuidList(gradeGuidList);
        Map<String, HsaMemberGradeInfo> gradeInfoMap = gradeInfoList.stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getRoleType, Function.identity(), (v1, v2) -> v1));
        roleTypeToVO.forEach(roleType -> {
            HsaMemberGradeInfo memberGradeInfo = gradeInfoMap.get(roleType);
            if (ObjectUtils.isEmpty(memberGradeInfo)) {
                GradeVo gradeVo = new GradeVo();
                gradeVo.setRoleType(roleType);
                gradeVoList.add(gradeVo);
                return;
            }
            GradeVo gradeVo = new GradeVo();
            gradeVo.setGuid(memberGradeInfo.getGuid());
            gradeVo.setVipGrade(memberGradeInfo.getVipGrade());
            gradeVo.setName(memberGradeInfo.getName());
            gradeVo.setRoleType(memberGradeInfo.getRoleType());
            gradeVo.setGradeIcon(memberGradeInfo.getGradeIcon());
            gradeVoList.add(gradeVo);
        });
        return gradeVoList;
    }
}
