package com.holderzone.member.base.manage;

import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.vo.grade.MemberGradeEquitiesPartnerVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EquitiesCenterManager {

    private final HsaControlledGradeStateService gradeStateService;

    private final IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    /**
     * 查询当前会员的等级权益列表
     */
    public List<MemberGradeEquitiesPartnerVO> queryByMember() {
        // 查询当前用户角色
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<String> currentRoleTypeList = RoleTypeEnum.getRoleTypeToVO(headerUserInfo.getRoleType());
        if (CollectionUtils.isEmpty(currentRoleTypeList)) {
            return Lists.newArrayList();
        }
        // 查询启用的等级
        List<String> enableRoleTypeList = gradeStateService.getEnableRoleType(ThreadLocalCache.getOperSubjectGuid());
        if (CollectionUtils.isEmpty(enableRoleTypeList)) {
            return Lists.newArrayList();
        }
        currentRoleTypeList.retainAll(enableRoleTypeList);

        // 查询等级权益
        String memberInfoGuid = ThreadLocalCache.getUserGuid();
        List<MemberGradeEquitiesPartnerVO> memberGradeEquities = Lists.newArrayList();
        currentRoleTypeList.forEach(roleType -> {
            MemberGradeEquitiesPartnerVO appletMemberGradeEquities = hsaMemberGradeInfoService.getAppletMemberGradeEquities(memberInfoGuid,
                    RoleTypeEnum.name2Code(roleType));
            memberGradeEquities.add(appletMemberGradeEquities);
        });
        return memberGradeEquities;
    }

}
