package com.holderzone.member.base.handler;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 给没有权限的字段影藏
 */
@Slf4j
public class ExportFieldPermissionHandler implements CellWriteHandler {

    /**操作列*/
    private List<Integer> columnIndex;

    public ExportFieldPermissionHandler(List<Integer> columnIndex) {
        this.columnIndex = columnIndex;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // Do nothing because of X and Y.
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // Do nothing because of X and Y.
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        boolean changeCell = CollectionUtils.isNotEmpty(columnIndex);
        int index = cell.getColumnIndex();
        if(Boolean.TRUE.equals(changeCell) && !this.columnIndex.contains(index)) {
            cell.getRow().removeCell(cell);
            Sheet sheet = writeSheetHolder.getSheet();
            sheet.setColumnWidth(index, 1);
        }
    }
}
