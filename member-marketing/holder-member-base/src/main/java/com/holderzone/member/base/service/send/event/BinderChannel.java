package com.holderzone.member.base.service.send.event;

import com.holderzone.member.common.constant.BinderConstant;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * 队列通道
 */
public interface BinderChannel {
    /**
     * 推送
     */
    @Output(BinderConstant.OUTPUT_PUSH_SHORT_MESSAGE_SEND)
    SubscribableChannel outputPushShortMessageSend();
}
