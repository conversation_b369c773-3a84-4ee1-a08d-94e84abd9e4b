package com.holderzone.member.base.entity.grade;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员等级权益适用商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGradeRightsCommodityRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 权益guid
     */
    private String gradeEquitiesGuid;

    /**
     * 权益id
     */
    private String gradeEquitiesVersionId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 单品1 固定2 可选3
     *
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    private Integer comboType;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 是否删除,0未删除,1已删除 2：删除但未生效
     */
    private Integer isDelete;

    /**
     * 是否有效 0:无效 1:有效
     */
    private Integer effective;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;


    /**
     * 业务类型 0 会员商城 1 pos,15 私域商城
     */
    private Integer businessType;

}
