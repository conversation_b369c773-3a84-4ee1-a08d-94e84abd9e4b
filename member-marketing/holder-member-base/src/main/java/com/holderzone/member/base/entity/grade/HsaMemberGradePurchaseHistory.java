package com.holderzone.member.base.entity.grade;

import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.enums.grade.GradeHisChangeTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员等级购买流水
 * @TableName hsa_member_grade_purchase_history
 */
@Data
@EqualsAndHashCode (callSuper = false)
@Accessors (chain = true)
public class HsaMemberGradePurchaseHistory extends HsaBaseEntity implements Serializable {

    private static final long serialVersionUID = -7211034150753370212L;
    /**
     * 订单号
     */
    private String no;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 会员昵称
     */
    private String memberInfoName;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 会员等级GUID
     */
    private String memberInfoGradeGuid;

    /**
     * 会员等级名称
     */
    private String memberInfoGradeName;

    /**
     * 购买会员级别
     */
    private Integer vipGrade;

    /**
     * 有效期
     * @see com.holderzone.member.common.enums.grade.EffectiveDurationTypeEnum
     */
    private Integer effectiveDurationType;

    /**
     * 支付金额（单位：毫）
     */
    private BigDecimal payAmount;

    /**
     * 支付方式
aliPay  支付宝
wxPay 微信支付
balance 储值支付
     */
    private String payType;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 乐观锁版本号
     */
    private Integer version;


    /**
     * 支付订单编号
     */
    private String orderNo;

    /**
     * 类型（新开，续费）
     * @see GradeHisChangeTypeEnum
     */
    private Integer type;

    /**
     * 平台id
     */
    private String operSubjectGuid;

    /**
     * 备注
     */
    private String remark;


    /**
     * 记录说明
     */
    private String recordDesc;

    /**
     * 变动来源
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer source;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人guid
     */
    private String operatorName;


}