package com.holderzone.member.base.mapper.grade;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.member.base.entity.grade.HsaMemberGradeRelation;
import com.holderzone.member.common.vo.grade.MemberGradeCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员和等级关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface HsaMemberGradeRelationMapper extends BaseMapper<HsaMemberGradeRelation> {


    /**
     * 查询会员等级对应人数
     *
     * @param gradeInfoGuids 会员等级guid集合
     * @return 查询结果
     */
    List<MemberGradeCountVO> queryMemberGradeCount(@Param("gradeInfoGuids") List<String> gradeInfoGuids,
                                                   @Param("memberGuidList") List<String> memberGuidList);

    void updateMemberGradeRelation(@Param("roleType") String roleType, @Param("vipGrade") Integer vipGrade,
                                   @Param("operSubjectGuid") String operSubjectGuid);
}
