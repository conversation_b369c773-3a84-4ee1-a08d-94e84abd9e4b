package com.holderzone.member.base.service.card;

import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;

public interface CardValidityType {
    String cardTimeHandler(HsaCardBaseInfo hsaCardBaseInfo,
                         HsaElectronicCard hsaElectronicCard,
                         HsaPhysicalCard hsaPhysicalCard);
}
