package com.holderzone.member.base.entity.member;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.base.entity.system.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 会员卡充值规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_card_recharge_rule")
public class HsaCardRechargeRule extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 充值类型(1:任意金额;2:固定金额)
     */
    private String rechargeType;

    /**
     * 充值固定金额列表["100",“200”]
     */
    private String rechargeMoneys;

    /**
     * 充值限制类型：0不限制 ，1限制
     */
    private Integer rechargeLimit;

    /**
     * 自定义最少充值金额
     */
    private BigDecimal rechargeMin;

    /**
     * 自定义最多充值金额
     */
    private BigDecimal rechargeMax;

    /**
     * 充值和消费描述说明内容
     */
    private String description;

}
