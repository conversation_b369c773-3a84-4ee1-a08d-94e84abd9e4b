package com.holderzone.member.base.service.base;


import com.holderzone.member.common.qo.base.CallbackApplyBusinesQO;
import com.holderzone.member.common.vo.equities.ApplyModuleVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HsaApplyDictionariesService {

    /**
     * 获取适用业务
     * @param isFilterSystem 是否过滤系统业务
     * @return  适用业务
     */
    List<ApplyTypeVO> getApplyBusiness(Boolean isFilterSystem);
    /**
     * 回调适用业务
     * @param request 回调
     * @return 操作结果
     */
    Boolean callbackApplyBusiness(CallbackApplyBusinesQO request);

    /**
     * 敏感字词校验
     * @param content
     */
    void checkKeyWords(String content);

    /**
     * 获取适用模块
     * 聚合业务，渠道，终端
     * @return
     */
    ApplyModuleVO getApplyModule ();
}
