package com.holderzone.member.base.mapper.coupon;

import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.ItemNum;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.qo.coupon.CouponPackageGiveQO;
import com.holderzone.member.common.qo.coupon.PackageListCountQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 会员券包发放 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface HsaMemberCouponPackageLinkMapper extends HolderBaseMapper<HsaMemberCouponPackageLink> {

    List<String> queueMemberGuidByGuid(@Param("guid") String guid);

    /**
     * 明细统计
     *
     * @param qo
     * @return
     */
    List<HsaMemberCouponPackageLink> listDetail(@Param("qo") CouponPackageGiveQO qo);

    /**
     * 发放数量统计
     *
     * @param qo
     * @return
     */
    List<ItemNum> countGiveNum(@Param("qo") CouponPackageGiveQO qo);

    /**
     * 发放成功数量
     *
     * @param qo
     * @return
     */
    List<ItemNum> countCouponPackage(@Param("qo") PackageListCountQO qo);
}
