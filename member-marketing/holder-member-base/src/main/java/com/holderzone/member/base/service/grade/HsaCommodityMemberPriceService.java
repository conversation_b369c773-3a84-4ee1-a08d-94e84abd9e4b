package com.holderzone.member.base.service.grade;

import com.holderzone.member.base.entity.grade.HsaCommodityMemberPrice;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.RelationCommodityPriceQO;
import com.holderzone.member.common.vo.grade.CommodityDeleteDataVO;
import com.holderzone.member.common.vo.grade.CommodityInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-07-07 14:38
 */
public interface HsaCommodityMemberPriceService extends IHolderBaseService<HsaCommodityMemberPrice> {

    /**
     * 查询商品数据
     *
     * @return 查询结果
     */
    PageResult queryCommodityInfo(CommodityInfoQO commodityInfoQO);

    /**
     * 保存自定义商品会员价
     *
     * @param request 操作结果
     */
    Integer saveCustomCommodityPrice(RelationCommodityPriceQO request);

    /**
     * 查询自定义商品会员价
     *
     */
    List<CommodityInfoVO> queryCustomCommodityPrice();


    boolean commodityDataSynchronization(List<CommodityInfoDTO> commodityInfoDTOList);

    List<StoreInfoVO> getStoreStall(StoreInfoDTO storeInfoDTO);


    List<StrategyInfoVO> getStrategyInfo(StrategyInfoDTO strategyInfoDTO);

    List<CommodityDeleteDataVO> queryDeleteData();

    boolean disposeDeleteData(List<Long> ids);

}
