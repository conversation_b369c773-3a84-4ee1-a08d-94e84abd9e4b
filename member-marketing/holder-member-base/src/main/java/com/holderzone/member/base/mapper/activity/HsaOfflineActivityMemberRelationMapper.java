package com.holderzone.member.base.mapper.activity;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.member.base.entity.activity.HsaOfflineActivityMemberRelation;
import com.holderzone.member.common.dto.activity.OfflineActivityMemberRelationDTO;
import com.holderzone.member.common.qo.activity.OfflineActivityMemberRelationQO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface HsaOfflineActivityMemberRelationMapper extends BaseMapper<HsaOfflineActivityMemberRelation> {

    List<MemberInfoVO> relationMemberInfoList(@Param("query") OfflineActivityMemberRelationQO query);

    List<String> queryActivityGuids(@Param("query") OfflineActivityMemberRelationQO query);

    List<OfflineActivityMemberRelationDTO> relationCount(@Param("query") OfflineActivityMemberRelationQO query);

    List<String> queryActivityGuidsByMemberGuid(@Param("memberGuid")String memberGuid);
}
