package com.holderzone.member.base.mapper.member;

import com.holderzone.member.base.entity.member.HsaDepositStrategy;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.card.PhysicalCardStrategyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaCanteenDepositStrategy
 * @Author: 张林
 * @Description: 会员卡押金策瑜mapper
 * @Version: 1.0.1
 */
public interface HsaDepositStrategyMapper extends HolderBaseMapper<HsaDepositStrategy> {

    List<PhysicalCardStrategyVO> listByCardStrategyGuid(@Param("cardStrategyGuids") List<String> cardStrategyGuids,
                                                        @Param("operSubjectGuid") String operSubjectGuid);
}
