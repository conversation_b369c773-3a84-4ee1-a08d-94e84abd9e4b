package com.holderzone.member.base.service.send.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.service.send.event.ShortMessageSendEvent;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.PushSettlementDiscountEvent;
import com.holderzone.member.common.dto.event.PushShortMessageEvent;
import com.holderzone.member.common.dto.integral.HsaIntegralExtraAwardRuleDTO;
import com.holderzone.member.common.dto.message.SendShortMessageDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SmsTemplateEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.ShortMessagesConfigDTO;
import com.holderzone.member.common.vo.coupon.MemberCouponOpenIdVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShortMessageSendServiceImpl implements ShortMessageSendService {

    @Resource
    private IPaasFeign iPaasFeign;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    @Qualifier("shortMessageThreadExecutor")
    private ExecutorService shortMessageThreadExecutor;

    public static final int BATCH_SEND_PARTITION = 10;

    //cardBalance
    private static final String CARD_BALANCE = "cardBalance";

    @Resource
    private ShortMessageSendEvent event;

    @Override
    public void send(SendShortMessageDTO messagesSend) {
        log.info("短信发送：{}", JSON.toJSONString(messagesSend));
        boolean isSend = event.send(new PushShortMessageEvent().setMessagesSend(messagesSend));
        log.info("短信发送结果：{}", isSend);
    }

    @Override
    public void subsidySendBatch(List<MessagesSendQO> messagesSendList) {
        log.info("补贴相关短信发送入参：{}", JSON.toJSONString(messagesSendList));
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return;
        }
        log.info("批量发送消息接收：{}", JacksonUtils.writeValueAsString(messagesSendList));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 防止存在不同运营主体
        Map<String, List<MessagesSendQO>> messagesSendMap = messagesSendList.stream().collect(Collectors.groupingBy(MessagesSendQO::getOperSubjectGuid));
        for (Map.Entry<String, List<MessagesSendQO>> entry : messagesSendMap.entrySet()) {
            List<MessagesSendQO> innerMessagesSendList = entry.getValue();
            MessagesSendQO innerMessagesSend = innerMessagesSendList.get(0);
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(innerMessagesSend.getOperSubjectGuid(), WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());
            if (checkMessagesConfig(sendMessagesConfig)) return;
            // 分批线程处理
            List<List<MessagesSendQO>> groupByMessageSendList = Lists.partition(innerMessagesSendList, BATCH_SEND_PARTITION);
            groupByMessageSend(groupByMessageSendList, headerUserInfo, sendMessagesConfig);
        }
    }

    private static boolean checkMessagesConfig(SendMessagesConfigVO sendMessagesConfig) {
        if (Objects.isNull(sendMessagesConfig) || sendMessagesConfig.getMessageStatus() == BooleanEnum.FALSE.getCode()) {
            log.error("模板不存在或未开启！名称：{}", WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());
            return true;
        }
        return false;
    }

    private void groupByMessageSend(List<List<MessagesSendQO>> groupByMessageSendList,
                                    HeaderUserInfo headerUserInfo,
                                    SendMessagesConfigVO sendMessagesConfig) {
        for (List<MessagesSendQO> messagesSendPartition : groupByMessageSendList) {
            shortMessageThreadExecutor.execute(() -> {
                ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));

                for (MessagesSendQO messagesSendQO : messagesSendPartition) {
                    SendShortMessageDTO sendShortMessageDTO = new SendShortMessageDTO();
                    Map<String, String> messageMap = new HashMap<>();
                    sendShortMessageDTO.setEnterpriseId(ThreadLocalCache.getEnterpriseGuid());
                    sendShortMessageDTO.setPhoneList(Collections.singletonList(messagesSendQO.getPhone()));
                    AmountSourceTypeEnum growthValueOperationEnum = AmountSourceTypeEnum.getEnum(messagesSendQO.getAmountSourceType());

                    log.info("变动类型:{}", JSON.toJSONString(growthValueOperationEnum));

                    ShortMessagesConfigDTO messagesConfigDTO = getShortMessagesConfigDTO(sendMessagesConfig, growthValueOperationEnum.getCode());

                    log.info("发送短信配置:{}", messagesConfigDTO);
                    messageMap.put("name", messagesSendQO.getMemberName());
                    switch (Objects.requireNonNull(growthValueOperationEnum)) {
                        case SUBSIDY_GRANT:
                            messageMap.put("subsidyAmount", messagesSendQO.getSubsidyAmount() + "");
                            break;
                        case SUBSIDY_EXPIRED:
                            messageMap.put("expiredBalance", messagesSendQO.getSubsidyAmount() + "");
                            break;
                        default:
                            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
                    }
                    messageMap.put(CARD_BALANCE, messagesSendQO.getCardBalance() + "");
                    sendShortMessageDTO.setNoticeContentMap(messageMap);
                    send(sendShortMessageDTO);
                }
            });
        }
    }

    @Override
    public void adminEditSendBatch(List<MessagesSendQO> messagesSendList) {
        log.info("后台调整余额短信推送参数：{}", JSON.toJSONString(messagesSendList));
        shortMessageThreadExecutor.execute(() -> {
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(messagesSendList.get(0).getOperSubjectGuid(),
                    WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());

            if (checkMessagesConfig(sendMessagesConfig)) return;
            for (MessagesSendQO messagesSendQO : messagesSendList) {
                Map<String, String> messageMap = new HashMap<>();
                SendShortMessageDTO sendShortMessageDTO = getSendShortMessageDTO(messagesSendQO);
                AmountSourceTypeEnum growthValueOperationEnum = AmountSourceTypeEnum.getEnum(messagesSendQO.getAmountSourceType());

                log.info("变动类型:{}", JSON.toJSONString(growthValueOperationEnum));

                ShortMessagesConfigDTO messagesConfigDTO = getShortMessagesConfigDTO(sendMessagesConfig, growthValueOperationEnum.getCode());
                sendShortMessageDTO.setSmsSign(messagesConfigDTO.getSmsSign());
                sendShortMessageDTO.setSmsTemplateCode(messagesConfigDTO.getSmsTemplateCode());
                log.info("发送短信配置:{}", messagesConfigDTO);
                messageMap.put("name", messagesSendQO.getMemberName());

                String type = messagesSendQO.getAmountFundingType() == BooleanEnum.FALSE.getCode() ? "增加" : "扣减";
                messageMap.put("operation", type);
                messageMap.put("operationBalance", messagesSendQO.getChangeAmount() + "");
                messageMap.put(CARD_BALANCE, messagesSendQO.getCardBalance() + "");
                sendShortMessageDTO.setNoticeContentMap(messageMap);
                send(sendShortMessageDTO);
            }
        });
    }

    private static ShortMessagesConfigDTO getShortMessagesConfigDTO(SendMessagesConfigVO sendMessagesConfig, int growthValueOperationEnum) {
        return JSON.parseArray(sendMessagesConfig.getShortMessageContentJson(), ShortMessagesConfigDTO.class)
                .stream()
                .collect(Collectors.toMap(ShortMessagesConfigDTO::getChangeType, Function.identity(), (entity1, entity2) -> entity1))
                .get(growthValueOperationEnum);
    }

    private static SendShortMessageDTO getSendShortMessageDTO(MessagesSendQO messagesSendQO) {
        SendShortMessageDTO sendShortMessageDTO = new SendShortMessageDTO();
        sendShortMessageDTO.setEnterpriseId(messagesSendQO.getEnterpriseGuid());
        sendShortMessageDTO.setPhoneList(Collections.singletonList(messagesSendQO.getPhone()));
        return sendShortMessageDTO;
    }

    @Override
    public void openCardSendBatch(List<MessagesSendQO> messagesSendList) {
        log.info("开卡推送短信参数：{}", JSON.toJSONString(messagesSendList));
        shortMessageThreadExecutor.execute(() -> {
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(messagesSendList.get(0).getOperSubjectGuid(),
                    SmsTemplateEnum.OPEN_CARD_SUCCESS_SMS.getDes());

            if (checkMessagesConfig(sendMessagesConfig)) return;

            Map<String, List<MessagesSendQO>> messagesSendMap = messagesSendList.stream()
                    .collect(Collectors.groupingBy(MessagesSendQO::getPhone));

            List<String> phoneNums = messagesSendList.stream().map(MessagesSendQO::getPhone).distinct().collect(Collectors.toList());
            ShortMessagesConfigDTO messagesConfigDTO = JSON.parseArray(sendMessagesConfig.getShortMessageContentJson(), ShortMessagesConfigDTO.class).get(0);
            for (String phoneNum : phoneNums) {
                List<MessagesSendQO> innerMessagesSendList = messagesSendMap.get(phoneNum);
                for (MessagesSendQO messagesSendQO : innerMessagesSendList) {
                    Map<String, String> messageMap = new HashMap<>();
                    SendShortMessageDTO sendShortMessageDTO = getSendShortMessageDTO(messagesSendQO);
                    sendShortMessageDTO.setSmsSign(messagesConfigDTO.getSmsSign());
                    sendShortMessageDTO.setSmsTemplateCode(messagesConfigDTO.getSmsTemplateCode());
                    messageMap.put(CARD_BALANCE, messagesSendQO.getCardBalance() + "");
                    sendShortMessageDTO.setNoticeContentMap(messageMap);
                    send(sendShortMessageDTO);
                }
            }
        });
    }

    @Override
    public void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponPackageVOS) {
        log.info("优惠券到账短信推送参数:{}", JSON.toJSONString(memberCouponPackageVOS));
        shortMessageThreadExecutor.execute(() -> {
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(memberCouponPackageVOS.get(0).getOperSubjectGuid(),
                    SmsTemplateEnum.DISCOUNT_COUPON_ARRIVAL_NOTICE_SMS.getDes());

            if (checkMessagesConfig(sendMessagesConfig)) return;

            ShortMessagesConfigDTO messagesConfigDTO = JSON.parseArray(sendMessagesConfig.getShortMessageContentJson(), ShortMessagesConfigDTO.class).get(0);
            for (MemberCouponPackageVO memberCouponPackageVO : memberCouponPackageVOS) {
                Map<String, String> messageMap = new HashMap<>();
                SendShortMessageDTO sendShortMessageDTO = new SendShortMessageDTO();
                sendShortMessageDTO.setEnterpriseId(memberCouponPackageVO.getEnterpriseGuid());
                sendShortMessageDTO.setPhoneList(Collections.singletonList(memberCouponPackageVO.getPhoneNum()));
                sendShortMessageDTO.setSmsSign(messagesConfigDTO.getSmsSign());
                sendShortMessageDTO.setSmsTemplateCode(messagesConfigDTO.getSmsTemplateCode());
                messageMap.put("coupon", memberCouponPackageVO.getCouponName());
                LocalDateTime couponEffectiveEndTime = memberCouponPackageVO.getCouponEffectiveEndTime();
                String minutes = couponEffectiveEndTime.getMinute() == 0 ? "00" : couponEffectiveEndTime.getMinute() + "";

                messageMap.put("time", couponEffectiveEndTime.getYear() + "年"
                        + couponEffectiveEndTime.getMonthValue() + "月"
                        + couponEffectiveEndTime.getDayOfMonth() + "日" + " "
                        + couponEffectiveEndTime.getHour() + ":" + minutes);
                sendShortMessageDTO.setNoticeContentMap(messageMap);
                send(sendShortMessageDTO);
            }
        });
    }

    @Override
    public void sendMemberCouponExpireNotice(List<MemberCouponOpenIdVO> couponOpenIdVOList) {
        log.info("优惠券过期短信推送参数:{}", JSON.toJSONString(couponOpenIdVOList));
        shortMessageThreadExecutor.execute(() -> {
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(couponOpenIdVOList.get(0).getOperSubjectGuid(),
                    SmsTemplateEnum.DISCOUNT_COUPON_EXPIRE_NOTICE_SMS.getDes());
            if (checkMessagesConfig(sendMessagesConfig)) return;
            Map<String, List<MemberCouponOpenIdVO>> memberCouponLinkMap =
                    couponOpenIdVOList.stream().collect(Collectors.groupingBy(MemberCouponOpenIdVO::getMemberGuid));
            Set<String> memberGuid = couponOpenIdVOList.stream().map(MemberCouponOpenIdVO::getMemberGuid).collect(Collectors.toSet());
            ShortMessagesConfigDTO messagesConfigDTO = JSON.parseArray(sendMessagesConfig.getShortMessageContentJson(), ShortMessagesConfigDTO.class).get(0);
            for (String guid : memberGuid) {
                List<MemberCouponOpenIdVO> memberCouponOpenIdVOS = memberCouponLinkMap.get(guid);
                memberCouponOpenIdVOS = memberCouponOpenIdVOS.stream()
                        .sorted(Comparator.comparing(MemberCouponOpenIdVO::getCouponEffectiveEndTime))
                        .collect(Collectors.toList());

                if (!memberCouponOpenIdVOS.isEmpty()) {
                    SendShortMessageDTO sendShortMessageDTO = getSendShortMessageDTO(memberCouponOpenIdVOS, messagesConfigDTO);
                    send(sendShortMessageDTO);
                }
            }
        });
    }

    private static SendShortMessageDTO getSendShortMessageDTO(List<MemberCouponOpenIdVO> memberCouponOpenIdVOS,
                                                              ShortMessagesConfigDTO messagesConfigDTO) {
        Map<String, String> messageMap = new HashMap<>();
        SendShortMessageDTO sendShortMessageDTO = new SendShortMessageDTO();
        sendShortMessageDTO.setEnterpriseId(memberCouponOpenIdVOS.get(0).getEnterpriseGuid());
        sendShortMessageDTO.setPhoneList(Collections.singletonList(memberCouponOpenIdVOS.get(0).getMemberPhone()));
        sendShortMessageDTO.setSmsSign(messagesConfigDTO.getSmsSign());
        sendShortMessageDTO.setSmsTemplateCode(messagesConfigDTO.getSmsTemplateCode());
        messageMap.put("count", memberCouponOpenIdVOS.size() + "");
        sendShortMessageDTO.setNoticeContentMap(messageMap);
        return sendShortMessageDTO;
    }

    public SendMessagesConfigVO getMessagesConfigByName(String operSubjectGuid, String name) {
        return memberMallToolFeign.
                getMessagesConfigByName(operSubjectGuid, name);
    }
}
