package com.holderzone.member.base.service.credit.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.base.entity.credit.HsaCreditFundingDetail;
import com.holderzone.member.base.entity.credit.HsaCreditOrderRecord;
import com.holderzone.member.base.mapper.credit.HsaCreditFundingDetailMapper;
import com.holderzone.member.base.mapper.credit.HsaCreditOrderRecordMapper;
import com.holderzone.member.base.mapper.credit.HsaCreditUserMapper;
import com.holderzone.member.base.service.credit.HsaCreditFundingDetailService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.credit.CreditFundingQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.credit.CreditFundingInfoVO;
import com.holderzone.member.common.vo.credit.CreditFundingTotalVO;
import com.holderzone.member.common.vo.credit.CreditUserAmountInfoVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 挂账资金来往服务实现
 */
@Service
public class HsaCreditFundingDetailServiceImpl extends HolderBaseServiceImpl<HsaCreditFundingDetailMapper, HsaCreditFundingDetail>
        implements HsaCreditFundingDetailService {

    private final HsaCreditFundingDetailMapper hsaCreditFundingDetailMapper;

    private final HsaCreditUserMapper hsaCreditUserMapper;

    private final HsaCreditInfoService hsaCreditInfoService;

    private final HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper;

    public HsaCreditFundingDetailServiceImpl(HsaCreditFundingDetailMapper hsaCreditFundingDetailMapper,
                                             HsaCreditUserMapper hsaCreditUserMapper,
                                             HsaCreditInfoService hsaCreditInfoService,
                                             HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper) {
        this.hsaCreditFundingDetailMapper = hsaCreditFundingDetailMapper;
        this.hsaCreditUserMapper = hsaCreditUserMapper;
        this.hsaCreditInfoService = hsaCreditInfoService;
        this.hsaCreditOrderRecordMapper = hsaCreditOrderRecordMapper;
    }

    @Override
    public PageResult creditFundingDetail(CreditFundingQO creditFundingQO) {
        PageMethod.startPage(creditFundingQO.getCurrentPage(), creditFundingQO.getPageSize());
        List<CreditFundingInfoVO> creditFundingInfoList = hsaCreditFundingDetailMapper
                .creditFundingDetail(creditFundingQO.getMemberInfoGuid(), creditFundingQO.getCreditInfoGuid());
        if (CollUtil.isEmpty(creditFundingInfoList)) {
            return new PageResult();
        }
        // 挂账订单记录guids
        List<String> creditOrderRecordGuids = creditFundingInfoList.stream()
                .map(CreditFundingInfoVO::getCreditOrderRecordGuid).collect(Collectors.toList());
        // 消费记录
        Map<String, HsaCreditOrderRecord> orderRecordMap = hsaCreditOrderRecordMapper.selectList(
                new LambdaQueryWrapper<HsaCreditOrderRecord>()
                        .in(HsaCreditOrderRecord::getGuid, creditOrderRecordGuids))
                .stream().filter(x->x.getOrderStatus() == NumberConstant.NUMBER_1)
                .collect(Collectors.toMap(HsaCreditOrderRecord::getGuid, Function.identity(), (obj, obj1) -> obj));

        for (CreditFundingInfoVO creditFundingInfoVO : creditFundingInfoList) {
            creditFundingInfoVO.setConsumptionTime(
                    DateUtil.formatLocalDateTime(creditFundingInfoVO.getGmtCreate(), DateUtil.PATTERN_DAY_TIME));
            creditFundingInfoVO.setOrderStatus(NumberConstant.NUMBER_0);
            if (orderRecordMap.containsKey(creditFundingInfoVO.getCreditOrderRecordGuid())) {
                creditFundingInfoVO.setOrderStatus(NumberConstant.NUMBER_1);
            }
        }
        return PageUtil.getPageResult(new PageInfo<>(creditFundingInfoList));
    }

    @Override
    public CreditFundingTotalVO creditFundingTotal(String memberInfoGuid, String creditInfoGuid) {
        CreditFundingTotalVO creditFundingTotalVO = new CreditFundingTotalVO();

        CreditUserAmountInfoVO creditUserAmountInfo = hsaCreditUserMapper.getCreditUserAmountInfo(memberInfoGuid, creditInfoGuid);
        List<CreditFundingInfoVO> creditFundingInfoList = hsaCreditFundingDetailMapper
                .creditFundingDetail(memberInfoGuid, creditInfoGuid);
        // 设置剩余可用挂账金额
        setResidueCreditAmount(creditFundingTotalVO, creditUserAmountInfo);
        // 设置结算合计金额
        setTotalAmount(creditFundingTotalVO, creditFundingInfoList);
        return creditFundingTotalVO;
    }

    /**
     * 设置结算合计金额
     *
     * @param creditFundingDetailVO 挂账资金来往明细统计
     * @param creditFundingInfoList 挂账来往明细信息
     */
    private void setTotalAmount(CreditFundingTotalVO creditFundingDetailVO, List<CreditFundingInfoVO> creditFundingInfoList) {
        if (CollUtil.isEmpty(creditFundingInfoList)) {
            creditFundingDetailVO.setSettlementTotal(BigDecimal.ZERO);
            creditFundingDetailVO.setNoSettlementTotal(BigDecimal.ZERO);
            return;
        }
        // 已经结算的消费金额
        BigDecimal settlementTotal = creditFundingInfoList.stream()
                .filter(x -> x.getTransactionType().equals(0) && x.getClearingStatus().equals(2))
                .map(CreditFundingInfoVO::getCreditAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 已结算的消费退款金额
        BigDecimal consumerRefundAmount = creditFundingInfoList.stream()
                .filter(x -> x.getTransactionType().equals(1) && x.getClearingStatus().equals(2))
                .map(CreditFundingInfoVO::getCreditAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        settlementTotal = settlementTotal.subtract(consumerRefundAmount);
        // 已结算合计=当前会员账户使用此挂账账户已挂账且已结算的挂账金额 - 消费退款的金额
        creditFundingDetailVO.setSettlementTotal(BigDecimalUtil.lessThan(settlementTotal, BigDecimal.ZERO) ? BigDecimal.ZERO : settlementTotal);


        // 未结算的消费金额
        BigDecimal noSettlementTotal = creditFundingInfoList.stream()
                .filter(x -> x.getTransactionType().equals(0) && !x.getClearingStatus().equals(2))
                .map(CreditFundingInfoVO::getCreditAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 未结算的消费退款金额
        BigDecimal unConsumerRefundAmount = creditFundingInfoList.stream()
                .filter(x -> x.getTransactionType().equals(1) && !x.getClearingStatus().equals(2))
                .map(CreditFundingInfoVO::getCreditAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 未结算合计=当前会员账户使用此挂账账户已挂账且未结算、结算中的挂账金额 - 消费退款的金额
        noSettlementTotal = noSettlementTotal.subtract(unConsumerRefundAmount);
        creditFundingDetailVO.setNoSettlementTotal(BigDecimalUtil.lessThan(noSettlementTotal, BigDecimal.ZERO) ? BigDecimal.ZERO : noSettlementTotal);
    }

    /**
     * 设置剩余可用挂账金额
     *
     * @param creditFundingDetailVO 挂账资金来往明细统计
     * @param creditUserAmountInfo  用户挂账金额相关信息
     */
    private void setResidueCreditAmount(CreditFundingTotalVO creditFundingDetailVO, CreditUserAmountInfoVO creditUserAmountInfo) {
        if (Objects.isNull(creditUserAmountInfo)) {
            creditFundingDetailVO.setResidueCreditAmount(BigDecimal.ZERO);
            return;
        }
        BigDecimal singlePersonUpperLimit = creditUserAmountInfo.getSinglePersonUpperLimit();
        String hsaCreditInfoGuid = creditUserAmountInfo.getHsaCreditInfoGuid();
        BigDecimal creditLimitedAmount = creditUserAmountInfo.getCreditLimitedAmount();
        BigDecimal totalCredit = creditUserAmountInfo.getTotalCredit();
        Integer creditLimitedSet = creditUserAmountInfo.getCreditLimitedSet();

        BigDecimal residueCreditAmount = hsaCreditInfoService.setResidueCreditAmount(
                singlePersonUpperLimit, hsaCreditInfoGuid, creditLimitedAmount, totalCredit, creditLimitedSet);
        creditFundingDetailVO.setResidueCreditAmount(residueCreditAmount);

    }
}
