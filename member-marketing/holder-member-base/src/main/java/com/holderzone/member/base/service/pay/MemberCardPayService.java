package com.holderzone.member.base.service.pay;

import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.member.MemberCardOpenCardPayCheckDTO;
import com.holderzone.member.common.dto.member.MemberCardOpenCardPayDTO;
import com.holderzone.member.common.dto.pay.AggPayCallbackDTO;
import com.holderzone.member.common.dto.pay.MemberCardPerPayVO;
import com.holderzone.member.common.dto.pay.MemberCardPollingPayVO;
import com.holderzone.member.common.dto.pay.SaasAggPayOdooDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.base.AppletStoreQO;
import com.holderzone.member.common.qo.card.MemberCardAggPayQO;
import com.holderzone.member.common.qo.card.MemberCardPayPollingQO;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-17 16:28
 */
public interface MemberCardPayService {

    MemberCardPerPayVO memberCardPerPay(MemberCardAggPayQO memberCardAggPayQO);

    /**
     * 付费开卡（校验）
     *
     * @param dto 请求参数
     */
    void openCardPayCheck(MemberCardOpenCardPayCheckDTO dto);

    /**
     * 付费开卡
     *
     * @param dto 请求参数
     * @return 订单号
     */
    String openCardPay(MemberCardOpenCardPayDTO dto);

    MemberCardPollingPayVO memberCardPolling(MemberCardPayPollingQO memberCardPayPollingQO);

    void callBack(AggPayCallbackDTO aggPayCallbackDTO);

    MemberCardPollingPayVO paySuccess(MemberCardPayPollingQO memberCardPayPollingQO);

    SaasAggPayOdooDTO getPaySetting(MemberCardAggPayQO memberCardAggPayQO, HeaderUserInfo headerUserInfo, String orderGuid, String payGuid,MemberCardPerPayVO memberCardPerPayVO);


    StoreBaseInfo getAppletStore(AppletStoreQO appletStoreQO);

    List<StoreBaseInfo> getAppletStoreList(AppletStoreQO appletStoreQO);



}
