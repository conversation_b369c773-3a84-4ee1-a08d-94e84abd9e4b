package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaCommodityMemberPrice;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.grade.CommodityDeleteDataVO;
import com.holderzone.member.common.vo.grade.CommodityInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-07-07 14:37
 */
public interface HsaCommodityMemberPriceMapper extends HolderBaseMapper<HsaCommodityMemberPrice> {

    List<CommodityDeleteDataVO> queryDeleteData(@Param("operSubjectGuid") String operSubjectGuid );

}
