package com.holderzone.member.base.entity.integral;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 额外奖励规则
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaIntegralExtraAwardRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则guid
     */
    private String guid;

    /**
     * 积分任务guid
     */
    private String integralTaskGuid;

    /**
     * 充值、消费金额
     */
    private BigDecimal consumptionAmount;

    /**
     * 积分
     */
    private Integer integralValue;

    /**
     * 消费笔数
     */
    private Integer consumptionFrequency;


    /* 以下只适用于连续登录/签到任务 */

    /**
     * 是否勾选
     */
    @ApiModelProperty(value = "是否勾选")
    private Integer isChecked;

    /**
     * 连续多少天
     */
    @ApiModelProperty(value = "连续多少天")
    private Integer continuousDays;

}
