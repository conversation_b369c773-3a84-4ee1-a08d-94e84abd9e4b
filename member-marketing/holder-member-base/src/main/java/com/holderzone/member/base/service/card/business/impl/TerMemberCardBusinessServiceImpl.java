package com.holderzone.member.base.service.card.business.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.assembler.MemberCardAssembler;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivityDetailRecord;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.card.HsaStoreCardRule;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.base.entity.recharge.HsaRechargeGiftAmountRecord;
import com.holderzone.member.base.helper.RechargeGiftActivityHelper;
import com.holderzone.member.base.mapper.activity.HsaSubsidyActivityDetailRecordMapper;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.member.HsaCardBalanceRuleMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaMemberFundingDetailMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.activity.HsaSubsidyActivityDetailRecordService;
import com.holderzone.member.base.service.card.business.TerMemberCardBusinessService;
import com.holderzone.member.base.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.base.service.gift.HsaRechargeGiftAmountRecordService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.OrderPartBackDTO;
import com.holderzone.member.common.dto.card.CardInfoDetailDTO;
import com.holderzone.member.common.dto.terminal.SettlementBalanceDTO;
import com.holderzone.member.common.dto.terminal.SubsidyAbatementBalanceDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.CardStatusEnum;
import com.holderzone.member.common.enums.card.CardTypeEnum;
import com.holderzone.member.common.enums.card.PhysicalCardStateEnum;
import com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.gift.TerRechargeGiftActivityQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.card.StoreCardRuleVO;
import com.holderzone.member.common.vo.card.TerBaseLoginMemberCardVO;
import com.holderzone.member.common.vo.card.TerLoginMemberCardVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.gift.RechargeGiftAmountRecordVO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;

/**
 * 会员持卡公共业务整合类
 */

@Slf4j
@Service
public class TerMemberCardBusinessServiceImpl implements TerMemberCardBusinessService {

    @Resource
    private RechargeGiftActivityHelper activityHelper;

    @Resource
    private HsaSubsidyActivityDetailRecordService hsaSubsidyActivityDetailRecordService;

    @Resource
    private HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper;

    @Resource
    private HsaRechargeGiftAmountRecordService hsaRechargeGiftAmountRecordService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService;

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaSubsidyActivityDetailRecordMapper hsaSubsidyActivityDetailRecordMapper;

    @Resource
    private HsaCardInfoMapper hsaCardInfoMapper;

    @Resource
    private HsaPhysicalCardMapper hsaPhysicalCardMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Override
    public void dealFreeze(TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                           TerLoginMemberCardQO terLoginMemberCardQO,
                           HsaOperationMemberInfo hsaOperationMemberInfo) {

        setCardRechargeGift(terBaseLoginMemberCardVO.getTerLoginMemberCardListVOS(), terLoginMemberCardQO.getStoreGuid(), hsaOperationMemberInfo);
    }

    private void setCardRechargeGift(List<TerLoginMemberCardVO> terLoginMemberCardListVOS,
                                     String storeGuid,
                                     HsaOperationMemberInfo hsaOperationMemberInfo) {
        List<String> cardGuidList = terLoginMemberCardListVOS
                .stream()
                .map(TerLoginMemberCardVO::getCardGuid)
                .collect(Collectors.toList());

        TerRechargeGiftActivityQO terRechargeGiftActivityQO = getTerRechargeGiftActivityQO(storeGuid, hsaOperationMemberInfo, cardGuidList);
        List<RechargeGiftActivityVO> activityList = activityHelper.getTerRechargeGiftActivityNew(terRechargeGiftActivityQO);
        for (TerLoginMemberCardVO terLoginMemberCardVO : terLoginMemberCardListVOS) {
            //获取冻结金额
            if (StringUtils.isNotBlank(terLoginMemberCardVO.getMemberInfoCardGuid())) {
                CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();
                cardQO.setMemberInfoCardGuid(terLoginMemberCardVO.getMemberInfoCardGuid());
                cardQO.setIsRefresh(BooleanEnum.FALSE.getCode());
                BigDecimal amount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardQO);
                terLoginMemberCardVO.setFreezeAmount(amount);
            }

            String rechargeTips = activityHelper.buildRechargeTipsByCard(terLoginMemberCardVO.getCardGuid(), activityList);
            terLoginMemberCardVO.setRechargeTips(rechargeTips);
        }
    }

    private static TerRechargeGiftActivityQO getTerRechargeGiftActivityQO(String storeGuid, HsaOperationMemberInfo hsaOperationMemberInfo, List<String> cardGuidList) {
        TerRechargeGiftActivityQO terRechargeGiftActivityQO = new TerRechargeGiftActivityQO();
        terRechargeGiftActivityQO.setStoreGuid(storeGuid);
        if (Objects.nonNull(hsaOperationMemberInfo)) {
            terRechargeGiftActivityQO.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
            terRechargeGiftActivityQO.setMemberGradeGuid(hsaOperationMemberInfo.getMemberGradeInfoGuid());
        }
        terRechargeGiftActivityQO.setMemberCardGuidList(cardGuidList);
        terRechargeGiftActivityQO.setIsUnderway(BooleanEnum.TRUE.getCode());
        return terRechargeGiftActivityQO;
    }

    @Override
    public void dealFreeze(List<TerLoginMemberCardVO> terLoginMemberCardListVOS,
                           String storeGuid,
                           HsaOperationMemberInfo hsaOperationMemberInfo) {
        setCardRechargeGift(terLoginMemberCardListVOS, storeGuid, hsaOperationMemberInfo);
    }


    @Override
    public List<TerLoginMemberCardVO> doCheckMemberInfoCardList(List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                                                HsaOperationMemberInfo hsaOperationMemberInfo,
                                                                HsaCardBalanceRule hsaCardBalanceRule,
                                                                TerCheckMemberCardQO terCheckMemberCardQO) {

        List<String> cardGuid = hsaMemberInfoCardList.stream().map(HsaMemberInfoCard::getCardGuid).collect(Collectors.toList());
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, cardGuid))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        List<TerLoginMemberCardVO> cardVOS = Lists.newArrayList();

        //查询门店
        Map<String, List<HsaStoreCardRule>> storeRuleGroupingMap = getStoreRuleGroupingMap(hsaMemberInfoCardList);

        //过滤已过期卡
        hsaMemberInfoCardList = hsaMemberInfoCardList.stream()
                .filter(in -> in.getCardValidity() == 0
                        || (Objects.nonNull(in.getCardValidityDate()) && !LocalDate.now().isAfter(in.getCardValidityDate())))
                .collect(Collectors.toList());


        for (HsaMemberInfoCard memberInfoCard : hsaMemberInfoCardList) {
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(memberInfoCard.getCardGuid());
            TerLoginMemberCardVO vo = new TerLoginMemberCardVO();

            //充值相关
            MemberCardAssembler.setRecharge(vo, hsaCardBaseInfo);

            //卡类型
            MemberCardAssembler.setCardType(memberInfoCard, vo);

            MemberCardAssembler.setMemberCardBase(hsaOperationMemberInfo, memberInfoCard, vo, hsaCardBaseInfo);

            //超额
            MemberCardAssembler.setExcess(memberInfoCard, hsaCardBaseInfo, vo);

            //卡状态判断
            MemberCardAssembler.setCardState(memberInfoCard, vo);

            //过滤不符合的卡
            if (terCheckMemberCardQO.getIsFilterStatus() == BooleanEnum.TRUE.getCode()
                    && checkMemberCard(terCheckMemberCardQO, memberInfoCard, hsaCardBaseInfo, vo, storeRuleGroupingMap)) {
                continue;
            }

            //判断激活状态
            checkNotActivate(memberInfoCard, vo);

            //是否需要密码
            setIsCheckPasswordAndCardState(hsaCardBalanceRule, hsaCardBaseInfo, vo, terCheckMemberCardQO);
            cardVOS.add(vo);
        }

        //根据开卡时间排序
        if (CollUtil.isNotEmpty(cardVOS)) {
            cardVOS = cardVOS.stream().sorted(Comparator.comparing(TerLoginMemberCardVO::getGmtCreate).reversed())
                    .collect(Collectors.toList());
        }

        return cardVOS;
    }

    @Override
    public Map<String, List<HsaStoreCardRule>> getStoreRuleGroupingMap(List<HsaMemberInfoCard> hsaMemberInfoCardList) {
        List<String> cardStoreCardRuleList = hsaMemberInfoCardList.stream().filter(in -> in.getApplicableAllStore() == GoodsApplicableStoreEnum.PORTION_STORE.getCode())
                .map(HsaMemberInfoCard::getGuid)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(cardStoreCardRuleList)) {
            return new HashMap<>();
        }
        return hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                        .in(HsaStoreCardRule::getMemberInfoCardGuid, cardStoreCardRuleList))
                .stream().collect(Collectors.groupingBy(HsaStoreCardRule::getMemberInfoCardGuid));
    }


    @Override
    public void saveGiftAmountRecord(HsaMemberConsumption hsaMemberConsumption,
                                     HsaMemberInfoCard hsaMemberInfoCard,
                                     HsaOperationMemberInfo hsaOperationMemberInfo,
                                     RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO,
                                     HsaMemberFundingDetail hsaMemberFundingDetail,
                                     HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay) {
        log.info("rechargeOrderGiftSummaryVO:{}", JSON.toJSONString(rechargeOrderGiftSummaryVO));
        if (CollUtil.isNotEmpty(rechargeOrderGiftSummaryVO.getRechargeGiftAmountRecordList())) {
            List<HsaRechargeGiftAmountRecord> rechargeGiftAmountRecords = Lists.newArrayList();
            final List<RechargeGiftAmountRecordVO> rechargeGiftAmountRecordList = rechargeOrderGiftSummaryVO.getRechargeGiftAmountRecordList();
            final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaRechargeGiftAmountRecord.class.getSimpleName(), rechargeGiftAmountRecordList.size());
            int i = 0;
            for (RechargeGiftAmountRecordVO rechargeGiftAmountRecordVO : rechargeGiftAmountRecordList) {
                HsaRechargeGiftAmountRecord recordPO = new HsaRechargeGiftAmountRecord();

                recordPO.setGuid(guids.get(i++));
                recordPO.setGiftAmount(rechargeGiftAmountRecordVO.getGiftAmount());
                recordPO.setRechargeActivityGuid(rechargeGiftAmountRecordVO.getRechargeActivityGuid());
                recordPO.setRechargeActivityName(rechargeGiftAmountRecordVO.getRechargeActivityName());

                recordPO.setGmtCreate(hsaMemberConsumption.getGmtCreate());
                recordPO.setGmtModified(hsaMemberConsumption.getGmtModified());

                recordPO.setRechargeStatus(1)
                        .setPayName(hsaMemberConsumptionPayWay.getPayName())
                        .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                        .setCardName(hsaMemberInfoCard.getCardName())
                        .setCardGuid(hsaMemberInfoCard.getCardGuid())
                        .setChangeSource(ThreadLocalCache.getSource())
                        .setMemberFundingDetailGuid(hsaMemberFundingDetail.getGuid())
                        .setOrderNumber(hsaMemberConsumption.getOrderNumber())
                        .setRechargeAmount(hsaMemberFundingDetail.getRechargeAmount())
                        .setMemberInfoCardGuid(hsaMemberFundingDetail.getMemberInfoCardGuid())
                        .setMemberInfoGuid(hsaMemberFundingDetail.getMemberInfoGuid())

                        .setStoreGuid(hsaMemberConsumption.getStoreGuid())
                        .setStoreName(hsaMemberConsumption.getStoreName());

                if (Objects.nonNull(hsaOperationMemberInfo)) {
                    recordPO.setMemberPhone(hsaOperationMemberInfo.getPhoneNum())
                            .setMemberName(hsaOperationMemberInfo.getUserName());
                }

                if (StringUtils.isNotBlank(hsaMemberInfoCard.getElectronicCardNum())) {
                    recordPO.setCardNum(hsaMemberInfoCard.getElectronicCardNum());
                } else {
                    recordPO.setCardNum(hsaMemberInfoCard.getPhysicalCardNum());
                }
                rechargeGiftAmountRecords.add(recordPO);
            }
            if (CollUtil.isNotEmpty(rechargeGiftAmountRecords)) {
                hsaRechargeGiftAmountRecordService.saveBatch(rechargeGiftAmountRecords);
            }
        }
    }


    @Override
    public Pair<HsaOperationMemberInfo, Long> parameterVerify(ProducePhysicalCardQO producePhysicalCardQO) {
        Optional<ProducePhysicalCardQO> physicalCardQO = Optional.ofNullable(producePhysicalCardQO);
        //通过cardGuid查询对应会员卡信息
        String cardGuid = physicalCardQO.map(ProducePhysicalCardQO::getCardGuid).orElse("-220");
        CardInfoDetailDTO availableEntityCard = hsaCardInfoMapper.getAvailableEntityCard(cardGuid);
        if (ObjectUtil.isNull(availableEntityCard)) {
            //会员卡不支持生成实体卡，请重新选择
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_PLEASE_SELECT_AGAIN);
        }
        //渠道不支持
        if (!availableEntityCard.canOpenPhysicalCardChannel()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_OPEN_PHYSICAL_CARD_CHANNEL);
        }
        //通过memberPhone 查询会员信息
        String memberPhone = physicalCardQO.map(ProducePhysicalCardQO::getMemberPhone).orElse("");
        HsaOperationMemberInfo hsaOperationMemberInfo = null;
        if (StringUtils.isNotBlank(memberPhone)) {
            HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(
                    new LambdaQueryWrapper<HsaPhysicalCard>()
                            .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaPhysicalCard::getCardGuid, cardGuid)
                            .eq(HsaPhysicalCard::getPhoneNum, memberPhone));
            if (ObjectUtil.isNotNull(hsaPhysicalCard)) {  //手机号已存在此会员卡，不可重复绑定
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_PHONE_REPETITION);
            }

            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberInfoCard>()
                            .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaMemberInfoCard::getCardGuid, cardGuid)
                            .eq(HsaMemberInfoCard::getMemberPhoneNum, memberPhone)
                            .isNotNull(HsaMemberInfoCard::getPhysicalCardGuid));
            if (ObjectUtil.isNotNull(hsaMemberInfoCard)) {  //手机号已存在此会员卡，不可重复绑定
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_PHONE_REPETITION);
            }
            //查询会员信息
            hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(
                    new LambdaQueryWrapper<HsaOperationMemberInfo>()
                            .eq(HsaOperationMemberInfo::getPhoneNum, memberPhone)
                            .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            if (ObjectUtil.isNull(hsaOperationMemberInfo)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_USER_NOT_REGISTERED);
            }
            //会员账户已被禁用，请联系管理员
            if (EnableEnum.NOT_ENABLE.getCode() == hsaOperationMemberInfo.getAccountState()) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getDes(), ThreadLocalCache.getOperSubjectGuid()));
            }
        }

        return new Pair<>(hsaOperationMemberInfo, availableEntityCard.getOpenPhysicalCardStrategyGuid());
    }

    @Override
    public HsaMemberInfoCard getHsaMemberInfoCard(TerMemberCardRechargeQO terMemberCardRechargeQO, HeaderUserInfo headerUserInfo) {
        HsaMemberInfoCard hsaMemberInfoCard;
        if (StringUtils.isNotBlank(terMemberCardRechargeQO.getMemberInfoCardGuid())) {
            hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(terMemberCardRechargeQO.getMemberInfoCardGuid());
        } else {
            if (terMemberCardRechargeQO.getCardType() == CardTypeEnum.CARD_TYPE_EQUITY.getCode()) {
                hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getElectronicCardNum, terMemberCardRechargeQO.getCardNum())
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
            } else {
                hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getPhysicalCardNum, terMemberCardRechargeQO.getCardNum())
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
            }
        }
        if (ObjectUtil.isNull(hsaMemberInfoCard)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        //卡信息校验
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        checkMemberInfoCard(hsaMemberInfoCard, terMemberCardRechargeQO.getCardType(), hsaCardBaseInfo, hsaOperationMemberInfo);
        if (hsaCardBaseInfo.getIsPreStored() == BooleanEnum.FALSE.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.UN_SUPPORT_RECHARGE, ThreadLocalCache.getOperSubjectGuid()));
        }
        return hsaMemberInfoCard;
    }

    public void checkMemberInfoCard(HsaMemberInfoCard hsaMemberInfoCard,
                                    Integer cardType,
                                    HsaCardBaseInfo hsaCardBaseInfo,
                                    HsaOperationMemberInfo hsaOperationMemberInfo) {
        checkCardIsNull(hsaMemberInfoCard, hsaOperationMemberInfo);

        checkCardValidity(hsaMemberInfoCard, hsaCardBaseInfo);
        //判断会员卡是否激活
        checkPhysicalCardState(hsaMemberInfoCard, cardType);

        checkElectronicCardState(hsaMemberInfoCard, cardType);
    }

    @Override
    public BigDecimal checkBalanceAmount(BigDecimal cardBalancePayAmount, HsaMemberInfoCard memberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        BigDecimal total = memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount().add(memberInfoCard.getSubsidyAmount()));
        if (total.compareTo(cardBalancePayAmount) < 0) {
            if ((ThreadLocalCache.getHeaderUserInfo().getSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()
                    || SourceTypeEnum.getMallSource().contains(ThreadLocalCache.getHeaderUserInfo().getSource()))
                    && hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
                return checkMemberInfoCard(memberInfoCard, hsaCardBaseInfo, cardBalancePayAmount, total);
            } else {
                return cardBalancePayAmount.subtract(total);
            }
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void subsidyRecordAmountAdd(HsaMemberInfoCard hsaMemberInfoCard, BigDecimal subsidyAmount) {
        List<HsaMemberFundingDetail> hsaMemberFundingDetailList = hsaMemberFundingDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberFundingDetail>()
                .eq(HsaMemberFundingDetail::getMemberInfoGuid, hsaMemberInfoCard.getMemberInfoGuid())
                .eq(HsaMemberFundingDetail::getAmountSubsidyFundingType, 0)
                .eq(HsaMemberFundingDetail::getAmountSourceType, AmountSourceTypeEnum.SUBSIDY_GRANT.getCode())
                .eq(HsaMemberFundingDetail::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid()));
        //需要更新的补贴明细记录剩余金额
        List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Lists.newArrayList();
        if (CollUtil.isNotEmpty(hsaMemberFundingDetailList)) {
            //无过期时间
            List<HsaMemberFundingDetail> permanentMemberFundingDetailList = Lists.newArrayList();
            //有效期限
            List<HsaMemberFundingDetail> deadlineMemberFundingDetailList = Lists.newArrayList();
            //分类补贴金
            froMemberFundingDetailList(hsaMemberFundingDetailList, deadlineMemberFundingDetailList, permanentMemberFundingDetailList);
            //获取补贴明细记录Guid
            Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap = getStringHsaSubsidyActivityDetailRecordMap(BooleanEnum.FALSE.getCode(), hsaMemberFundingDetailList);
            if (CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecordMap)) {
                dealSubsidyActivityDetailRecordMap(hsaMemberInfoCard, subsidyAmount, deadlineMemberFundingDetailList, hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, permanentMemberFundingDetailList);
            } else {
                hsaMemberInfoCard.setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().add(subsidyAmount));
            }
        } else {
            hsaMemberInfoCard.setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().add(subsidyAmount));
        }
    }

    @Override
    public OrderPartBackDTO getOrderPartBackAmountDTO(HsaMemberConsumption memberConsumption, HsaMemberFundingDetail memberFundingDetail) {
        //计算部分退款金额
        List<HsaMemberConsumption> hsaMemberConsumptionList = hsaMemberConsumptionMapper
                .selectList(new LambdaQueryWrapper<HsaMemberConsumption>()
                        .eq(HsaMemberConsumption::getOrderNumber, memberConsumption.getOrderNumber())
                        .eq(HsaMemberConsumption::getIsCancel, BooleanEnum.TRUE.getCode()));

        OrderPartBackDTO orderPartBackDTO = new OrderPartBackDTO();
        orderPartBackDTO.setGiftPartBackAmount(memberFundingDetail.getGiftAmount());
        orderPartBackDTO.setSubsidyPartBackAmount(memberFundingDetail.getSubsidyAmount());
        orderPartBackDTO.setRechargePartBackAmount(memberFundingDetail.getRechargeAmount());

        if (CollUtil.isNotEmpty(hsaMemberConsumptionList)) {
            BigDecimal rechargePartBackAmount = orderPartBackDTO.getRechargePartBackAmount();

            BigDecimal giftPartBackAmount = orderPartBackDTO.getGiftPartBackAmount();

            BigDecimal subsidyPartBackAmount = orderPartBackDTO.getSubsidyPartBackAmount();

            List<String> memberConsumptionGuids = hsaMemberConsumptionList.stream().map(HsaMemberConsumption::getGuid).collect(Collectors.toList());

            List<HsaMemberFundingDetail> hsaMemberFundingDetailList = hsaMemberFundingDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberFundingDetail>()
                    .in(HsaMemberFundingDetail::getMemberConsumptionGuid, memberConsumptionGuids));

            //实退实充
            BigDecimal rechargeAmountSum = hsaMemberFundingDetailList.stream()
                    .map(HsaMemberFundingDetail::getRechargeAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            rechargePartBackAmount = rechargePartBackAmount.subtract(rechargeAmountSum);
            orderPartBackDTO.setRechargePartBackAmount(rechargePartBackAmount);

            //实退赠送
            BigDecimal giftAmountSum = hsaMemberFundingDetailList.stream()
                    .map(HsaMemberFundingDetail::getGiftAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            giftPartBackAmount = giftPartBackAmount.subtract(giftAmountSum);
            orderPartBackDTO.setGiftPartBackAmount(giftPartBackAmount);

            //实退补贴
            BigDecimal subsidyAmountSum = hsaMemberFundingDetailList.stream()
                    .map(HsaMemberFundingDetail::getSubsidyAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            subsidyPartBackAmount = subsidyPartBackAmount.subtract(subsidyAmountSum);
            orderPartBackDTO.setSubsidyPartBackAmount(subsidyPartBackAmount);
        }
        return orderPartBackDTO;
    }

    private void dealSubsidyActivityDetailRecordMap(HsaMemberInfoCard hsaMemberInfoCard, BigDecimal subsidyAmount, List<HsaMemberFundingDetail> deadlineMemberFundingDetailList, List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords, Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap, List<HsaMemberFundingDetail> permanentMemberFundingDetailList) {
        SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO = new SubsidyAbatementBalanceDTO();
        subsidyAbatementBalanceDTO.setSubsidyAmount(subsidyAmount);
        if (CollUtil.isNotEmpty(deadlineMemberFundingDetailList)) {
            deadlineMemberFundingDetailList = deadlineMemberFundingDetailList
                    .stream()
                    .sorted(Comparator.comparing(HsaMemberFundingDetail::getOutOfDate))
                    .collect(Collectors.toList());
            //遍历依次增加
            checkSubsidyMoneyAdd(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, deadlineMemberFundingDetailList, subsidyAbatementBalanceDTO);
        }
        //永久有效的补贴明细记录
        //若有效期时间的补贴记录扣减不够则永久有效的资金继续扣减
        if (CollUtil.isNotEmpty(permanentMemberFundingDetailList) && subsidyAmount.compareTo(BigDecimal.ZERO) > 0) {
            checkSubsidyMoneyAdd(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, permanentMemberFundingDetailList, subsidyAbatementBalanceDTO);
        }

        //若可退金额大于实退金额  则直接增加实退金额
        if (subsidyAbatementBalanceDTO.getSubsidyAmountBack().compareTo(subsidyAmount) >= 0) {
            subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAmount);
        }
        //持卡数据可退补贴金额实际扣减
        hsaMemberInfoCard.setRetreatSubsidyAmount(hsaMemberInfoCard.getRetreatSubsidyAmount().add(subsidyAbatementBalanceDTO.getSubsidyAmountBack()));
        //持卡数据补贴金额实际扣减
        hsaMemberInfoCard.setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().add(subsidyAmount));
        //持久化
        if (CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecords)) {
            hsaSubsidyActivityDetailRecords.forEach(hsaSubsidyActivityDetailRecordService::updateByGuid);
        }
    }

    /**
     * 计算补贴明细记录增加金额
     */
    private void checkSubsidyMoneyAdd(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                      Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap,
                                      List<HsaMemberFundingDetail> hsaMemberFundingDetailPerpetual,
                                      SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO) {
        //补贴可退金额
        BigDecimal subsidyAmountBack = subsidyAbatementBalanceDTO.getSubsidyAmountBack();
        //补贴不可退金额
        BigDecimal subsidyAmountNotBack = subsidyAbatementBalanceDTO.getSubsidyAmountNotBack();
        //增加金额
        BigDecimal subsidyAmountAdd = subsidyAbatementBalanceDTO.getSubsidyAmount();
        for (HsaMemberFundingDetail hsaMemberFundingDetail : hsaMemberFundingDetailPerpetual) {
            if (isaBoolean(hsaSubsidyActivityDetailRecordMap, subsidyAbatementBalanceDTO, hsaMemberFundingDetail)) {
                HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = hsaSubsidyActivityDetailRecordMap.get(hsaMemberFundingDetail.getSubsidyDetailRecordGuid());
                //相差额
                BigDecimal discrepancyAmount = hsaSubsidyActivityDetailRecord.getSubsidyMoney().subtract(hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney());

                //表示使用过此补贴金额  需要冲正的金额
                if (discrepancyAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //判断本次是否可以冲正金额
                    BigDecimal subtractAmount = discrepancyAmount.subtract(subsidyAmountAdd);
                    if (subtractAmount.compareTo(BigDecimal.ZERO) >= 0) {
                        greaterSubsidy(subsidyAbatementBalanceDTO, hsaSubsidyActivityDetailRecord, subsidyAmountAdd, subsidyAmountNotBack);

                        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney().add(subsidyAmountAdd));
                        hsaSubsidyActivityDetailRecords.add(hsaSubsidyActivityDetailRecord);
                        subsidyAmountAdd = BigDecimal.ZERO;
                        break;
                    } else {
                        //此逻辑 说明回退金额大于本补贴明细 冲正金额  因此可以将本此补贴明细金额冲正  并记录剩余补贴 用于下次补贴明细剩余冲正
                        subsidyAmountAdd = subsidyAmountAdd.subtract(discrepancyAmount);
                        subsidyAmountBack = getSubsidyAmountBack(subsidyAbatementBalanceDTO, hsaSubsidyActivityDetailRecord, subsidyAmountBack, discrepancyAmount);

                        subsidyAmountNotBack = getAmountNotBack(subsidyAbatementBalanceDTO, hsaSubsidyActivityDetailRecord, subsidyAmountNotBack, subsidyAmountAdd);


                        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney().add(discrepancyAmount));
                        hsaSubsidyActivityDetailRecords.add(hsaSubsidyActivityDetailRecord);
                    }
                }
            }
        }
        subsidyAbatementBalanceDTO.setSubsidyAmount(subsidyAmountAdd);
        subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAmountBack);
        subsidyAbatementBalanceDTO.setSubsidyAmountNotBack(subsidyAmountNotBack);
    }


    private static BigDecimal getAmountNotBack(SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO,
                                               HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord,
                                               BigDecimal subsidyAmountNotBack,
                                               BigDecimal subsidyAmountAdd) {
        if (hsaSubsidyActivityDetailRecord.getIsRetreat() == 0) {
            subsidyAmountNotBack = subsidyAmountNotBack.add(subsidyAmountAdd);
            subsidyAbatementBalanceDTO.setSubsidyAmountNotBack(subsidyAbatementBalanceDTO.getSubsidyAmountNotBack().add(subsidyAmountNotBack));
        }
        return subsidyAmountNotBack;
    }

    private static BigDecimal getSubsidyAmountBack(SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO,
                                                   HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord,
                                                   BigDecimal subsidyAmountBack,
                                                   BigDecimal discrepancyAmount) {
        if (hsaSubsidyActivityDetailRecord.getIsRetreat() == 1) {
            subsidyAmountBack = subsidyAmountBack.add(discrepancyAmount);
            subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAbatementBalanceDTO.getSubsidyAmountBack().add(subsidyAmountBack));
        }
        return subsidyAmountBack;
    }

    private static void greaterSubsidy(SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO, HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord, BigDecimal subsidyAmountAdd, BigDecimal subsidyAmountNotBack) {
        if (hsaSubsidyActivityDetailRecord.getIsRetreat() == 1) {
            subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAbatementBalanceDTO.getSubsidyAmountBack().add(subsidyAmountAdd));
        } else {
            subsidyAmountNotBack = subsidyAmountNotBack.add(subsidyAmountAdd);
            subsidyAbatementBalanceDTO.setSubsidyAmountNotBack(subsidyAbatementBalanceDTO.getSubsidyAmountNotBack().add(subsidyAmountNotBack));
        }
    }

    private static boolean isaBoolean(Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap, SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO, HsaMemberFundingDetail hsaMemberFundingDetail) {
        return CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecordMap)
                && hsaSubsidyActivityDetailRecordMap.containsKey(hsaMemberFundingDetail.getSubsidyDetailRecordGuid())
                && subsidyAbatementBalanceDTO.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取补贴明细
     *
     * @param type                            1 消费 0 回退
     * @param deadlineMemberFundingDetailList
     * @return
     */
    private Map<String, HsaSubsidyActivityDetailRecord> getStringHsaSubsidyActivityDetailRecordMap(Integer type, List<HsaMemberFundingDetail> deadlineMemberFundingDetailList) {
        Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap = new HashMap<>();
        List<String> subsidyDetailRecordGuidList = deadlineMemberFundingDetailList
                .stream()
                .map(HsaMemberFundingDetail::getSubsidyDetailRecordGuid)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(subsidyDetailRecordGuidList)) {
            return hsaSubsidyActivityDetailRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityDetailRecord>()
                            .in(HsaSubsidyActivityDetailRecord::getGuid, subsidyDetailRecordGuidList)
                            .gt(type == BooleanEnum.TRUE.getCode(), HsaSubsidyActivityDetailRecord::getSubsidyResidueMoney, BigDecimal.ZERO)
                            .eq(HsaSubsidyActivityDetailRecord::getIsWithdraw, BooleanEnum.FALSE.getCode()))
                    .stream()
                    .collect(Collectors.toMap(HsaSubsidyActivityDetailRecord::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaSubsidyActivityDetailRecordMap;
    }

    private static void froMemberFundingDetailList(List<HsaMemberFundingDetail> hsaMemberFundingDetailList, List<HsaMemberFundingDetail> deadlineMemberFundingDetailList, List<HsaMemberFundingDetail> permanentMemberFundingDetailList) {
        for (HsaMemberFundingDetail in : hsaMemberFundingDetailList) {
            if (in.getAmountSourceType() == AmountSourceTypeEnum.SUBSIDY_GRANT.getCode() && Objects.nonNull(in.getOutOfDate())) {
                deadlineMemberFundingDetailList.add(in);
            } else {
                permanentMemberFundingDetailList.add(in);
            }
        }
    }

    private BigDecimal checkMemberInfoCard(HsaMemberInfoCard memberInfoCard,
                                           HsaCardBaseInfo hsaCardBaseInfo,
                                           BigDecimal cardBalancePayAmount,
                                           BigDecimal total) {
        if (hsaCardBaseInfo.getExcessType() == 0 && memberInfoCard.getExcessTimes() <= 0) {
            return cardBalancePayAmount.subtract(total);
        } else if (hsaCardBaseInfo.getExcessType() == 1) {
            return checkBalancePayAmount(memberInfoCard, cardBalancePayAmount, total);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal checkBalancePayAmount(HsaMemberInfoCard memberInfoCard, BigDecimal cardBalancePayAmount, BigDecimal total) {
        if (memberInfoCard.getExcessAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return cardBalancePayAmount.subtract(total);
        }
        if (memberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0) {
            BigDecimal amountNum = memberInfoCard.getExcessAmount().add(memberInfoCard.getSubsidyAmount().add(memberInfoCard.getGiftAmount()));
            if (amountNum.subtract(cardBalancePayAmount).compareTo(BigDecimal.ZERO) < 0) {
                return cardBalancePayAmount.subtract(amountNum);
            }
        } else {
            if (memberInfoCard.getExcessAmount().add(total).subtract(cardBalancePayAmount).compareTo(BigDecimal.ZERO) < 0) {
                return cardBalancePayAmount.subtract(memberInfoCard.getExcessAmount().add(total));
            }
        }
        return BigDecimal.ZERO;
    }

    private void checkPhysicalCardState(HsaMemberInfoCard hsaMemberInfoCard, Integer cardType) {
        if (cardType == CardTypeEnum.CARD_TYPE_MAIN.getCode()) {
            if (hsaMemberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.NOT_ACTIVATE.getCode()) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_NOT_ACTIVATE, ThreadLocalCache.getOperSubjectGuid()));
            }
            if (hsaMemberInfoCard.getPhysicalCardState() == 3 || hsaMemberInfoCard.getPhysicalCardState() == 0) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_FREEZE, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
    }

    private void checkElectronicCardState(HsaMemberInfoCard hsaMemberInfoCard, Integer cardType) {
        if (cardType == CardTypeEnum.CARD_TYPE_EQUITY.getCode()) {
            if (StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardGuid())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_NOT_ACTIVATE, ThreadLocalCache.getOperSubjectGuid()));
            }
            if (hsaMemberInfoCard.getElectronicCardState() == 3 || hsaMemberInfoCard.getElectronicCardState() == 0) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_FREEZE, ThreadLocalCache.getOperSubjectGuid()));

            }
        }
    }

    private void checkCardValidity(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        //判断会员卡是否禁用
        if (Objects.isNull(hsaCardBaseInfo) || hsaCardBaseInfo.getCardStatus() != CardStatusEnum.ENABLE.getCode()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_PAST_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
        }
        //判断会员卡是否过期
        if (hsaMemberInfoCard.getCardValidity() == 1 && LocalDate.now().isAfter(hsaMemberInfoCard.getCardValidityDate())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_NOT_PAST, ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    private void checkCardIsNull(HsaMemberInfoCard hsaMemberInfoCard, HsaOperationMemberInfo hsaOperationMemberInfo) {
        //判断会员卡是否存在
        if (ObjectUtil.isNull(hsaMemberInfoCard)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST
                    , hsaOperationMemberInfo.getOperSubjectGuid()));
        }
        //判断当前会员是否禁用
        if (Objects.nonNull(hsaOperationMemberInfo) && hsaOperationMemberInfo.getAccountState() == 1) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getDes(), ThreadLocalCache.getOperSubjectGuid()));

        }
    }

    private void setIsCheckPasswordAndCardState(HsaCardBalanceRule hsaCardBalanceRule,
                                                HsaCardBaseInfo hsaCardBaseInfo,
                                                TerLoginMemberCardVO vo,
                                                TerCheckMemberCardQO terCheckMemberCardQO) {
        // 优先级 禁用<-冻结<-正常
        if (hsaCardBaseInfo.getCardStatus() == CardStatusEnum.FORBIDDEN.getCode()) {
            vo.setCardState(4);
        }

        Integer isCheckPassword = getCheckPassword(hsaCardBalanceRule,
                hsaCardBaseInfo,
                terCheckMemberCardQO.getTerminalCheckStatus());

        vo.setIsCheckPassword(isCheckPassword);
    }

    @Override
    public Integer getCheckPassword(HsaCardBalanceRule hsaCardBalanceRule,
                                    HsaCardBaseInfo hsaCardBaseInfo,
                                    Integer terminalStatus) {
        if (terminalStatus == TerminalCheckStatusEnum.PHONE_NUM.getCode()
                || terminalStatus == TerminalCheckStatusEnum.CARD_NUM_PAY.getCode()) {
            terminalStatus = TerminalCheckStatusEnum.PHONE_CARD_NUM_PAY.getCode();
        }

        log.info("当前登录方式={}", TerminalCheckStatusEnum.getPayName(terminalStatus));
        int isCheckPassword = BooleanEnum.FALSE.getCode();
        if (Objects.nonNull(hsaCardBalanceRule) && hsaCardBalanceRule.getUseCheck() == BooleanEnum.TRUE.getCode()) {
            String terminalCheckStatus = hsaCardBalanceRule.getTerminalCheckStatus();
            String[] terminalCheckStatusList = terminalCheckStatus.split(StringConstant.COMMA);
            List<String> list = Arrays.asList(terminalCheckStatusList);
            //校验经营终端校验状态
            boolean checkStatus = list.contains(terminalStatus + StringConstant.EMPTY);

            if (checkStatus) {
                isCheckPassword = BooleanEnum.TRUE.getCode();
            } else {
                isCheckPassword = BooleanEnum.FALSE.getCode();
            }
        }


        //若无密码  就无需校验
        if (StringUtils.isEmpty(hsaCardBaseInfo.getCardPayPassword())) {
            isCheckPassword = BooleanEnum.FALSE.getCode();
        }
        return isCheckPassword;
    }

    @Override
    public List<StoreCardRuleVO> getStoreCardRuleVOS(HsaMemberInfoCard hsaMemberInfoCard) {
        List<StoreCardRuleVO> saveStoreCardRuleQOList = Lists.newArrayList();
        List<HsaStoreCardRule> hsaStoreCardRules;
        if (hsaMemberInfoCard.getApplicableAllStore() == NumberConstant.NUMBER_1) {
            return saveStoreCardRuleQOList;
        }
        //门店级
        hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getCardGuid, hsaMemberInfoCard.getCardGuid())
                .eq(StringUtils.isNotBlank(hsaMemberInfoCard.getGuid()), HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isEmpty(hsaStoreCardRules)) {
            hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .in(HsaStoreCardRule::getCardGuid, hsaMemberInfoCard.getCardGuid())
                    .isNull(HsaStoreCardRule::getParentGuid));
        }

        if (CollUtil.isNotEmpty(hsaStoreCardRules)) {
            for (HsaStoreCardRule hsaStoreCardRule : hsaStoreCardRules) {
                StoreCardRuleVO storeCardRuleQO = new StoreCardRuleVO();
                storeCardRuleQO.setStoreGuid(String.valueOf(hsaStoreCardRule.getStoreGuid()));
                storeCardRuleQO.setStoreName(hsaStoreCardRule.getStoreName());
                storeCardRuleQO.setGuid(String.valueOf(hsaStoreCardRule.getGuid()));
                storeCardRuleQO.setCardGuid(String.valueOf(hsaMemberInfoCard.getCardGuid()));
                saveStoreCardRuleQOList.add(storeCardRuleQO);
            }
        }
        return saveStoreCardRuleQOList;
    }

    private static void checkNotActivate(HsaMemberInfoCard memberInfoCard, TerLoginMemberCardVO vo) {
        if (StringUtils.isNotBlank(memberInfoCard.getPhysicalCardNum()) &&
                StringUtils.isEmpty(memberInfoCard.getElectronicCardNum()) &&
                memberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.NOT_ACTIVATE.getCode()) {
            vo.setCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
        }
    }

    private boolean checkMemberCard(TerCheckMemberCardQO terCheckMemberCardQO,
                                    HsaMemberInfoCard memberInfoCard,
                                    HsaCardBaseInfo hsaCardBaseInfo,
                                    TerLoginMemberCardVO vo,
                                    Map<String, List<HsaStoreCardRule>> storeRuleGroupingMap) {
        if (terCheckMemberCardQO.getType() == NUMBER_0 && hsaCardBaseInfo.getIsPreStored() == NUMBER_0) {
            return true;
        }
        return checkNormalCard(hsaCardBaseInfo, vo.getCardState(), memberInfoCard, terCheckMemberCardQO.getStoreGuid(), storeRuleGroupingMap);
    }


    @Override
    public boolean checkNormalCard(HsaCardBaseInfo hsaCardBaseInfo,
                                   int state,
                                   HsaMemberInfoCard hsaMemberInfoCard,
                                   String storeGuid,
                                   Map<String, List<HsaStoreCardRule>> storeRuleGroupingMap) {
        if (hsaCardBaseInfo.getCardStatus() == CardStatusEnum.FORBIDDEN.getCode()) {
            return true;
        }

        if (state == PhysicalCardStateEnum.ALREADY_FROZEN.getCode() || state == PhysicalCardStateEnum.TO_ALREADY_FROZEN.getCode()) {
            return true;
        }

        //门店范围判断
        if (hsaMemberInfoCard.getApplicableAllStore() == NUMBER_0 && storeRuleGroupingMap.containsKey(hsaMemberInfoCard.getGuid())) {
            List<HsaStoreCardRule> hsaStoreCardRuleList = storeRuleGroupingMap.get(hsaMemberInfoCard.getGuid());
            if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
                List<String> storeGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
                if (!storeGuidList.contains(storeGuid)) {
                    return true;
                }
            }
        }

        //判断实体卡是否激活
        return StringUtils.isNotBlank(hsaMemberInfoCard.getPhysicalCardNum()) &&
                StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardNum()) &&
                hsaMemberInfoCard.getPhysicalCardState() == PhysicalCardStateEnum.NOT_ACTIVATE.getCode();
    }

    @Override
    public SettlementBalanceDTO payByBalance(RequestConfirmPayVO requestConfirmPay, HsaMemberInfoCard memberInfoCard, HsaOperationMemberInfo hsaOperationMemberInfo) {
        return null;
    }

}
