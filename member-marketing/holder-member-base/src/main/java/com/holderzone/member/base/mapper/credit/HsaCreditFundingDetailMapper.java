package com.holderzone.member.base.mapper.credit;

import com.holderzone.member.base.entity.credit.HsaCreditFundingDetail;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.credit.CreditFundingInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 挂账资金来往明细mapper
 * <AUTHOR>
 */
@Mapper
public interface HsaCreditFundingDetailMapper extends HolderBaseMapper<HsaCreditFundingDetail> {

    /**
     * 获取当前用户挂账明细信息
     * @param memberInfoGuid 会员guid
     * @param creditInfoGuid 挂账账户guid
     * @return 操作结果
     */
    List<CreditFundingInfoVO> creditFundingDetail(@Param("memberInfoGuid") String memberInfoGuid,
                                                  @Param("creditInfoGuid") String creditInfoGuid);

    void updateClearingStatus(@Param("clearingStatementNumber") String clearingStatementNumber);

}
