package com.holderzone.member.base.mapper.card;

import com.holderzone.member.common.dto.card.CardInfoDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


public interface HsaCardInfoMapper{


    CardInfoDetailDTO getCardInfoDetail(@Param("cardGuid") String cardGuid);

    CardInfoDetailDTO getAvailableEntityCard(@Param("cardGuid") String cardGuid);

    List<CardInfoDetailDTO> getAvailableElectronicCard(@Param("cardGuidList")List<String> cardGuidList);

    CardInfoDetailDTO getSelfFreeOpenCard(@Param("cardGuid") String cardGuid);

    List<CardInfoDetailDTO> listCardInfoDetail(@Param("cardGuidList")List<String> cardGuidList);


    List<CardInfoDetailDTO> getCardInfoDetailList(@Param("cardGuidList") Set<String> cardGuidList);
}
