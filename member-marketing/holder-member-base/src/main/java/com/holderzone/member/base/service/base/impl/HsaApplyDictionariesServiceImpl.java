package com.holderzone.member.base.service.base.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.entity.equities.HsaApplyDictionaries;
import com.holderzone.member.base.mapper.equities.HsaApplyDictionariesMapper;
import com.holderzone.member.base.service.base.HsaApplyDictionariesService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.equities.ApplyModuleEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.base.ApplyBusinesQO;
import com.holderzone.member.common.qo.base.CallbackApplyBusinesQO;
import com.holderzone.member.common.qo.tool.CheckContentQO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.equities.ApplyModuleVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.feign.CrmFeignVo;
import com.holderzone.member.common.vo.tool.CheckContentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaApplyDictionariesServiceImpl implements HsaApplyDictionariesService {

    @Resource
    private HsaApplyDictionariesMapper hsaApplyDictionariesMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private CrmFeign crmFeign;

    @Override
    public List<ApplyTypeVO> getApplyBusiness(Boolean isFilterSystem) {
        List<HsaApplyDictionaries> list;
        if (isFilterSystem) {
            list = hsaApplyDictionariesMapper.selectList(
                    new LambdaQueryWrapper<HsaApplyDictionaries>()
                            .eq(HsaApplyDictionaries::getSystem, ThreadLocalCache.getSystem())
            );
        }else{
            list = hsaApplyDictionariesMapper.selectList(new LambdaQueryWrapper<HsaApplyDictionaries>().eq(HsaApplyDictionaries::getIsShow, NumberConstant.NUMBER_1));
        }
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<HsaApplyDictionaries> hsaApplyDictionaries = list.stream()
                .filter(x -> ApplyModuleEnum.APPLY_BUSINESS.getType().equals(x.getModule()))
                .collect(Collectors.toList());
        List<ApplyTypeVO> applyTypeList = new ArrayList<>();
        for (HsaApplyDictionaries hsaApplyDictionary : hsaApplyDictionaries) {
            ApplyTypeVO applyTypeVO = new ApplyTypeVO();
            applyTypeVO.setType(hsaApplyDictionary.getType());
            applyTypeVO.setTypeName(systemRoleHelper.getReplace(hsaApplyDictionary.getTypeName(), ThreadLocalCache.getOperSubjectGuid()));
            applyTypeVO.setModule(hsaApplyDictionary.getModule());
            applyTypeVO.setChannel(hsaApplyDictionary.getChannel());
            applyTypeList.add(applyTypeVO);
        }
        return applyTypeList;
    }

    @Override
    public Boolean callbackApplyBusiness(CallbackApplyBusinesQO request) {
        String type = request.getType();
        //  type存在回调走删除  不存在走新增
        if (!StringUtils.isEmpty(type)) {
            return hsaApplyDictionariesMapper.delete(new LambdaQueryWrapper<HsaApplyDictionaries>()
                    .eq(HsaApplyDictionaries::getModule, ApplyModuleEnum.APPLY_BUSINESS.getType())
                    .eq(HsaApplyDictionaries::getType, type)) > 0;
        }
        if (CollUtil.isEmpty(request.getApplyBusiness())) {
            return Boolean.FALSE;
        }
        LocalDateTime now = LocalDateTime.now();
        for (ApplyBusinesQO business : request.getApplyBusiness()) {
            HsaApplyDictionaries applyDictionaries = new HsaApplyDictionaries();
            applyDictionaries.setModule(ApplyModuleEnum.APPLY_BUSINESS.getType());
            applyDictionaries.setType(business.getType());
            applyDictionaries.setTypeName(systemRoleHelper.getReplace(business.getTypeName(), ThreadLocalCache.getOperSubjectGuid()));
            applyDictionaries.setGmtCreate(now);
            applyDictionaries.setGmtModified(now);
            hsaApplyDictionariesMapper.insert(applyDictionaries);
        }
        return Boolean.TRUE;
    }

    @Override
    public void checkKeyWords(String content) {
        try {
            CheckContentQO checkContentQO = new CheckContentQO();
            checkContentQO.setSearch_word(content);
            CrmFeignVo<CheckContentVO> checkKeyWords = crmFeign.checkKeyWords(checkContentQO);
            log.info("校验关键词结果：{},关键词：{}", content, JSON.toJSONString(checkKeyWords));
            if (Objects.nonNull(checkKeyWords.getData()) && CollUtil.isNotEmpty(checkKeyWords.getData().getKey_list())) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CONTENT_REQUEST);
            }
        } catch (Exception e) {
            if (log != null
                    && Objects.nonNull(e.getMessage())
                    && e.getMessage().contains(MemberAccountExceptionEnum.ERROR_CONTENT_REQUEST.getDes())) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CONTENT_REQUEST);
            }
        }
    }

    @Override
    public ApplyModuleVO getApplyModule () {
        // 查询需要显示的模块
        List<HsaApplyDictionaries> list = hsaApplyDictionariesMapper.selectList(new LambdaQueryWrapper<HsaApplyDictionaries>().eq(HsaApplyDictionaries::getIsShow, NumberConstant.NUMBER_1));

        return getApplyModuleVO(list, systemRoleHelper);

    }

    public static ApplyModuleVO getApplyModuleVO (List<HsaApplyDictionaries> list, SystemRoleHelper systemRoleHelper) {
        List<ApplyTypeVO> applyTypeVOS = new ArrayList<>();
        for (HsaApplyDictionaries hsaApplyDictionaries : list) {
            ApplyTypeVO applyTypeVO = new ApplyTypeVO();
            applyTypeVO.setType(hsaApplyDictionaries.getType());
            applyTypeVO.setTypeName(systemRoleHelper.getReplace(hsaApplyDictionaries.getTypeName(), ThreadLocalCache.getOperSubjectGuid()));
            applyTypeVO.setModule(hsaApplyDictionaries.getModule());
            applyTypeVO.setChannel(hsaApplyDictionaries.getChannel());
            applyTypeVOS.add(applyTypeVO);
        }
        Map<String, List<ApplyTypeVO>> map = applyTypeVOS.stream()
                                                     .collect(Collectors.groupingBy(ApplyTypeVO::getModule));
        ApplyModuleVO applyModuleVO = new ApplyModuleVO();
        applyModuleVO.setApplyBusinessList(map.get(ApplyModuleEnum.APPLY_BUSINESS.getType()));
        applyModuleVO.setApplyChannelList(map.get(ApplyModuleEnum.APPLY_CHANNEL.getType()));
        applyModuleVO.setApplyTerminalList(map.get(ApplyModuleEnum.APPLY_TERMINAL.getType()));
        return applyModuleVO;
    }
}
