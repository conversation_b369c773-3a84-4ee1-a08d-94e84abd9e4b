package com.holderzone.member.base.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;

/**
 * 文件大小限制
 * <AUTHOR>
 * @date 2021/12/31
 **/
@Configuration
public class MultipartConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //允许上传的文件最大值 5m
        factory.setMaxFileSize(DataSize.ofBytes(5242880L * 3));
        /// 设置总上传数据总大小
//        factory.setMaxRequestSize("50MB");
        return factory.createMultipartConfig();
    }
}
