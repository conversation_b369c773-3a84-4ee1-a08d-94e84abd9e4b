package com.holderzone.member.base.mapper.grade;

import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.equities.MemberEquitiesRecordQO;
import com.holderzone.member.common.vo.grade.GradeEquitiesInfoVO;
import com.holderzone.member.common.vo.grade.GradeEquitiesVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: 成长权益mapper
 * @author: pan tao
 * @create: 2022-01-18 18:20
 */
public interface HsaBusinessEquitiesMapper extends HolderBaseMapper<HsaBusinessEquities> {

    int deleteByGradeInfoGuid(@Param("type") int type, @Param("gradeInfoGuid") String gradeInfoGuid);

    /**
     * 查询当前会员等级权益信息
     *
     * @param memberGradeGuid 等级guid
     * @return 权益信息
     */
    List<GradeEquitiesVO> getGradeEquitieInfo(@Param("memberGradeGuid") String memberGradeGuid);


    /**
     * 查询当前会员卡权益信息
     *
     * @param memberGradeGuid 等级guid
     * @return 权益信息
     */
    List<GradeEquitiesVO> getCardEquitieInfo(@Param("memberGradeGuid") String memberGradeGuid);

    /**
     * 批量查询当前会员卡权益信息
     *
     * @param memberGradeGuidList 等级guid列表
     * @return 权益信息
     */
    List<GradeEquitiesVO> batchCardEquitieInfo(@Param("memberGradeGuidList") List<String> memberGradeGuidList);

    /**
     * 查询当前会员等级权益名称（给会员等级页面使用的）
     * 其他地方使用需要注意，该方法查询只查询最新权益（isDelete = 0）
     *
     * @param memberGradeGuids 等级guids
     * @return 权益信息
     */
    List<String> getGradeEquitieName(@Param("memberGradeGuids") List<String> memberGradeGuids);

    /**
     * 查询当前会员等级权益名称
     *
     * @param memberGradeGuids 等级guids
     * @return 权益信息
     */
    List<GradeEquitiesInfoVO> getGradeEquitiesInfosByGradeGuids (@Param("memberGradeGuids") List<String> memberGradeGuids);

    /**
     * 查询当前会员历史权益
     *
     * @param request 请求参数
     * @return 权益信息
     */
    List<HsaBusinessEquities> getHistoryEquities(@Param("request") MemberEquitiesRecordQO request);

}
