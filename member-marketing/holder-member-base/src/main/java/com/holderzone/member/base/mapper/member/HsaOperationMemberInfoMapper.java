package com.holderzone.member.base.mapper.member;


import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.business.MemberSeqFiledDTO;
import com.holderzone.member.common.dto.card.MemberAccountDTO;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.label.LabelRechargeInfo;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.dto.member.DataItemDTO;
import com.holderzone.member.common.dto.partner.PartnerMemberBaseDTO;
import com.holderzone.member.common.qo.member.GradeAccountQO;
import com.holderzone.member.common.qo.member.MemberListExcelQO;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.vo.card.MemberPhoneMathVO;
import com.holderzone.member.common.vo.grade.GradeAccountListVO;
import com.holderzone.member.common.vo.grade.GradeLevelVO;
import com.holderzone.member.common.vo.grade.MemberGradeCountVO;
import com.holderzone.member.common.vo.grade.MemberGradeRelationVO;
import com.holderzone.member.common.vo.growth.GrowthLevelVO;
import com.holderzone.member.common.vo.member.*;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import com.holderzone.member.common.vo.member.MemberInfoExcelVO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.common.vo.member.MemberPhoneVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * <p>
 * 运营主体会员信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface HsaOperationMemberInfoMapper extends HolderBaseMapper<HsaOperationMemberInfo> {

    /**
     * 手机号后四位匹配
     */
    List<MemberPhoneMathVO> memberPhoneMatch(@Param("phoneNum") String phoneNum, @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 查询数量
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    int countMemberInfo(@Param("request") MemberListQO request);

    /**
     * 查询会员等级对应人数
     *
     * @param memberGradeInfoGuids 会员等级guid集合
     * @return 查询结果
     */
    List<MemberGradeCountVO> queryMemberFreeGradeCount (@Param("memberGradeInfoGuids") List<String> memberGradeInfoGuids);

    /**
     * 查询付费等级对应人数
     * @param memberGradeInfoGuids
     * @return
     */
    List<MemberGradeCountVO> queryMemberPaidGradeCount (@Param("memberGradeInfoGuids") List<String> memberGradeInfoGuids);

    /**
     * 查询会员等级对应会员Guid
     *
     * @param memberGradeInfoGuids 会员等级guid集合
     * @return 查询结果
     */
    List<String> queryMemberGrade(@Param("memberGradeInfoGuids") List<String> memberGradeInfoGuids);

    /**
     * 分页查询会员列表
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<String> listMemberInfoGuids(@Param("request") MemberListQO request);

    /**
     * 分页查询会员列表
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<MemberInfoVO> listMemberInfo(@Param("request") MemberListQO request);

    /**
     * 查询所有会员数量
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<MemberInfoVO> findAllMemberInfo(@Param("request") MemberListQO request);

    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    Set<String> queueSubjectGuid(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 功能描述：查询导入数据
     *
     * @param request 查询会员列表QO
     * @return java.util.List<com.holderzone.member.common.vo.member.MemberListQO>
     * @date 2021/8/31
     */
    List<MemberInfoExcelVO> findExcelMemberInfo(@Param("request") MemberListExcelQO request);


    List<MemberInfoExcelVO> findPageExcelMemberInfo(@Param("request") MemberListExcelQO request);

    /**
     * 功能描述：查询运营主体下所有会员
     *
     * @param operSubjectGuid 运营主体
     * @return java.util.List<java.lang.String>
     * @date 2021/8/31
     */
    List<String> findAllPhone(@Param("operSubjectGuid") String operSubjectGuid, @Param("phoneNums") List<String> phoneNums);

    /**
     * 获取guid
     *
     * @param operSubjectGuid
     * @return
     */
    List<String> findAllGuidByOperSubjectGuid(@Param("operSubjectGuid") String operSubjectGuid,
                                              @Param("roleType") String roleType);

    /**
     * 分段查询用户id
     *
     * @param pageIndex
     * @param pageNum
     * @param operSubjectGuid
     * @return
     */
    List<String> listByPage(@Param("pageIndex") int pageIndex, @Param("pageNum") int pageNum,
                            @Param("operSubjectGuid") String operSubjectGuid);


    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    List<MemberPhoneDTO> listByGuid(@Param("request") List<String> request);

    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    List<MemberPhoneDTO> listByPhone(@Param("request") List<String> request, @Param("operSubjectGuid") String operSubjectGuid);


    /**
     * 分批查询会员账户
     *
     * @return MemberPhoneDTO
     */
    List<MemberAccountDTO> listByAccountNum(@Param("request") List<String> request, @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 标签基础信息查询过滤
     *
     * @param query
     * @return
     */
    List<HsaOperationMemberInfo> memberLabelBaseFilter(@Param("query") RequestLabelQuery query);

    /**
     * 标签注册信息查询过滤
     *
     * @param query
     * @return
     */
    List<HsaOperationMemberInfo> memberLabelRegisterFilter(@Param("query") RequestLabelQuery query);


    /**
     * 标签身份信息查询过滤
     *
     * @param query
     * @return
     */
    List<HsaOperationMemberInfo> memberLabelEquityCardFilter(@Param("query") RequestLabelQuery query);

    /**
     * 标签充值信息查询过滤
     *
     * @param query 查询条件
     * @return 操作结果
     */
    List<LabelRechargeInfo> memberLabelRechargeInfoFilter(@Param("query") RequestLabelQuery query);

    /**
     * 查詢所有单位名称
     *
     * @param operSubjectGuid 运营主体guid
     * @param keywords        关键字
     * @return 查询结果
     */
    List<String> findAllCompanyName(@Param("operSubjectGuid") String operSubjectGuid, @Param("keywords") String keywords);


    /**
     * 查詢所有部门名称
     *
     * @param operSubjectGuid 运营主体guid
     * @param keywords        关键字
     * @return 结果
     */
    List<String> findAllDepartmentName(@Param("operSubjectGuid") String operSubjectGuid, @Param("keywords") String keywords);

    /**
     * 查詢所有职位名称
     *
     * @param operSubjectGuid 运营主体guid
     * @param keywords        关键字
     * @return 结果
     */
    List<String> findAllJobTitle(@Param("operSubjectGuid") String operSubjectGuid, @Param("keywords") String keywords);

    /**
     * 批量更新
     *
     * @param hsaOperationMemberInfos
     */
    void batchUpdateGrowth(@Param("list") List<HsaOperationMemberInfo> hsaOperationMemberInfos);

    /**
     * 批量更新会员积分
     *
     * @param hsaOperationMemberInfos
     */
    void batchUpdateIntegral(@Param("list") List<HsaOperationMemberInfo> hsaOperationMemberInfos);


    void updateIntegralAndPerfectTime(@Param("activity") HsaOperationMemberInfo activity);

    /**
     * 批量修改等级名称
     *
     * @param gradeName 等级名称
     * @param gradeGuid 等级guid
     */
    void batchUpdateGradeName(@Param("gradeInfoList") List<HsaMemberGradeInfo> gradeInfoList);

    /**
     * 获取等级账户列表
     *
     * @param request 筛选条件
     * @return 等级账户列表
     */
    List<GradeAccountListVO> getAccountList(@Param("request") GradeAccountQO request);

    /**
     * 获取等级账户列表
     *
     * @param request 筛选条件
     * @return 等级账户列表
     */
    List<GradeAccountListVO> getAccountListByPartner(@Param("request") GradeAccountQO request);

    /**
     * 获取选中
     *
     * @param request 筛选条件
     * @return 等级账户列表
     */
    List<GradeAccountListVO> getSelectedList(@Param("request") GradeAccountQO request);

    /**
     * 查询当前会员的等级信息
     *
     * @param operSubjectGuid 运营主体
     * @param memberInfoGuid  会员guid
     * @return 等级信息
     */
    GrowthLevelVO getMemberGradeInfo(@Param("operSubjectGuid") String operSubjectGuid,
                                     @Param("memberInfoGuid") String memberInfoGuid,
                                     @Param("roleType") String roleType);

    /**
     * 查询当前会员的付费等级信息
     *
     * @param operSubjectGuid 运营主体
     * @param memberInfoGuid  会员guid
     * @return 等级信息
     */
    GradeLevelVO getMemberPaidGradeInfo(@Param("operSubjectGuid") String operSubjectGuid,
                                    @Param("memberInfoGuid") String memberInfoGuid,
                                    @Param("roleType") String roleType);

    /**
     * 查询当前会员的免费等级信息
     * @param operSubjectGuid 运营主体
     * @param memberInfoGuid 会员guid
     * @param roleType 角色类型
     * @return 等级信息
     */
    GradeLevelVO getMemberFreeGradeInfo (@Param("operSubjectGuid") String operSubjectGuid,
                                         @Param("memberInfoGuid") String memberInfoGuid,
                                         @Param("roleType") String roleType);

    /**
     * 查询当前会员guid
     *
     * @param operSubjectGuid 运营主体
     * @param memberInfoGuid  会员guid
     * @return 等级信息
     */
    String getMemberGradeGuid(@Param("operSubjectGuid") String operSubjectGuid,
                              @Param("memberInfoGuid") String memberInfoGuid);

    /**
     * 通过关键字查询会员guid
     *
     * @param keywords        关键字
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    List<String> getMemberInfoGuids(@Param("keywords") String keywords, @Param("operSubjectGuid") String operSubjectGuid);


    /**
     * 查看会员手机相关信息列表
     *
     * @param guids           会员guids
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    List<MemberPhoneVO> getMemberPhoneList(@Param("guids") List<String> guids, @Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 获取当前运营主体下所有用户guid
     *
     * @param operSubjectGuid 运营主体
     * @return 用户guid集合
     */
    List<String> getMemberGuidBySubject(@Param("operSubjectGuid") String operSubjectGuid);

    Set<String> queryIntegralMember(@Param("request") RequestLabelQuery request);

    /**
     * 更新会员积分
     *
     * @param memberIntegral 新增积分
     * @param guid           会员guid
     */
    void addMemberIntegral(@Param("memberIntegral") Integer memberIntegral,
                           @Param("guid") String guid);


    /**
     * 更新会员积分
     *
     * @param memberIntegral 新增积分
     * @param guid           会员guid
     */
    void subtractMemberIntegral(@Param("memberIntegral") Integer memberIntegral,
                                @Param("guid") String guid);


    /**
     * 更新会员成长值
     *
     * @param memberGrowth 会员成长值
     * @param guid           会员guid
     */
    void subtractMemberGrowth(@Param("memberGrowth") Integer memberGrowth,
                              @Param("guid") String guid);

    /**
     * 修改会员等级
     */
    void updateMemberGrade(@Param("request") HsaOperationMemberInfo request);

    /**
     * 更新会员积分
     *
     * @param memberIntegral 减少积分
     * @param guid           会员guid
     */
    void reduceMemberIntegral(@Param("memberIntegral") Integer memberIntegral,
                              @Param("guid") String guid);


    /**
     * 通过会员guid查询手机号
     *
     * @param guid 会员guid
     * @return 手机号
     */
    String queryMemberPhone(@Param("guid") String guid);

    /**
     * 是否开启积分购物默认抵扣
     *
     * @param memberInfoGuid 会员guid
     * @param enabled        0：关闭   1：开启
     * @return 操作结果
     */
    Integer updateIntegralDeduction(@Param("memberInfoGuid") String memberInfoGuid,
                                    @Param("enabled") Integer enabled);

    /**
     * 获取会员积分商品抵扣状态
     *
     * @param memberInfoGuid 会员guid
     * @return 抵扣状态
     */
    Integer getIntegralDeduction(@Param("memberInfoGuid") String memberInfoGuid);

    HsaOperationMemberInfo getMemberInfoByKeyWord(@Param("operSubjectGuid") String operSubjectGuid,
                                                  @Param("keyWord") String keyWord);

    void updateBySeqFiled(@Param("seqFiled") MemberSeqFiledDTO seqFiled);

    List<PartnerMemberBaseDTO> listCircleMemberByGuidList(@Param("guidList") Set<String> circleMemberGuidList);

    List<PartnerMemberBaseDTO> listCircleMemberByIndustry(@Param("excludeGuid") String excludeGuid, @Param("operSubjectGuid") String operSubjectGuid, @Param("industryCodes") String industryCodes, @Param("guidList") List<String> memberGuidSet);

    List<PartnerMemberBaseDTO> listNonPartnerMember(@Param("operSubjectGuid") String operSubjectGuid, @Param("guidList") List<String> circleMemberGuidList);

    List<PartnerMemberBaseDTO> searchPartnerMember(@Param("searchList") Set<String> searchSet, @Param("operSubjectGuid") String operSubjectGuid, @Param("index") int index);

    void updateConsumptionTime(@Param("guid") String guid, @Param("time") LocalDateTime time);

    /**
     * 分页查询会员 一体机 count
     *
     * @return 查询结果
     */
    int terMemberInfoNum(@Param("request") TerFaceMemberListQO request);

    /**
     * 分页查询会员 一体机
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<TerFaceMemberInfoVO> terListMemberInfo(@Param("request") TerFaceMemberListQO request);

    /**
     * 根据运营主体查询会员guid
     *
     * @param request
     * @return
     */
    List<String> queueGuidByOperSubjectGuid(@Param("request") List<String> request);


    List<InitMemberPinyinListDTO> queueMemberPinyin(@Param("guidList") List<String> guidList);

    /**
     * 批量修改pinyin
     *
     * @param initMemberPinyinList
     */
    void updatePinyinByGuid(@Param("request") List<InitMemberPinyinListDTO> initMemberPinyinList);

    int batchCardUpdateGrowthValue(@Param("list") List<MemberGrowthValueRelationVO> list);

    int batchCardUpdateIntegralValue(@Param("list") List<MemberGrowthValueRelationVO> list);

    List<MemberGradeRelationVO> selectMemberGuidByGuid(@Param("memberInfoGuidList") Set<String> memberInfoGuidList);

    /**
     * 校验会员资料项
     */
    List<String> getUserDataItem(@Param("request") List<DataItemDTO> dataItemDTO);

    /**
     * 批量修改会员付费等级信息
     * @param memberInfoList
     */
    void batchUpdatePaidGradeInfo (@Param("list") List<HsaOperationMemberInfo> memberInfoList);
}
