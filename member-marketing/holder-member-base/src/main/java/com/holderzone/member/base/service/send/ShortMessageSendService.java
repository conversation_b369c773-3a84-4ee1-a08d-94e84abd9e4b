package com.holderzone.member.base.service.send;

import com.holderzone.member.common.dto.message.SendShortMessageDTO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.vo.coupon.MemberCouponOpenIdVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;

import java.util.List;

public interface ShortMessageSendService {

    void send(SendShortMessageDTO messagesSend);

    /**
     * 补贴相关短信发送
     */
    void subsidySendBatch(List<MessagesSendQO> messagesSendList);

    /**
     * 调整余额相关短信发送
     */
    void adminEditSendBatch(List<MessagesSendQO> messagesSendList);

    /**
     * 开卡相关短信发送
     */
    void openCardSendBatch(List<MessagesSendQO> messagesSendList);

    /**
     * 优惠券到账短信
     */
    void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponPackageVOS);

    /**
     * 优惠券过期短信
     */
    void sendMemberCouponExpireNotice(List<MemberCouponOpenIdVO> couponOpenIdVOList);
}
