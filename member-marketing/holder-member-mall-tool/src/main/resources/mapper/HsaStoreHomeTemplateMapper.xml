<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.tool.mapper.HsaStoreHomeTemplateMapper">


    <select id="pageInfo" resultType="com.holderzone.member.common.vo.tool.StoreHomeTemplateVO">
        select
            t.guid,
            t.template_name,
            t.gmt_modified,
            GROUP_CONCAT(r.store_guid) as "storeGuidStr"
        from
            hsa_store_home_template t
            left join hsa_store_home_template_relation r on t.guid = r.template_guid
        <where>
            t.is_delete = 0 and t.oper_subject_guid = #{query.operSubjectGuid}
            <if test="query.templateName != null and query.templateName != ''">
                and t.template_name like concat('%',#{query.templateName},'%')
            </if>
            <if test="query.templateGuid != null and query.templateGuid != ''">
                and t.guid = #{query.templateGuid}
            </if>
        </where>
        GROUP BY t.guid
        order by t.id desc
    </select>

    <select id="get" resultType="com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO">
        select
            guid,
            template_name,
            description,
            json
        from
            hsa_store_home_template
        where
        is_delete = 0 and guid = #{guid}
    </select>

</mapper>
