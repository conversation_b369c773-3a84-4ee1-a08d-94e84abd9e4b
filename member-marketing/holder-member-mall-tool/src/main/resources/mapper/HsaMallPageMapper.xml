<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.tool.mapper.HsaMallPageMapper">

    <select id="queryMallPage" resultType="com.holderzone.member.common.vo.mall.MallPageVO">
        select
        guid,
        name,
        state,
        gmt_modified
        from hsa_mall_page
        where is_delete = 0
        and oper_subject_guid = #{request.operSubjectGuid}
        <if test="request.keywords != null and request.keywords != '' ">
            and name like CONCAT('%',#{request.keywords},'%')
        </if>
        <if test="request.state != null and request.state != -1">
            and state = #{request.state}
        </if>

        <if test="request.type != null">
            and type = #{request.type}
        </if>
    </select>

</mapper>
