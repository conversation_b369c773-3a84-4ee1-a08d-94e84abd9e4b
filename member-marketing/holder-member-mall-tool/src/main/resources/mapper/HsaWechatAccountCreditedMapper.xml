<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.tool.mapper.wechat.HsaWechatAccountCreditedMapper">

    <update id="updateIsDefault">
        UPDATE hsa_wechat_account_credited
        SET is_default = 0
        WHERE is_default = 1
          and is_delete = 0
          and oper_subject_guid = #{operSubjectGuid};
    </update>


    <select id="getMemberPayCreditedType" resultType="java.lang.String">
        select guid
        from hsa_wechat_account_credited
        where oper_subject_guid = #{operSubjectGuid}
        and is_delete = 0

        <if test="guid != null and guid != ''">
            and guid != #{guid}
        </if>
        AND pay_credited_json regexp concat('(',0,')')
    </select>


    <select id="getAccountCreditedPage"
            resultType="com.holderzone.member.common.vo.wechat.WechatAccountCreditedPageVO">
        select guid,
               is_default,
               credited_name,
               pay_credited_json
        from hsa_wechat_account_credited
        where oper_subject_guid = #{operSubjectGuid}
        and is_delete = 0
        order by gmt_modified desc
    </select>
</mapper>
