package com.holderzone.member.mall.tool.controller;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.malltool.BasicPageReqDTO;
import com.holderzone.member.mall.tool.service.IHsaBasicPageService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * 基础页面表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/hsa_basic_page")
public class HsaBasicPageController {

    private IHsaBasicPageService basicPageService;

    @ApiOperation("批量新增基础页面")
    @PostMapping("/save_batch")
    public Result<Void> saveBatch(@RequestBody @Valid @NotNull @Size(min = 1, message = "页面数量不能小于1")
                                          List<BasicPageReqDTO> reqDTOList) {
        basicPageService.saveBatch(reqDTOList);
        return Result.success();
    }
}
