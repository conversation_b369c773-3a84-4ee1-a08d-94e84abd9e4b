package com.holderzone.member.mall.tool.entity.ali;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
@Data
@Accessors(chain = true)
public class HsaAliAppletInfo{

    private static final long serialVersionUID = 1L;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 0未删除，时间戳：删除
     */
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
    private Long isDelete;

    @ApiModelProperty(value = "小程序appid")
    private String appId;

    @ApiModelProperty(value = "应用公钥")
    private String applyPublicKey;

    @ApiModelProperty(value = "应用私钥")
    private String applyPrivateKey;

    @ApiModelProperty(value = "支付宝公钥")
    private String aliPublicKey;

    @ApiModelProperty(value = "主体guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "appName")
    private String appName;

    @ApiModelProperty(value = "appLogo")
    private String appLogo;

    @ApiModelProperty(value = "aes")
    private String aes;
}
