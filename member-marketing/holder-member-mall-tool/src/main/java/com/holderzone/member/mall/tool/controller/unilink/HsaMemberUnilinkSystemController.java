package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.unilink.AuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.MemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.UnAuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystem;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员通-系统Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system")
public class HsaMemberUnilinkSystemController {

    @Resource
    private HsaMemberUnilinkSystemService service;

    /**
     * 保存系统
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody @Valid MemberUnilinkSystemDTO dto) {
        return Result.success(service.saveSystem(dto));
    }

    /**
     * 系统授权
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/auth")
    public Result<Boolean> auth(@RequestBody @Valid AuthMemberUnilinkSystemDTO dto) {
        return Result.success(service.authSystem(dto));
    }

    /**
     * 解除系统授权
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/unAuth")
    public Result<Boolean> unAuth(@RequestBody @Valid UnAuthMemberUnilinkSystemDTO dto) {
        return Result.success(service.unAuthSystem(dto));
    }

    /**
     * 根据运营主体查询授权系统
     *
     * @param operSubjectGuid 运营主体GUID
     * @return 授权系统列表
     */
    @GetMapping("/listByOperSubject")
    public Result<List<MemberUnilinkSystemVO>> listByOperSubject(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        return Result.success(service.listByOperSubject(operSubjectGuid));
    }

}
