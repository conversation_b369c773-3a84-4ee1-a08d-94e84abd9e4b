package com.holderzone.member.mall.tool.service.ali;

import com.holderzone.member.common.dto.ali.AliAuthorizerInfoDTO;
import com.holderzone.member.common.dto.ali.AliOpenAuthDTO;
import com.holderzone.member.common.vo.ali.AliAppletInfoVO;
import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.mall.tool.entity.ali.HsaAliAuthorizerInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 微页面表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
public interface IHsaAliAuthorizerInfoService extends IHolderBaseService<HsaAliAuthorizerInfo> {

    /**
     * 根据小程序appId 查询授权信息
     */
    AliAuthorizerInfoVO getByAuthAppId(String authAppId);

    /**
     * 根据运营主体guid获取授权信息
     */
    AliAuthorizerInfoDTO getByOperSubjectGuid(String operSubjectGuid);

    String getAuthLink();

    /**
     * 回调获取授权码
     *
     * @param aliOpenAuthDTO
     */
    void callBackAppAuthCode(AliOpenAuthDTO aliOpenAuthDTO);

    /**
     * 获取小程序基础信息
     *
     * @return
     */
    AliAppletInfoVO getAliAppletInfo(String appAuthToken);

    /**
     * 取消授权
     */
    void callbackUnAuth(String appId, String msgMethod, String notifyId);

    /**
     * 获取此主体是否绑定信息
     *
     * @return
     */
    AliAuthorizerInfoVO getAppAuthInfo();
}
