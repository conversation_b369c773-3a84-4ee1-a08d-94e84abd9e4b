package com.holderzone.member.mall.tool.service.unilink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemChannelConfig;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemConfig;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemChannelConfigMapper;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemConfigMapper;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemChannelConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员通-系统渠道配置Service实现
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
public class HsaMemberUnilinkSystemChannelConfigServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemChannelConfigMapper, HsaMemberUnilinkSystemChannelConfig> implements HsaMemberUnilinkSystemChannelConfigService {

    @Resource
    private HsaMemberUnilinkSystemConfigMapper systemConfigMapper;

    @Override
    public List<MemberUnilinkSelectOptionVO> listSystemChannel(Boolean isIncludeAdmin) {
        // 查询所有系统配置
        List<HsaMemberUnilinkSystemConfig> systemConfigs = systemConfigMapper.selectList(new LambdaQueryWrapper<>());

        // 查询所有渠道配置
        List<HsaMemberUnilinkSystemChannelConfig> channelConfigs = this.list(
                new LambdaQueryWrapper<HsaMemberUnilinkSystemChannelConfig>()
                        .orderByAsc(HsaMemberUnilinkSystemChannelConfig::getSystemConfigId)
                        .orderByAsc(HsaMemberUnilinkSystemChannelConfig::getSortOrder)
        );

        // 将系统配置转换为Map，方便查找
        Map<Integer, HsaMemberUnilinkSystemConfig> systemConfigMap = systemConfigs.stream()
                .collect(Collectors.toMap(HsaMemberUnilinkSystemConfig::getId, config -> config));

        // 转换为VO
        return channelConfigs.stream()
                .filter(channel -> {
                    // 如果不包含后台，则过滤掉systemConfigId为0的
                    if (!isIncludeAdmin && channel.getSystemConfigId() == NumberConstant.NUMBER_0) {
                        return false;
                    } else if (isIncludeAdmin && channel.getSystemConfigId() == NumberConstant.NUMBER_0) {
                        return true;
                    } else {
                        return systemConfigMap.containsKey(channel.getSystemConfigId());
                    }
                })
                .map(channel -> {
                    HsaMemberUnilinkSystemConfig systemConfig = systemConfigMap.get(channel.getSystemConfigId());
                    String label = systemConfig == null ? channel.getChannelName() : systemConfig.getSystemName() + "-" + channel.getChannelName();
                    String systemType = systemConfig == null ? null : systemConfig.getSystemType();
                    return new MemberUnilinkSelectOptionVO()
                            .setLabel(label)
                            .setValue(channel.getChannelCode())
                            .setSystemType(systemType);
                })
                .collect(Collectors.toList());
    }
} 