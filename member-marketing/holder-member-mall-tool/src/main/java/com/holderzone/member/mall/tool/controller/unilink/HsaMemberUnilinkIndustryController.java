package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.unilink.MemberUnilinkIndustryDTO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkIndustryIncludeSystemVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkIndustryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员通-行业Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_industry")
public class HsaMemberUnilinkIndustryController {

    @Resource
    private HsaMemberUnilinkIndustryService hsaMemberUnilinkIndustryService;

    /**
     * 保存/修改行业
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody @Valid MemberUnilinkIndustryDTO dto) {
        return Result.success(hsaMemberUnilinkIndustryService.saveOrUpdate(dto));
    }

    /**
     * 查询行业列表
     *
     * @param isIncludeSystem 是否包含系统
     * @return 查询结果
     */
    @GetMapping("/list")
    public Result<List<MemberUnilinkIndustryIncludeSystemVO>> list(@RequestParam(defaultValue = "0") int isIncludeSystem) {
        return Result.success(hsaMemberUnilinkIndustryService.listByEnterpriseAndOperSubject(isIncludeSystem));
    }

    /**
     * 查询行业列表
     *
     * @return 授权系统列表
     */
    @GetMapping("/listIndustry")
    public Result<List<MemberUnilinkSelectOptionVO>> listUnilinkIndustry() {
        return Result.success(hsaMemberUnilinkIndustryService.listUnilinkIndustry());
    }
}
