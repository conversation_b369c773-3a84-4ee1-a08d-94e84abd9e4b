package com.holderzone.member.mall.tool.service.ali;

import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.mall.tool.entity.ali.HsaAliAppletInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
public interface IHsaAliAppletInfoService extends IHolderBaseService<HsaAliAppletInfo> {

    HsaAliAppletInfoVO getAliAppletInfoByOper(String operSubjectGuid);


    AliAuthorizerInfoVO getByAuthAppId(String appId);
}
