package com.holderzone.member.mall.tool.service.wechat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.cache.LocalCacheConfig;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoDTO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatConfigInfo;
import com.holderzone.member.mall.tool.mapper.wechat.HsaWeChatConfigInfoMapper;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudService;
import com.holderzone.member.mall.tool.service.wechat.HsaWeChatConfigInfoService;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatAccountCreditedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 小程序配置实现类
 */
@Service
@Slf4j
public class HsaWeChatConfigInfoServiceImpl extends HolderBaseServiceImpl<HsaWeChatConfigInfoMapper, HsaWechatConfigInfo>
        implements HsaWeChatConfigInfoService {

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Resource
    private HsaWechatAccountCreditedService hsaWechatAccountCreditedService;

    @Resource
    private IHsaApplyOperSubjectCloudService hsaApplyOperSubjectCloudService;

    private static final String WE_CHAT_CONFIG_KEY = "WE_CHAT_CONFIG_KEY:";

    @Override
    public void saveOrUpdateWeChatConfig(WeChatConfigInfoQO request) {
        log.info("保存或更新小程序配置信息:{}", JSON.toJSONString(request));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("HeaderUserInfo:{}", JSON.toJSONString(headerUserInfo));

        if (StringUtils.isEmpty(ThreadLocalCache.getOperSubjectGuid())) {
            throw new MallBaseException("主体参数缺失");
        }

        HsaWechatConfigInfo info = baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatConfigInfo>()
                .eq(HsaWechatConfigInfo::getAppId, request.getAppId()));
        copyDataInfo(request,info);
        //若存在则直接覆盖
        if (info.getId() != null) {
            stringRedisTemplate.delete(WE_CHAT_CONFIG_KEY + info.getOperSubjectGuid());
            baseMapper.updateById(info);
            return;
        }
        baseMapper.insert(info);
    }

    private void copyDataInfo(WeChatConfigInfoQO request, HsaWechatConfigInfo hsaWeChatConfigInfo) {
        if(hsaWeChatConfigInfo == null){
            hsaWeChatConfigInfo = new HsaWechatConfigInfo();
        }
        hsaWeChatConfigInfo.setAppName(request.getAppName());
        hsaWeChatConfigInfo.setAppLogo(request.getAppLogo());
        hsaWeChatConfigInfo.setApplyPrivateKey(request.getApplyPrivateKey());
        hsaWeChatConfigInfo.setAppId(request.getAppId());
        hsaWeChatConfigInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        hsaWeChatConfigInfo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
    }

    private HsaWechatConfigInfo getHsaWeChatConfigInfo() {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatConfigInfo>()
                .eq(HsaWechatConfigInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    private HsaWechatConfigInfo getWeChatConfigInfoByAppId(String appId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatConfigInfo>()
                .eq(HsaWechatConfigInfo::getAppId, appId)
                .last("limit 1"));
    }

    @Override
    public WeChatConfigInfoVO queryByOperSubjectGuid() {
        WeChatConfigInfoVO weChatConfigInfoVO = null;
        String cache = stringRedisTemplate.opsForValue().get(getCacheKey());

        if (!StringUtils.isEmpty(cache)) {
            weChatConfigInfoVO = JSON.parseObject(cache, WeChatConfigInfoVO.class);
        } else {

            HsaWechatConfigInfo hsaWeChatConfigInfo = getHsaWeChatConfigInfo();
            if (Objects.nonNull(hsaWeChatConfigInfo)) {
                weChatConfigInfoVO = new WeChatConfigInfoVO();
                BeanUtils.copyProperties(hsaWeChatConfigInfo, weChatConfigInfoVO);
                stringRedisTemplate.opsForValue().set(getCacheKey(), JSON.toJSONString(weChatConfigInfoVO));
            }
        }
        log.info("查询小程序配置信息:{}", JSON.toJSONString(weChatConfigInfoVO));
        return weChatConfigInfoVO;
    }

    @Override
    @Cacheable(cacheNames = {LocalCacheConfig.CacheExpires.MINUTES_1},key = "'WE_CHAT_CONFIG' + #appId + #appId" ,unless = "#result == null")
    public WeChatConfigVO queryByAppId(String appId) {
        HsaWechatConfigInfo hsaWeChatConfigInfo = getWeChatConfigInfoByAppId(appId);
        if(ObjectUtil.isNull(hsaWeChatConfigInfo)){
            throw new MemberToolException("小程序未进行配置");
        }
        WeChatConfigVO weChatConfigVO = new WeChatConfigVO();
        weChatConfigVO.setAppLogo(hsaWeChatConfigInfo.getAppLogo());
        weChatConfigVO.setAppName(hsaWeChatConfigInfo.getAppName());
        weChatConfigVO.setEnterpriseGuid(hsaWeChatConfigInfo.getEnterpriseGuid());
        weChatConfigVO.setOperSubjectGuid(hsaWeChatConfigInfo.getOperSubjectGuid());
        weChatConfigVO.setAppId(hsaWeChatConfigInfo.getAppId());
        //根据主体查询
        OperSubjectCloudVO operSubjectCloudVO = hsaApplyOperSubjectCloudService.queryByOperGuid(hsaWeChatConfigInfo.getOperSubjectGuid());
        weChatConfigVO.setMultiEnterpriseGuid(operSubjectCloudVO.getMultiEnterpriseGuid());
        weChatConfigVO.setMultiOperSubjectGuid(operSubjectCloudVO.getMultiOperSubiectGuid());
        return weChatConfigVO;
    }

    @Override
    public PaySettingVO getPaySetting(PaySettingDTO paySettingDTO) {
        PaySettingVO paySettingVO = new PaySettingVO();

        WeChatConfigInfoVO weChatConfigInfoVO = queryByOperSubjectGuid();
        BeanUtils.copyProperties(weChatConfigInfoVO, paySettingVO);
        WechatAccountCreditedVO wechatAccountCreditedVO = null;
        if (!StringUtils.isEmpty(paySettingDTO.getStoreId())) {
            // 如果门店不为空，则优先查询门店配置
            wechatAccountCreditedVO = hsaWechatAccountCreditedService.queryAccountCreditedByStoreGuid(paySettingDTO.getStoreId());
        } else if (!StringUtils.isEmpty(paySettingDTO.getOperSubjectGuid())) {
            // 如果门店为空，运营主体不为空，则查询会员运营配置
            wechatAccountCreditedVO = hsaWechatAccountCreditedService.queryAccountCreditedByOperSubjectGuid(paySettingDTO.getOperSubjectGuid());
        }
        if (Objects.nonNull(wechatAccountCreditedVO)) {
            paySettingVO.setPayMerchantKey(wechatAccountCreditedVO.getPayMerchantKey());
            paySettingVO.setPayMerchantNum(wechatAccountCreditedVO.getPayMerchantNum());
            log.info("查询微信小程序收款账户结果:{}", JacksonUtils.writeValueAsString(wechatAccountCreditedVO));
        }
        log.info("查询小程序支付配置信息:{}", JacksonUtils.writeValueAsString(paySettingVO));
        return paySettingVO;
    }

    @Override
    public List<WeChatConfigInfoVO> queryByOperSubjectGuidList(List<String> operSubjectGuidList) {
        List<HsaWechatConfigInfo> hsaWechatConfigInfos = baseMapper.selectList(new LambdaQueryWrapper<HsaWechatConfigInfo>()
                .in(HsaWechatConfigInfo::getOperSubjectGuid, operSubjectGuidList));

        if (CollUtil.isEmpty(hsaWechatConfigInfos)) {
            return Collections.emptyList();
        }
        List<WeChatConfigInfoVO> weChatConfigInfoVOS = Lists.newArrayList();
        for (HsaWechatConfigInfo hsaWechatConfigInfo : hsaWechatConfigInfos) {
            WeChatConfigInfoVO weChatConfigInfoVO = new WeChatConfigInfoVO();
            weChatConfigInfoVO.setOperSubjectGuid(hsaWechatConfigInfo.getOperSubjectGuid())
                    .setAppId(hsaWechatConfigInfo.getAppId())
                    .setOperSubjectGuid(hsaWechatConfigInfo.getOperSubjectGuid())
                    .setAppLogo(hsaWechatConfigInfo.getAppLogo())
                    .setAppName(hsaWechatConfigInfo.getAppName())
                    .setApplyPrivateKey(hsaWechatConfigInfo.getApplyPrivateKey());
            weChatConfigInfoVOS.add(weChatConfigInfoVO);
        }

        return weChatConfigInfoVOS;
    }

    private static String getCacheKey() {
        return WE_CHAT_CONFIG_KEY + ThreadLocalCache.getOperSubjectGuid();
    }
}
