package com.holderzone.member.mall.tool.controller.cloud;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.cloud.OperSubjectCloudQO;
import com.holderzone.member.common.vo.cloud.MultiMemberVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 系统主体关联 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/cloud")
public class HsaApplyOperSubjectCloudController {

    @Resource
    private IHsaApplyOperSubjectCloudService hsaApplyOperSubjectCloudService;

    /**
     * 获取云平台主体
     *
     * @return
     */
    @ApiOperation("获取云平台主体")
    @GetMapping("/queryMultiOperSubject")
    Result<List<MultiMemberVO>> queryMultiOperSubject(String enterpriseGuid) {
        return Result.success(hsaApplyOperSubjectCloudService.queryMultiOperSubject(enterpriseGuid));
    }

    /**
     * 获取关联配置
     *
     * @return
     */
    @ApiOperation("获取关联配置")
    @GetMapping("/query")
    Result<OperSubjectCloudVO> query() {
        return Result.success(hsaApplyOperSubjectCloudService.query());
    }

    /**
     * 保存配置
     *
     * @return
     */
    @ApiOperation("保存配置")
    @PostMapping("/save")
    Result<Boolean> save(@RequestBody OperSubjectCloudQO operSubjectCloudQO) {
        return Result.success(hsaApplyOperSubjectCloudService.save(operSubjectCloudQO));
    }


    /**
     * 获取关联配置
     *
     * @return
     */
    @ApiOperation("获取关联配置")
    @GetMapping("/query_oper")
    OperSubjectCloudVO queryByOperSubiectGuid(@RequestParam(value = "operSubjectGuid") String operSubiectGuid) {
        return hsaApplyOperSubjectCloudService.queryByOperGuid(operSubiectGuid);
    }

    /**
     * 根据三方运营主体guid获取关联配置
     */
    @ApiOperation("根据三方运营主体guid获取关联配置")
    @GetMapping("/query_oper/by_multi_oper")
    OperSubjectCloudVO queryByMultiOperSubjectGuid(@RequestParam(value = "multiOperSubjectGuid") String multiOperSubjectGuid) {
        return hsaApplyOperSubjectCloudService.queryByMultiOperSubjectGuid(multiOperSubjectGuid);
    }
}

