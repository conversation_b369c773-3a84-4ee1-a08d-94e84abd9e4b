package com.holderzone.member.mall.tool.service;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.tool.StoreHomeTemplateQO;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO;
import com.holderzone.member.mall.tool.entity.HsaStoreHomeTemplate;

/**
 * <p>
 * 店铺主页模板 服务类
 * </p>
 */
public interface HsaStoreHomeTemplateService extends IHolderBaseService<HsaStoreHomeTemplate> {

    PageResult pageInfo(StoreHomeTemplateQO query);

    StoreHomeTemplateDetailsVO get(String guid);
}
