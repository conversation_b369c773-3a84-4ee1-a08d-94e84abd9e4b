package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.feign.CloudFeign;
import com.holderzone.member.common.qo.cloud.OperSubjectCloudQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.cloud.MultiMemberVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.feign.CloudFeignModel;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloud;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloudRule;
import com.holderzone.member.mall.tool.mapper.cloud.HsaApplyOperSubjectCloudMapper;
import com.holderzone.member.mall.tool.mapper.cloud.HsaApplyOperSubjectCloudRuleMapper;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统主体关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
@Slf4j
public class HsaApplyOperSubjectCloudServiceImpl extends HolderBaseServiceImpl<HsaApplyOperSubjectCloudMapper, HsaApplyOperSubjectCloud> implements IHsaApplyOperSubjectCloudService {

    @Resource
    private CloudFeign cloudFeign;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaApplyOperSubjectCloudRuleMapper hsaApplyOperSubjectCloudRuleMapper;

    @Override
    public List<MultiMemberVO> queryMultiOperSubject(String enterpriseGuid) {
        log.info("queryMultiOperSubject入参：{}", enterpriseGuid);
        CloudFeignModel<MultiMemberDTO> cloudFeignModel = cloudFeign.list(enterpriseGuid);
        log.info("queryMultiOperSubject返参：{}", JSON.toJSON(cloudFeignModel));

        List<MultiMemberVO> multiMemberVOListList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cloudFeignModel.getTdata())) {
            for (MultiMemberDTO multiMemberDTO : cloudFeignModel.getTdata()) {
                MultiMemberVO multiMemberVO = new MultiMemberVO();
                BeanUtils.copyProperties(multiMemberDTO, multiMemberVO);
                multiMemberVOListList.add(multiMemberVO);
            }

        }
        return multiMemberVOListList;
    }

    @Override
    public OperSubjectCloudVO query() {
        OperSubjectCloudVO operSubjectCloudVO = new OperSubjectCloudVO();
        HsaApplyOperSubjectCloud hsaApplyOperSubjectCloud = getHsaApplyOperSubjectCloud();

        if (Objects.nonNull(hsaApplyOperSubjectCloud)) {
            BeanUtils.copyProperties(hsaApplyOperSubjectCloud, operSubjectCloudVO);
        }
        return operSubjectCloudVO;
    }

    @Override
    public OperSubjectCloudVO queryByOperGuid(String operSubiectGuid) {
        log.info("queryByOperGuid:{}", operSubiectGuid);
        OperSubjectCloudVO operSubjectCloudVO = new OperSubjectCloudVO();
        HsaApplyOperSubjectCloudRule hsaApplyOperSubjectCloudRule = hsaApplyOperSubjectCloudRuleMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloudRule>()
                .eq(HsaApplyOperSubjectCloudRule::getOperSubjectGuid, operSubiectGuid));

        log.info("hsaApplyOperSubjectCloudRule:{}", JSON.toJSONString(hsaApplyOperSubjectCloudRule));

        if (Objects.nonNull(hsaApplyOperSubjectCloudRule) && hsaApplyOperSubjectCloudRule.getIsSwitch() == BooleanEnum.TRUE.getCode()) {
            HsaApplyOperSubjectCloud hsaApplyOperSubjectCloud = baseMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloud>()
                    .eq(HsaApplyOperSubjectCloud::getOperSubjectGuid, operSubiectGuid));
            log.info("hsaApplyOperSubjectCloud:{}", JSON.toJSONString(hsaApplyOperSubjectCloud));
            operSubjectCloudVO.setMultiOperSubiectGuid(hsaApplyOperSubjectCloud.getMultiOperSubiectGuid())
                    .setMultiEnterpriseGuid(hsaApplyOperSubjectCloud.getMultiEnterpriseGuid());
        }
        return operSubjectCloudVO;
    }

    @Override
    public OperSubjectCloudVO queryByMultiOperSubjectGuid(String multiOperSubjectGuid) {
        log.info("queryByMultiOperSubjectGuid:{}", multiOperSubjectGuid);
        HsaApplyOperSubjectCloud applyOperSubjectCloud = baseMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloud>()
                .eq(HsaApplyOperSubjectCloud::getMultiOperSubiectGuid, multiOperSubjectGuid));
        log.info("applyOperSubjectCloud:{}", JSON.toJSONString(applyOperSubjectCloud));
        if (Objects.isNull(applyOperSubjectCloud)) {
            log.warn("查询关联配置未空");
            return null;
        }
        HsaApplyOperSubjectCloudRule applyOperSubjectCloudRule = hsaApplyOperSubjectCloudRuleMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloudRule>()
                .eq(HsaApplyOperSubjectCloudRule::getOperSubjectGuid, applyOperSubjectCloud.getOperSubjectGuid()));
        log.info("applyOperSubjectCloudRule:{}", JacksonUtils.writeValueAsString(applyOperSubjectCloudRule));
        if (Objects.isNull(applyOperSubjectCloudRule) || applyOperSubjectCloudRule.getIsSwitch() == BooleanEnum.FALSE.getCode()) {
            return null;
        }
        OperSubjectCloudVO operSubjectCloudVO = new OperSubjectCloudVO();
        operSubjectCloudVO.setMultiOperSubiectGuid(applyOperSubjectCloud.getMultiOperSubiectGuid())
                .setMultiEnterpriseGuid(applyOperSubjectCloud.getMultiEnterpriseGuid())
                .setEnterpriseGuid(applyOperSubjectCloud.getEnterpriseGuid())
                .setOperSubjectGuid(applyOperSubjectCloud.getOperSubjectGuid());
        return operSubjectCloudVO;
    }

    private HsaApplyOperSubjectCloud getHsaApplyOperSubjectCloud() {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloud>()
                .eq(HsaApplyOperSubjectCloud::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    @Override
    public Boolean save(OperSubjectCloudQO operSubjectCloudQO) {
        HsaApplyOperSubjectCloud hsaApplyOperSubjectCloud = getHsaApplyOperSubjectCloud();

        HsaApplyOperSubjectCloud operSubjectCloud = baseMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloud>()
                .eq(HsaApplyOperSubjectCloud::getMultiOperSubiectGuid, operSubjectCloudQO.getMultiOperSubiectGuid())
                .notIn(HsaApplyOperSubjectCloud::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        //删除掉老主体之前绑定关系并降开关关闭
        if (Objects.nonNull(operSubjectCloud)) {

            HsaApplyOperSubjectCloudRule hsaApplyOperSubjectCloudRule = hsaApplyOperSubjectCloudRuleMapper
                    .selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloudRule>()
                            .eq(HsaApplyOperSubjectCloudRule::getOperSubjectGuid, operSubjectCloud.getOperSubjectGuid()));

            baseMapper.removeByGuid(operSubjectCloud.getGuid());
            hsaApplyOperSubjectCloudRule.setIsSwitch(BooleanEnum.FALSE.getCode());
            hsaApplyOperSubjectCloudRuleMapper.updateByGuid(hsaApplyOperSubjectCloudRule);
        }

        if (StringUtils.isEmpty(operSubjectCloudQO.getGuid())) {
            hsaApplyOperSubjectCloud = new HsaApplyOperSubjectCloud();
            hsaApplyOperSubjectCloud.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid())
                    .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                    .setMultiEnterpriseGuid(operSubjectCloudQO.getMultiEnterpriseGuid())
                    .setMultiOperSubiectGuid(operSubjectCloudQO.getMultiOperSubiectGuid())
                    .setGuid(guidGeneratorUtil.getStringGuid(HsaApplyOperSubjectCloud.class.getSimpleName()));
            baseMapper.insert(hsaApplyOperSubjectCloud);
        } else {
            hsaApplyOperSubjectCloud.setMultiOperSubiectGuid(operSubjectCloudQO.getMultiOperSubiectGuid())
                    .setMultiEnterpriseGuid(operSubjectCloudQO.getMultiEnterpriseGuid());
            baseMapper.updateByGuid(hsaApplyOperSubjectCloud);
        }


        return true;
    }
}
