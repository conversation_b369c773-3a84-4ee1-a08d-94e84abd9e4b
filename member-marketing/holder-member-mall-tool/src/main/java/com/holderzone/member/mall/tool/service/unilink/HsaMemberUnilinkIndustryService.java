package com.holderzone.member.mall.tool.service.unilink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.unilink.MemberUnilinkIndustryDTO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkIndustryIncludeSystemVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkIndustry;

import java.util.List;

/**
 * 会员通-行业Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface HsaMemberUnilinkIndustryService extends IService<HsaMemberUnilinkIndustry> {
    /**
     * 保存/修改行业
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    Boolean saveOrUpdate(MemberUnilinkIndustryDTO dto);

    /**
     * 查询行业列表
     *
     * @param isIncludeSystem 是否包含系统
     * @return 查询结果
     */
    List<MemberUnilinkIndustryIncludeSystemVO> listByEnterpriseAndOperSubject(int isIncludeSystem);

    /**
     * 查询行业列表
     *
     * @return 授权系统列表
     */
    List<MemberUnilinkSelectOptionVO> listUnilinkIndustry();
}
