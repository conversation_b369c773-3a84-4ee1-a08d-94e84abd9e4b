package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemChannelConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员通-系统渠道配置Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system_channel_config")
public class HsaMemberUnilinkSystemChannelConfigController {

    @Resource
    private HsaMemberUnilinkSystemChannelConfigService systemChannelConfigService;

    /**
     * 查询系统渠道列表
     *
     * @param isIncludeAdmin 是否包含后台，默认不包含
     * @return 系统渠道列表
     */
    @GetMapping("/list")
    public Result<List<MemberUnilinkSelectOptionVO>> listSystemChannel(@RequestParam(value = "isIncludeAdmin", required = false, defaultValue = "false") Boolean isIncludeAdmin) {
        return Result.success(systemChannelConfigService.listSystemChannel(isIncludeAdmin));
    }
} 