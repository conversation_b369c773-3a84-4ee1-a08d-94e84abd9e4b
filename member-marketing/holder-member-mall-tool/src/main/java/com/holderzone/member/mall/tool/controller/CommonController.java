package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.crm.CrmBindAppletDTO;
import com.holderzone.member.common.vo.tool.UserOperSubjectVO;
import com.holderzone.member.mall.tool.manager.CommonManager;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022年12月30日 下午4:21
 * @description 公共接口控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common")
public class CommonController {

    private CommonManager commonManager;

    @ApiOperation("运营主体和小程序列表")
    @GetMapping("/list_oper_subject_and_applet")
    public Result<UserOperSubjectVO> listOperSubjectAndApplet() {
        return Result.success(commonManager.listOperSubjectAndApplet());
    }

    @ApiOperation("获取绑定小程序地址")
    @PostMapping("/get_bind_applet_url")
    public Result<String> getBindAppletUrl(@RequestBody CrmBindAppletDTO bindAppletDTO) {
        return Result.success(commonManager.getBindAppletUrl(bindAppletDTO));
    }
}
