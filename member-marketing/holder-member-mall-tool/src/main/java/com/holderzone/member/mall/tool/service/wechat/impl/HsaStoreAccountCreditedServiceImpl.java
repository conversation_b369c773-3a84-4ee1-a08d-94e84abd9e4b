package com.holderzone.member.mall.tool.service.wechat.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.StallInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.mall.tool.entity.HsaBasicPage;
import com.holderzone.member.mall.tool.entity.wechat.HsaStoreAccountCredited;
import com.holderzone.member.mall.tool.mapper.wechat.HsaStoreAccountCreditedMapper;
import com.holderzone.member.mall.tool.service.wechat.HsaStoreAccountCreditedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HsaStoreAccountCreditedServiceImpl extends HolderBaseServiceImpl<HsaStoreAccountCreditedMapper, HsaStoreAccountCredited>
        implements HsaStoreAccountCreditedService {

    @Resource
    private CrmFeign crmFeign;

    @Resource
    private ExternalSupport externalSupport;

    @Override
    public List<StoreAccountCreditedVO> queryStore(QueryStoreBasePage query) {
        List<StoreAccountCreditedVO> storeAccountCreditedVOS = Lists.newArrayList();
        //获取门店
        List<StoreBaseInfo> storeInfo = externalSupport.storeServer(ThreadLocalCache.getSystem()).listStoreAndStall(query);
        log.info("storeInfoVOCrmFeignModel:{}", JSON.toJSONString(storeInfo));

        if (CollUtil.isEmpty(storeInfo)) {
            return storeAccountCreditedVOS;
        }

        //获取档口
        List<String> storeId = storeInfo.stream().map(in -> String.valueOf(in.getId())).collect(Collectors.toList());
        QueryStallBasePage queryStallBasePage = new QueryStallBasePage();
        queryStallBasePage.setStoreId(storeId);

        for (StoreBaseInfo storeInfoVO : storeInfo) {
            StoreAccountCreditedVO storeAccountCreditedVO = new StoreAccountCreditedVO();
            storeAccountCreditedVO.setStoreGuid(storeInfoVO.getId())
                    .setStoreName(storeInfoVO.getStore_name());

            storeAccountCreditedVO.setStoreNumber(storeInfoVO.getStore_number());
            storeAccountCreditedVO.setIsCheck(BooleanEnum.TRUE.getCode());
            storeAccountCreditedVOS.add(storeAccountCreditedVO);
        }
        return storeAccountCreditedVOS;
    }
}
