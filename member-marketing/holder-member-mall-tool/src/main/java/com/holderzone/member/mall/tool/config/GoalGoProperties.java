package com.holderzone.member.mall.tool.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 其它系统配置信息
 * @date 2021/12/1
 */
@Data
@Component
@ConfigurationProperties(prefix = "goalgo")
public class GoalGoProperties {

    private String host;

    private String store;

    private String crm;
}
