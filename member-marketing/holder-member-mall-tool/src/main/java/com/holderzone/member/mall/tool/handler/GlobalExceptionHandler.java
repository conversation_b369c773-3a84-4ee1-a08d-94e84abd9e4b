package com.holderzone.member.mall.tool.handler;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberToolException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常拦截
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    /**
     * 处理自定义的业务异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = MemberToolException.class)
    @ResponseBody
    public Result memberToolExceptionHandler(MemberToolException e) {
        logger.error("商城装修服务发生业务异常！原因是：{}", e.getDes());
        return Result.error(e.getCode(), e.getDes());
    }

    /**
     * 处理其他异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result exceptionHandler(Exception e) {
        logger.error("未知异常！原因是:", e);
        return Result.error(CommonEnum.FAILED);
    }

    @ResponseBody
    @ExceptionHandler(value = BindException.class)
    public Result toolHandleBindException(BindException bindException) {
        BindingResult toolBindingResult = bindException.getBindingResult();
        String toolMessage = MemberAccountExceptionEnum.ERROR_DATA_REQUEST.getDes();
        if (toolBindingResult.hasErrors()) {
            FieldError fieldError = toolBindingResult.getFieldError();
            if (fieldError != null) {
                toolMessage = fieldError.getDefaultMessage();
            }
        }
        return Result.error(toolMessage);
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result toolHandleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult toolBindingResult = e.getBindingResult();
        String errorDataRequestDes = MemberAccountExceptionEnum.ERROR_DATA_REQUEST.getDes();
        if (toolBindingResult.hasErrors()) {
            FieldError fieldError = toolBindingResult.getFieldError();
            if (fieldError != null) {
                errorDataRequestDes = fieldError.getDefaultMessage();
            }
        }
        return Result.error(errorDataRequestDes);
    }

}
