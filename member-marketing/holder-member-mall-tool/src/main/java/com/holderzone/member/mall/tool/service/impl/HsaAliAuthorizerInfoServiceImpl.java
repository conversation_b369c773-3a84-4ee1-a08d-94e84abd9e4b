package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayOpenAuthTokenAppQueryRequest;
import com.alipay.api.request.AlipayOpenAuthTokenAppRequest;
import com.alipay.api.request.AlipayOpenMiniBaseinfoQueryRequest;
import com.alipay.api.response.AlipayOpenAuthTokenAppQueryResponse;
import com.alipay.api.response.AlipayOpenAuthTokenAppResponse;
import com.alipay.api.response.AlipayOpenMiniBaseinfoQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.ali.*;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.ali.AliOpenAuthTokenQO;
import com.holderzone.member.common.util.date.ValidTimeUtils;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.ali.AliAppletInfoVO;
import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.mall.tool.entity.ali.HsaAliAuthorizerInfo;
import com.holderzone.member.mall.tool.mapper.ali.HsaAliAuthorizerInfoMapper;
import com.holderzone.member.mall.tool.service.ali.IHsaAliAuthorizerInfoService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudService;
import com.holderzone.member.mall.tool.utils.AliPayMessagesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static me.chanjar.weixin.common.util.http.URIUtil.encodeURIComponent;

/**
 * <p>
 * 支付宝授权 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Slf4j
@Service
public class HsaAliAuthorizerInfoServiceImpl extends HolderBaseServiceImpl<HsaAliAuthorizerInfoMapper, HsaAliAuthorizerInfo> implements IHsaAliAuthorizerInfoService {

    @Resource
    private AliPayMessagesUtil aliPayMessagesUtil;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private SaasStoreFeign saasStoreFeign;

    @Resource
    private IHsaApplyOperSubjectCloudService handlerOperSubjectCloudService;

    @Override
    public AliAuthorizerInfoVO getByAuthAppId(String authAppId) {
        HsaAliAuthorizerInfo aliAuthorizerInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getAuthAppId, authAppId)
                .eq(HsaAliAuthorizerInfo::getIsDeleted, BooleanEnum.FALSE.getCode())
                .last("limit 1"));
        if (Objects.isNull(aliAuthorizerInfo)) {
            return null;
        }
        AliAuthorizerInfoVO aliAuthorizerInfoVO = new AliAuthorizerInfoVO();
        BeanUtils.copyProperties(aliAuthorizerInfo, aliAuthorizerInfoVO);
        return aliAuthorizerInfoVO;
    }

    @Override
    public AliAuthorizerInfoDTO getByOperSubjectGuid(String operSubjectGuid) {
        HsaAliAuthorizerInfo aliAuthorizerInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaAliAuthorizerInfo::getIsDeleted, BooleanEnum.FALSE.getCode())
                .last("limit 1"));
        if (Objects.isNull(aliAuthorizerInfo)) {
            return null;
        }
        AliAuthorizerInfoDTO aliAuthorizerInfoDTO = new AliAuthorizerInfoDTO();
        BeanUtils.copyProperties(aliAuthorizerInfo, aliAuthorizerInfoDTO);
        return aliAuthorizerInfoDTO;
    }

    @Override
    public String getAuthLink() {

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        AliOpenAuthDTO aliOpenAuthDTO = new AliOpenAuthDTO();
        aliOpenAuthDTO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        aliOpenAuthDTO.setEnterpriseGuid(headerUserInfo.getEnterpriseGuid());

        AliOpenAuthLinkDTO aliOpenAuthLinkDTO = new AliOpenAuthLinkDTO();
        aliOpenAuthLinkDTO.setTaskType(StringConstant.INTERFACE_AUTH);
        aliOpenAuthLinkDTO.setPlatformCode("O");
        AliOpenAuthLinkParamDTO agentOpParam = new AliOpenAuthLinkParamDTO();
        agentOpParam.setAppTypes(Collections.singletonList(StringConstant.TINYAPP));
        agentOpParam.setState(Base64.encode(headerUserInfo.getOperSubjectGuid() + StringConstant.COMMA + headerUserInfo.getEnterpriseGuid(), StandardCharsets.UTF_8));
        agentOpParam.setRedirectUri(aliPayMessagesUtil.getRedirectUri());
        agentOpParam.setIsvAppId(aliPayMessagesUtil.getAppId());
        aliOpenAuthLinkDTO.setAgentOpParam(agentOpParam);

        String bizData = encodeURIComponent(JSON.toJSONString(aliOpenAuthLinkDTO));


        return aliPayMessagesUtil.getAliQrcode() + bizData;
    }


    @Override
    public AliAppletInfoVO getAliAppletInfo(String appAuthToken) {
        AliAppletInfoVO aliAppletInfoVO = new AliAppletInfoVO();
        AlipayClient alipayClient = getAlipayClient(aliPayMessagesUtil.getAliTokenUrl(), "UTF8");
        AlipayOpenMiniBaseinfoQueryRequest request = new AlipayOpenMiniBaseinfoQueryRequest();
        AliOpenAuthDTO aliOpenAuthDTO = new AliOpenAuthDTO();
        request.setBizModel(aliOpenAuthDTO);
        AlipayOpenMiniBaseinfoQueryResponse response = null;
        try {
            log.info("获取支付宝小程序基础信息入参：{}", JSON.toJSONString(request));

            response = alipayClient.execute(request, null, appAuthToken);
        } catch (AlipayApiException e) {
            log.info("获取支付宝小程序基础信息异常", e);
            throw new MemberToolException("获取支付宝小程序基础信息接口异常");
        }
        log.info("获取支付宝小程序基础信息返参：{}", JSON.toJSONString(response));
        if (response.isSuccess()) {
            aliAppletInfoVO.setAppDesc(response.getAppDesc());
            aliAppletInfoVO.setAppSlogan(response.getAppSlogan());
            aliAppletInfoVO.setAppName(response.getAppName());
            aliAppletInfoVO.setAppLogo(response.getAppLogo());
        } else {
            log.info("获取支付宝小程序基础信息失败响应码：{}，msg：{}", response.getCode(), response.getMsg());
        }

        return aliAppletInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callBackAppAuthCode(AliOpenAuthDTO aliOpenAuthDTO) {
        log.info("回调成功授权码参数：{}", JSON.toJSONString(aliOpenAuthDTO));

        if (Objects.isNull(aliOpenAuthDTO) || StringUtils.isEmpty(aliOpenAuthDTO.getApp_auth_code())) {
            return;
        }

        //获取授权token
        AliOpenAuthTokenDTO authTokenDTO = getAppAuthToken(aliOpenAuthDTO.getApp_auth_code());

        if (StringUtils.isEmpty(authTokenDTO.getAppAuthToken())) {
            log.info("获取token失败");
            return;
        }
        //获取授权信息
        AliOpenAuthInfoDTO aliOpenAuthInfoDTO = getAppAuthInfo(authTokenDTO.getAppAuthToken());

        //保存授权信息
        saveAliAuthorizerInfo(aliOpenAuthDTO, authTokenDTO, aliOpenAuthInfoDTO);
    }

    @Override
    public void callbackUnAuth(String appId, String msgMethod, String notifyId) {
        //是否为取消授权通知
        if (!msgMethod.equals("alipay.open.auth.appauth.cancelled")) {
            return;
        }

        //双校验
        List<HsaAliAuthorizerInfo> hsaAliAuthorizerInfoList = baseMapper.selectList(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getNotifyId, notifyId));
        if (CollUtil.isNotEmpty(hsaAliAuthorizerInfoList)) {
            log.info("已处理过此消息：{}", notifyId);
            return;
        }

        HsaAliAuthorizerInfo hsaAliAuthorizerInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getAuthAppId, appId)
                .eq(HsaAliAuthorizerInfo::getIsDeleted, BooleanEnum.FALSE.getCode()));

        if (Objects.nonNull(hsaAliAuthorizerInfo)) {
            if (!StringUtils.isEmpty(hsaAliAuthorizerInfo.getNotifyId())
                    && hsaAliAuthorizerInfo.getNotifyId().equals(notifyId)) {
                log.info("已处理过此消息：{}", notifyId);
            } else {
                hsaAliAuthorizerInfo.setNotifyId(notifyId);
                hsaAliAuthorizerInfo.setIsDeleted(BooleanEnum.TRUE.getCode());
                baseMapper.updateByGuid(hsaAliAuthorizerInfo);

                //取消门店授权
                synSaasAuth(hsaAliAuthorizerInfo, 2);
                log.info("取消成功");
            }
        }
    }

    private void saveAliAuthorizerInfo(AliOpenAuthDTO aliOpenAuthDTO, AliOpenAuthTokenDTO authTokenDTO, AliOpenAuthInfoDTO aliOpenAuthInfoDTO) {
        HsaAliAuthorizerInfo hsaAliAuthorizerInfo = new HsaAliAuthorizerInfo();

        hsaAliAuthorizerInfo.setAuthAppId(aliOpenAuthInfoDTO.getAuthAppId())
                .setAuthorizerAppId(aliPayMessagesUtil.getAppId())
                .setAuthEnd(ValidTimeUtils.getLocalDateTimeByDate(aliOpenAuthInfoDTO.getAuthEnd()))
                .setAuthStart(ValidTimeUtils.getLocalDateTimeByDate(aliOpenAuthInfoDTO.getAuthStart()))
                .setGuid(guidGeneratorUtil.getStringGuid(HsaAliAuthorizerInfo.class.getSimpleName()))
                .setIsByAppAuth(aliOpenAuthInfoDTO.getAuthAppId())
                .setExpiresIn(aliOpenAuthInfoDTO.getExpiresIn().intValue())
                .setAuthorizerAccessToken(authTokenDTO.getAppAuthToken())
                .setAuthorizerRefreshToken(authTokenDTO.getRefreshToken())
                .setIsDeleted(BooleanEnum.FALSE.getCode())
                .setUserId(authTokenDTO.getUserId())
                .setStatus(aliOpenAuthInfoDTO.getStatus());

        String[] splitList;
        try {
            splitList = Base64.decodeStr(URLDecoder.decode(aliOpenAuthDTO.getState(), "UTF-8"), StandardCharsets.UTF_8).split(StringConstant.COMMA);
        } catch (UnsupportedEncodingException e) {
            log.info("解析回调参数异常", e);
            return;
        }
        log.info("splitList：{}", JSON.toJSONString(splitList));
        //获取关联主体
        hsaAliAuthorizerInfo.setOperSubjectGuid(splitList[0])
                .setEnterpriseGuid(splitList[1]);
        log.info("hsaAliAuthorizerInfo：{}", JSON.toJSONString(hsaAliAuthorizerInfo));

        HsaAliAuthorizerInfo aliAuthorizerInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getAuthAppId, hsaAliAuthorizerInfo.getAuthAppId())
                .eq(HsaAliAuthorizerInfo::getIsDeleted, BooleanEnum.FALSE.getCode()));

        //若之前商户已绑定  则需要删除对应关系
        if (Objects.nonNull(aliAuthorizerInfo)) {
            baseMapper.removeByGuid(aliAuthorizerInfo.getGuid());
        }
        //通知门店授权
        synSaasAuth(hsaAliAuthorizerInfo, BooleanEnum.TRUE.getCode());
        baseMapper.insert(hsaAliAuthorizerInfo);
    }

    private void synSaasAuth(HsaAliAuthorizerInfo hsaAliAuthorizerInfo, Integer notifyType) {
        OperSubjectCloudVO operSubjectCloudVO = handlerOperSubjectCloudService.queryByOperGuid(hsaAliAuthorizerInfo.getOperSubjectGuid());
        if (!StringUtils.isEmpty(operSubjectCloudVO.getMultiEnterpriseGuid())) {
            AliSynAuthDTO aliSynAuthDTO = new AliSynAuthDTO();
            aliSynAuthDTO.setOperSubjectGuid(operSubjectCloudVO.getMultiOperSubiectGuid())
                    .setEnterpriseGuid(operSubjectCloudVO.getMultiEnterpriseGuid())
                    .setAppId(hsaAliAuthorizerInfo.getAuthAppId())
                    .setAppAuthToken(hsaAliAuthorizerInfo.getAuthorizerAccessToken())
                    .setNotifyType(notifyType);
            try {
                saasStoreFeign.synSaasAuth(aliSynAuthDTO);
            } catch (Exception e) {
                log.info("商户授权同步门店异常");
            }
        }
    }

    @Override
    public AliAuthorizerInfoVO getAppAuthInfo() {
        AliAuthorizerInfoVO aliAuthorizerInfoVO = new AliAuthorizerInfoVO();
        HsaAliAuthorizerInfo aliAuthorizerInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAuthorizerInfo>()
                .eq(HsaAliAuthorizerInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaAliAuthorizerInfo::getIsDeleted, BooleanEnum.FALSE.getCode()));

        if (Objects.isNull(aliAuthorizerInfo)) {
            aliAuthorizerInfoVO.setIsAuth(BooleanEnum.FALSE.getCode());
        } else {
            aliAuthorizerInfoVO.setIsAuth(BooleanEnum.TRUE.getCode());
            aliAuthorizerInfoVO.setAuthAppId(aliAuthorizerInfo.getAuthAppId());
            AliAppletInfoVO aliAppletInfoVO = getAliAppletInfo(aliAuthorizerInfo.getAuthorizerAccessToken());
            aliAuthorizerInfoVO.setAppName(aliAppletInfoVO.getAppName());
            aliAuthorizerInfoVO.setAppLogo(aliAppletInfoVO.getAppLogo());
        }
        return aliAuthorizerInfoVO;
    }

    private AliOpenAuthInfoDTO getAppAuthInfo(String token) {
        AliOpenAuthInfoDTO aliOpenAuthInfoDTO = new AliOpenAuthInfoDTO();
        AlipayClient alipayClient = getAlipayClient(aliPayMessagesUtil.getAliTokenUrl(), StringConstant.GBK);
        AlipayOpenAuthTokenAppQueryRequest request = new AlipayOpenAuthTokenAppQueryRequest();
        AliOpenAuthTokenQO authTokenDTO = new AliOpenAuthTokenQO();
        authTokenDTO.setApp_auth_token(token);
        request.setBizContent(JSON.toJSONString(authTokenDTO));
        AlipayOpenAuthTokenAppQueryResponse response;
        try {
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            throw new MemberToolException("获取支付宝授权信息接口异常");
        }
        if (response.isSuccess()) {
            log.info("获取授权信息成功：{}", JSON.toJSONString(response));
            BeanUtils.copyProperties(response, aliOpenAuthInfoDTO);
        } else {
            log.info("获取授权信息失败：{}", JSON.toJSONString(response));
            log.info("获取授权信息失败响应码：{}，msg：{}", response.getSubCode(), response.getSubMsg());
        }
        return aliOpenAuthInfoDTO;
    }

    private AliOpenAuthTokenDTO getAppAuthToken(String appAuthCode) {
        AliOpenAuthTokenDTO aliOpenAuthTokenDTO = new AliOpenAuthTokenDTO();
        AliOpenAuthTokenQO authTokenDTO = new AliOpenAuthTokenQO();
        authTokenDTO.setGrant_type(StringConstant.AUTHORIZATION_CODE);
        authTokenDTO.setCode(appAuthCode);

        //远程调用第三方授权
        AlipayClient alipayClient = getAlipayClient(aliPayMessagesUtil.getAliTokenUrl(), StringConstant.GBK);
        AlipayOpenAuthTokenAppRequest request = new AlipayOpenAuthTokenAppRequest();
        request.setBizContent(JSON.toJSONString(authTokenDTO));
        log.info("获取授权Token入参:{}", JSON.toJSONString(request));
        AlipayOpenAuthTokenAppResponse response;
        try {
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            log.info("获取授权Token异常", e);
            throw new MemberToolException("获取授权Token接口异常");
        }

        if (response.isSuccess()) {
            log.info("获取授权Token成功：{}", JSON.toJSONString(response));
            String authToken = response.getAppAuthToken();
            //保存刷新token
            aliOpenAuthTokenDTO.setAppAuthToken(authToken);
            aliOpenAuthTokenDTO.setRefreshToken(response.getAppRefreshToken());
            aliOpenAuthTokenDTO.setUserId(response.getUserId());
        } else {
            log.info("获取token失败：{}", JSON.toJSONString(response));
            log.info("获取token失败响应码：{}，msg：{}", response.getSubCode(), response.getSubMsg());
        }
        return aliOpenAuthTokenDTO;
    }

    private AlipayClient getAlipayClient(String url, String charset) {
        return new DefaultAlipayClient(
                url,
                aliPayMessagesUtil.getAppId(),
                aliPayMessagesUtil.getPrivateKey(),
                "json",
                charset,
                aliPayMessagesUtil.getPayPublicKey(),
                StringConstant.RSA2);
    }
}
