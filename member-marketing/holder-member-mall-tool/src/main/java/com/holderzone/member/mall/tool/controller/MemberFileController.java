package com.holderzone.member.mall.tool.controller;


import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.FileConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.FileIllegalException;
import com.holderzone.member.common.util.verify.VerifyUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberFileController
 */
@RestController
@RequestMapping("/file/member")
@ApiModel(description = "会员上传文件接口")
@Slf4j
public class MemberFileController {

    /**
     * 卡背景图片有效格式
     */
    private static final List<String> VALID_IMAGE_TYPE = Arrays.asList("jpeg", "jpg", "png","mp3","gif","mp4");
    /**
     * 卡背景图片最大5M
     * 1024 * 1024 * 5 = 5242880
     */
    private static final long IMAGE_BYTE_SIZE = 5242880;

    @Autowired
    private FileOssService baseService;

    @ApiOperation(value = "会员导入接口,文件上传name 必须为file", notes = "单个文件不能超过5000条，上传成功返回文件的地址，失败返回空")
    @PostMapping("/memberUploadExcel")
    public Result<String> memberUploadExcel(@RequestParam(value = "file") MultipartFile file) {
        log.info("文件上传， fileName={}", file.getOriginalFilename());
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : null;
        if (!FileConstant.CORRECT_TYPE_LIST.contains(fileType)) {
            log.info("当前文件类型为：{}", fileType);
            throw new FileIllegalException(MemberAccountExceptionEnum.ERROR_FILE.getDes());
        }
        String upload;
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
            upload = baseService.upload(fileDto);
            log.info("图片上传下载路径->>>>>{}", upload);
        } catch (IOException e) {
            log.error("上传文件失败");
            return Result.error("上传失败");
        }
        return Result.success(upload);
    }


    /**
     * 图片上传，如需修改文件大小，需修改 {@link com.holderzone.member.base.config.MultipartConfig }
     * @param fileInfo 文件
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "上传卡背景图片(.jpeg .jpg .png且大小不超过5M)")
    @PostMapping("/uploadImage")
    public Result<String> uploadImage(FileInfo fileInfo) {
        // 校验图片格式
        MultipartFile file = fileInfo.getFile();
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase() : null;
        VerifyUtil.verify(VALID_IMAGE_TYPE.contains(fileType), MemberAccountExceptionEnum.ERROR_CARD_IMAGE_TYPE);
        // 校验图片大小
        VerifyUtil.verify(file.getSize() <= IMAGE_BYTE_SIZE, MemberAccountExceptionEnum.ERROR_CARD_IMAGE_SIZE);
        // 上传图片
        FileDto fileDto = new FileDto();
        try {
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
            String imagePath = baseService.upload(fileDto).replace("http", "https");
            return Result.success(imagePath);
        } catch (IOException e) {
            log.error("上传文件失败");
            return Result.error("上传失败");
        }
    }

    @Data
    class FileInfo {
        // 请求时报序列化错误解决方案
        @JSONField(serialize = false)
        private MultipartFile file;
    }

}