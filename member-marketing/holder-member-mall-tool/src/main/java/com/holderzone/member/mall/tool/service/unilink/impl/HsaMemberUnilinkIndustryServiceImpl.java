package com.holderzone.member.mall.tool.service.unilink.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.constant.CommonConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.unilink.MemberUnilinkIndustryDTO;
import com.holderzone.member.common.enums.exception.MemberMallToolExceptionEnum;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.vo.unilink.MemberUnilinkIndustryIncludeSystemVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkIndustry;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystem;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkIndustryMapper;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkIndustryService;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemService;
import com.holderzone.member.mall.tool.transform.MemberUnilinkTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员通-行业Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
@Slf4j
public class HsaMemberUnilinkIndustryServiceImpl extends ServiceImpl<HsaMemberUnilinkIndustryMapper, HsaMemberUnilinkIndustry> implements HsaMemberUnilinkIndustryService {

    @Resource
    private HsaMemberUnilinkSystemService hsaMemberUnilinkSystemService;

    @Override
    public Boolean saveOrUpdate(MemberUnilinkIndustryDTO dto) {
        // 企业
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        // 运营主体
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        if (dto.getId() == null) {
            // 校验行业名称是否重复
            if (checkIndustryNameExists(enterpriseGuid, operSubjectGuid, dto.getName(), null)) {
                throw new MemberToolException(MemberMallToolExceptionEnum.INDUSTRY_NAME_EXISTS);
            }

            HsaMemberUnilinkIndustry industry = MemberUnilinkTransform.INSTANCES.fromMemberUnilinkIndustryDTO(dto);
            industry.setEnterpriseGuid(enterpriseGuid);
            industry.setOperSubjectGuid(operSubjectGuid);
            return save(industry);
        } else {
            HsaMemberUnilinkIndustry industry = this.getById(dto.getId());
            if (industry == null) {
                throw new MemberToolException(MemberMallToolExceptionEnum.INDUSTRY_NOT_EXISTS);
            }

            // 校验行业名称是否重复
            if (checkIndustryNameExists(industry.getEnterpriseGuid(), industry.getOperSubjectGuid(), dto.getName(), dto.getId())) {
                throw new MemberToolException(MemberMallToolExceptionEnum.INDUSTRY_NAME_EXISTS);
            }

            industry.setName(dto.getName());
            industry.setGmtModified(LocalDateTime.now());
            return updateById(industry);
        }
    }

    @Override
    public List<MemberUnilinkIndustryIncludeSystemVO> listByEnterpriseAndOperSubject(int isIncludeSystem) {
        // 企业
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        // 运营主体
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        // 查询行业列表
        List<HsaMemberUnilinkIndustry> industryList = getIndustryList(operSubjectGuid);

        if (CollUtil.isEmpty(industryList)) {
            hsaMemberUnilinkSystemService.initializeDefaultSystems(enterpriseGuid, operSubjectGuid);
            // 重新查询以获取初始化后的行业数据
            industryList = getIndustryList(operSubjectGuid);
        }

        // 查询行业对应的系统列表
        List<HsaMemberUnilinkSystem> systemList;
        if (isIncludeSystem == CommonConstant.YES) {
            systemList = hsaMemberUnilinkSystemService.list(new LambdaQueryWrapper<HsaMemberUnilinkSystem>()
                    .eq(HsaMemberUnilinkSystem::getEnterpriseGuid, enterpriseGuid)
                    .eq(HsaMemberUnilinkSystem::getOperSubjectGuid, operSubjectGuid)
                    .in(HsaMemberUnilinkSystem::getIndustryId, industryList.stream().map(HsaMemberUnilinkIndustry::getId).collect(Collectors.toList())));
        } else {
            systemList = new ArrayList<>();
        }

        // 组装行业列表
        return buildIndustryList(isIncludeSystem, industryList, systemList);
    }

    @Override
    public List<MemberUnilinkSelectOptionVO> listUnilinkIndustry() {
        List<HsaMemberUnilinkIndustry> industryList = getIndustryList(ThreadLocalCache.getOperSubjectGuid());

        return industryList.stream()
                .map(industry -> new MemberUnilinkSelectOptionVO()
                        .setValue(String.valueOf(industry.getId()))
                        .setLabel(industry.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 组装行业列表
     *
     * @param isIncludeSystem 是否包含系统列表
     * @param industryList    行业列表
     * @param systemList      系统列表
     * @return 行业列表
     */
    private static List<MemberUnilinkIndustryIncludeSystemVO> buildIndustryList(int isIncludeSystem, List<HsaMemberUnilinkIndustry> industryList, List<HsaMemberUnilinkSystem> systemList) {
        return industryList.stream()
                .map(industry -> {
                    MemberUnilinkIndustryIncludeSystemVO vo = new MemberUnilinkIndustryIncludeSystemVO()
                            .setId(industry.getId())
                            .setName(industry.getName());
                    // 是否包含系统列表
                    if (isIncludeSystem == CommonConstant.YES) {
                        List<MemberUnilinkSystemVO> systems = systemList.stream()
                                .filter(system -> system.getIndustryId().equals(industry.getId()))
                                .map(MemberUnilinkTransform.INSTANCES::toMemberUnilinkSystemVO)
                                .collect(Collectors.toList());
                        vo.setSystemList(systems);
                    } else {
                        vo.setSystemList(Collections.emptyList());
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询行业列表
     *
     * @param operSubjectGuid 运营主体GUID
     * @return 行业列表
     */
    private List<HsaMemberUnilinkIndustry> getIndustryList(String operSubjectGuid) {
        return list(new LambdaQueryWrapper<HsaMemberUnilinkIndustry>()
                .eq(HsaMemberUnilinkIndustry::getOperSubjectGuid, operSubjectGuid)
                .orderByAsc(HsaMemberUnilinkIndustry::getId));
    }

    /**
     * 校验行业名称是否重复
     *
     * @param enterpriseGuid  企业GUID
     * @param operSubjectGuid 运营主体GUID
     * @param industryName    行业名称
     * @param excludeId       需要排除的id
     * @return 是否存在重复
     */
    private boolean checkIndustryNameExists(String enterpriseGuid, String operSubjectGuid, String industryName, Long excludeId) {
        LambdaQueryWrapper<HsaMemberUnilinkIndustry> lambdaQueryWrapper = new LambdaQueryWrapper<HsaMemberUnilinkIndustry>()
                .eq(HsaMemberUnilinkIndustry::getEnterpriseGuid, enterpriseGuid)
                .eq(HsaMemberUnilinkIndustry::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberUnilinkIndustry::getName, industryName);

        if (excludeId != null) {
            lambdaQueryWrapper.ne(HsaMemberUnilinkIndustry::getId, excludeId);
        }
        return count(lambdaQueryWrapper) > 0;
    }

}
