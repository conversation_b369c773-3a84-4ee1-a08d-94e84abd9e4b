package com.holderzone.member.mall.tool.controller.ali;


import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.mall.tool.service.ali.IHsaAliAppletInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
@RestController
@RequestMapping("/ali_applet_info")
public class HsaAliAppletInfoController {

    @Resource
    private IHsaAliAppletInfoService hsaAliAppletInfoService;

    @ApiOperation("根据小程序appId获取授权信息")
    @GetMapping("/get_info")
    HsaAliAppletInfoVO getAliAppletInfoByOper(@RequestParam(value = "operSubjectGuid") String operSubjectGuid) {
        return hsaAliAppletInfoService.getAliAppletInfoByOper(operSubjectGuid);
    }


    @ApiOperation("根据小程序appId获取授权信息")
    @GetMapping("/get_appId")
    public AliAuthorizerInfoVO getByAuthAppId(@RequestParam(value = "authAppId") String authAppId) {
        return hsaAliAppletInfoService.getByAuthAppId(authAppId);
    }
}

