package com.holderzone.member.mall.tool.service.unilink.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.enums.MemberUnilinkSystemTypeEnum;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemConfig;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemConfigMapper;
import com.holderzone.member.mall.tool.service.unilink.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 会员通-系统配置Service实现
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Slf4j
@Service
public class HsaMemberUnilinkSystemConfigServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemConfigMapper, HsaMemberUnilinkSystemConfig> implements HsaMemberUnilinkSystemConfigService {

    @Resource
    private HsaMemberUnilinkSystemChannelConfigService systemChannelConfigService;

    @Resource
    private HsaMemberUnilinkSystemTerminalConfigService systemTerminalConfigService;

    @Resource
    private HsaMemberUnilinkSystemBizConfigService bizConfigService;

    @Resource
    private HsaMemberUnilinkSystemService hsaMemberUnilinkSystemService;

    @Resource
    private Executor memberMallToolThreadExecutor;

    @Override
    public MemberUnilinkSystemConfigVO listConfig() {
        log.info("开始查询系统配置列表");
        long startTime = System.currentTimeMillis();

        try {
            // 判断当前运营主体是否有REPAST餐饮云系统授权
            String operSubjectGuid = com.holderzone.member.common.constant.ThreadLocalCache.getOperSubjectGuid();
            boolean hasRepastAuth = hsaMemberUnilinkSystemService.listByOperSubject(operSubjectGuid)
                    .stream()
                    .anyMatch(systemVO -> systemVO.getSystemType() != null &&
                            MemberUnilinkSystemTypeEnum.REPAST == systemVO.getSystemType());

            // 使用CompletableFuture并行查询三个列表
            CompletableFuture<List<MemberUnilinkSelectOptionVO>> channelFuture = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return systemChannelConfigService.listSystemChannel(Boolean.TRUE);
                        } catch (Exception e) {
                            log.error("查询渠道列表异常", e);
                            return new ArrayList<>();
                        }
                    }, memberMallToolThreadExecutor);

            CompletableFuture<List<MemberUnilinkSelectOptionVO>> terminalFuture = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return systemTerminalConfigService.listSystemTerminal();
                        } catch (Exception e) {
                            log.error("查询终端列表异常", e);
                            return new ArrayList<>();
                        }
                    }, memberMallToolThreadExecutor);

            CompletableFuture<List<MemberUnilinkSelectOptionVO>> bizFuture = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return bizConfigService.listSystemBiz();
                        } catch (Exception e) {
                            log.error("查询业务列表异常", e);
                            return new ArrayList<>();
                        }
                    }, memberMallToolThreadExecutor);

            // 等待所有查询完成，设置超时时间
            List<MemberUnilinkSelectOptionVO> channelList = channelFuture.get(5, TimeUnit.SECONDS);
            List<MemberUnilinkSelectOptionVO> terminalList = terminalFuture.get(5, TimeUnit.SECONDS);
            List<MemberUnilinkSelectOptionVO> bizList = bizFuture.get(5, TimeUnit.SECONDS);

            // 没有授权餐饮云，过滤掉 systemType = MemberUnilinkSystemTypeEnum.REPAST.name() 的项
            if (!hasRepastAuth) {
                String repastType = MemberUnilinkSystemTypeEnum.REPAST.name();
                channelList = channelList.stream()
                        .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                        .collect(java.util.stream.Collectors.toList());
                terminalList = terminalList.stream()
                        .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                        .collect(java.util.stream.Collectors.toList());
                bizList = bizList.stream()
                        .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                        .collect(java.util.stream.Collectors.toList());
            }

            // 构建返回结果
            MemberUnilinkSystemConfigVO result = new MemberUnilinkSystemConfigVO()
                    .setChannel(CollectionUtils.isEmpty(channelList) ? new ArrayList<>() : channelList)
                    .setTerminal(CollectionUtils.isEmpty(terminalList) ? new ArrayList<>() : terminalList)
                    .setBiz(CollectionUtils.isEmpty(bizList) ? new ArrayList<>() : bizList);

            log.info("查询系统配置列表完成，耗时：{}ms", System.currentTimeMillis() - startTime);
            return result;

        } catch (Exception e) {
            log.error("查询系统配置列表异常", e);
            // 发生异常时返回空列表
            return new MemberUnilinkSystemConfigVO()
                    .setChannel(Collections.emptyList())
                    .setTerminal(Collections.emptyList())
                    .setBiz(Collections.emptyList());
        }
    }
}