package com.holderzone.member.mall.tool.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.tool.StoreHomeTemplateQO;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateVO;
import com.holderzone.member.mall.tool.entity.HsaStoreHomeTemplate;
import com.holderzone.member.mall.tool.mapper.HsaStoreHomeTemplateMapper;
import com.holderzone.member.mall.tool.service.HsaStoreHomeTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class HsaStoreHomeTemplateServiceImpl extends HolderBaseServiceImpl<HsaStoreHomeTemplateMapper, HsaStoreHomeTemplate> implements HsaStoreHomeTemplateService {

    @Override
    public PageResult pageInfo(StoreHomeTemplateQO query) {
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<StoreHomeTemplateVO> list = baseMapper.pageInfo(query);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public StoreHomeTemplateDetailsVO get(String guid) {
        return baseMapper.get(guid);
    }
}
