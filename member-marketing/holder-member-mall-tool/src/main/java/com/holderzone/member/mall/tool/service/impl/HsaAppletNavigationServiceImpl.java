package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.mall.AppletNavigationDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.malltool.MiniProgramEnum;
import com.holderzone.member.common.qo.mall.NavigationQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.tool.entity.HsaAppletNavigation;
import com.holderzone.member.mall.tool.mapper.HsaAppletNavigationMapper;
import com.holderzone.member.mall.tool.service.HsaAppletNavigationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class HsaAppletNavigationServiceImpl extends ServiceImpl<HsaAppletNavigationMapper, HsaAppletNavigation>
        implements HsaAppletNavigationService {

    @Resource
    private HsaAppletNavigationMapper hsaAppletNavigationMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public boolean saveAppletNavigation(NavigationQO request) {
        request.validate();
        if (CollectionUtil.isEmpty(request.getAppletNavigationDTOList())) {
            return false;
        }
        //删除之前的
        hsaAppletNavigationMapper.delete(new LambdaQueryWrapper<HsaAppletNavigation>()
                .eq(HsaAppletNavigation::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaAppletNavigation::getType, request.getType()));
        List<HsaAppletNavigation> list = new ArrayList<>();
        for (AppletNavigationDTO dto : request.getAppletNavigationDTOList()) {
            HsaAppletNavigation hsaAppletNavigation = new HsaAppletNavigation();
            hsaAppletNavigation.setGuid(guidGeneratorUtil.getStringGuid(HsaAppletNavigation.class.getCanonicalName()));
            hsaAppletNavigation.setGmtCreate(LocalDateTime.now());
            hsaAppletNavigation.setGmtModified(LocalDateTime.now());
            hsaAppletNavigation.setIsDelete(BooleanEnum.FALSE.getCode());
            hsaAppletNavigation.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            hsaAppletNavigation.setTitle(dto.getTitle());
            hsaAppletNavigation.setOnPicture(dto.getOnPicture());
            hsaAppletNavigation.setOffPicture(dto.getOffPicture());
            hsaAppletNavigation.setUrl(dto.getUrl());
            hsaAppletNavigation.setSelectName(dto.getSelectName());
            hsaAppletNavigation.setSelectValue(dto.getSelectValue());
            hsaAppletNavigation.setTypeText(dto.getTypeText());
            hsaAppletNavigation.setLinkId(dto.getLinkId());
            hsaAppletNavigation.setType(request.getType());
            list.add(hsaAppletNavigation);
        }
        return this.saveBatch(list);
    }

    @Override
    public List<AppletNavigationDTO> getAppletNavigation(Integer type) {
        if(type == null){
            type = MiniProgramEnum.WE_CHAT.getCode();
        }
        List<HsaAppletNavigation> list = hsaAppletNavigationMapper.selectList(new LambdaQueryWrapper<HsaAppletNavigation>()
                .eq(HsaAppletNavigation::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaAppletNavigation::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaAppletNavigation::getType, type));
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<AppletNavigationDTO> dtoList = Lists.newArrayList();
        for (HsaAppletNavigation hsaAppletNavigation : list) {
            AppletNavigationDTO appletNavigationDTO = new AppletNavigationDTO();
            appletNavigationDTO.setTitle(hsaAppletNavigation.getTitle());
            appletNavigationDTO.setUrl(hsaAppletNavigation.getUrl());
            appletNavigationDTO.setOffPicture(hsaAppletNavigation.getOffPicture());
            appletNavigationDTO.setOnPicture(hsaAppletNavigation.getOnPicture());
            appletNavigationDTO.setSelectName(hsaAppletNavigation.getSelectName());
            appletNavigationDTO.setSelectValue(hsaAppletNavigation.getSelectValue());
            appletNavigationDTO.setTypeText(hsaAppletNavigation.getTypeText());
            appletNavigationDTO.setLinkId(hsaAppletNavigation.getLinkId());
            appletNavigationDTO.setType(hsaAppletNavigation.getType());
            dtoList.add(appletNavigationDTO);
        }
        return dtoList;
    }

}
