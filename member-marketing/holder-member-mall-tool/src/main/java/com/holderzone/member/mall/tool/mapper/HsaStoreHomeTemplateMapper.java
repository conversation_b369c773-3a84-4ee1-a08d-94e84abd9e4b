package com.holderzone.member.mall.tool.mapper;

import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.qo.tool.StoreHomeTemplateQO;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateVO;
import com.holderzone.member.mall.tool.entity.HsaStoreHomeTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HsaStoreHomeTemplateMapper extends HolderBaseMapper<HsaStoreHomeTemplate> {

    List<StoreHomeTemplateVO> pageInfo(@Param("query") StoreHomeTemplateQO query);

    StoreHomeTemplateDetailsVO get(@Param("guid") String guid);
}
