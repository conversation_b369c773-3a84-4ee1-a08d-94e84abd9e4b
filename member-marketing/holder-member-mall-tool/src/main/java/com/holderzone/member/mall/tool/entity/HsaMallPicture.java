package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022年12月29日 下午5:37
 * @description 图片
 */
@Data
public class HsaMallPicture {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    /**
     * @see com.holderzone.member.common.enums.mall.PictureEnum
     */
    @ApiModelProperty(value = "图片类型 0：我的图片 1：图片库")
    private Integer pictureType;

    @ApiModelProperty(value = "图片库位置 0:底部导航 1:导航图标")
    private Integer picturePosition;

    @ApiModelProperty(value = "图片地址")
    private String pictureUrl;

}
