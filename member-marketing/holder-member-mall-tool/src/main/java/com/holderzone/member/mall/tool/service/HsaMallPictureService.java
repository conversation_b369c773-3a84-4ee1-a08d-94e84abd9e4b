package com.holderzone.member.mall.tool.service;

import com.holderzone.member.common.qo.mall.DeletePictureQO;
import com.holderzone.member.common.qo.mall.MallPictureQO;
import com.holderzone.member.common.vo.mall.MallPictureVO;

import java.util.List;

public interface HsaMallPictureService {

    Boolean saveHsaMallPicture(MallPictureQO request);

    List<MallPictureVO> getMallPicture(String operSubjectGuid, int pictureType);

    boolean deletePicture(DeletePictureQO request);
}
