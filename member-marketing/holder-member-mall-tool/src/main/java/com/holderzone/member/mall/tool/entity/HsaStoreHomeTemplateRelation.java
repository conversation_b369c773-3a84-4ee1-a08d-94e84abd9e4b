package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 店铺主页模板关联门店
 * </p>
 */
@Data
public class HsaStoreHomeTemplateRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "店铺guid")
    private String templateGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
