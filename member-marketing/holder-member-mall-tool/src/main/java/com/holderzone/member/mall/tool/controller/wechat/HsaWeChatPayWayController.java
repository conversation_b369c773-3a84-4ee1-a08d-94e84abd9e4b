package com.holderzone.member.mall.tool.controller.wechat;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.wechat.WechatPayWayQO;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatPayWayService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 微信小程序配置
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/pay/way")
public class HsaWeChatPayWayController {

    @Resource
    private HsaWechatPayWayService hsaWechatPayWayService;

    @ApiOperation("保存配置")
    @PostMapping("/save")
    Result<Boolean> saveOrUpdateWeChatPayWay(@RequestBody WechatPayWayQO request) {
        hsaWechatPayWayService.saveOrUpdateWeChatPayWay(request);
        return Result.success();
    }

    @ApiOperation("获取配置")
    @GetMapping("/get")
    Result<List<String>> queryByOperSubjectGuid() {
        return Result.success(hsaWechatPayWayService.queryByOperSubjectGuid());
    }

}

