package com.holderzone.member.mall.tool.service.wechat;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.qo.wechat.WechatPayWayQO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatConfigInfo;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatPayWay;

import java.util.List;

public interface HsaWechatPayWayService extends IService<HsaWechatPayWay> {

    /**
     * 保存或者修改
     * @param request
     */
    void saveOrUpdateWeChatPayWay(WechatPayWayQO request);


    /**
     * 查询
     * @return
     */
    List<String> queryByOperSubjectGuid();
}
