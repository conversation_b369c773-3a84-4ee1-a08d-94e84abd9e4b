package com.holderzone.member.mall.tool.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.MallPageQO;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.mall.MallPageVO;
import com.holderzone.member.mall.tool.entity.HsaMallPage;
import com.holderzone.member.mall.tool.mapper.HsaMallPageMapper;
import com.holderzone.member.mall.tool.service.IHsaMallPageService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 微页面表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Service
@AllArgsConstructor
public class HsaMallPageServiceImpl extends ServiceImpl<HsaMallPageMapper, HsaMallPage> implements IHsaMallPageService {


    @Resource
    private HsaMallPageMapper hsaMallPageMapper;

    @Override
    public PageResult queryMallPage(MallPageQO request){
        request.validate();
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<MallPageVO> list = hsaMallPageMapper.queryMallPage(request);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }
}
