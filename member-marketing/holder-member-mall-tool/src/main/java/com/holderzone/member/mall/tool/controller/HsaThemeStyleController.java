package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.ThemeStyleQO;
import com.holderzone.member.mall.tool.service.HsaThemeStyleService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/theme")
@Slf4j
public class HsaThemeStyleController {

    @Resource
    private HsaThemeStyleService hsaThemeStyleService;

    @ApiOperation("保存主题风格")
    @PostMapping(value = "/saveOrUpdateThemeStyle", produces = "application/json;charset=utf-8")
    public Result saveOrUpdateThemeStyle(@RequestBody ThemeStyleQO request) {
        return Result.success(hsaThemeStyleService.saveOrUpdateThemeStyle(request));
    }

    @ApiOperation("查询主题风格")
    @GetMapping(value = "/getThemeStyle", produces = "application/json;charset=utf-8")
    public Result getThemeStyle(Integer type) {

        return Result.success(hsaThemeStyleService.getThemeStyle(type));
    }
}
