package com.holderzone.member.mall.tool.controller;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.malltool.MallPageReqDTO;
import com.holderzone.member.common.dto.malltool.MallPageRespDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.MallPageQO;
import com.holderzone.member.common.qo.tool.PagePopularizeQO;
import com.holderzone.member.mall.tool.manager.HsaMallPageManager;
import com.holderzone.member.mall.tool.service.IHsaMallPageService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 微页面表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/hsa_mall_page")
public class HsaMallPageController {

    private HsaMallPageManager mallPageManage;


    private IHsaMallPageService hsaMallPageService;

    @ApiOperation("新增或编辑微页面")
    @PostMapping("/save_or_update")
    public Result<Void> saveOrUpdate(@RequestBody @Validated MallPageReqDTO reqDTO) {
        mallPageManage.saveOrUpdate(reqDTO);
        return Result.success();
    }

    @ApiOperation("微页面详情")
    @GetMapping("/get")
    public Result<MallPageRespDTO> get(@RequestParam("guid") String guid) {
        return Result.success(mallPageManage.get(guid));
    }

    @ApiOperation("查询微页面列表")
    @PostMapping("/queryMallPage")
    public Result<PageResult> queryMallPage(@RequestBody @Validated MallPageQO request) {

        return Result.success(hsaMallPageService.queryMallPage(request));
    }

    @ApiOperation("批量删除微页面")
    @PostMapping("/delete_batch")
    public Result<Void> deleteBatch(@RequestBody SingleDataDTO request) {
        mallPageManage.deleteBatch(request);
        return Result.success();
    }

    /**
     * 更改微页面状态
     *
     * @param guid 页面guid
     * @return 当前页面状态: 0未发布 1已发布 -1表示更新失败
     */
    @ApiOperation("更改微页面状态")
    @GetMapping("/update_state")
    public Result<Integer> updateState(@RequestParam("guid") String guid) {
        return Result.success(mallPageManage.updateState(guid));
    }

    @ApiOperation("页面推广")
    @PostMapping("/popularize")
    public Result<String> popularize(@RequestBody PagePopularizeQO qo) {
        return Result.success(mallPageManage.popularize(qo));
    }
}
