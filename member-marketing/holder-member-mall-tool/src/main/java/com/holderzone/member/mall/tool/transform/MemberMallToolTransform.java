package com.holderzone.member.mall.tool.transform;

import com.holderzone.member.common.dto.malltool.BasicPageReqDTO;
import com.holderzone.member.common.dto.malltool.BasicPageRespDTO;
import com.holderzone.member.common.dto.malltool.MallPageReqDTO;
import com.holderzone.member.common.dto.malltool.MallPageRespDTO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import com.holderzone.member.mall.tool.entity.HsaBasicPage;
import com.holderzone.member.mall.tool.entity.HsaMallPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberMallToolTransform {

    MemberMallToolTransform INSTANCE = Mappers.getMapper(MemberMallToolTransform.class);

    HsaBasicPage reqDTO2BasicPage(BasicPageReqDTO reqDTO);

    List<HsaBasicPage> reqDTOList2BasicPageList(List<BasicPageReqDTO> reqDTOList);

    BasicPageRespDTO basicPage2BasicPageDTO(HsaBasicPage basicPage);

    List<BasicPageRespDTO> basicPageList2BasicPageDTOList(List<HsaBasicPage> basicPageList);

    HsaMallPage mallPageReqDTO2mallPage(MallPageReqDTO reqDTO);

    MallPageRespDTO mallPage2MallPageRespDTO(HsaMallPage mallPage);

    LinkUniteVO mallPage2LinkUniteVO(HsaMallPage mallPage);

    List<LinkUniteVO> mallPageList2LinkUniteVOList(List<HsaMallPage> list);

    @Mappings({
            @Mapping(source = "store_id", target = "guid"),
            @Mapping(source = "store_name", target = "name")
    })
    LinkUniteVO storeInfoVO2LinkUniteVO(StoreInfoVO storeInfoVO);

    List<LinkUniteVO> storeInfoVOList2LinkUniteVOList(List<StoreInfoVO> storeInfoVOList);

    @Mappings({
            @Mapping(source = "category_id", target = "guid"),
            @Mapping(source = "category_name", target = "name"),
            @Mapping(source = "commodity_number", target = "commodityNumber"),
    })
    LinkUniteVO categoryVO2LinkUniteVO(ProductCrmCategoryVO responseList);

    List<LinkUniteVO> categoryVOList2LinkUniteVOList(List<ProductCrmCategoryVO> responseList);

    @Mappings({
            @Mapping(source = "commodityCode", target = "guid"),
            @Mapping(source = "commodityName", target = "name")
    })
    LinkUniteVO commodityVO2LinkUniteVO(CommodityVO commodityVO);

    List<LinkUniteVO> commodityVOList2LinkUniteVOList(List<CommodityVO> commodityVOList);
}