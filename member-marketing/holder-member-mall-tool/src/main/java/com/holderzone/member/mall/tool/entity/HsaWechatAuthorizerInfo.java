package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @version 1.0
 * @className WxStoreAuthorizerInfoDO
 * @description 微信授权方信息DO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HsaWechatAuthorizerInfo {

    @TableId
    private Long id;

    private String guid;

    private String operSubjectGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 授权方AppId
     */
    private String authorizerAppid;

    /**
     * 运营商Appid
     */
    private String componentAppid;

    /**
     * 授权方accessToken
     */
    private String authorizerAccessToken;

    /**
     * 过期时间，存入到期时间的时间戳，若当前时间戳大于该时间戳，则已过期
     */
    private Long expiresIn;

    /**
     * 授权方刷新token
     */
    private String authorizerRefreshToken;

    /**
     * 已授权权限集合（以JSON格式存入）
     */
    private String funcInfo;

    /**
     * 授权方昵称（公众号名）
     */
    private String nickName;

    /**
     * 授权方头像
     */
    private String headImg;

    /**
     * 授权方公众号类型
     * 0代表订阅号，
     * 1代表由历史老帐号升级后的订阅号，
     * 2代表服务号
     */
    private Integer serviceTypeInfo;

    /**
     * 授权方认证类型
     * -1代表未认证，
     * 0代表微信认证，
     * 1代表新浪微博认证，
     * 2代表腾讯微博认证，
     * 3代表已资质认证通过但还未通过名称认证，
     * 4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，
     * 5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
     */
    private Integer verifyTypeInfo;

    /**
     * 授权方公众号的原始ID
     */
    private String userName;

    /**
     * 公众号的主体名称
     */
    private String principalName;

    /**
     * 授权方公众号所设置的微信号，可能为空
     */
    private String alias;

    /**
     * 二维码图片的URL
     */
    private String qrcodeUrl;

    /**
     * 二维码图片的URL,oss地址,微信有防盗
     */
    private String qrcodeUrlOss;

    /**
     * 帐号介绍
     */
    private String signature;

    /**
     * 解绑用户guid
     */
    private String unBandUserGuid;

    /**
     * 解绑时间
     */
    private LocalDateTime unBandTime;

    @TableLogic
    private Integer isDeleted;

    @Version
    private Integer version = 0;

}
