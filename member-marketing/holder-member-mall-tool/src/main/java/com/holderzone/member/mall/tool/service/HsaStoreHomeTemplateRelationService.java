package com.holderzone.member.mall.tool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.mall.tool.entity.HsaStoreHomeTemplateRelation;

import java.util.List;

/**
 * <p>
 * 店铺主页模板关联门店 服务类
 * </p>
 */
public interface HsaStoreHomeTemplateRelationService extends IService<HsaStoreHomeTemplateRelation> {

    Integer countByTemplateGuid(String templateGuid);

    String getTemplateGuidByStoreGuid(String storeGuid);

    List<String> excludeByTemplateGuid(String templateGuid);

    List<String> listTemplateGuidByStoreGuids(List<String> storeGuids);

    List<String> listStoreGuidByTemplateGuid(String templateGuid);

    void removeByTemplateGuid(String templateGuid);

}
