package com.holderzone.member.mall.tool.service.unilink.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.constant.CommonConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.unilink.AuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.MemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.UnAuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.enums.MemberUnilinkIndustryTypeEnum;
import com.holderzone.member.common.enums.exception.MemberMallToolExceptionEnum;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkIndustry;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystem;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemMapper;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkIndustryService;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemService;
import com.holderzone.member.mall.tool.transform.MemberUnilinkTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员通-系统Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
@Slf4j
public class HsaMemberUnilinkSystemServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemMapper, HsaMemberUnilinkSystem> implements HsaMemberUnilinkSystemService {
    @Resource
    private HsaMemberUnilinkIndustryService hsaMemberUnilinkIndustryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeDefaultSystems(String enterpriseGuid, String operSubjectGuid) {
        log.info("初始化默认行业，enterpriseGuid：{}，operSubjectGuid：{}", enterpriseGuid, operSubjectGuid);
        List<MemberUnilinkIndustryTypeEnum> defaultIndustryTypeList = MemberUnilinkIndustryTypeEnum.getDefaultIndustryTypeList();
        List<HsaMemberUnilinkSystem> allSystemList = new ArrayList<>();
        try {
            for (MemberUnilinkIndustryTypeEnum industryType : defaultIndustryTypeList) {
                HsaMemberUnilinkIndustry industry = new HsaMemberUnilinkIndustry(enterpriseGuid, operSubjectGuid, industryType.getIndustryName());
                hsaMemberUnilinkIndustryService.save(industry);

                List<HsaMemberUnilinkSystem> defaultSystemList = HsaMemberUnilinkSystem.getDefaultSystemListByIndustryType(industryType, industry.getId(), enterpriseGuid, operSubjectGuid);
                if (CollUtil.isNotEmpty(defaultSystemList)) {
                    allSystemList.addAll(defaultSystemList);
                }
            }

            if (CollUtil.isNotEmpty(allSystemList)) {
                log.info("初始化默认系统，enterpriseGuid：{}，operSubjectGuid：{}", enterpriseGuid, operSubjectGuid);
                saveBatch(allSystemList);
            }
        } catch (DuplicateKeyException e) {
            log.error("重复初始化默认行业，enterpriseGuid：{}，operSubjectGuid：{}", enterpriseGuid, operSubjectGuid);
        }
    }

    @Override
    public Boolean saveSystem(MemberUnilinkSystemDTO dto) {
        // 企业
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        // 运营主体
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        // 校验系统备注是否重复
        if (checkSystemRemarkExists(enterpriseGuid, operSubjectGuid, dto.getSystemRemark(), null)) {
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_REMARK_EXISTS);
        }

        // 保存系统
        HsaMemberUnilinkSystem system = MemberUnilinkTransform.INSTANCES.fromMemberUnilinkSystemDTO(dto);
        system.setEnterpriseGuid(enterpriseGuid);
        system.setOperSubjectGuid(operSubjectGuid);
        return this.save(system);
    }

    @Override
    public Boolean authSystem(AuthMemberUnilinkSystemDTO dto) {
        HsaMemberUnilinkSystem system = this.getById(dto.getId());
        if (system == null) {
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_NOT_EXISTS);
        }

        // 校验是否重复授权
        if (checkAuthExists(system, dto)) {
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_AUTH_EXISTS);
        }

        // 不能对相同运营主体授权
        if (system.getOperSubjectGuid().equals(dto.getAuthOperSubjectGuid())){
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_AUTH_SAME_OPER_SUBJECT);
        }

        system.setIsAuth(CommonConstant.YES)
                .setUnifiedMember(dto.getUnifiedMember())
                .setUnifiedMarketing(dto.getUnifiedMarketing())
                .setAuthEnterpriseGuid(dto.getAuthEnterpriseGuid())
                .setAuthOperSubjectGuid(dto.getAuthOperSubjectGuid())
                .setAuthEnterpriseName(dto.getAuthEnterpriseName())
                .setAuthOperSubjectName(dto.getAuthOperSubjectName())
                .setGmtModified(LocalDateTime.now());

        return updateById(system);
    }

    @Override
    public Boolean unAuthSystem(UnAuthMemberUnilinkSystemDTO dto) {
        HsaMemberUnilinkSystem system = this.getById(dto.getId());
        if (system == null) {
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_NOT_EXISTS);
        }

        // 解除授权，清空授权信息
        system.setIsAuth(CommonConstant.NO)
                .setAuthEnterpriseGuid(StrUtil.EMPTY)
                .setAuthOperSubjectGuid(StrUtil.EMPTY)
                .setAuthEnterpriseName(StrUtil.EMPTY)
                .setAuthOperSubjectName(StrUtil.EMPTY)
                .setGmtModified(LocalDateTime.now());

        return updateById(system);
    }

    /**
     * 校验系统备注是否重复
     *
     * @param enterpriseGuid  企业GUID
     * @param operSubjectGuid 运营主体GUID
     * @param systemRemark    系统备注
     * @param excludeId       需要排除的id
     * @return 是否存在重复
     */
    private boolean checkSystemRemarkExists(String enterpriseGuid, String operSubjectGuid, String systemRemark, Long excludeId) {
        LambdaQueryWrapper<HsaMemberUnilinkSystem> lambdaQueryWrapper = new LambdaQueryWrapper<HsaMemberUnilinkSystem>()
                .eq(HsaMemberUnilinkSystem::getEnterpriseGuid, enterpriseGuid)
                .eq(HsaMemberUnilinkSystem::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberUnilinkSystem::getSystemRemark, systemRemark);

        if (excludeId != null) {
            lambdaQueryWrapper.ne(HsaMemberUnilinkSystem::getId, excludeId);
        }
        return count(lambdaQueryWrapper) > 0;
    }

    /**
     * 校验是否重复授权
     *
     * @param system 系统
     * @param dto    请求参数
     * @return 是否存在
     */
    private boolean checkAuthExists(HsaMemberUnilinkSystem system, AuthMemberUnilinkSystemDTO dto) {
        LambdaQueryWrapper<HsaMemberUnilinkSystem> lambdaQueryWrapper = new LambdaQueryWrapper<HsaMemberUnilinkSystem>()
                .eq(HsaMemberUnilinkSystem::getSystemType, system.getSystemType())
                .eq(HsaMemberUnilinkSystem::getEnterpriseGuid, system.getEnterpriseGuid())
                .eq(HsaMemberUnilinkSystem::getOperSubjectGuid, system.getOperSubjectGuid())
                .eq(HsaMemberUnilinkSystem::getAuthEnterpriseGuid, dto.getAuthEnterpriseGuid())
                .eq(HsaMemberUnilinkSystem::getAuthOperSubjectGuid, dto.getAuthOperSubjectGuid())
                .ne(HsaMemberUnilinkSystem::getId, system.getId());

        return count(lambdaQueryWrapper) > 0;
    }

    @Override
    public List<MemberUnilinkSystemVO> listByOperSubject(String operSubjectGuid) {
        List<HsaMemberUnilinkSystem> systemList = this.list(new LambdaQueryWrapper<HsaMemberUnilinkSystem>()
                .eq(HsaMemberUnilinkSystem::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberUnilinkSystem::getIsAuth, NumberConstant.NUMBER_1)
        );

        return systemList.stream()
                .map(MemberUnilinkTransform.INSTANCES::toMemberUnilinkSystemVO)
                .collect(Collectors.toList());
    }
}
