package com.holderzone.member.mall.tool.entity.unilink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员通-系统业务配置
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@Accessors(chain = true)
@TableName("hsa_member_unilink_system_biz_config")
public class HsaMemberUnilinkSystemBizConfig implements Serializable {

    private static final long serialVersionUID = 2526788689166606134L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 系统配置ID
     */
    private Integer systemConfigId;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
} 