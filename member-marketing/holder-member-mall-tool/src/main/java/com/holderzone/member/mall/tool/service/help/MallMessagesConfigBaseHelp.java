package com.holderzone.member.mall.tool.service.help;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.enums.malltool.MsgTypeEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.qo.tool.ShortMessagesConfigDTO;

import java.util.ArrayList;
import java.util.List;

public class MallMessagesConfigBaseHelp {

    private static final String SKIP_ORDER = "/package/orderList/orderList?orderState=all";

    private static final String SKIP_BALANCE_DETAIL = "/package/balanceDetail/balanceDetail?";

    private static final String STORE_RECEIVING_ORDERS = "商家接单通知";

    //暂无
    private static final String STORE_RECEIVING_ORDERS_DETAIL = "暂无";

    //掌控者
    private static final String SMS_SIGN = "掌控者";

    private MallMessagesConfigBaseHelp() {
        throw new IllegalStateException("Utility class");
    }

    public static List<MessagesConfigQO> initMessagesConfig(String guid) {
        List<MessagesConfigQO> messagesConfigQOList = new ArrayList<>();
        MessagesConfigQO messagesConfigQO3 = new MessagesConfigQO();
        messagesConfigQO3.setAppletMsgKid("7,3,6")
                .setDataSort("门店名称:thing7,订单商品:thing3,温馨提示:thing6")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("订单评价提醒")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setScenarioDescription("评价")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("8996")
                .setAppletMsgContent("门店名称:XX门店\n订单商品:菠萝蜜*1\n温馨提示:点击进行订单评价\n")
                .setPushRule("订单触发收货，订单待评价");
        messagesConfigQOList.add(messagesConfigQO3);


        MessagesConfigQO messagesConfigQO5 = new MessagesConfigQO();
        messagesConfigQO5.setAppletMsgKid("1,9,3,2,8")
                .setDataSort("商品信息:thing1,门店名称:thing9,取货时段:date3,取货码:character_string2,温馨提示:thing8")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("商品取货提醒")
                .setScenarioDescription("取货")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("4049")
                .setAppletMsgContent("商品信息:百事可乐（罐）\n门店名称:华红超市\n取货时段:2019-12-10 8:00--16:00（此时间段根据客户购买的时间开始计算保存时间）\n取货码:112343\n温馨提示：请在23:00前取走商品，以免超时回收。")
                .setMpTemplateNo("44538")
                .setMpMsgContent("取餐号:0001\n餐厅名称:微信饭店\n商品名称:番茄牛肉\n")
                .setPushRule("到店自提”订单备货完成");
        messagesConfigQOList.add(messagesConfigQO5);


        MessagesConfigQO messagesConfigQO7 = new MessagesConfigQO();
        messagesConfigQO7.setAppletMsgKid("8,18,4,10,11")
                .setDataSort("商品信息:thing8,配送方式:thing18,配送人:name4,配送员电话:phone_number10,温馨提示:thing11")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("订单配送通知")
                .setScenarioDescription("配送")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("1128")
                .setAppletMsgContent("商品信息:苹果无线耳机\n配送方式:商家自配送\n配送人:梁某某\n配送员电话:+86-0766-66888866\n温馨提示:点击进入查看详细物流信息")
                .setPushRule("同城配送订单触发“开始配送”");
        messagesConfigQOList.add(messagesConfigQO7);


        MessagesConfigQO messagesConfigQO9 = new MessagesConfigQO();
        messagesConfigQO9.setAppletMsgKid("8,1,4,7,2")
                .setDataSort("变动类型:thing8,变动金额:amount1,时间:date4,账户名称:thing7,账户余额:amount2")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_BALANCE.getCode())
                .setMsgTitle("资金变动提醒")
                .setScenarioDescription("余额")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("4148")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent("变动类型:VIP会员卡\n变动金额:-20.00\n时间:2019-12-11 09:00:00\n账户名称:五粮液\n账户余额:180.00\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】#Name#您好，您的余额 #增加/扣减 Money#元，卡内余额#Money#元，期待您的光临")
                .setPushRule("会员卡余额用户消费/充值/退款/过期的变动");

        List<ShortMessagesConfigDTO> shortMessages = getShortMessagesConfigDTOS();

        messagesConfigQO9.setShortMessageContentJson(JSON.toJSONString(shortMessages));

        messagesConfigQOList.add(messagesConfigQO9);


//        MessagesConfigQO messagesConfigQO10 = new MessagesConfigQO();
//        messagesConfigQO10.setAppletMsgKid(STORE_RECEIVING_ORDERS_DETAIL)
//                .setDataSort(STORE_RECEIVING_ORDERS_DETAIL)
//                .setOperSubjectGuid(guid)
//                .setMsgType(0)
//                .setIsDelete(0)
//                .setMsgCategory(MsgTypeEnum.MSG_MEMBER.getCode())
//                .setMsgTitle("开卡成功通知")
//                .setScenarioDescription("开卡成功通知")
//                .setTitle(MsgTypeEnum.MSG_MEMBER.getDes())
//                .setMpTitle(MsgTypeEnum.MSG_MEMBER.getDes())
//                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
//                .setAppletTemplateNo("101")
//                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
//                .setAppletMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
//                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
//                .setMessageContent("【#Brand#】恭喜您成功开通会员卡！当前余额：#Surplus#元，谢谢您的支持，祝您生活愉快！")
//                .setPushRule("开通会员卡成功");
//
//        List<ShortMessagesConfigDTO> shortOpenCardMessages = new ArrayList<>();
//
//        ShortMessagesConfigDTO shortMessagesOpenCardDTO = new ShortMessagesConfigDTO();
//        shortMessagesOpenCardDTO.setChangeType(AmountSourceTypeEnum.OPEN_CARD.getCode());
//        shortMessagesOpenCardDTO.setSmsSign(SMS_SIGN);
//        shortMessagesOpenCardDTO.setSmsTemplateCode("SMS_479065635");
//        shortOpenCardMessages.add(shortMessagesOpenCardDTO);
//        messagesConfigQO10.setShortMessageContentJson(JSON.toJSONString(shortOpenCardMessages));
//        messagesConfigQOList.add(messagesConfigQO10);

        MessagesConfigQO messagesConfigQO11 = new MessagesConfigQO();
        messagesConfigQO11.setAppletMsgKid("1,6,4,10,5")
                .setDataSort("券名称:thing1,优惠券数量:number6,过期日期:time4,商家名称:time10,备注:thing5")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_ACTIVITY.getCode())
                .setMsgTitle("优惠券到账提醒")
                .setScenarioDescription("优惠券到账提醒")
                .setTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setMpTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletTemplateNo("3209")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent("券名称:【优惠券】100-20\n优惠券数量:6\n过期日期:2019-12-12 18:22:20\n商家名称:孖啡咖啡馆\n备注:领券成功！请立即使用\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】恭喜您获得优惠券#Coupon#，请及时于#Time#前使用，期待您的光临！")
                .setPushRule("会员账户收到优惠券通知");

        List<ShortMessagesConfigDTO> shortToCouponMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesCouponDTO = new ShortMessagesConfigDTO();
        shortMessagesCouponDTO.setSmsSign(SMS_SIGN);
        shortMessagesCouponDTO.setSmsTemplateCode("SMS_478950586");
        shortToCouponMessages.add(shortMessagesCouponDTO);
        messagesConfigQO11.setShortMessageContentJson(JSON.toJSONString(shortToCouponMessages));

        messagesConfigQOList.add(messagesConfigQO11);

        MessagesConfigQO messagesConfigQO12 = new MessagesConfigQO();
        messagesConfigQO12.setAppletMsgKid("2,7,3,1")
                .setDataSort("券名称:thing2,券数量:number7,截止日期:time3,温馨提示:thing1")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_ACTIVITY.getCode())
                .setMsgTitle("优惠券过期提醒")
                .setScenarioDescription("优惠券过期提醒")
                .setTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setMpTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletTemplateNo("3118")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent("券名称:【代金券】100-20\n券数量:1\n截止日期:2019-12-12 18:22:20\n温馨提示:您的优惠券即将过期")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】您的卡券包里有XX张优惠券就要到期了，请及时使用！期待您的光临！")
                .setPushRule("优惠券临近3天即将失效提醒\n" +
                        "\n" +
                        "1、多张即将过期券仅触发一次\n" +
                        "2、已提醒过的券不可重复提醒\n" +
                        "3、新到账的券有效期≤3天不提醒\n" +
                        "4、每日11:30触发一次");

        List<ShortMessagesConfigDTO> shortExpiredCouponMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesExpiredCouponDTO = new ShortMessagesConfigDTO();
        shortMessagesExpiredCouponDTO.setSmsSign(SMS_SIGN);
        shortMessagesExpiredCouponDTO.setSmsTemplateCode("SMS_479525288");
        shortExpiredCouponMessages.add(shortMessagesExpiredCouponDTO);
        messagesConfigQO12.setShortMessageContentJson(JSON.toJSONString(shortExpiredCouponMessages));

        messagesConfigQOList.add(messagesConfigQO12);

        MessagesConfigQO messagesConfigQO13 = new MessagesConfigQO();
        messagesConfigQO13.setAppletMsgKid("11,16,6,12,5")
                .setDataSort("活动名称:thing11,活动标题:thing16,活动时间:time6,联系电话:phone_number12,温馨提示:thing5")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_ACTIVITY.getCode())
                .setMsgTitle("活动提醒")
                .setScenarioDescription("活动提醒")
                .setTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setMpTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("666")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("活动名称:积分秒杀\n活动标题:有福利！秒杀好物 限时钜惠\n活动时间:2020年5月1号 上午9:00-2020年5月4号 下午6:00\n联系电话:18108406937\n温馨提示:活动即将开始，请做好准备\n")
                .setMpMsgContent("储值门店:新城店\n储值金额:1200.00元\n储值时间:2023年11月11日 11:11:11\n储值余额:3000.00元\n")
                .setMessageContent("【#Brand#】#Name#您好，您的余额 #增加/扣减 Money#元，卡内余额#Money#元，期待您的光临")
                .setPushRule("管理后台手动触发");

        messagesConfigQOList.add(messagesConfigQO13);

        MessagesConfigQO messagesConfigQO14 = new MessagesConfigQO();
        messagesConfigQO14.setAppletMsgKid("8,15,18,5")
                .setDataSort("变动原因:thing8,积分变动值:character_string15,积分余额:number18,温馨提示:thing5")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_INTEGRATE.getCode())
                .setMsgTitle("积分变动通知")
                .setScenarioDescription("积分变动")
                .setTitle(MsgTypeEnum.MSG_INTEGRATE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_INTEGRATE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("3613")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("变动原因:原因\n积分变动值:+123\n积分余额:300\n温馨提示:您的积分已经到账，快去看看吧~\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setPushRule("会员账户积分发生变动时");


        messagesConfigQOList.add(messagesConfigQO14);

        MessagesConfigQO messagesConfigQO15 = new MessagesConfigQO();
        messagesConfigQO15.setAppletMsgKid("1,2")
                .setDataSort("积分失效时间:time1,剩余积分:number2")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_INTEGRATE.getCode())
                .setMsgTitle("积分即将失效提醒")
                .setScenarioDescription("积分过期")
                .setTitle(MsgTypeEnum.MSG_INTEGRATE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_INTEGRATE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("61038")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("积分失效时间:2024年4月11日 10：00\n剩余积分:2000\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setPushRule("积分触发过期前N天触发提醒");


        messagesConfigQOList.add(messagesConfigQO15);

        MessagesConfigQO messagesConfigQO16 = new MessagesConfigQO();
        messagesConfigQO16.setAppletMsgKid("29,3,1,2,8")
                .setDataSort("商品名称:thing29,发货时间:time3,快递公司:thing1,快递单号:character_string2,备注:thing8")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("订单发货通知")
                .setScenarioDescription("发货")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("1458")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("商品名称:订单分类记录\n发货时间:2019-11-1 09:29:26\n快递公司:海硕高铁速递\n快递单号:EP136542054362\n备注:天气原因，时间可能延长\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setPushRule("物流配送订单触发发货");
        messagesConfigQOList.add(messagesConfigQO16);

        MessagesConfigQO messagesConfigQO17 = new MessagesConfigQO();
        messagesConfigQO17.setAppletMsgKid("19,15,22,6,9")
                .setDataSort("客户名称:thing19,联系方式:phone_number15,收货地址:thing22,商品名称:thing6,订单金额:amount9")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("订单取消通知")
                .setScenarioDescription("订单取消")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("1134")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("客户名称:小王\n联系方式:13510543756\n收货地址:某某地址\n商品名称:泰国菠萝1个\n订单金额:￥1300.00\n")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setPushRule("商家触发订单取消");
        messagesConfigQOList.add(messagesConfigQO17);

        MessagesConfigQO messagesConfigQO18 = new MessagesConfigQO();
        messagesConfigQO18.setAppletMsgKid("1,2,3,12,13")
                .setDataSort("退款状态:thing1,退款商品:thing2,退款金额:amount3,门店名称:thing12,备注:thing13")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("退款通知")
                .setScenarioDescription("退款")
                .setTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setMpTitle(MsgTypeEnum.MSG_TRADE.getDes())
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("1451")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("退款状态:退款成功（店主已同意退款）\n退款商品:泰国小菠萝*1 金枕榴莲*2\n退款金额:¥198.69\n门店名称:全家吴中路三店\n备注:退款将原路返回您的支付账户，请注意查收~")
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setPushRule("用户申请取消，后台审核通过/拒绝");
        messagesConfigQOList.add(messagesConfigQO18);
        return messagesConfigQOList;
    }

    private static List<ShortMessagesConfigDTO> getShortMessagesConfigDTOS() {
        List<ShortMessagesConfigDTO> shortMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesRechargeDTO = new ShortMessagesConfigDTO();
        shortMessagesRechargeDTO.setChangeType(AmountSourceTypeEnum.RECHARGE.getCode());
        shortMessagesRechargeDTO.setSmsSign(SMS_SIGN);
        shortMessagesRechargeDTO.setSmsTemplateCode("SMS_478995597");
        shortMessages.add(shortMessagesRechargeDTO);

        ShortMessagesConfigDTO shortMessagesAdminEditDTO = new ShortMessagesConfigDTO();
        shortMessagesAdminEditDTO.setChangeType(AmountSourceTypeEnum.ADMIN_EDIT.getCode());
        shortMessagesAdminEditDTO.setSmsSign(SMS_SIGN);
        shortMessagesAdminEditDTO.setSmsTemplateCode("SMS_481675058");
        shortMessages.add(shortMessagesAdminEditDTO);

        ShortMessagesConfigDTO shortMessagesRetreatDTO = new ShortMessagesConfigDTO();
        shortMessagesRetreatDTO.setChangeType(AmountSourceTypeEnum.RETREAT_CARD.getCode());
        shortMessagesRetreatDTO.setSmsSign(SMS_SIGN);
        shortMessagesRetreatDTO.setSmsTemplateCode("SMS_479080624");
        shortMessages.add(shortMessagesRetreatDTO);

        ShortMessagesConfigDTO shortMessagesConsumptionDTO = new ShortMessagesConfigDTO();
        shortMessagesConsumptionDTO.setChangeType(AmountSourceTypeEnum.CONSUMPTION.getCode());
        shortMessagesConsumptionDTO.setSmsSign(SMS_SIGN);
        shortMessagesConsumptionDTO.setSmsTemplateCode("SMS_479050617");
        shortMessages.add(shortMessagesConsumptionDTO);

        ShortMessagesConfigDTO shortMessagesSubsidyGrantDTO = new ShortMessagesConfigDTO();
        shortMessagesSubsidyGrantDTO.setChangeType(AmountSourceTypeEnum.SUBSIDY_GRANT.getCode());
        shortMessagesSubsidyGrantDTO.setSmsSign(SMS_SIGN);
        shortMessagesSubsidyGrantDTO.setSmsTemplateCode("SMS_479155622");
        shortMessages.add(shortMessagesSubsidyGrantDTO);

        ShortMessagesConfigDTO shortMessagesSubsidyExpiredDTO = new ShortMessagesConfigDTO();
        shortMessagesSubsidyExpiredDTO.setChangeType(AmountSourceTypeEnum.SUBSIDY_EXPIRED.getCode());
        shortMessagesSubsidyExpiredDTO.setSmsSign(SMS_SIGN);
        shortMessagesSubsidyExpiredDTO.setSmsTemplateCode("SMS_478965589");
        shortMessages.add(shortMessagesSubsidyExpiredDTO);
        return shortMessages;
    }
}
