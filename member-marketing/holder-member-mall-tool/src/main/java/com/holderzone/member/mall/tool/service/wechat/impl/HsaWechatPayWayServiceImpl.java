package com.holderzone.member.mall.tool.service.wechat.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.wechat.WechatPayWayQO;

import com.holderzone.member.common.vo.wechat.WechatAccountCreditedPageVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatPayWay;
import com.holderzone.member.mall.tool.mapper.wechat.HsaWechatPayWayMapper;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatAccountCreditedService;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatPayWayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 微信小程序支付方式
 */
@Service
@Slf4j
public class HsaWechatPayWayServiceImpl extends HolderBaseServiceImpl<HsaWechatPayWayMapper, HsaWechatPayWay>
        implements HsaWechatPayWayService {

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Resource
    private HsaWechatAccountCreditedService wechatAccountCreditedService;

    private static final String WE_CHAT_PAY_WAY = "WE_CHAT_PAY_WAY:";

    @Override
    public void saveOrUpdateWeChatPayWay(WechatPayWayQO request) {
        HsaWechatPayWay hsaWechatPayWay = getHsaWechatPayWay();

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        if (!ObjectUtils.isEmpty(hsaWechatPayWay)) {
            setPayWayJson(request, hsaWechatPayWay);
            baseMapper.updateById(hsaWechatPayWay);
        } else {
            hsaWechatPayWay = new HsaWechatPayWay();
            BeanUtils.copyProperties(headerUserInfo, hsaWechatPayWay);
            setPayWayJson(request, hsaWechatPayWay);
            baseMapper.insert(hsaWechatPayWay);
        }
    }

    private HsaWechatPayWay getHsaWechatPayWay() {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatPayWay>()
                .eq(HsaWechatPayWay::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    private void setPayWayJson(WechatPayWayQO request, HsaWechatPayWay hsaWechatPayWay) {
        String payWayJson = JSON.toJSONString(request.getPayWayList());
        hsaWechatPayWay.setPayWayJson(payWayJson);
        stringRedisTemplate.opsForValue().set(getCacheKey(), payWayJson);
    }

    @Override
    public List<String> queryByOperSubjectGuid() {
        PageResult<WechatAccountCreditedPageVO> wechatAccountCreditedPageVOPageResult = wechatAccountCreditedService.getAccountCreditedPage(new PageDTO());

        if (Objects.isNull(wechatAccountCreditedPageVOPageResult) || wechatAccountCreditedPageVOPageResult.getRecords().isEmpty()) {
            stringRedisTemplate.delete(getCacheKey());
            baseMapper.delete(new LambdaQueryWrapper<HsaWechatPayWay>()
                    .eq(HsaWechatPayWay::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            return Collections.emptyList();
        }
        String payWayJson = stringRedisTemplate.opsForValue().get(getCacheKey());
        if (StringUtils.isEmpty(payWayJson)) {
            HsaWechatPayWay hsaWechatPayWay = getHsaWechatPayWay();
            if (!ObjectUtils.isEmpty(hsaWechatPayWay)) {
                payWayJson = hsaWechatPayWay.getPayWayJson();
                stringRedisTemplate.opsForValue().set(getCacheKey(), payWayJson);
                return JSON.parseArray(payWayJson, String.class);
            }
        } else {
            return JSON.parseArray(payWayJson, String.class);
        }
        return Collections.emptyList();
    }

    private static String getCacheKey() {
        return WE_CHAT_PAY_WAY + ThreadLocalCache.getOperSubjectGuid();
    }
}
