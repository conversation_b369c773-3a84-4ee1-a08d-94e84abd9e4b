package com.holderzone.member.mall.tool.controller.wechat;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoDTO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.common.vo.tool.UserOperSubjectVO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigVO;
import com.holderzone.member.mall.tool.service.ali.IHsaAliAppletInfoService;
import com.holderzone.member.mall.tool.service.wechat.HsaWeChatConfigInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 微信小程序配置
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/config")
public class HsaWeChatConfigController {

    @Resource
    private HsaWeChatConfigInfoService hsaWeChatConfigInfoService;

    @ApiOperation("保存小程序配置")
    @PostMapping("/save")
    Result<Boolean> saveOrUpdateWeChatConfig(@RequestBody WeChatConfigInfoQO request) {
        hsaWeChatConfigInfoService.saveOrUpdateWeChatConfig(request);
        return Result.success();
    }

    @ApiOperation("获取小程序配置")
    @GetMapping("/get")
    Result<WeChatConfigInfoVO> queryByOperSubjectGuid() {
        return Result.success(hsaWeChatConfigInfoService.queryByOperSubjectGuid());
    }

    /**
     * 查询
     * @return
     */
    @ApiOperation("获取小程序配置")
    @GetMapping("/get/setting/{appId}")
    Result<WeChatConfigVO> queryByAppId(@PathVariable("appId") String appId){
        return Result.success(hsaWeChatConfigInfoService.queryByAppId(appId));
    }

    @ApiOperation("获取小程序支付配置")
    @PostMapping("/get/pay/setting")
    PaySettingVO getPaySetting(@RequestBody PaySettingDTO paySettingDTO) {
        return hsaWeChatConfigInfoService.getPaySetting(paySettingDTO);
    }

    @ApiOperation("获取小程序配置")
    @PostMapping("/get/list")
    Result<List<WeChatConfigInfoVO>> queryByOperSubjectGuidList(@RequestBody List<String> operSubjectGuidList) {
        return Result.success(hsaWeChatConfigInfoService.queryByOperSubjectGuidList(operSubjectGuidList));
    }

}

