package com.holderzone.member.mall.tool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.mall.tool.entity.HsaStoreHomeTemplateRelation;
import com.holderzone.member.mall.tool.mapper.HsaStoreHomeTemplateRelationMapper;
import com.holderzone.member.mall.tool.service.HsaStoreHomeTemplateRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
public class HsaStoreHomeTemplateRelationServiceImpl extends HolderBaseServiceImpl<HsaStoreHomeTemplateRelationMapper, HsaStoreHomeTemplateRelation> implements HsaStoreHomeTemplateRelationService {

    @Override
    public Integer countByTemplateGuid(String templateGuid) {
        QueryWrapper<HsaStoreHomeTemplateRelation> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaStoreHomeTemplateRelation::getTemplateGuid, templateGuid);
        return count(qw);
    }

    @Override
    public String getTemplateGuidByStoreGuid(String storeGuid) {
        QueryWrapper<HsaStoreHomeTemplateRelation> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaStoreHomeTemplateRelation::getStoreGuid, storeGuid);
        qw.lambda().select(HsaStoreHomeTemplateRelation::getTemplateGuid);
        HsaStoreHomeTemplateRelation relation = getOne(qw);
        if (Objects.isNull(relation)) {
            return null;
        }
        return relation.getTemplateGuid();
    }

    @Override
    public List<String> excludeByTemplateGuid(String templateGuid) {
        QueryWrapper<HsaStoreHomeTemplateRelation> qw = new QueryWrapper<>();
        qw.lambda().ne(HsaStoreHomeTemplateRelation::getTemplateGuid, templateGuid);
        qw.lambda().eq(HsaStoreHomeTemplateRelation::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid());
        qw.lambda().select(HsaStoreHomeTemplateRelation::getStoreGuid);
        List<HsaStoreHomeTemplateRelation> list = list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(HsaStoreHomeTemplateRelation::getStoreGuid).collect(Collectors.toList());
    }

    @Override
    public List<String> listTemplateGuidByStoreGuids(List<String> storeGuids) {
        if (CollectionUtils.isEmpty(storeGuids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<HsaStoreHomeTemplateRelation> qw = new QueryWrapper<>();
        qw.lambda().in(HsaStoreHomeTemplateRelation::getStoreGuid, storeGuids);
        qw.lambda().select(HsaStoreHomeTemplateRelation::getTemplateGuid);
        List<HsaStoreHomeTemplateRelation> list = list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(HsaStoreHomeTemplateRelation::getTemplateGuid).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> listStoreGuidByTemplateGuid(String templateGuid) {
        QueryWrapper<HsaStoreHomeTemplateRelation> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaStoreHomeTemplateRelation::getTemplateGuid, templateGuid);
        qw.lambda().select(HsaStoreHomeTemplateRelation::getStoreGuid);
        List<HsaStoreHomeTemplateRelation> list = list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(HsaStoreHomeTemplateRelation::getStoreGuid).collect(Collectors.toList());
    }

    @Override
    public void removeByTemplateGuid(String templateGuid) {
        UpdateWrapper<HsaStoreHomeTemplateRelation> uw = new UpdateWrapper<>();
        uw.lambda().eq(HsaStoreHomeTemplateRelation::getTemplateGuid, templateGuid);
        remove(uw);
    }
}
