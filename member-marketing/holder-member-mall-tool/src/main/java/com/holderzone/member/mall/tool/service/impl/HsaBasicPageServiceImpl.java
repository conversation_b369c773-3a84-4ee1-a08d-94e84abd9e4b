package com.holderzone.member.mall.tool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.dto.malltool.BasicPageReqDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.tool.entity.HsaBasicPage;
import com.holderzone.member.mall.tool.mapper.HsaBasicPageMapper;
import com.holderzone.member.mall.tool.service.IHsaBasicPageService;
import com.holderzone.member.mall.tool.transform.MemberMallToolTransform;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 基础页面表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Slf4j
@Service
@AllArgsConstructor
public class HsaBasicPageServiceImpl extends ServiceImpl<HsaBasicPageMapper, HsaBasicPage> implements IHsaBasicPageService {

    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<BasicPageReqDTO> reqDTOList) {
        Set<String> nameList = reqDTOList.stream().map(BasicPageReqDTO::getName).collect(Collectors.toSet());
        List<HsaBasicPage> list = this.list(new LambdaQueryWrapper<HsaBasicPage>().in(HsaBasicPage::getName, nameList));
        List<HsaBasicPage> basicPageList = MemberMallToolTransform.INSTANCE.reqDTOList2BasicPageList(reqDTOList);
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> existNameList = list.stream().map(HsaBasicPage::getName).collect(Collectors.toSet());
            basicPageList.removeIf(p -> existNameList.contains(p.getName()));
        }
        basicPageList.forEach(p -> {
            p.setGuid(guidGeneratorUtil.getStringGuid(HsaBasicPage.class.getSimpleName()));
        });
        this.saveBatch(basicPageList);
    }
}
