package com.holderzone.member.mall.tool.entity.unilink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员通-行业
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsa_member_unilink_industry")
public class HsaMemberUnilinkIndustry implements Serializable {

    private static final long serialVersionUID = 1645555482377191682L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 行业名称
     */
    private String name;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    public HsaMemberUnilinkIndustry(String enterpriseGuid, String operSubjectGuid, String name) {
        this.enterpriseGuid = enterpriseGuid;
        this.operSubjectGuid = operSubjectGuid;
        this.name = name;
    }

}
