package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微页面表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsaMemberMallPage对象", description="微页面表")
public class HsaMallPage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "页面描述")
    private String description;

    @ApiModelProperty(value = "页面json数据")
    private String json;

    @ApiModelProperty(value = "微页面名称")
    private String name;

    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "状态: 0未发布 1已发布")
    private Integer state;

    @ApiModelProperty(value = "微页面guid")
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    @ApiModelProperty(value = "1 微信  2支付宝")
    private Integer type;

}
