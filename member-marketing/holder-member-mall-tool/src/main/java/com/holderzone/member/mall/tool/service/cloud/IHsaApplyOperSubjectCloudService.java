package com.holderzone.member.mall.tool.service.cloud;

import com.holderzone.member.common.qo.cloud.OperSubjectCloudQO;
import com.holderzone.member.common.vo.cloud.MultiMemberVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloud;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

import java.util.List;

/**
 * <p>
 * 系统主体关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface IHsaApplyOperSubjectCloudService extends IHolderBaseService<HsaApplyOperSubjectCloud> {

    /**
     * 获取云平台主体
     *
     * @return
     */
    List<MultiMemberVO> queryMultiOperSubject(String enterpriseGuid);

    /**
     * 获取配置
     *
     * @return
     */
    OperSubjectCloudVO query();

    OperSubjectCloudVO queryByOperGuid(String operSubiectGuid);

    OperSubjectCloudVO queryByMultiOperSubjectGuid(String multiOperSubjectGuid);

    /**
     * 保存配置
     *
     * @return
     */
    Boolean save(OperSubjectCloudQO operSubjectCloudQO);

}
