package com.holderzone.member.mall.tool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.wx.WxOpenAuthDTO;
import com.holderzone.member.common.dto.wx.WxPreCodReqDTO;
import com.holderzone.member.common.qo.wx.WxCommonReqDTO;
import com.holderzone.member.common.qo.wx.WxMessageReqDTO;
import com.holderzone.member.common.vo.wx.WechatAuthorizerInfoVO;
import com.holderzone.member.mall.tool.entity.HsaWechatAuthorizerInfo;
import me.chanjar.weixin.common.error.WxErrorException;

import java.io.UnsupportedEncodingException;

/**
 * @version 1.0
 * @className HsaWechatAuthorizerInfoService
 */
public interface HsaWechatAuthorizerInfoService extends IService<HsaWechatAuthorizerInfo> {

    /**
     * 接收微信方推送的receiveTicket
     *
     * @return 默认字段：'success'
     */
    String receiveTicket(WxCommonReqDTO wxCommonReqDTO);


    /**
     * 获取用户授权url
     *
     * @param wxPreCodReqDTO 品牌guid，企业guid
     */
    String getPreAuthCode(WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException;

    /**
     * 通过授权码拉取获取授权方信息
     * 通过穿透字段brandGuid将授权方公众号信息与品牌绑定
     */
    String callbackAuth(WxOpenAuthDTO wxOpenAuthDTO);

    /**
     * 获取授权公众号信息
     */
    WechatAuthorizerInfoVO queryAuth();

    /**
     * 获取授权公众号信息
     */
    WechatAuthorizerInfoVO getAuthorizerAccessTokenByOperSubjectGuid(String operSubjectGuid);

    /**
     * 获取授权公众号信息
     */
    WechatAuthorizerInfoVO getAuthorizerAccessTokenByAppId(String appId);

    /**
     * 获取公众号回复消息内容xml
     */
    String getWxMpXmlMessage(WxMessageReqDTO wxMessageReqDTO);
}
