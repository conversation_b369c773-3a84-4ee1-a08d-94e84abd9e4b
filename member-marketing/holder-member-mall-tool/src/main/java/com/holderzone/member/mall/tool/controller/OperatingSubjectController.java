package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 基础数据
 */
@RestController
@RequestMapping("/hsa-base")
@Slf4j
public class OperatingSubjectController {

    @Resource
    private ExternalSupport externalSupport;

    /**
     * 获取基础数据
     */
    @ApiOperation("获取基础数据")
    @GetMapping(value = "/getOperatingSubjectInfo", produces = "application/json;charset=utf-8")
    public Result<OperSubjectInfoVO> getOperatingSubjectInfo() {
        return Result.success(externalSupport.baseServer(ThreadLocalCache.getSystem()).getOperatingSubjectInfo());
    }
}
