package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemBizConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员通-系统业务配置Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system_biz_config")
public class HsaMemberUnilinkSystemBizConfigController {

    @Resource
    private HsaMemberUnilinkSystemBizConfigService systemBizConfigService;

    /**
     * 根据系统配置ID查询业务列表
     *
     * @return 业务列表
     */
    @GetMapping("/list")
    public Result<List<MemberUnilinkSelectOptionVO>> listSystemBiz() {
        return Result.success(systemBizConfigService.listSystemBiz());
    }
} 