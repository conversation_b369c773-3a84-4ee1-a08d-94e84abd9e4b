package com.holderzone.member.mall.tool.entity.ali;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微页面表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@Accessors(chain = true)
public class HsaAliAuthorizerInfo {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    private String guid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    /**
     * 第三方平台AppId
     */
    private String authorizerAppId;

    /**
     * 令牌
     */
    private String authorizerAccessToken;

    /**
     * 刷新令牌
     */
    private String authorizerRefreshToken;

    private String qrcodeUrl;

    @ApiModelProperty(value = "授权商户的user_id")
    private String userId;

    @ApiModelProperty(value = "授权商户的appid")
    private String authAppId;

    private Integer isDeleted;

    @ApiModelProperty(value = "授权开始时间")
    private LocalDateTime authStart;

    private Integer expiresIn;

    @ApiModelProperty(value = "当商家选择全权委托授权时值为true，未选择时为false。")
    private String isByAppAuth;

    @ApiModelProperty(value = "valid：有效状态；invalid：无效状态")
    private String status;

    @ApiModelProperty(value = "授权结束时间")
    private LocalDateTime authEnd;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 通知消息标识
     */
    private String notifyId;

}
