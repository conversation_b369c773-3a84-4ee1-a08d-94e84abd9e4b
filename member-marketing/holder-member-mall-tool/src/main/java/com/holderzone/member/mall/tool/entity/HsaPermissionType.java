package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 门店权限类型表
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_permission_type")
public class HsaPermissionType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 岗位id或者角色id
     */
    private String positionGuid;

    /**
     * 类型 0:运营主体权限 1:门店权限
     */
    private int type;

    /**
     * 0：岗位
     * 1:角色
     */
    private Integer isRole;

    /**
     * 判断门店选则是全部还是部分、
     * @see com.holderzone.member.common.enums.permission.SubjectPermissionTypeEnum
     */
    private Integer isAll;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
