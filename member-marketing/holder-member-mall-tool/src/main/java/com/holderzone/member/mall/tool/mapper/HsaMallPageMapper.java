package com.holderzone.member.mall.tool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.member.common.qo.mall.MallPageQO;
import com.holderzone.member.common.vo.mall.MallPageVO;
import com.holderzone.member.mall.tool.entity.HsaMallPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 微页面表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
public interface HsaMallPageMapper extends BaseMapper<HsaMallPage> {


    List<MallPageVO> queryMallPage(@Param("request") MallPageQO request);
}
