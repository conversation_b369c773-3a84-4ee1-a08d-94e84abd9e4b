package com.holderzone.member.mall.tool;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;

@SpringBootApplication
@EnableFeignClients(basePackages="com.holderzone.member.common.feign")
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.common.qrcode"),
        @ComponentScan("com.holderzone.member.common.config"),
        @ComponentScan("com.holderzone.member.common.external"),
        @ComponentScan("com.holderzone.member.common.util"),
        @ComponentScan("com.holderzone.member.common.handler"),
        @ComponentScan("com.holderzone.member.mall.tool.handler"),
        @ComponentScan("com.holderzone.member.common.client")
})
@MapperScan(basePackages = "com.holderzone.member.mall.tool.mapper")
public class HolderSaasMemberMallToolApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasMemberMallToolApplication.class, args);
    }

}
