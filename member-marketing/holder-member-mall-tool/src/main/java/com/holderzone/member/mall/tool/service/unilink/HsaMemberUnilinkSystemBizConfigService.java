package com.holderzone.member.mall.tool.service.unilink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemBizConfig;

import java.util.List;

/**
 * 会员通-系统业务配置Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface HsaMemberUnilinkSystemBizConfigService extends IService<HsaMemberUnilinkSystemBizConfig> {

    /**
     * 根据系统配置ID查询业务列表
     *
     * @return 业务列表
     */
    List<MemberUnilinkSelectOptionVO> listSystemBiz();
} 