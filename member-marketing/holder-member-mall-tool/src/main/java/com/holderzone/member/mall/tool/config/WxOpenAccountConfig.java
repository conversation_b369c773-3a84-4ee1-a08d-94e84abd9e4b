package com.holderzone.member.mall.tool.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @className WxOpenAccountConfig
 * @description 微信第三方平台账号配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat.open")
@RefreshScope
public class WxOpenAccountConfig {
    private String componentAppId;

    private String componentSecret;

    private String componentToken;

    private String componentAesKey;


}
