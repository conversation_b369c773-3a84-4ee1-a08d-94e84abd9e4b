package com.holderzone.member.mall.tool.service.wechat.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.base.StallBaseAdapterInfo;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.IsCheckCanEnum;
import com.holderzone.member.common.enums.wechat.WechatAccountCreditedTypeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.PayUatFeign;
import com.holderzone.member.common.qo.wechat.CheckWechatAccountCreditedQO;
import com.holderzone.member.common.qo.wechat.StoreAccountCreditedQO;
import com.holderzone.member.common.qo.wechat.WechatAccountCreditedQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.PayUatFeignModel;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedPageVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaStoreAccountCredited;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatAccountCredited;
import com.holderzone.member.mall.tool.mapper.wechat.HsaStoreAccountCreditedMapper;
import com.holderzone.member.mall.tool.mapper.wechat.HsaWechatAccountCreditedMapper;
import com.holderzone.member.mall.tool.service.wechat.HsaStoreAccountCreditedService;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatAccountCreditedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微信小程序收款账户
 */
@Service
@Slf4j
public class HsaWechatAccountCreditedServiceImpl extends HolderBaseServiceImpl<HsaWechatAccountCreditedMapper, HsaWechatAccountCredited>
        implements HsaWechatAccountCreditedService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Resource
    private HsaStoreAccountCreditedMapper hsaStoreAccountCreditedMapper;

    @Resource
    private PayUatFeign payUatFeign;

    @Resource
    private CrmFeign crmFeign;

    @Resource
    private HsaStoreAccountCreditedService hsaStoreAccountCreditedService;

    private static final String ACCOUNT_CREDITED_CHECK_WARNING = "请输入正确的支付平台商户号/请输入正确的支付平台商户KEY";

    private static final String ACCOUNT_CREDITED_NAME_ALREADY_EXIST = "账户名称已存在";

    private static final String ACCOUNT_CREDITED_NUM_ALREADY_EXIST = "支付平台商户号已存在";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateAccountCredited(WechatAccountCreditedQO request) {
        log.info("保存或更新微信小程序收款账户:{}", JSON.toJSONString(request));

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        //验重
        checkCreditedValidity(request);

        if (request.getIsDefault() == BooleanEnum.TRUE.getCode()) {
            baseMapper.updateIsDefault(ThreadLocalCache.getOperSubjectGuid());
        }

        HsaWechatAccountCredited hsaWechatAccountCredited;
        if (StringUtils.isNotBlank(request.getGuid())) {
            hsaWechatAccountCredited = getHsaWechatAccountCredited(request.getGuid());

            //清除之前的档口数据
            hsaStoreAccountCreditedMapper.delete(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                    .eq(HsaStoreAccountCredited::getAccountCreditedGuid, request.getGuid())
                    .eq(HsaStoreAccountCredited::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));

            if (ObjectUtils.isNotEmpty(hsaWechatAccountCredited)) {
                BeanUtils.copyProperties(request, hsaWechatAccountCredited);
                hsaWechatAccountCredited.setPayCreditedJson(JSON.toJSONString(request.getPayCreditedTypeList()));
                baseMapper.updateByGuid(hsaWechatAccountCredited);
            }
        } else {
            hsaWechatAccountCredited = new HsaWechatAccountCredited();
            BeanUtils.copyProperties(request, hsaWechatAccountCredited);
            BeanUtils.copyProperties(headerUserInfo, hsaWechatAccountCredited);
            hsaWechatAccountCredited.setPayCreditedJson(JSON.toJSONString(request.getPayCreditedTypeList()));
            hsaWechatAccountCredited.setGuid(guidGeneratorUtil.getStringGuid(HsaWechatAccountCredited.class.getSimpleName()));
            baseMapper.insert(hsaWechatAccountCredited);
        }
        addStoreAccountCredited(hsaWechatAccountCredited.getGuid(), request, headerUserInfo);
    }

    private HsaWechatAccountCredited getHsaWechatAccountCredited(String guid) {
        HsaWechatAccountCredited hsaWechatAccountCredited;
        hsaWechatAccountCredited = baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatAccountCredited>()
                .eq(HsaWechatAccountCredited::getGuid, guid)
                .eq(HsaWechatAccountCredited::getIsDelete, BooleanEnum.FALSE.getCode()));
        return hsaWechatAccountCredited;
    }

    private void checkCreditedValidity(WechatAccountCreditedQO request) {

        //校验名称是否重复
        HsaWechatAccountCredited hsaWechatAccountCredited = baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatAccountCredited>()
                .eq(HsaWechatAccountCredited::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaWechatAccountCredited::getCreditedName, request.getCreditedName())
                .eq(HsaWechatAccountCredited::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .notIn(StringUtils.isNotBlank(request.getGuid()), HsaWechatAccountCredited::getGuid, request.getGuid()));


        if (ObjectUtils.isNotEmpty(hsaWechatAccountCredited)) {
            throw new MemberToolException(ACCOUNT_CREDITED_NAME_ALREADY_EXIST);
        }

        //校验支付商户号是否重复
        HsaWechatAccountCredited wechatAccountCredited = baseMapper.selectOne(new LambdaQueryWrapper<HsaWechatAccountCredited>()
                .eq(HsaWechatAccountCredited::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaWechatAccountCredited::getPayMerchantNum, request.getPayMerchantNum())
                .eq(HsaWechatAccountCredited::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .notIn(StringUtils.isNotBlank(request.getGuid()), HsaWechatAccountCredited::getGuid, request.getGuid()));

        if (ObjectUtils.isNotEmpty(wechatAccountCredited)) {
            throw new MemberToolException(ACCOUNT_CREDITED_NUM_ALREADY_EXIST);
        }
    }

    private void addStoreAccountCredited(String guid, WechatAccountCreditedQO request, HeaderUserInfo headerUserInfo) {
        if (CollUtil.isNotEmpty(request.getStoreAccountCreditedQOS())) {
            List<HsaStoreAccountCredited> hsaStoreAccountCreditedList = Lists.newArrayList();
            for (StoreAccountCreditedQO storeAccountCreditedQO : request.getStoreAccountCreditedQOS()) {

                HsaStoreAccountCredited storeAccountCredited = new HsaStoreAccountCredited();
                BeanUtils.copyProperties(headerUserInfo, storeAccountCredited);
                BeanUtils.copyProperties(storeAccountCreditedQO, storeAccountCredited);
                storeAccountCredited.setParentGuid(storeAccountCreditedQO.getParentGuid());
                storeAccountCredited.setAccountCreditedGuid(guid);
                storeAccountCredited.setParentName(storeAccountCreditedQO.getParentName());
                storeAccountCredited.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreAccountCredited.class.getSimpleName()));
                if (CollUtil.isNotEmpty(storeAccountCreditedQO.getStoreAccountCreditedQOS())) {
                    for (StoreAccountCreditedQO accountCreditedQO : storeAccountCreditedQO.getStoreAccountCreditedQOS()) {
                        HsaStoreAccountCredited stallAccountCredited = new HsaStoreAccountCredited();

                        BeanUtils.copyProperties(headerUserInfo, stallAccountCredited);
                        BeanUtils.copyProperties(accountCreditedQO, stallAccountCredited);

                        stallAccountCredited.setParentGuid(storeAccountCreditedQO.getStoreGuid());
                        stallAccountCredited.setAccountCreditedGuid(guid);
                        stallAccountCredited.setParentName(storeAccountCreditedQO.getStoreName());
                        stallAccountCredited.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreAccountCredited.class.getSimpleName()));
                        hsaStoreAccountCreditedList.add(stallAccountCredited);
                    }
                }
                hsaStoreAccountCreditedList.add(storeAccountCredited);
            }

            if (CollUtil.isNotEmpty(hsaStoreAccountCreditedList)) {
                hsaStoreAccountCreditedService.saveBatch(hsaStoreAccountCreditedList);
            }
        }
    }

    @Override
    public WechatAccountCreditedVO queryAccountCreditedByGuid(String guid) {
        WechatAccountCreditedVO wechatAccountCreditedVO = new WechatAccountCreditedVO();
        HsaWechatAccountCredited hsaWechatAccountCredited = getHsaWechatAccountCredited(guid);
        if (ObjectUtils.isEmpty(hsaWechatAccountCredited)) {
            return wechatAccountCreditedVO;
        }

        BeanUtils.copyProperties(hsaWechatAccountCredited, wechatAccountCreditedVO);
        List<String> payCreditedTypeList = JSON.parseArray(hsaWechatAccountCredited.getPayCreditedJson(), String.class);
        wechatAccountCreditedVO.setPayCreditedTypeList(JSON.parseArray(hsaWechatAccountCredited.getPayCreditedJson(), String.class));
        //会员营收是否可勾选
        dealIsCheckCan(guid, payCreditedTypeList, wechatAccountCreditedVO);


        List<HsaStoreAccountCredited> presentAccountCreditedList = hsaStoreAccountCreditedMapper.selectList(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                .eq(HsaStoreAccountCredited::getAccountCreditedGuid, guid));


        if (CollUtil.isEmpty(presentAccountCreditedList)) {
            return wechatAccountCreditedVO;
        }

        //查询收款账户门店
        List<StoreAccountCreditedVO> storeAccountCreditedQOS = getStoreAccountCreditedVOS(presentAccountCreditedList);
        wechatAccountCreditedVO.setStoreAccountCreditedQOS(storeAccountCreditedQOS);
        return wechatAccountCreditedVO;
    }

    @Override
    public WechatAccountCreditedVO queryAccountCreditedByStoreGuid(String guid) {
        List<HsaStoreAccountCredited> hsaStoreAccountCreditedList = hsaStoreAccountCreditedMapper.selectList(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                .eq(HsaStoreAccountCredited::getStoreGuid, guid));

        if (CollUtil.isNotEmpty(hsaStoreAccountCreditedList)) {
            String accountCreditedGuid = hsaStoreAccountCreditedList.get(0).getAccountCreditedGuid();
            return queryAccountCreditedByGuid(accountCreditedGuid);
        }
        return null;
    }

    @Override
    public WechatAccountCreditedVO queryAccountCreditedByOperSubjectGuid(String operSubjectGuid) {
        String memberPlatformAccountGuid = baseMapper.getMemberPayCreditedType(operSubjectGuid, null);
        if (StringUtils.isNotEmpty(memberPlatformAccountGuid)) {
            return queryAccountCreditedByGuid(memberPlatformAccountGuid);
        }
        return null;
    }

    private static List<StoreAccountCreditedVO> getStoreAccountCreditedVOS(List<HsaStoreAccountCredited> presentAccountCreditedList) {
        List<StoreAccountCreditedVO> storeAccountCreditedQOS = Lists.newArrayList();

        for (HsaStoreAccountCredited hsaStoreAccountCredited : presentAccountCreditedList) {
            StoreAccountCreditedVO storeAccountCreditedVO = new StoreAccountCreditedVO();
            List<StoreAccountCreditedVO> stallAccountCreditedQOS = Lists.newArrayList();
            if (StringUtils.isEmpty(hsaStoreAccountCredited.getParentGuid())) {
                dealStallAccountCredited(presentAccountCreditedList, hsaStoreAccountCredited, storeAccountCreditedVO, stallAccountCreditedQOS, storeAccountCreditedQOS);
            } else {
                //判断是否有此档口的门店
                List<HsaStoreAccountCredited> storeAccountCreditedList = presentAccountCreditedList.stream()
                        .filter(in -> in.getStoreGuid().equals(hsaStoreAccountCredited.getParentGuid())).collect(Collectors.toList());
                if (CollUtil.isEmpty(storeAccountCreditedList)) {
                    StoreAccountCreditedVO stallAccountCreditedVO = new StoreAccountCreditedVO();
                    BeanUtils.copyProperties(hsaStoreAccountCredited, stallAccountCreditedVO);
                    storeAccountCreditedQOS.add(stallAccountCreditedVO);
                }

            }
            storeAccountCreditedVO.setStoreAccountCreditedQOS(stallAccountCreditedQOS);
        }
        return storeAccountCreditedQOS;
    }

    private static void dealStallAccountCredited(List<HsaStoreAccountCredited> presentAccountCreditedList, HsaStoreAccountCredited hsaStoreAccountCredited, StoreAccountCreditedVO storeAccountCreditedVO, List<StoreAccountCreditedVO> stallAccountCreditedQOS, List<StoreAccountCreditedVO> storeAccountCreditedQOS) {
        BeanUtils.copyProperties(hsaStoreAccountCredited, storeAccountCreditedVO);

        //判断是否有此门店的档口
        List<HsaStoreAccountCredited> stallAccountCredited = presentAccountCreditedList.stream()
                .filter(in -> StringUtils.isNotBlank(in.getParentGuid()) &&
                        in.getParentGuid().equals(hsaStoreAccountCredited.getStoreGuid())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(stallAccountCredited)) {

            for (HsaStoreAccountCredited storeAccountCredited : stallAccountCredited) {
                StoreAccountCreditedVO stallAccountCreditedVO = new StoreAccountCreditedVO();
                BeanUtils.copyProperties(storeAccountCredited, stallAccountCreditedVO);
                stallAccountCreditedQOS.add(stallAccountCreditedVO);
            }

        }
        storeAccountCreditedQOS.add(storeAccountCreditedVO);
    }

    @Override
    public List<StoreAccountCreditedVO> queryStore(String guid) {
        QueryStoreBasePage query = new QueryStoreBasePage();
        query.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
        List<StoreAccountCreditedVO> storeAccountCreditedVOS = hsaStoreAccountCreditedService.queryStore(query);

        dealCheckStore(guid, storeAccountCreditedVOS);
        return storeAccountCreditedVOS;
    }

    private void dealIsCheckCan(String guid, List<String> payCreditedTypeList, WechatAccountCreditedVO wechatAccountCreditedVO) {
        if (payCreditedTypeList.contains(WechatAccountCreditedTypeEnum.MEMBER_REVENUE.getCode() + "")) {
            wechatAccountCreditedVO.setIsCheckCan(BooleanEnum.TRUE.getCode());
        } else if (Boolean.TRUE.equals(isCheckCan(guid))) {
            wechatAccountCreditedVO.setIsCheckCan(BooleanEnum.FALSE.getCode());
        } else {
            wechatAccountCreditedVO.setIsCheckCan(BooleanEnum.TRUE.getCode());
        }
    }

    private void dealCheckStore(String guid, List<StoreAccountCreditedVO> storeAccountCreditedVOS) {

        //获取所有门店
        QueryStoreBasePage query = new QueryStoreBasePage();
        query.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());

        if (CollUtil.isEmpty(storeAccountCreditedVOS)) {
            return;
        }

        //其他收款账户勾选门店档口
        List<HsaStoreAccountCredited> hsaStoreAccountCreditedList = getOtherStoreAccountCreditedList(guid);


        forDealCheck(storeAccountCreditedVOS, hsaStoreAccountCreditedList);
    }

    private static void forDealCheck(List<StoreAccountCreditedVO> storeAccountCreditedVOS,
                                     List<HsaStoreAccountCredited> hsaStoreAccountCreditedList) {

        //获取已勾选的门店
        Map<String, HsaStoreAccountCredited> hsaStoreAccountCreditedMap = hsaStoreAccountCreditedList.stream()
                .filter(in -> StringUtils.isEmpty(in.getParentGuid()))
                .collect(Collectors.toList()).stream().
                collect(Collectors.
                        toMap(HsaStoreAccountCredited::getStoreGuid, Function.identity(), (entity1, entity2) -> entity1));

        //获取已勾选的门店档口
        Map<String, List<HsaStoreAccountCredited>> storeAccountCreditedVOMap = hsaStoreAccountCreditedList.stream()
                .filter(in -> StringUtils.isNotBlank(in.getParentGuid()))
                .collect(Collectors.toList())
                .stream()
                .collect(Collectors.groupingBy(HsaStoreAccountCredited::getParentGuid));

        for (StoreAccountCreditedVO storeAccountCreditedVO : storeAccountCreditedVOS) {

            //门店若已被其他收款账户勾选  则标记为不可勾选
            if (CollUtil.isNotEmpty(hsaStoreAccountCreditedMap) && hsaStoreAccountCreditedMap.containsKey(storeAccountCreditedVO.getStoreGuid())) {
                storeAccountCreditedVO.setIsCheck(IsCheckCanEnum.NOT_CAN_CHECK.getCode());
            }
            if (CollUtil.isNotEmpty(storeAccountCreditedVOMap) &&
                    storeAccountCreditedVOMap.containsKey(storeAccountCreditedVO.getStoreGuid()) &&
                    CollUtil.isNotEmpty(storeAccountCreditedVO.getStoreAccountCreditedQOS())) {
                stallAccountCreditedCheck(storeAccountCreditedVO, storeAccountCreditedVOMap);
            }
        }
    }

    private static void stallAccountCreditedCheck(StoreAccountCreditedVO storeAccountCreditedVO, Map<String, List<HsaStoreAccountCredited>> storeAccountCreditedVOMap) {
        for (StoreAccountCreditedVO stallAccountCreditedQO : storeAccountCreditedVO.getStoreAccountCreditedQOS()) {

            Map<String, HsaStoreAccountCredited> hsaStoreAccountCreditedMap = storeAccountCreditedVOMap.get(storeAccountCreditedVO.getStoreGuid())
                    .stream()
                    .collect(Collectors.
                            toMap(HsaStoreAccountCredited::getStoreGuid, Function.identity(), (entity1, entity2) -> entity1));
            if (hsaStoreAccountCreditedMap.containsKey(stallAccountCreditedQO.getStoreGuid())) {
                stallAccountCreditedQO.setIsCheck(IsCheckCanEnum.NOT_CAN_CHECK.getCode());
            }
        }
    }

    private List<HsaStoreAccountCredited> getOtherStoreAccountCreditedList(String guid) {
        return hsaStoreAccountCreditedMapper.selectList(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                .eq(HsaStoreAccountCredited::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .notIn(StringUtils.isNotBlank(guid), HsaStoreAccountCredited::getAccountCreditedGuid, guid));
    }

    @Override
    public PageResult<WechatAccountCreditedPageVO> getAccountCreditedPage(PageDTO pageDTO) {
        List<WechatAccountCreditedPageVO> wechatAccountCreditedPageVOList;
        PageMethod.startPage(pageDTO.getCurrentPage(), pageDTO.getPageSize());
        wechatAccountCreditedPageVOList = baseMapper.getAccountCreditedPage(ThreadLocalCache.getOperSubjectGuid());

        //获取门店
        getStoreName(wechatAccountCreditedPageVOList);

        return PageUtil.pageResult(wechatAccountCreditedPageVOList);
    }

    @Override
    public Boolean delete(String guid) {
        HsaWechatAccountCredited hsaWechatAccountCredited = baseMapper.queryByGuid(guid);
        if (ObjectUtils.isNotEmpty(hsaWechatAccountCredited)) {
            hsaWechatAccountCredited.setIsDelete(BooleanEnum.TRUE.getCode());
            baseMapper.updateByGuid(hsaWechatAccountCredited);


            hsaStoreAccountCreditedMapper.delete(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                    .eq(HsaStoreAccountCredited::getAccountCreditedGuid, guid));
        }
        return true;
    }

    private void getStoreName(List<WechatAccountCreditedPageVO> wechatAccountCreditedPageVOList) {
        if (CollUtil.isNotEmpty(wechatAccountCreditedPageVOList)) {
            List<String> wechatAccountCreditedGuids = wechatAccountCreditedPageVOList.stream().map(WechatAccountCreditedPageVO::getGuid).collect(Collectors.toList());

            List<HsaStoreAccountCredited> hsaStoreAccountCreditedList = hsaStoreAccountCreditedMapper.selectList(new LambdaQueryWrapper<HsaStoreAccountCredited>()
                    .in(HsaStoreAccountCredited::getAccountCreditedGuid, wechatAccountCreditedGuids));
            Map<String, List<HsaStoreAccountCredited>> dataByAccountCreditedGuidMap = hsaStoreAccountCreditedList.stream()
                    .collect(Collectors.groupingBy(HsaStoreAccountCredited::getAccountCreditedGuid));

            for (WechatAccountCreditedPageVO wechatAccountCreditedPageVO : wechatAccountCreditedPageVOList) {

                wechatAccountCreditedPageVO.setPayCreditedTypeList(JSON.parseArray(wechatAccountCreditedPageVO.getPayCreditedJson(), String.class));

                if (CollUtil.isNotEmpty(dataByAccountCreditedGuidMap)) {
                    List<HsaStoreAccountCredited> storeAccountCreditedList = dataByAccountCreditedGuidMap.get(wechatAccountCreditedPageVO.getGuid());
                    if (CollUtil.isNotEmpty(storeAccountCreditedList)) {
                        Set<String> storeNameList = storeAccountCreditedList.stream()
                                .filter(in -> StringUtils.isEmpty(in.getParentGuid()))
                                .map(HsaStoreAccountCredited::getStoreName).collect(Collectors.toSet());

                        Set<String> parentNameList = storeAccountCreditedList.stream()
                                .filter(in -> StringUtils.isNotBlank(in.getParentGuid()))
                                .map(HsaStoreAccountCredited::getParentName).collect(Collectors.toSet());
                        storeNameList.addAll(parentNameList);

                        wechatAccountCreditedPageVO.setStoreAccountCreditedQOS(new ArrayList<>(storeNameList));
                    }
                }
            }
        }
    }

    @Override
    public Boolean isCheckCan(String guid) {
        return StringUtils.isNotBlank(baseMapper.getMemberPayCreditedType(ThreadLocalCache.getOperSubjectGuid(), guid));
    }


}
