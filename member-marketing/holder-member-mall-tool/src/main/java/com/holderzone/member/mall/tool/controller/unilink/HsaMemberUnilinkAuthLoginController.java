package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.ipaas.CheckPasswordDTO;
import com.holderzone.member.common.vo.ipass.EnterpriseUserRoleInfoVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkAuthLoginService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员通-授权登录Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_auth_login")
public class HsaMemberUnilinkAuthLoginController {

    @Resource
    private HsaMemberUnilinkAuthLoginService hsaMemberUnilinkLoginService;

    /**
     * 商城登录
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/mall")
    public Result<List<EnterpriseUserRoleInfoVO>> mallLogin(@RequestBody CheckPasswordDTO dto) {
        return Result.success(hsaMemberUnilinkLoginService.mallLogin(dto));
    }

}
