package com.holderzone.member.mall.tool.manager;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.config.WeChatConfig;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.malltool.MallPageReqDTO;
import com.holderzone.member.common.dto.malltool.MallPageRespDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.wechat.AccessTokenRespDTO;
import com.holderzone.member.common.dto.wechat.ErrorRespDTO;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import com.holderzone.member.common.qo.tool.PagePopularizeQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.tool.entity.HsaMallPage;
import com.holderzone.member.mall.tool.service.IHsaMallPageService;
import com.holderzone.member.mall.tool.transform.MemberMallToolTransform;
import com.holderzone.member.mall.tool.utils.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022年12月27日 下午12:27
 * @description 微页面管理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class HsaMallPageManager {

    private IHsaMallPageService mallPageService;

    private GuidGeneratorUtil guidGeneratorUtil;

    private WeChatConfig weChatConfig;

    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 新增或编辑微页面
     *
     * @param reqDTO 微页面信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(MallPageReqDTO reqDTO) {
        reqDTO.validate();
        HsaMallPage mallPage = MemberMallToolTransform.INSTANCE.mallPageReqDTO2mallPage(reqDTO);
        if (StringUtils.isEmpty(mallPage.getGuid())) {
            mallPage.setGuid(guidGeneratorUtil.getStringGuid(HsaMallPage.class.getSimpleName()));
        }
        mallPage.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        mallPage.setType(reqDTO.getType());
        mallPageService.saveOrUpdate(mallPage);
    }

    /**
     * @param guid 微页面guid
     * @return 微页面详情
     */
    public MallPageRespDTO get(String guid) {
        HsaMallPage mallPage = mallPageService.getById(guid);
        return MemberMallToolTransform.INSTANCE.mallPage2MallPageRespDTO(mallPage);
    }

    /**
     * 批量删除微页面
     *
     * @param request datas guid
     */
    public void deleteBatch(SingleDataDTO request) {
        if (CollectionUtils.isEmpty(request.getDatas())) {
            throw new BusinessException(MemberTerminalExceptionEnum.ERROR_VERIFY.getDes());
        }
        mallPageService.removeByIds(request.getDatas());
    }

    /**
     * 更改微页面状态
     *
     * @param guid 页面guid
     * @return 当前页面状态: 0未发布 1已发布 -1表示更新失败
     */
    public Integer updateState(String guid) {
        HsaMallPage mallPage = mallPageService.getById(guid);
        if (Objects.isNull(mallPage)) {
            log.warn(MemberTerminalExceptionEnum.ERROR_NOT_FOUND.getDes());
            return -1;
        }
        mallPage.setState(mallPage.getState() == 0 ? 1 : 0);
        boolean update = mallPageService.updateById(mallPage);
        if (update) {
            return mallPage.getState();
        }
        return -1;
    }

    /**
     * 页面推广
     *
     * @return 二维码图片
     */
    public String popularize(PagePopularizeQO request) {
        String appId = request.getAppId();
        String appSecret = request.getApp_secret();
        String accessToken = getAccessToken(appId, appSecret);
        return getWxQRCode(appId, appSecret, accessToken, request.getPageLink());
    }

    /**
     * 获取接口调用凭据
     *
     * @param appId     小程序id
     * @param appSecret 小程序secret
     * @return 接口调用凭据
     */
    private String getAccessToken(String appId, String appSecret) {
        String popularizeKey = RedisKeyConstant.POPULARIZE_KEY + appId + StringConstant.COLON + appSecret;
        Object accessTokenObj = redisTemplate.opsForValue().get(popularizeKey);
        String accessToken;
        if (ObjectUtils.isEmpty(accessTokenObj)) {
            AccessTokenRespDTO tokenRespDTO = getAccessTokenResp(appId, appSecret);
            redisTemplate.opsForValue().set(popularizeKey, tokenRespDTO.getAccess_token(), tokenRespDTO.getExpires_in(), TimeUnit.SECONDS);
            accessToken = tokenRespDTO.getAccess_token();
        } else {
            accessToken = String.valueOf(accessTokenObj);
        }
        return accessToken;
    }

    /**
     * 获取微信小程序二维码
     *
     * @param appId       小程序id
     * @param appSecret   小程序secret
     * @param accessToken 接口调用凭据
     * @param pageLink    页面链接
     */
    private String getWxQRCode(String appId, String appSecret, String accessToken, String pageLink) {
        String wxQRcodeUrl = weChatConfig.getWxQRcodeUrl() + "?access_token=" + accessToken;
        HashMap<String, Object> params = new HashMap<>();
        params.put("page", pageLink);
        params.put("scene", "a=1");
        params.put("check_path", false);
        params.put("env_version", "develop");
        String result = "";
        try {
            result = HttpUtil.doPost(wxQRcodeUrl, JSON.toJSONString(params));
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("获取微信小程序二维码失败");
        }
        ErrorRespDTO wxqrCodeRespDTO = null;
        try {
            wxqrCodeRespDTO = JacksonUtils.toObject(ErrorRespDTO.class, result);
        } catch (Exception e) {
            e.printStackTrace();
            // 成功时返回的图片Buffer，会解析失败
            return result;
        }
        // access_token expired情况重新获取再次发起一次请求
        if (!StringUtils.isEmpty(wxqrCodeRespDTO.getErrcode())) {
            if (42001 == wxqrCodeRespDTO.getErrcode()) {
                AccessTokenRespDTO tokenRespDTO = getAccessTokenResp(appId, appSecret);
                String rwxQRcodeUrl = weChatConfig.getWxQRcodeUrl() + "?access_token=" + tokenRespDTO.getAccess_token();
                try {
                    result = HttpUtil.doPost(rwxQRcodeUrl, JSON.toJSONString(params));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (StringUtils.isEmpty(result)) {
                    throw new BusinessException("获取微信小程序二维码失败");
                }
                try {
                    wxqrCodeRespDTO = JacksonUtils.toObject(ErrorRespDTO.class, result);
                } catch (Exception e) {
                    e.printStackTrace();
                    // 成功时返回的图片Buffer，会解析失败
                    return result;
                }
            }
            throw new BusinessException(wxqrCodeRespDTO.getErrmsg());
        }
        return null;
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据，token有效期为7200s，开发者需要进行妥善保存
     *
     * @param appId     小程序id
     * @param appSecret 小程序secret
     * @return 接口调用凭据
     */
    public AccessTokenRespDTO getAccessTokenResp(String appId, String appSecret) {
        String accessTokenUrl = weChatConfig.getAccessTokenUrl();
        HashMap<String, Object> params = new HashMap<>();
        params.put("grant_type", "client_credential");
        params.put("appid", appId);
        params.put("secret", appSecret);
        String result = "";
        try {
            result = HttpUtil.doGet(accessTokenUrl, params);
        } catch (IOException | URISyntaxException e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("获取微信调用凭据失败");
        }
        AccessTokenRespDTO tokenRespDTO = JacksonUtils.toObject(AccessTokenRespDTO.class, result);
        if (!StringUtils.isEmpty(tokenRespDTO.getErrmsg())) {
            throw new BusinessException(tokenRespDTO.getErrmsg());
        }
        return tokenRespDTO;
    }
}
