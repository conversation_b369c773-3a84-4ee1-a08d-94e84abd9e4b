package com.holderzone.member.mall.tool.service.unilink;

import com.holderzone.member.common.dto.ipaas.CheckPasswordDTO;
import com.holderzone.member.common.vo.ipass.EnterpriseUserRoleInfoVO;

import java.util.List;

/**
 * 会员通-登录Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface HsaMemberUnilinkAuthLoginService {

    /**
     * 商城登录
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    List<EnterpriseUserRoleInfoVO> mallLogin(CheckPasswordDTO dto);

}
