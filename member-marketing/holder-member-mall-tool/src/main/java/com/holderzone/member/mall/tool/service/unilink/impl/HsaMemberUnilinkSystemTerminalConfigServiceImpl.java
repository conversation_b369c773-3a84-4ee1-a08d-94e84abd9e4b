package com.holderzone.member.mall.tool.service.unilink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemConfig;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemTerminalConfig;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemConfigMapper;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemTerminalConfigMapper;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemTerminalConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员通-系统终端配置Service实现
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
public class HsaMemberUnilinkSystemTerminalConfigServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemTerminalConfigMapper, HsaMemberUnilinkSystemTerminalConfig> implements HsaMemberUnilinkSystemTerminalConfigService {

    @Resource
    private HsaMemberUnilinkSystemConfigMapper systemConfigMapper;

    @Override
    public List<MemberUnilinkSelectOptionVO> listSystemTerminal() {
        // 查询所有系统配置
        List<HsaMemberUnilinkSystemConfig> systemConfigs = systemConfigMapper.selectList(new LambdaQueryWrapper<>());

        // 查询所有终端配置
        List<HsaMemberUnilinkSystemTerminalConfig> terminalConfigs = this.list(
                new LambdaQueryWrapper<HsaMemberUnilinkSystemTerminalConfig>()
                        .orderByAsc(HsaMemberUnilinkSystemTerminalConfig::getSystemConfigId)
                        .orderByAsc(HsaMemberUnilinkSystemTerminalConfig::getSortOrder)
        );

        // 将系统配置转换为Map，方便查找
        Map<Integer, HsaMemberUnilinkSystemConfig> systemConfigMap = systemConfigs.stream()
                .collect(Collectors.toMap(HsaMemberUnilinkSystemConfig::getId, config -> config));

        // 转换为VO
        return terminalConfigs.stream()
                .filter(terminal -> systemConfigMap.containsKey(terminal.getSystemConfigId()))
                .map(terminal -> {
                    HsaMemberUnilinkSystemConfig systemConfig = systemConfigMap.get(terminal.getSystemConfigId());
                    String label = systemConfig == null ? terminal.getTerminalName() : systemConfig.getSystemName() + "-" + terminal.getTerminalName();
                    String systemType = systemConfig == null ? null : systemConfig.getSystemType();
                    return new MemberUnilinkSelectOptionVO()
                            .setLabel(label)
                            .setValue(terminal.getTerminalCode())
                            .setSystemType(systemType);
                })
                .collect(Collectors.toList());
    }
}