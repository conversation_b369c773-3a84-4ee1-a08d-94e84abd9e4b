package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemTerminalConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员通-系统终端配置Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system_terminal_config")
public class HsaMemberUnilinkSystemTerminalConfigController {

    @Resource
    private HsaMemberUnilinkSystemTerminalConfigService systemTerminalConfigService;

    /**
     * 查询系统终端列表
     *
     * @return 系统终端列表
     */
    @GetMapping("/list")
    public Result<List<MemberUnilinkSelectOptionVO>> listSystemTerminal() {
        return Result.success(systemTerminalConfigService.listSystemTerminal());
    }
} 