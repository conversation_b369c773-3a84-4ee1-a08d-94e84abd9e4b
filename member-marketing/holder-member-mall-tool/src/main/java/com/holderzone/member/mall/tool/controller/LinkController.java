package com.holderzone.member.mall.tool.controller;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.malltool.BasicPageTypeRespDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.vo.mall.CheckKeyVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import com.holderzone.member.mall.tool.manager.LinkManager;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 链接控制器
 * 链接相关接口
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("/link")
public class LinkController {

    private LinkManager linkManager;

    @ApiOperation("基础页面列表")
    @GetMapping("/list_basic_page")
    public Result<List<BasicPageTypeRespDTO>> listBasicPage() {
        return Result.success(linkManager.listBasicPage());
    }

    @ApiOperation("微页面列表")
    @PostMapping("/list_mall_page")
    public Result<List<LinkUniteVO>> listMallPage(@RequestBody SingleDataDTO request) {
        return Result.success(linkManager.listMallPage(request));
    }

    @ApiOperation("店铺主页列表")
    @PostMapping("/list_store_page")
    public Result<List<LinkUniteVO>> listStorePage(@RequestBody SingleDataDTO request) {
        return Result.success(linkManager.listStorePage(request));
    }

    @ApiOperation("档口列表页列表")
    @PostMapping("/list_window_page")
    public Result<List<LinkUniteVO>> listWindowPage(@RequestBody SingleDataDTO request) {
        return Result.success(linkManager.listWindowPage(request));
    }

    @ApiOperation("商城分类页列表")
    @PostMapping("/list_type_page")
    public Result<List<LinkUniteVO>> listTypePage(@RequestBody SingleDataDTO request) {
        return Result.success(linkManager.listTypePage(request));
    }

    @ApiOperation("商品详情分页")
    @PostMapping("/page_item_page")
    public Result<PageResult> pageItemPage(@RequestBody SingleDataDTO request) {
        return Result.success(linkManager.pageItemPage(request));
    }

    @ApiOperation("检查主键")
    @GetMapping("/check_key")
    public Result<CheckKeyVO> checkKey() {
        return Result.success(linkManager.checkKey());
    }
}
