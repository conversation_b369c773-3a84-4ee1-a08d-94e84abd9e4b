package com.holderzone.member.mall.tool.service.unilink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.unilink.AuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.MemberUnilinkSystemDTO;
import com.holderzone.member.common.dto.unilink.UnAuthMemberUnilinkSystemDTO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystem;
import java.util.List;

/**
 * 会员通-系统Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface HsaMemberUnilinkSystemService extends IService<HsaMemberUnilinkSystem> {

    /**
     * 初始化默认系统
     *
     * @param enterpriseGuid  企业GUID
     * @param operSubjectGuid 运营主体GUID
     */
    void initializeDefaultSystems(String enterpriseGuid, String operSubjectGuid);

    /**
     * 保存系统
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    Boolean saveSystem(MemberUnilinkSystemDTO dto);

    /**
     * 系统授权
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    Boolean authSystem(AuthMemberUnilinkSystemDTO dto);

    /**
     * 解除系统授权
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    Boolean unAuthSystem(UnAuthMemberUnilinkSystemDTO dto);

    /**
     * 根据运营主体查询授权系统
     *
     * @param operSubjectGuid 运营主体GUID
     * @return 授权系统列表
     */
    List<MemberUnilinkSystemVO> listByOperSubject(String operSubjectGuid);
}
