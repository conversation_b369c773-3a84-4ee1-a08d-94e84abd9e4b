package com.holderzone.member.mall.tool.service.wechat;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaStoreAccountCredited;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatAccountCredited;

import java.util.List;

public interface HsaStoreAccountCreditedService extends IService<HsaStoreAccountCredited> {

    /**
     * 查询可勾选的门店
     * @return
     */
    List<StoreAccountCreditedVO> queryStore(QueryStoreBasePage query);
}
