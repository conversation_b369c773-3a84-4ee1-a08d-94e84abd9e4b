package com.holderzone.member.mall.tool.support;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO;
import com.holderzone.member.mall.tool.service.HsaStoreHomeTemplateRelationService;
import com.holderzone.member.mall.tool.service.HsaStoreHomeTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 维护两个缓存
 * 1.模板缓存
 * string结构
 * key：店铺主页key:运营主体guid:模板guid, value:模板详情
 * 2.门店关联模板缓存
 * map结构
 * key: 门店guid, value：模板guid
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StoreHomeTemplateCacheSupport {


    public static final String STORE_HOME_TEMPLATE_KEY = "STORE_HOME_TEMPLATE:%s:%s";

    public static final String STORE_HOME_TEMPLATE_RELATION_KEY = "STORE_HOME_TEMPLATE_RELATION";

    private final RedisTemplate<String, String> redisTemplate;

    private final HsaStoreHomeTemplateService storeHomeTemplateService;

    private final HsaStoreHomeTemplateRelationService storeHomeTemplateRelationService;


    /**
     * 根据门店guid获取模板详情
     */
    public StoreHomeTemplateDetailsVO getByStore(String storeGuid) {
        String storeTemplateRelation = getStoreTemplateRelation(storeGuid);
        if (StringUtils.isEmpty(storeTemplateRelation)) {
            log.error("当前店铺没有主页模板,storeGuid:{}", storeGuid);
            throw new MemberToolException("当前店铺没有主页模板");
        }
        StoreHomeTemplateDetailsVO template = getTemplate(storeTemplateRelation);
        if (Objects.isNull(template)) {
            throw new MemberToolException("当前店铺没有主页模板");
        }
        return template;
    }


    /**
     * 根据模板guid 获取模板信息
     */
    public StoreHomeTemplateDetailsVO getTemplate(String templateGuid) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String redisKey = String.format(STORE_HOME_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            String templateString = redisTemplate.opsForValue().get(redisKey);
            if (!StringUtils.isEmpty(templateString)) {
                return JacksonUtils.toObject(StoreHomeTemplateDetailsVO.class, templateString);
            }
            log.error("店铺主页模板缓存有误,redisKey:{},", redisKey);
            return null;
        }
        // 查询db
        StoreHomeTemplateDetailsVO templateVO = storeHomeTemplateService.get(templateGuid);
        if (Objects.isNull(templateVO)) {
            return null;
        }
        setTemplate(templateVO);
        return templateVO;
    }

    /**
     * 更新模板
     */
    public void setTemplate(StoreHomeTemplateDetailsVO templateVO) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String templateGuid = templateVO.getGuid();
        String redisKey = String.format(STORE_HOME_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        redisTemplate.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(templateVO), 1, TimeUnit.DAYS);
    }

    /**
     * 删除模板
     */
    public void removeTemplate(String templateGuid) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String redisKey = String.format(STORE_HOME_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        redisTemplate.delete(redisKey);
    }


    /**
     * 更新单个绑定门店关联模板关系
     */
    public void putStoreTemplateRelation(String templateGuid, String storeGuid) {
        redisTemplate.opsForHash().put(STORE_HOME_TEMPLATE_RELATION_KEY, storeGuid, templateGuid);
        redisTemplate.expire(STORE_HOME_TEMPLATE_RELATION_KEY, 1, TimeUnit.DAYS);
    }

    /**
     * 删除门店关联模板关系
     */
    public void removeStoreTemplateRelation(List<String> storeGuids) {
        redisTemplate.opsForHash().delete(STORE_HOME_TEMPLATE_RELATION_KEY, storeGuids.toArray());
    }

    /**
     * 根据门店guid获取模板guid
     */
    public String getStoreTemplateRelation(String storeGuid) {
        Object templateGuid = redisTemplate.opsForHash().get(STORE_HOME_TEMPLATE_RELATION_KEY, storeGuid);
        if (Objects.isNull(templateGuid)) {
            // db
            String templateGuidByStoreGuid = storeHomeTemplateRelationService.getTemplateGuidByStoreGuid(storeGuid);
            if (StringUtils.isEmpty(templateGuidByStoreGuid)) {
                return null;
            }
            putStoreTemplateRelation(templateGuidByStoreGuid, storeGuid);
            return templateGuidByStoreGuid;
        }
        return templateGuid.toString();
    }
}
