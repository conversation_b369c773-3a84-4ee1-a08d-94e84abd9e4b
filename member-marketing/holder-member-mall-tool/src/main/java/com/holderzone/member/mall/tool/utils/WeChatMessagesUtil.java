package com.holderzone.member.mall.tool.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.holderzone.member.common.dto.message.WeChatMessageDataItemVm;
import com.holderzone.member.common.dto.message.WechatLoginInfo;
import com.holderzone.member.common.enums.malltool.ErrCodeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.qo.tool.MessagesConfigStatusQO;
import com.holderzone.member.mall.tool.entity.HsaMessagesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
public class WeChatMessagesUtil {

    /**
     * 小程序获取access_token的url地址
     */
    private static final String MINI_PROGRAM_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

    /**
     * 微信accessToken缓存key
     */
    private static final String MINI_PROGRAM_ACCESS_TOKEN_KEY = "miniProgramAccessToken:%s";

    @Resource
    private StringRedisTemplate stringredisTemplate;

    private static final String PRI_TMPL_ID = "priTmplId";

    private static final String ERR_CODE = "errcode";


    /**
     * 添加小程序模板
     */
    private static final String ADD_TEMPLATE_APPLET = "https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate?access_token=%s";


    /**
     * 删除小程序模板
     */
    private static final String DELETE_TEMPLATE_APPLET = "https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate?access_token=%s";


    /**
     * 添加公众号模板
     */
    private static final String ADD_TEMPLATE = "https://api.weixin.qq.com/cgi-bin/template/api_add_template?access_token=%s";

    /**
     * 删除公众号模板
     */
    private static final String DELETE_TEMPLATE = "https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate?access_token=%s";


    public String getAccessTokenByAppId(String appId, String appSecret) {
        String accessToken = stringredisTemplate.opsForValue().get(String.format(MINI_PROGRAM_ACCESS_TOKEN_KEY, appId));

        if (true) {
            String result = "";
            try {
                HashMap<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("grant_type", "client_credential");
                paramMap.put("appid", appId);
                paramMap.put("secret", appSecret);
                result = HttpsClientUtils.doGet(MINI_PROGRAM_TOKEN_URL, paramMap);
                log.info("get weChat access token:{} ", result);
                Map<String, Object> accessTokenMap = JSON.parseObject(result, Map.class);
                accessToken = accessTokenMap.get("access_token").toString();
                stringredisTemplate.opsForValue().set(String.format(MINI_PROGRAM_ACCESS_TOKEN_KEY, appId), accessToken, 55, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("get weiChat access token fail:", e);
            }
        }

        return accessToken;
    }

    /**
     * 添加公众号模板
     */
    public String getTemplateId(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        String accessToken = checkToken(messagesConfigVO);
        JSONObject preParams = new JSONObject();
        //添加公众号模板
        preParams.put("template_id_short", messagesConfig.getTemplateNo());
        String url = String.format(ADD_TEMPLATE, accessToken).intern();
        String result = HttpsClientUtils.doPost(url, preParams.toJSONString());
        return JSON.parseObject(result).getString("template_id");

    }

    private String checkToken(MessagesConfigStatusQO messagesConfigVO) {
        String accessToken = getAccessTokenByAppId(messagesConfigVO.getAppId(), messagesConfigVO.getAppSecret());
        if (StringUtils.isEmpty(accessToken)) {
            throw new MallBaseException("请先授权");
        }
        return accessToken;
    }

    /**
     * 添加小程序模板
     */
    public String getAppletTemplateId(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        String accessToken = checkToken(messagesConfigVO);
        JSONObject preParams = new JSONObject();
        try {
            preParams.put("tid", messagesConfig.getAppletTemplateNo());
            preParams.put("kidList", Arrays.stream(messagesConfig.getAppletMsgKid().split(","))
                    .mapToInt(Integer::valueOf).toArray());
            preParams.put("sceneDesc", messagesConfig.getScenarioDescription());
            String url = String.format(ADD_TEMPLATE_APPLET, accessToken).intern();
            String result = HttpsClientUtils.doPost(url, preParams.toJSONString());
            JSONObject jsonObject = JSON.parseObject(result);
            String errCode = jsonObject.getString(ERR_CODE);
            ErrCodeEnum errCodeEnum = ErrCodeEnum.getEnumByName(Integer.valueOf(errCode));
            if (Objects.nonNull(errCodeEnum)) {
                log.error("开启失败：{}", errCodeEnum.getDes());
                throw new MemberToolException("开启失败！请确认小程序类目");
            }
            return jsonObject.getString(PRI_TMPL_ID);
        } catch (Exception e) {
            log.error("获取小程序模板失败：{}", e.getMessage());
            throw new MemberToolException("获取小程序模板失败，请检查配置");
        }
    }


    /**
     * 删除小程序模板
     */
    public void deleteAppletTemplateId(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        String accessToken = checkToken(messagesConfigVO);
        JSONObject preParams = new JSONObject();
        preParams.put(PRI_TMPL_ID, messagesConfig.getAppletTemplateId());
        String url = String.format(DELETE_TEMPLATE_APPLET, accessToken).intern();
        HttpsClientUtils.doPost(url, preParams.toJSONString());
    }


    /**
     * 删除公众号模板
     */
    public void deleteTemplateId(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        String accessToken = checkToken(messagesConfigVO);
        JSONObject preParams = new JSONObject();
        preParams.put(PRI_TMPL_ID, messagesConfig.getAppletTemplateId());
        String url = String.format(DELETE_TEMPLATE, accessToken).intern();
        HttpsClientUtils.doPost(url, preParams.toJSONString());
    }
}
