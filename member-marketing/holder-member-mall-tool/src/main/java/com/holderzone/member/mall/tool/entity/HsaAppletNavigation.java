package com.holderzone.member.mall.tool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 小程序导航
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaAppletNavigation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "点亮图标")
    private String onPicture;

    @ApiModelProperty(value = "未点亮图标")
    private String offPicture;

    @ApiModelProperty(value = "链接地址")
    private String url;

    /**
     * 页面名字
     */
    private String selectName;

    /**
     * 页面值
     */
    private String selectValue;

    /**
     * 页面类型
     */
    private String typeText;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    /**
     * 链接地址id
     */
    private String linkId;

    private Integer type;

}
