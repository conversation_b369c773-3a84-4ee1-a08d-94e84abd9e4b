package com.holderzone.member.mall.tool.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.holderzone.member.common.qo.tool.MessagesConfigStatusQO;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.vo.tool.MessagesConfigVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import com.holderzone.member.mall.tool.entity.HsaMessagesConfig;

import java.util.List;


public interface HsaMessagesConfigService extends IService<HsaMessagesConfig> {

    void saveMessagesConfig(List<MessagesConfigQO> messagesConfigQO);


    SendMessagesConfigVO getMessagesConfigByName(String operSubjectGuid, String name);


    boolean updateMessagesStatus(MessagesConfigStatusQO messagesConfigVO);


    List<MessagesConfigVO> getMessagesConfig();


    void initMessagesConfig();

    void initMessagesBySubjectGuid(List<String> operSubjectGuidList);

}
