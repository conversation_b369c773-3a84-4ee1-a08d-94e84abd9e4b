package com.holderzone.member.mall.tool.controller.unilink;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 会员通-系统终端配置Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system_config")
public class HsaMemberUnilinkSystemConfigController {

    @Resource
    private HsaMemberUnilinkSystemConfigService systemConfigService;

    /**
     * 查询系统配置（终端、渠道、业务）列表
     *
     * @return 系统终端列表
     */
    @GetMapping("/listConfig")
    public Result<MemberUnilinkSystemConfigVO> listConfig() {
        return Result.success(systemConfigService.listConfig());
    }
} 