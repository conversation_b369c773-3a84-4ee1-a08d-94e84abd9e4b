package com.holderzone.member.mall.tool.service.wechat;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.card.MemberCardExcelQO;
import com.holderzone.member.common.qo.wechat.WechatAccountCreditedQO;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedPageVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatAccountCredited;

import java.util.List;

public interface HsaWechatAccountCreditedService extends IService<HsaWechatAccountCredited> {

    /**
     * 保存修改收款账户
     * @param request
     */
    void saveOrUpdateAccountCredited(WechatAccountCreditedQO request);

    /**
     * 查询收款账户详情
     * @param guid
     * @return
     */
    WechatAccountCreditedVO queryAccountCreditedByGuid(String guid);

    /**
     * 根据门店查询
     * @param guid
     * @return
     */
    WechatAccountCreditedVO queryAccountCreditedByStoreGuid(String guid);

    /**
     * 查询运营主体下 会员营收类型收款账户
     */
    WechatAccountCreditedVO queryAccountCreditedByOperSubjectGuid(String operSubjectGuid);


    //获取所有门店
    List<StoreAccountCreditedVO> queryStore(String guid);

    /**
     * 分页查询收款账户列表
     * @return
     */
    PageResult<WechatAccountCreditedPageVO> getAccountCreditedPage(PageDTO pageDTO);


    /**
     * 删除数据
     * @return
     */
    Boolean delete(String guid);


    /**
     * @return
     */
    Boolean isCheckCan(String guid);
}
