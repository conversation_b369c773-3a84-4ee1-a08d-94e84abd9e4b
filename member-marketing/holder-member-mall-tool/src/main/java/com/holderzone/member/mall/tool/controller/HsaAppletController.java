package com.holderzone.member.mall.tool.controller;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.mall.tool.service.HsaAppletNavigationService;
import com.holderzone.member.mall.tool.service.HsaThemeStyleService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/applets/")
public class HsaAppletController {

    @Resource
    private HsaAppletNavigationService hsaAppletNavigationService;

    @Resource
    private HsaThemeStyleService hsaThemeStyleService;


    @ApiOperation("查询导航")
    @GetMapping("navigation/getAppletNavigation")
    public Result getAppletNavigation(Integer type) {

        return Result.success(hsaAppletNavigationService.getAppletNavigation(type));
    }

    @ApiOperation("查询主题风格")
    @GetMapping(value = "theme/getThemeStyle", produces = "application/json;charset=utf-8")
    public Result getThemeStyle(Integer type) {

        return Result.success(hsaThemeStyleService.getThemeStyle(type));
    }
}
