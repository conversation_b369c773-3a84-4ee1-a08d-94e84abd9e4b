package com.holderzone.member.mall.tool.service.unilink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemBizConfig;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemConfig;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemBizConfigMapper;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemConfigMapper;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkSystemBizConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员通-系统业务配置Service实现
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
public class HsaMemberUnilinkSystemBizConfigServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemBizConfigMapper, HsaMemberUnilinkSystemBizConfig> implements HsaMemberUnilinkSystemBizConfigService {

    @Resource
    private HsaMemberUnilinkSystemConfigMapper systemConfigMapper;

    @Override
    public List<MemberUnilinkSelectOptionVO> listSystemBiz() {
        // 查询所有系统配置
        List<HsaMemberUnilinkSystemConfig> systemConfigs = systemConfigMapper.selectList(new LambdaQueryWrapper<>());

        // 查询所有业务配置
        List<HsaMemberUnilinkSystemBizConfig> bizConfigs = this.list(
                new LambdaQueryWrapper<HsaMemberUnilinkSystemBizConfig>()
                        .orderByAsc(HsaMemberUnilinkSystemBizConfig::getSystemConfigId)
                        .orderByAsc(HsaMemberUnilinkSystemBizConfig::getSortOrder)
        );

        // 将系统配置转换为Map，方便查找
        Map<Integer, HsaMemberUnilinkSystemConfig> systemConfigMap = systemConfigs.stream()
                .collect(Collectors.toMap(HsaMemberUnilinkSystemConfig::getId, config -> config));

        // 转换为VO
        return bizConfigs.stream()
                .filter(biz -> systemConfigMap.containsKey(biz.getSystemConfigId()))
                .map(biz -> {
                    HsaMemberUnilinkSystemConfig systemConfig = systemConfigMap.get(biz.getSystemConfigId());
                    String label = systemConfig == null ? biz.getBizName() : systemConfig.getSystemName() + "-" + biz.getBizName();
                    String systemType = systemConfig == null ? null : systemConfig.getSystemType();
                    return new MemberUnilinkSelectOptionVO()
                            .setLabel(label)
                            .setValue(biz.getBizCode())
                            .setSystemType(systemType);
                })
                .collect(Collectors.toList());
    }
}