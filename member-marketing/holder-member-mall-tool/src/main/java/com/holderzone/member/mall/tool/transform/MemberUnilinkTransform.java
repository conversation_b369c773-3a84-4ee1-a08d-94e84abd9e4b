package com.holderzone.member.mall.tool.transform;

import com.holderzone.member.common.dto.unilink.MemberUnilinkIndustryDTO;
import com.holderzone.member.common.dto.unilink.MemberUnilinkSystemDTO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkIndustry;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 会员通-类型转换器
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Mapper
public interface MemberUnilinkTransform {
    MemberUnilinkTransform INSTANCES = Mappers.getMapper(MemberUnilinkTransform.class);

    @Mapping(target = "id", ignore = true)
    HsaMemberUnilinkIndustry fromMemberUnilinkIndustryDTO(MemberUnilinkIndustryDTO dto);

    HsaMemberUnilinkSystem fromMemberUnilinkSystemDTO(MemberUnilinkSystemDTO dto);

    MemberUnilinkSystemVO toMemberUnilinkSystemVO(HsaMemberUnilinkSystem hsaMemberUnilinkSystem);
}
