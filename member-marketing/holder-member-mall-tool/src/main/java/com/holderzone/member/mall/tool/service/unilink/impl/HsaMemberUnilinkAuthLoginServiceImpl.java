package com.holderzone.member.mall.tool.service.unilink.impl;

import com.holderzone.member.common.constant.CommonConstant;
import com.holderzone.member.common.dto.ipaas.CheckPasswordDTO;
import com.holderzone.member.common.dto.ipaas.QueryUserEnterpriseDTO;
import com.holderzone.member.common.enums.exception.MemberMallToolExceptionEnum;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.ipass.EnterpriseUserRoleInfoVO;
import com.holderzone.member.mall.tool.service.unilink.HsaMemberUnilinkAuthLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员通-系统Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Service
@Slf4j
public class HsaMemberUnilinkAuthLoginServiceImpl implements HsaMemberUnilinkAuthLoginService {
    @Resource
    private IPaasFeign iPaasFeign;

    @Override
    public List<EnterpriseUserRoleInfoVO> mallLogin(CheckPasswordDTO dto) {
        // 校验密码有效性
        IPaasFeignModel<Boolean> checkPasswordResult = iPaasFeign.checkPassword(dto);
        if (checkPasswordResult == null || checkPasswordResult.getData() == null || !checkPasswordResult.getData()) {
            throw new MemberToolException(MemberMallToolExceptionEnum.SYSTEM_AUTH_ACCOUNT_PASSWORD_ERROR);
        }

        // 查询账号下商城企业
        IPaasFeignModel<EnterpriseUserRoleInfoVO> enterpriseResult = iPaasFeign.findUserRoleEnterpriseWithProduct(
                new QueryUserEnterpriseDTO(
                        dto.getAccount(),
                        CommonConstant.PRODUCT_IDENTIFICATION_PRIVATE_MALL
                )
        );
        if (enterpriseResult == null || enterpriseResult.getDataList() == null) {
            return Collections.emptyList();
        }

        // 过滤作为企业负责人的企业
        return enterpriseResult.getDataList().stream().filter(item -> item.getResponsibleUserAccount().equals(dto.getAccount())).collect(Collectors.toList());
    }
}
