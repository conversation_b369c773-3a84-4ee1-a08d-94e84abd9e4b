package com.holderzone.member.mall.tool.entity.cloud;

import com.baomidou.mybatisplus.annotation.TableId;
import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统主体关联开关
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
public class HsaApplyOperSubjectCloudRule{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    private String enterpriseGuid;

    @ApiModelProperty(value = "1 开启 0关闭")
    private Integer isSwitch;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


}
