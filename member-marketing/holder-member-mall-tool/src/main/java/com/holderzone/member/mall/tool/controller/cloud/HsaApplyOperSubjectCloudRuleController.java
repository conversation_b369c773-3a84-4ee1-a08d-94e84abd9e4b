package com.holderzone.member.mall.tool.controller.cloud;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudRuleVO;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudRuleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 系统主体关联开关 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@RestController
@RequestMapping("/cloud/rule")
public class HsaApplyOperSubjectCloudRuleController {

    @Resource
    private IHsaApplyOperSubjectCloudRuleService hsaApplyOperSubjectCloudRuleService;

    /**
     * 查询关联餐饮云门店系统开关
     */
    @ApiOperation("获取云平台主体")
    @GetMapping("/query")
    Result<OperSubjectCloudRuleVO> query(){
        return Result.success(hsaApplyOperSubjectCloudRuleService.query());
    }

    /**
     * 保存
     * @param isSwitch 1 开启 0 关闭
     * @return
     */
    @ApiOperation("获取云平台主体")
    @GetMapping("/save")
    Result<Boolean> save(Integer isSwitch){
        return Result.success(hsaApplyOperSubjectCloudRuleService.save(isSwitch));
    }
}

