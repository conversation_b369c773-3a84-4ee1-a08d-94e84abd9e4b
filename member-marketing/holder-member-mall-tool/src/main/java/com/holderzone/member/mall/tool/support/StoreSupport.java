package com.holderzone.member.mall.tool.support;

import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * crm 门店数据支持
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StoreSupport {

    private final CrmFeign crmBaseFeign;

    /**
     * 远程调用获取门店和档口信息
     */
    public List<StoreInfoVO> getStoreInfo() {
        StoreInfoDTO query = new StoreInfoDTO();
        query.setChannel("会员商城");
        query.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
        CrmFeignModel<StoreInfoVO> storeStallResult = crmBaseFeign.getStoreStall(query).getResult();
        log.info("[listStorePage]查询主体下的店铺和档口,query={},storeStallResult={}", JacksonUtils.writeValueAsString(query),
                JacksonUtils.writeValueAsString(storeStallResult));
        return storeStallResult.getDataList();
    }

    /**
     * 获取门店ids
     */
    public List<Long> getStoreIds() {
        List<StoreInfoVO> storeList = getStoreInfo();
        if (CollectionUtils.isEmpty(storeList)) {
            return Lists.newArrayList();
        }
        return storeList.stream().map(StoreInfoVO::getStore_id).distinct().collect(Collectors.toList());
    }


    /**
     * 查询门店基础信息
     */
    public List<StoreDataInfoVO> getStoreById(List<Integer> storeIds) {
        StoreByIdQO storageByIdQuery = new StoreByIdQO();
        storageByIdQuery.setStore_ids(storeIds);
        FeignModel<?> result = crmBaseFeign.getStoreById(storageByIdQuery).getResult();
        if(result == null || result.getDataList() == null){
            return Collections.emptyList();
        }
        String storeData = JSONArray.toJSONString(result.getDataList());
        return JSONArray.parseArray(storeData, StoreDataInfoVO.class);
    }


}
