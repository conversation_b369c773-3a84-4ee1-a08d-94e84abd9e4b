package com.holderzone.member.mall.tool.controller.wechat;


import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.wechat.WechatAccountCreditedQO;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedPageVO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedVO;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatAccountCreditedService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import com.holderzone.member.common.constant.Result;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 微信小程序收款账户配置
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/account/credited")
public class HsaWechatAccountCreditedController {

    @Resource
    private HsaWechatAccountCreditedService wechatAccountCreditedService;


    /**
     * 保存修改收款账户
     * @param request
     */
    @ApiOperation("保存修改收款账户")
    @PostMapping("/save")
    Result<Boolean> saveOrUpdateAccountCredited(@RequestBody WechatAccountCreditedQO request){
        wechatAccountCreditedService.saveOrUpdateAccountCredited(request);
        return Result.success();
    }

    /**
     * 查询收款账户详情
     * @param guid
     * @return
     */
    @ApiOperation("查询收款账户详情")
    @GetMapping("/get")
    Result<WechatAccountCreditedVO> queryAccountCreditedByGuid(@RequestParam(value = "guid") String guid){
       return Result.success(wechatAccountCreditedService.queryAccountCreditedByGuid(guid));
    }

    @ApiOperation("查询收款账户门店档口")
    @GetMapping("/query/store")
    Result<List<StoreAccountCreditedVO>> queryStore(@RequestParam(value = "guid") String guid){
        return Result.success(wechatAccountCreditedService.queryStore(guid));
    }

    /**
     * 分页查询收款账户列表
     * @return
     */
    @ApiOperation("分页查询收款账户列表")
    @PostMapping("/get/page")
    Result<PageResult<WechatAccountCreditedPageVO>> getAccountCreditedPage(@RequestBody PageDTO pageDTO){
        return Result.success(wechatAccountCreditedService.getAccountCreditedPage(pageDTO));
    }


    /**
     * 删除数据
     * @return
     */
    @ApiOperation("删除数据")
    @GetMapping("/delete")
    Result<Boolean> delete(@RequestParam(value = "guid") String guid){
        return Result.success(wechatAccountCreditedService.delete(guid));
    }


    /**
     * 查询会员营收是否可勾选
     * @return
     */
    @ApiOperation("查询会员营收是否可勾选")
    @GetMapping("/isCheckCan")
    Result<Boolean> isCheckCan(@RequestParam(value = "guid") String guid){
        return Result.success(wechatAccountCreditedService.isCheckCan(guid));
    }
}

