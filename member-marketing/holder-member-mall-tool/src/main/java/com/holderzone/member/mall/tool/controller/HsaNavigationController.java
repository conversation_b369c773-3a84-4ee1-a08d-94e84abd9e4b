package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.NavigationQO;
import com.holderzone.member.mall.tool.service.HsaAppletNavigationService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/navigation")
public class HsaNavigationController {

    @Resource
    private HsaAppletNavigationService hsaAppletNavigationService;

    @ApiOperation("新增小程序导航")
    @PostMapping("/saveAppletNavigation")
    public Result saveAppletNavigation(@RequestBody NavigationQO reqDTO) {

        return Result.success(hsaAppletNavigationService.saveAppletNavigation(reqDTO));
    }

    @ApiOperation("查询导航")
    @GetMapping("/getAppletNavigation")
    public Result getAppletNavigation(Integer type) {

        return Result.success(hsaAppletNavigationService.getAppletNavigation(type));
    }

}
