package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.PermissionResult;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.mall.tool.service.HsaPermissionTypeService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/permission")
public class HsaPermissionController {

    @Resource
    private HsaPermissionTypeService hsaPermissionTypeService;

    /**
     * 运营主体权限
     */
    private static final int SUBJECT_PERMISSION = 0;

    private static final int STORE_PERMISSION = 1;

    @ApiOperation("查询权限门店")
    @PostMapping("/getStorePermission")
    public PermissionResult getPermissionStore(@RequestBody OperSubjectPermissionQO request) {

        return PermissionResult.success(hsaPermissionTypeService.getStorePermission(request));
    }


    @ApiOperation("更新商城门店权限")
    @PostMapping("/updateStorePermission")
    public PermissionResult updateStorePermission(@RequestBody HsaOperSubjectPermissionQO request) {

        return PermissionResult.isSuccess(hsaPermissionTypeService.updatePermission(request, STORE_PERMISSION));
    }

    @ApiOperation("查询运营主体权限")
    @PostMapping("/getOperSubjectPermission")
    public PermissionResult getOperSubjectPermission(@RequestBody OperSubjectPermissionQO request) {

        return PermissionResult.success(hsaPermissionTypeService.getOperSubjectPermission(request));
    }

    @ApiOperation("更新运营主体权限")
    @PostMapping("/updateOperSubjectPermission")
    public PermissionResult updateOperSubjectPermission(@RequestBody HsaOperSubjectPermissionQO request) {

        return PermissionResult.isSuccess(hsaPermissionTypeService.updatePermission(request, SUBJECT_PERMISSION));
    }

    @ApiOperation("查询权限")
    @GetMapping("/getAccountPermission")
    public PermissionResult getAccountPermission(String identification) {
        return PermissionResult.success(hsaPermissionTypeService.getAccountPermission(identification));
    }


    @ApiOperation("权限处理")
    @PostMapping("/toOperSubjectPermission")
    public List<PermissionModelDTO> toOperSubjectPermission(@RequestBody RoleAndPostIdDTO roleAndPostIdDTO) {
        return hsaPermissionTypeService.toOperSubjectPermission(roleAndPostIdDTO, ThreadLocalCache.getHeaderUserInfo());
    }


}
