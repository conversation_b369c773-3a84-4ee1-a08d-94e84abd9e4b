package com.holderzone.member.mall.tool.service.wechat;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoDTO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigVO;
import com.holderzone.member.mall.tool.entity.wechat.HsaWechatConfigInfo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface HsaWeChatConfigInfoService extends IService<HsaWechatConfigInfo> {

    /**
     * 保存或者修改
     * @param request
     */
    void saveOrUpdateWeChatConfig(WeChatConfigInfoQO request);


    /**
     * 查询
     * @return
     */
    WeChatConfigInfoVO queryByOperSubjectGuid();

    /**
     * 查询
     * @return
     */
    WeChatConfigVO queryByAppId(String appId);


    PaySettingVO getPaySetting(PaySettingDTO paySettingDTO);

    List<WeChatConfigInfoVO> queryByOperSubjectGuidList(List<String> operSubjectGuidList);
}
