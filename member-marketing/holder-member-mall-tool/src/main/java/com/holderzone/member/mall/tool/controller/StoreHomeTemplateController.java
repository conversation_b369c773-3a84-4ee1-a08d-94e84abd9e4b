package com.holderzone.member.mall.tool.controller;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.tool.home.StoreHomeTemplateCreateReqDTO;
import com.holderzone.member.common.dto.tool.home.StoreHomeTemplateRelationReqDTO;
import com.holderzone.member.common.dto.tool.home.StoreHomeTemplateUpdateReqDTO;
import com.holderzone.member.common.qo.tool.StoreHomeTemplateQO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.tool.StoreHomeTemplateDetailsVO;
import com.holderzone.member.mall.tool.manager.StoreHomeTemplateManager;
import com.holderzone.member.mall.tool.manager.bo.StoreHomeTemplateBO;
import com.holderzone.member.mall.tool.manager.builder.StoreHomeTemplateBizBuilder;
import com.holderzone.member.mall.tool.service.HsaStoreHomeTemplateService;
import com.holderzone.member.mall.tool.support.StoreHomeTemplateCacheSupport;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 店铺主页模板
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/store/home/<USER>")
public class StoreHomeTemplateController {

    private final HsaStoreHomeTemplateService storeHomeTemplateService;

    private final StoreHomeTemplateManager storeHomeTemplateManager;

    private final StoreHomeTemplateCacheSupport storeHomeTemplateCacheSupport;


    @ApiOperation("小程序查询店铺主页模板")
    @GetMapping("/store/{storeGuid}")
    public Result<StoreHomeTemplateDetailsVO> getByStore(@PathVariable("storeGuid") String storeGuid) {
        return Result.success(storeHomeTemplateCacheSupport.getByStore(storeGuid));
    }

    @ApiOperation("模板列表")
    @PostMapping("/page")
    public Result<PageResult> pageInfo(@RequestBody StoreHomeTemplateQO query) {
        return Result.success(storeHomeTemplateManager.pageInfo(query));
    }

    @ApiOperation("查询模板")
    @GetMapping("/{guid}")
    public Result<StoreHomeTemplateDetailsVO> get(@PathVariable("guid") String guid) {
        return Result.success(storeHomeTemplateService.get(guid));
    }

    @ApiOperation("新增模板")
    @PostMapping("/save")
    public Result<Void> save(@RequestBody StoreHomeTemplateCreateReqDTO request) {
        StoreHomeTemplateBO biz = StoreHomeTemplateBizBuilder.build(request);
        storeHomeTemplateManager.save(biz);
        return Result.success();
    }

    @ApiOperation("编辑模板")
    @PostMapping("/update")
    public Result<Void> update(@RequestBody StoreHomeTemplateUpdateReqDTO request) {
        StoreHomeTemplateBO biz = StoreHomeTemplateBizBuilder.build(request);
        storeHomeTemplateManager.update(biz);
        return Result.success();
    }

    @ApiOperation("删除模板")
    @DeleteMapping("/remove/{guid}")
    public Result<Void> remove(@PathVariable("guid") String guid) {
        storeHomeTemplateManager.remove(guid);
        return Result.success();
    }

    @ApiOperation("应用店铺")
    @PostMapping("/relation")
    public Result<Void> relation(@RequestBody StoreHomeTemplateRelationReqDTO request) {
        List<String> relationGuids = storeHomeTemplateManager.relation(request.getGuid(), request.getStoreGuids());
        if (CollectionUtils.isNotEmpty(relationGuids)) {
            log.info("更新店铺模板缓存,templateGuid:{},StoreGuids:{},relationGuids:{}", request.getGuid(),
                    request.getStoreGuids(), relationGuids);
            storeHomeTemplateCacheSupport.removeStoreTemplateRelation(relationGuids);
        }
        return Result.success();
    }

    @ApiOperation("可选门店")
    @GetMapping("/choice/{guid}")
    public Result<List<StoreInfoVO>> choiceStores(@PathVariable("guid") String guid) {
        return Result.success(storeHomeTemplateManager.choiceStores(guid));
    }

    @ApiOperation("查询主体下门店列表")
    @PostMapping("/store/list")
    public Result<List<StoreInfoVO>> storeList(@RequestBody SingleDataDTO request) {
        return Result.success(storeHomeTemplateManager.storeList(request));
    }
}
