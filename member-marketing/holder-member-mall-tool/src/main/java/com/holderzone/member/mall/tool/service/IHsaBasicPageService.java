package com.holderzone.member.mall.tool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.malltool.BasicPageReqDTO;
import com.holderzone.member.mall.tool.entity.HsaBasicPage;

import java.util.List;

/**
 * <p>
 * 基础页面表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
public interface IHsaBasicPageService extends IService<HsaBasicPage> {

    void saveBatch(List<BasicPageReqDTO> reqDTOList);
}
