package com.holderzone.member.mall.tool.service.cloud;

import com.holderzone.member.common.vo.cloud.OperSubjectCloudRuleVO;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloudRule;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

/**
 * <p>
 * 系统主体关联开关 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface IHsaApplyOperSubjectCloudRuleService extends IHolderBaseService<HsaApplyOperSubjectCloudRule> {

    /**
     * 查询关联餐饮云门店系统开关
     */
    OperSubjectCloudRuleVO query();

    /**
     * 保存
     * @param isSwitch 1 开启 0 关闭
     * @return
     */
    Boolean save(Integer isSwitch);
}
