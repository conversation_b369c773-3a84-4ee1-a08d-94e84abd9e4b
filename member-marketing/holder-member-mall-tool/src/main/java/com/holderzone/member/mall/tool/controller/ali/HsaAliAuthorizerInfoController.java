package com.holderzone.member.mall.tool.controller.ali;


import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.ali.AliAuthorizerInfoDTO;
import com.holderzone.member.common.dto.ali.AliCallbackMessAgeDTO;
import com.holderzone.member.common.dto.ali.AliOpenAuthDTO;
import com.holderzone.member.common.vo.ali.AliAppletInfoVO;
import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.mall.tool.service.ali.IHsaAliAuthorizerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @version 1.0
 * @className AliAuthOpenController
 * @description 支付宝授权Controller
 */
@RestController
@RequestMapping("/alipay_open")
@Api(value = "支付宝授权Controller")
@Slf4j
public class HsaAliAuthorizerInfoController {

    @Autowired
    private IHsaAliAuthorizerInfoService aliAuthOpenService;


    @ApiOperation("获取授权链接")
    @GetMapping("/getAuthLink")
    Result<String> getAuthLink() {
        return Result.success(aliAuthOpenService.getAuthLink());
    }

    @ApiOperation("根据小程序appId获取授权信息")
    @GetMapping("/getAliAppletInfo/by_auth_appId")
    public AliAuthorizerInfoVO getByAuthAppId(String authAppId) {
        return aliAuthOpenService.getByAuthAppId(authAppId);
    }

    /**
     * 根据运营主体查询 授权信息
     */
    @ApiOperation("根据运营主体guid获取授权信息")
    @GetMapping("/getAliAppletInfo/by_oper_subject_guid")
    AliAuthorizerInfoDTO getByOperSubjectGuid(String operSubjectGuid) {
        return aliAuthOpenService.getByOperSubjectGuid(operSubjectGuid);
    }

    @ApiOperation("获取小程序基础信息")
    @GetMapping("/getAliAppletInfo")
    Result<AliAppletInfoVO> getAliAppletInfo(String appAuthToken) {
        return Result.success(aliAuthOpenService.getAliAppletInfo(appAuthToken));
    }

    /**
     * 回调获取授权码
     *
     * @param
     */
    @ApiOperation("回调获取授权码")
    @PostMapping("/callback_auth")
    void callBackAppAuthCode(@RequestBody AliOpenAuthDTO aliOpenAuthDTO) {
        log.info("aliOpenAuthDTO:{}", JSON.toJSONString(aliOpenAuthDTO));
        aliAuthOpenService.callBackAppAuthCode(aliOpenAuthDTO);
    }

    @ApiOperation("取消回授权")
    @PostMapping("/callback_message")
    String callbackUnAuth(@RequestParam(value = "biz_content", required = false) String bizContent,
                          @RequestParam(value = "app_id", required = false) String appId,
                          @RequestParam(value = "msg_method", required = false) String msgMethod,
                          @RequestParam(value = "notify_id", required = false) String notifyId) {

        log.info("appId:{}", JSON.toJSONString(appId));
        log.info("bizContent:{}", JSON.toJSONString(bizContent));
        log.info("msgMethod:{}", JSON.toJSONString(msgMethod));
        log.info("notify_id:{}", JSON.toJSONString(notifyId));
        AliCallbackMessAgeDTO aliCallbackMessAgeDTO = JSON.parseObject(bizContent.replaceAll("\\\\(.)", "$1"), AliCallbackMessAgeDTO.class);
        log.info("aliCallbackMessAgeDTO:{}", JSON.toJSONString(aliCallbackMessAgeDTO));
        aliAuthOpenService.callbackUnAuth(aliCallbackMessAgeDTO.getAuth_app_id(), msgMethod, notifyId);
        return StringConstant.SUCCESS;
    }


    /**
     * 获取此主体是否绑定信息
     *
     * @return
     */
    @ApiOperation("获取此主体是否绑定信息")
    @GetMapping("/getAppAuthInfo")
    Result<AliAuthorizerInfoVO> getAppAuthInfo() {
        return Result.success(aliAuthOpenService.getAppAuthInfo());
    }

}

