package com.holderzone.member.mall.tool.utils;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/1/4 下午2:13
 * @description http工具类
 */
@Slf4j
public class HttpUtil {

    private HttpUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @return String
     * @throws IOException IO异常
     */
    public static String doPost(String url, String jsonString) throws IOException {
        return doPost(url, jsonString, new HashMap<String, String>());
    }

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @param headerMap  要设置的头部信息
     * @return String
     * @throws IOException IO异常
     */
    public static String doPost(String url, String jsonString, Map<String, String> headerMap) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{} headerMap={}", url, jsonString, headerMap);
        HttpClient httpClient = HttpClientBuilder.create().build();
        StringEntity stringEntity = new StringEntity(jsonString, "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            httpPost.setHeader(name, value);
        }
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost, responseHandler);
        log.info("http post result：{}", execute);
        return execute;
    }

    /**
     * HTTP post請求
     *
     * @param url      请求地址
     * @param jsonData new JSONObject(params)
     * @return HttpResponse
     */
    public static HttpResponse doPost(String url, JSONObject jsonData) {
        // 指定Post请求
        HttpPost httpPost = new HttpPost(url);
        // 创建httpclient
        HttpClient httpClient = new DefaultHttpClient();
        // 发送请求
        HttpResponse httpResponse;
        // 返回的json
        JSONObject jsonObject = null;
        // 封装post请求数据
        StringEntity entity = new StringEntity(jsonData.toString(), "utf-8");
        httpPost.setEntity(entity);
        try {
            // 发送请求
            httpResponse = httpClient.execute(httpPost);
            // 判断请求是否成功
            if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return httpResponse;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 发送 GET 请求（HTTP），不带输入数据
     *
     * @param url    请求地址
     * @param params 请求参数，K-V形式
     * @return 返回json结果
     */
    public static String doGet(String url, Map<String, Object> params) throws IOException, URISyntaxException {
        return doGet(url, params, new HashMap<String, String>());
    }

    /**
     * 发送 GET 请求（HTTP），K-V形式
     *
     * @param url    请求地址
     * @param params 请求参数，K-V形式
     * @return 返回json结果
     */
    public static String doGet(String url, Map<String, Object> params, Map<String, String> header)
            throws IOException, URISyntaxException {
        log.info("httpRequest:---> requestUrl={},requestBody={} headerMap={}", url, params, header);
        URIBuilder uriBuilder = new URIBuilder(url);
        for (Map.Entry<String, Object> key : params.entrySet()) {
            uriBuilder.setParameter(key.getKey(), key.getValue().toString());
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        for (Map.Entry<String, String> entry : header.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            httpGet.setHeader(name, value);
        }
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String result = httpClient.execute(httpGet, responseHandler);
        log.info("http get result={}", result);
        return result;
    }
}