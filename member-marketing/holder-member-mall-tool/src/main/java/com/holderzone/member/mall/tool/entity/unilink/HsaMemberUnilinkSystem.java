package com.holderzone.member.mall.tool.entity.unilink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.constant.CommonConstant;
import com.holderzone.member.common.enums.MemberUnilinkIndustryTypeEnum;
import com.holderzone.member.common.enums.MemberUnilinkSystemTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 会员通-系统
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@Accessors(chain = true)
@TableName("hsa_member_unilink_system")
public class HsaMemberUnilinkSystem implements Serializable {
    private static final long serialVersionUID = -1307802759662955571L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 行业id
     */
    private Long industryId;

    /**
     * 系统类型
     */
    private MemberUnilinkSystemTypeEnum systemType;

    /**
     * 系统备注
     */
    private String systemRemark;

    /**
     * 系统描述
     */
    private String systemDescription;

    /**
     * 是否启用统一会员：0-否，1-是
     */
    private Integer unifiedMember;

    /**
     * 是否启用统一营销：0-否，1-是
     */
    private Integer unifiedMarketing;

    /**
     * 是否授权
     */
    private Integer isAuth;

    /**
     * 授权企业GUID
     */
    private String authEnterpriseGuid;

    /**
     * 授权运营主体GUID
     */
    private String authOperSubjectGuid;

    /**
     * 授权企业名称
     */
    private String authEnterpriseName;

    /**
     * 授权运营主体名称
     */
    private String authOperSubjectName;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    /**
     * 根据行业类型获取默认系统
     *
     * @param industryType    行业类型
     * @param industryId      行业id
     * @param enterpriseGuid  企业GUID
     * @param operSubjectGuid 运营主体GUID
     * @return 行业列表
     */
    public static List<HsaMemberUnilinkSystem> getDefaultSystemListByIndustryType(MemberUnilinkIndustryTypeEnum industryType, Long industryId, String enterpriseGuid, String operSubjectGuid) {
        List<HsaMemberUnilinkSystem> systemList = new ArrayList<>();
        switch (industryType) {
            case REPAST_INDUSTRY:
                // 餐饮行业，初始化餐饮云
                HsaMemberUnilinkSystem repast = new HsaMemberUnilinkSystem()
                        .setEnterpriseGuid(enterpriseGuid)
                        .setOperSubjectGuid(operSubjectGuid)
                        .setIndustryId(industryId)
                        .setSystemType(MemberUnilinkSystemTypeEnum.REPAST)
                        .setSystemRemark(MemberUnilinkSystemTypeEnum.REPAST.getSystemRemark())
                        .setSystemDescription(MemberUnilinkSystemTypeEnum.REPAST.getSystemDescription())
                        .setUnifiedMember(CommonConstant.YES)
                        .setUnifiedMarketing(CommonConstant.YES);
                systemList.add(repast);
                break;
            case RETAIL_INDUSTRY:
                // 零售行业，初始化会员商城
                HsaMemberUnilinkSystem mall = new HsaMemberUnilinkSystem()
                        .setEnterpriseGuid(enterpriseGuid)
                        .setOperSubjectGuid(operSubjectGuid)
                        .setIndustryId(industryId)
                        .setSystemType(MemberUnilinkSystemTypeEnum.MALL)
                        .setSystemRemark(MemberUnilinkSystemTypeEnum.MALL.getSystemRemark())
                        .setSystemDescription(MemberUnilinkSystemTypeEnum.MALL.getSystemDescription())
                        .setUnifiedMember(CommonConstant.YES)
                        .setUnifiedMarketing(CommonConstant.YES);
                systemList.add(mall);
                break;
            default:
                break;
        }
        return systemList;
    }
}
