package com.holderzone.member.mall.tool.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.mall.tool.entity.ali.HsaAliAppletInfo;
import com.holderzone.member.mall.tool.mapper.ali.HsaAliAppletInfoMapper;
import com.holderzone.member.mall.tool.service.ali.IHsaAliAppletInfoService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
@Service
@Slf4j
public class HsaAliAppletInfoServiceImpl extends HolderBaseServiceImpl<HsaAliAppletInfoMapper, HsaAliAppletInfo> implements IHsaAliAppletInfoService {

    @Override
    public HsaAliAppletInfoVO getAliAppletInfoByOper(String operSubjectGuid) {
        log.info("getAliAppletInfoByOper:{}", operSubjectGuid);
        HsaAliAppletInfoVO appletInfoVO = new HsaAliAppletInfoVO();
        HsaAliAppletInfo hsaAliAppletInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAppletInfo>()
                .eq(HsaAliAppletInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaAliAppletInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        log.info("hsaAliAppletInfo:{}", JSON.toJSONString(hsaAliAppletInfo));
        if (Objects.nonNull(hsaAliAppletInfo)) {
            BeanUtils.copyProperties(hsaAliAppletInfo, appletInfoVO);
            appletInfoVO.setAppLogo(hsaAliAppletInfo.getAppLogo())
                    .setAes(hsaAliAppletInfo.getAes())
                    .setAppName(hsaAliAppletInfo.getAppName());
        }
        return appletInfoVO;
    }

    @Override
    public AliAuthorizerInfoVO getByAuthAppId(String appId) {
        log.info("getByAuthAppId:{}", appId);
        AliAuthorizerInfoVO appletInfoVO = new AliAuthorizerInfoVO();
        HsaAliAppletInfo hsaAliAppletInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaAliAppletInfo>()
                .eq(HsaAliAppletInfo::getAppId, appId)
                .eq(HsaAliAppletInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        log.info("hsaAliAppletInfo:{}", JSON.toJSONString(hsaAliAppletInfo));
        if (Objects.nonNull(hsaAliAppletInfo)) {
            appletInfoVO.setOperSubjectGuid(hsaAliAppletInfo.getOperSubjectGuid())
                    .setEnterpriseGuid(hsaAliAppletInfo.getEnterpriseGuid())
                    .setAppLogo(hsaAliAppletInfo.getAppLogo())
                    .setAppName(hsaAliAppletInfo.getAppName());
        }
        return appletInfoVO;
    }
}
