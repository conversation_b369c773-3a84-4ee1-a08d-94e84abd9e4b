package com.holderzone.member.mall.tool.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * 支付宝基础配置
 */
@Component
@Slf4j
@RefreshScope
@Data
public class AliPayMessagesUtil {

    /**
     * 微信accessToken缓存key
     */
    private static final String MINI_PROGRAM_ACCESS_TOKEN_KEY = "miniProgramAccessToken:%s";

    @Resource
    private StringRedisTemplate stringredisTemplate;


    @Value("${alipay.authLink}")
    private String authLink;

    @Value("${alipay.authToken}")
    private String authToken;

    @Value("${alipay.applyPublicKey}")
    private String applyPublicKey;

    @Value("${alipay.payPublicKey}")
    private String payPublicKey;

    @Value("${alipay.privateKey}")
    private String privateKey;

    @Value("${alipay.appId}")
    private String appId;

    //获取微信AccessToken地址
    @Value("${alipay.aliTokenUrl}")
    private String aliTokenUrl;

    @Value("${alipay.aliQrcode}")
    private String aliQrcode;

    @Value("${alipay.redirectUri}")
    private String redirectUri;
}
