package com.holderzone.member.mall.tool.service;


import com.holderzone.member.common.dto.wx.WxPreCodReqDTO;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * @version 1.0
 * @className WxStoreAuthorizerInfoService
 * @description 微信授权方账号信息Service
 */
public interface WxStoreAuthorizerInfoService{

    /**
     * 获取用户授权url
     *
     * @param wxPreCodReqDTO 品牌guid，企业guid
     * @return
     * @throws WxErrorException
     */
    String getPreAuthCode(WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException;
}
