package com.holderzone.member.mall.tool.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/27
 */
@Configuration
public class MemberBaseConfig implements WebMvcConfigurer {
    @Bean
    public LocaleResolver localeResolver() {
        return new MemberBaseLocalResolver();
    }
}
