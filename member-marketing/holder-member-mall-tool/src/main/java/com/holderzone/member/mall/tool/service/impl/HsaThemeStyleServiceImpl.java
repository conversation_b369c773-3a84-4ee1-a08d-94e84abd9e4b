package com.holderzone.member.mall.tool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.qo.mall.ThemeStyleQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.tool.entity.HsaThemeStyle;
import com.holderzone.member.mall.tool.mapper.HsaThemeStyleMapper;
import com.holderzone.member.mall.tool.service.HsaThemeStyleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Service
public class HsaThemeStyleServiceImpl extends HolderBaseServiceImpl<HsaThemeStyleMapper, HsaThemeStyle>
        implements HsaThemeStyleService {

    @Resource
    private HsaThemeStyleMapper hsaThemeStyleMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public boolean saveOrUpdateThemeStyle(ThemeStyleQO request) {
        HsaThemeStyle hsaThemeStyle = hsaThemeStyleMapper.selectOne(new LambdaQueryWrapper<HsaThemeStyle>()
                .eq(HsaThemeStyle::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaThemeStyle::getType, request.getType()));

        if (Objects.isNull(hsaThemeStyle)) {
            hsaThemeStyle = new HsaThemeStyle();
            hsaThemeStyle.setGuid(guidGeneratorUtil.getStringGuid(HsaThemeStyle.class.getSimpleName()));
            hsaThemeStyle.setIsDelete(BooleanEnum.FALSE.getCode());
            hsaThemeStyle.setGmtCreate(LocalDateTime.now());
            hsaThemeStyle.setOperSubjectGuid(request.getOperSubjectGuid());
        }
        hsaThemeStyle.setColorStyle(request.getColorStyle());
        hsaThemeStyle.setGmtModified(LocalDateTime.now());
        hsaThemeStyle.setType(request.getType());
        return this.saveOrUpdate(hsaThemeStyle);
    }

    @Override
    public String getThemeStyle(Integer type) {

        HsaThemeStyle hsaThemeStyle = hsaThemeStyleMapper.selectOne(new LambdaQueryWrapper<HsaThemeStyle>()
                .eq(HsaThemeStyle::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(Objects.nonNull(type), HsaThemeStyle::getType, type)
                .eq(Objects.isNull(type), HsaThemeStyle::getType, BooleanEnum.TRUE.getCode()));
        if (Objects.isNull(hsaThemeStyle)) {
            return null;
        }
        return hsaThemeStyle.getColorStyle();
    }
}
