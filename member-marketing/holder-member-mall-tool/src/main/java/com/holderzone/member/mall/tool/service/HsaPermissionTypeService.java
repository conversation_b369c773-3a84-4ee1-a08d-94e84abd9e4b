package com.holderzone.member.mall.tool.service;

import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;

import java.util.List;

public interface HsaPermissionTypeService {


    List<HsaOperSubjectPermissionVO> getStorePermission(OperSubjectPermissionQO request);

    boolean updatePermission(HsaOperSubjectPermissionQO request,int type);

    List<HsaOperSubjectPermissionVO> getOperSubjectPermission(OperSubjectPermissionQO request);

    MemberSystemPermissionVO getAccountPermission(String identification);

    List<PermissionModelDTO> toOperSubjectPermission(RoleAndPostIdDTO roleAndPostIdDTO, HeaderUserInfo headerUserInfo);
}
