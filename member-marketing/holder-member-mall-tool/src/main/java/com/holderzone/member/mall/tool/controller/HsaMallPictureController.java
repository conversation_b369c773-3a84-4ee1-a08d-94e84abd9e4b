package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.DeletePictureQO;
import com.holderzone.member.common.qo.mall.MallPictureQO;
import com.holderzone.member.mall.tool.service.HsaMallPictureService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022年12月29日 下午5:53
 * @description
 */
@RestController
@AllArgsConstructor
@RequestMapping("/picture")
public class HsaMallPictureController {

    private HsaMallPictureService hsaMallPictureService;

    @ApiOperation("保存图片")
    @PostMapping("/saveHsaMallPicture")
    public Result<Boolean> saveHsaMallPicture(@RequestBody MallPictureQO request) {
        return Result.success(hsaMallPictureService.saveHsaMallPicture(request));
    }

    @ApiOperation("查询图片")
    @GetMapping("/getMallPicture")
    public Result getMallPicture(String operSubjectGuid, int pictureType) {

        return Result.success(hsaMallPictureService.getMallPicture(operSubjectGuid, pictureType));
    }

    @ApiOperation("删除图片")
    @PostMapping("/deletePicture")
    public Result<Boolean> deletePicture(@RequestBody DeletePictureQO request) {
        return Result.success(hsaMallPictureService.deletePicture(request));
    }

}
