package com.holderzone.member.mall.tool.aop;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取请求的入参和出参
 */
@Component
@Aspect
@Slf4j
public class RequestAspect {

    @Pointcut("@within(org.springframework.stereotype.Controller) || @within(org.springframework.web.bind.annotation.RestController)")
    public void pointcut() {
        //default implementation ignored
    }

    @Around("pointcut()")
    public Object toolHandle(ProceedingJoinPoint toolJoinPoint) throws Throwable {
        HttpServletRequest toolRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String[] toolParameterNames = ((MethodSignature) toolJoinPoint.getSignature()).getParameterNames();
        if (toolParameterNames == null) {
            log.info("接口请求:{},入参无", toolRequest.getRequestURI());
            return toolJoinPoint.proceed();
        }
        Object[] args = toolJoinPoint.getArgs();
        int size = toolParameterNames.length;
        Map<String, Object> params = new HashMap<>(size);
        for (int i = 0; i < size; i++) {
            params.put(toolParameterNames[i], args[i]);
        }
        log.info("接口请求:{},入参 => {}", toolRequest.getRequestURI(), JSONUtil.toJsonStr(params));
        return toolJoinPoint.proceed();
    }
}