package com.holderzone.member.mall.tool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudRuleVO;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloud;
import com.holderzone.member.mall.tool.entity.cloud.HsaApplyOperSubjectCloudRule;
import com.holderzone.member.mall.tool.mapper.cloud.HsaApplyOperSubjectCloudMapper;
import com.holderzone.member.mall.tool.mapper.cloud.HsaApplyOperSubjectCloudRuleMapper;
import com.holderzone.member.mall.tool.service.cloud.IHsaApplyOperSubjectCloudRuleService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 系统主体关联开关 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Service
public class HsaApplyOperSubjectCloudRuleServiceImpl extends HolderBaseServiceImpl<HsaApplyOperSubjectCloudRuleMapper, HsaApplyOperSubjectCloudRule> implements IHsaApplyOperSubjectCloudRuleService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaApplyOperSubjectCloudMapper hsaApplyOperSubjectCloudMapper;

    @Override
    public OperSubjectCloudRuleVO query() {
        OperSubjectCloudRuleVO query = new OperSubjectCloudRuleVO();
        HsaApplyOperSubjectCloudRule hsaApplyOperSubjectCloudRule = getHsaApplyOperSubjectCloudRule();
        if (Objects.nonNull(hsaApplyOperSubjectCloudRule)) {
            BeanUtils.copyProperties(hsaApplyOperSubjectCloudRule, query);
        }
        return query;
    }

    private HsaApplyOperSubjectCloudRule getHsaApplyOperSubjectCloudRule() {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaApplyOperSubjectCloudRule>()
                .eq(HsaApplyOperSubjectCloudRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    @Override
    public Boolean save(Integer isSwitch) {
        HsaApplyOperSubjectCloudRule hsaApplyOperSubjectCloudRule = getHsaApplyOperSubjectCloudRule();
        if (Objects.isNull(hsaApplyOperSubjectCloudRule)) {
            hsaApplyOperSubjectCloudRule = new HsaApplyOperSubjectCloudRule();
            hsaApplyOperSubjectCloudRule.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid())
                    .setGuid(guidGeneratorUtil.getStringGuid(HsaApplyOperSubjectCloudRule.class.getSimpleName()))
                    .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                    .setIsSwitch(isSwitch);
            baseMapper.insert(hsaApplyOperSubjectCloudRule);
        } else {
            hsaApplyOperSubjectCloudRule.setIsSwitch(isSwitch);
            baseMapper.updateByGuid(hsaApplyOperSubjectCloudRule);
        }

        if (isSwitch == BooleanEnum.FALSE.getCode()) {
            hsaApplyOperSubjectCloudMapper.delete(new LambdaQueryWrapper<HsaApplyOperSubjectCloud>()
                    .eq(HsaApplyOperSubjectCloud::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        }
        return true;
    }
}
