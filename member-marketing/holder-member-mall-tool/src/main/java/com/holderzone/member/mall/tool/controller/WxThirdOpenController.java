package com.holderzone.member.mall.tool.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.wx.WxOpenAuthDTO;
import com.holderzone.member.common.dto.wx.WxPreCodReqDTO;
import com.holderzone.member.common.qo.wx.WxCommonReqDTO;
import com.holderzone.member.common.qo.wx.WxMessageReqDTO;
import com.holderzone.member.common.vo.wx.WechatAuthorizerInfoVO;
import com.holderzone.member.mall.tool.service.HsaWechatAuthorizerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @version 1.0
 * @className WxComponentController
 * @description 微信开放平台相关Controller
 */
@RestController
@RequestMapping("/wx_open")
@Api(value = "微信开放平台相关Controller")
@Slf4j
public class WxThirdOpenController {

    @Autowired
    private HsaWechatAuthorizerInfoService wxThirdOpenService;

    @PostMapping("/receive_ticket")
    @ApiOperation(value = "接收微信方推送的component_verify_ticket，验证后直接返回success字符串")
    public String receiveVerifyTicket(WxCommonReqDTO wxCommonReqDTO, @RequestBody String requestBody) {
        log.info("第三方推送component_verify_ticket协议");
        log.info("requestBody:{}", requestBody);
        wxCommonReqDTO.setXml(requestBody);
        log.info("wxCommonReqDTO:{}", wxCommonReqDTO);
        return wxThirdOpenService.receiveTicket(wxCommonReqDTO);
    }

    @PostMapping("/get_pre_code")
    public Result<String> getPreCode(@RequestBody WxPreCodReqDTO wxPreCodReqDTO) throws WxErrorException {
        log.info("微信服务已收到预授权请求参数：{}", JacksonUtils.writeValueAsString(wxPreCodReqDTO));
        return Result.success(wxThirdOpenService.getPreAuthCode(wxPreCodReqDTO));
    }

    @PostMapping("/callback_auth")
    public Result<String> callbackAuth(@RequestBody WxOpenAuthDTO wxOpenAuthDTO) {
        log.info("已收到微信公众号授权回调：授权码：{}，主体guid：{}，授权码过期时间： {}"
                , wxOpenAuthDTO.getAuth_code(), wxOpenAuthDTO.getOperSubjectGuid(), wxOpenAuthDTO.getExpires_in());
            return Result.success(wxThirdOpenService.callbackAuth(wxOpenAuthDTO));
    }

    /**
     * 消息处理
     */
    @PostMapping("/wx_handler/{APPID}/call_back")
    public String handleMessage(@PathVariable("APPID") String appId, @RequestBody(required = false) String message, WxCommonReqDTO wxCommonReqDTO) {
        log.info("接收到微信端推送的消息，appid:{}", appId);
        log.info("接收到微信端推送的消息，wxCommonReqDTO:{}", JacksonUtils.writeValueAsString(wxCommonReqDTO));
        log.info("接收到微信端推送的消息，wxMpXmlMessage:\n{}", message);
        WxMessageReqDTO wxMessageReqDTO = new WxMessageReqDTO();
        wxMessageReqDTO.setAppId(appId);
        wxMessageReqDTO.setBody(message);
        wxMessageReqDTO.setWxCommonReqDTO(wxCommonReqDTO);
        return wxThirdOpenService.getWxMpXmlMessage(wxMessageReqDTO);
    }

    /**
     * 获取授权公众号信息
     */
    @GetMapping("/query_auth")
    Result<WechatAuthorizerInfoVO> queryAuth() {
        return Result.success(wxThirdOpenService.queryAuth());
    }


    /**
     * 获取当前运营主体授权第三方平台信息
     */
    @GetMapping("/get_authorizer_access_token")
    public WechatAuthorizerInfoVO getAuthorizerAccessToken(String operSubjectGuid) {
        return wxThirdOpenService.getAuthorizerAccessTokenByOperSubjectGuid(operSubjectGuid);
    }
}
