package com.holderzone.member.mall.tool.service.help;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.enums.malltool.MsgTypeEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.qo.tool.MessagesDataQO;
import com.holderzone.member.common.qo.tool.ShortMessagesConfigDTO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class MessagesConfigBaseHelp {

    private static final String SKIP_ORDER = "/package/orderList/orderList?orderState=all";

    private static final String SKIP_BALANCE_DETAIL = "/package/balanceDetail/balanceDetail?";

    private static final String STORE_RECEIVING_ORDERS = "商家接单通知";

    //暂无
    private static final String STORE_RECEIVING_ORDERS_DETAIL = "暂无";

    //掌控者
    private static final String SMS_SIGN = "掌控者";

    private MessagesConfigBaseHelp() {
        throw new IllegalStateException("Utility class");
    }

    public static List<MessagesConfigQO> initMessagesConfig(String guid) {
        List<MessagesConfigQO> messagesConfigQOList = new ArrayList<>();

        MessagesConfigQO messagesConfigQO = new MessagesConfigQO();


        messagesConfigQO.setAppletMsgKid("1,4,6,3")
                .setDataSort("商家名称:thing1,订单状态:phrase4,商品名称:thing6,温馨提示:thing3")
                .setOperSubjectGuid(guid)
                .setIsDelete(0)
                .setMsgType(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("商家接单或拒单通知")
                .setTitle(STORE_RECEIVING_ORDERS)
                .setScenarioDescription(STORE_RECEIVING_ORDERS)
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("39777")
                .setAppletMsgContent("商家名称:超市1号\n订单状态:商家已接单\n商品名称:辣炒年糕\n温馨提示:点击查看订单详情>>\n")
                .setPushRule("商家在一体机点击接单或拒单（拒单填写信息后)");
        messagesConfigQOList.add(messagesConfigQO);


        MessagesConfigQO messagesConfigQO2 = new MessagesConfigQO();

        messagesConfigQO2.setAppletMsgKid("3,1,4")
                .setDataSort("核销门店:thing3,商品名称:thing1,核销时间:time4")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("订单核销提醒")
                .setScenarioDescription("核销成功通知")
                .setTitle("核销成功通知")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("13423")
                .setAppletMsgContent("核销门店:**门店\n商品名称:烧腊\n核销时间:20**年*月*日\n")
                .setPushRule("核销成功后或超过用餐时间自动核销");
        messagesConfigQOList.add(messagesConfigQO2);


        MessagesConfigQO messagesConfigQO3 = new MessagesConfigQO();
        messagesConfigQO3.setAppletMsgKid("8,6,5,7")
                .setDataSort("餐厅名称:thing8,消费金额:amount6,消费时间:date5,点评提醒:thing7")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("用餐点评提醒")
                .setScenarioDescription("点评提醒")
                .setTitle("用餐评价提醒")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("254")
                .setAppletMsgContent("餐厅名称:某某餐厅\n消费金额:￥200\n消费时间:2020-01-07 13:10\n点评提醒:请您对餐厅服务进行评价!\n")
                .setPushRule("食堂/门店订单完成，待评价");
        messagesConfigQOList.add(messagesConfigQO3);


        MessagesConfigQO messagesConfigQO4 = new MessagesConfigQO();
        messagesConfigQO4.setAppletMsgKid("34,29,40,11,13")
                .setDataSort("取餐凭证:thing34,取餐时段:time29,取餐位置:thing40,餐品详情:thing11,订单金额:amount13")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("取餐柜取餐提醒")
                .setScenarioDescription("取餐柜取餐提醒")
                .setTitle("取餐提醒")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("250")
                .setAppletMsgContent("取餐凭证:流金岁月\n取餐时段:12:00\n取餐位置:取茶柜\n餐品详情:酸菜卤肉饭\n订单金额:100.00元\n")
                .setMpTemplateNo("44538")
                .setMpTitle("取餐通知")
                .setMpMsgContent("取餐号:0001\n餐厅名称:微信饭店\n商品名称:番茄牛肉\n取餐地址:集团总部食堂\n")
                .setPushRule("食堂预定/外卖将商品放入取餐柜之后");
        messagesConfigQOList.add(messagesConfigQO4);


        MessagesConfigQO messagesConfigQO5 = new MessagesConfigQO();
        messagesConfigQO5.setAppletMsgKid("4,2,5,11")
                .setDataSort("取餐号码:character_string4,门店名称:thing2,餐品详情:thing5,温馨提示:thing11")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("取餐提醒")
                .setScenarioDescription("取餐提醒")
                .setTitle("取餐提醒")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("1820")
                .setAppletMsgContent("取餐号码:ASDE123\n门店名称:饭来了财富中心店\n餐品详情:酸菜卤肉饭*2\n温馨提示:您的餐品已放入餐柜，请速来取餐。\n")
                .setMpTemplateNo("44538")
                .setMpTitle("取餐通知")
                .setMpMsgContent("取餐号:0001\n餐厅名称:微信饭店\n商品名称:番茄牛肉\n")
                .setPushRule("一体机点击叫号");
        messagesConfigQOList.add(messagesConfigQO5);


        MessagesConfigQO messagesConfigQO7 = new MessagesConfigQO();
        messagesConfigQO7.setAppletMsgKid("1,6,4,5")
                .setDataSort("出餐门店:thing1,商品名:thing6,送出时间:time4,备注:thing5")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("外卖出餐提醒")
                .setScenarioDescription("配送")
                .setTitle("订单配送通知")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("584")
                .setAppletMsgContent("出餐门店:K11店\n商品名:珍珠奶茶\n送出时间:2019-10-10 11:11\n备注:您的外卖正在配送中，请注意签收\n")
                .setPushRule("一体机点击出餐");
        messagesConfigQOList.add(messagesConfigQO7);


        MessagesConfigQO messagesConfigQO8 = new MessagesConfigQO();
        messagesConfigQO8.setAppletMsgKid("12,1,2,3")
                .setDataSort("门店名称:thing12,商品名:thing1,送达时间:time2,温馨提示:thing3")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_TRADE.getCode())
                .setMsgTitle("外卖送达通知")
                .setTitle("商品送达通知")
                .setScenarioDescription("送达提醒")
                .setPagePath(SKIP_ORDER)
                .setAppletTemplateNo("542")
                .setAppletMsgContent("门店名称:某某门店\n商品名:珍珠奶茶\n送达时间:2019.09.10 10:00\n温馨提醒:您的商品已送达，如有疑问请及时联系商家\n")
                .setPushRule("一体机点击确认送达");
        messagesConfigQOList.add(messagesConfigQO8);


        MessagesConfigQO messagesConfigQO9 = new MessagesConfigQO();
        messagesConfigQO9.setAppletMsgKid("5,7,6,8")
                .setDataSort("变动时间:time5,变动原因:thing7,变动金额:amount6,当前余额:amount8")
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_BALANCE.getCode())
                .setMsgTitle("资金变动提醒")
                .setScenarioDescription("资金变动提醒")
                .setTitle("账户余额提醒")
                .setMpTitle("储值成功提醒")
                .setPagePath(SKIP_BALANCE_DETAIL)
                .setAppletTemplateNo("919")
                .setMpTemplateNo("OPENTM415437052")
                .setAppletMsgContent("变动时间:2021年6月16日 10:10\n变动原因:购买套餐\n变动金额:1\n当前余额:10\n")
                .setMpMsgContent("储值门店:新城店\n储值金额:1200.00元\n储值时间:2023年11月11日 11:11:11\n储值余额:3000.00元\n")
                .setMessageContent("【#Brand#】#Name#您好，您的余额 #增加/扣减 Money#元，卡内余额#Money#元，期待您的光临")
                .setPushRule("会员卡余额用户消费/充值/退款/过期的变动");

        List<ShortMessagesConfigDTO> shortMessages = getShortMessagesConfigDTOS();

        messagesConfigQO9.setShortMessageContentJson(JSON.toJSONString(shortMessages));

        messagesConfigQOList.add(messagesConfigQO9);


        MessagesConfigQO messagesConfigQO10 = new MessagesConfigQO();
        messagesConfigQO10.setAppletMsgKid(STORE_RECEIVING_ORDERS_DETAIL)
                .setDataSort(STORE_RECEIVING_ORDERS_DETAIL)
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_MEMBER.getCode())
                .setMsgTitle("开卡成功通知")
                .setScenarioDescription("开卡成功通知")
                .setTitle(MsgTypeEnum.MSG_MEMBER.getDes())
                .setMpTitle(MsgTypeEnum.MSG_MEMBER.getDes())
                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletTemplateNo("101")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】恭喜您成功开通会员卡！当前余额：#Surplus#元，谢谢您的支持，祝您生活愉快！")
                .setPushRule("开通会员卡成功");

        List<ShortMessagesConfigDTO> shortOpenCardMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesOpenCardDTO = new ShortMessagesConfigDTO();
        shortMessagesOpenCardDTO.setChangeType(AmountSourceTypeEnum.OPEN_CARD.getCode());
        shortMessagesOpenCardDTO.setSmsSign(SMS_SIGN);
        shortMessagesOpenCardDTO.setSmsTemplateCode("SMS_479065635");
        shortOpenCardMessages.add(shortMessagesOpenCardDTO);
        messagesConfigQO10.setShortMessageContentJson(JSON.toJSONString(shortOpenCardMessages));
        messagesConfigQOList.add(messagesConfigQO10);

        MessagesConfigQO messagesConfigQO11 = new MessagesConfigQO();
        messagesConfigQO11.setAppletMsgKid(STORE_RECEIVING_ORDERS_DETAIL)
                .setDataSort(STORE_RECEIVING_ORDERS_DETAIL)
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_ACTIVITY.getCode())
                .setMsgTitle("优惠券到账通知")
                .setScenarioDescription("优惠券到账通知")
                .setTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setMpTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletTemplateNo("102")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】恭喜您获得优惠券#Coupon#，请及时于#Time#前使用，期待您的光临！")
                .setPushRule("会员账户收到优惠券通知");

        List<ShortMessagesConfigDTO> shortToCouponMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesCouponDTO = new ShortMessagesConfigDTO();
        shortMessagesCouponDTO.setSmsSign(SMS_SIGN);
        shortMessagesCouponDTO.setSmsTemplateCode("SMS_478950586");
        shortToCouponMessages.add(shortMessagesCouponDTO);
        messagesConfigQO11.setShortMessageContentJson(JSON.toJSONString(shortToCouponMessages));

        messagesConfigQOList.add(messagesConfigQO11);

        MessagesConfigQO messagesConfigQO12 = new MessagesConfigQO();
        messagesConfigQO12.setAppletMsgKid(STORE_RECEIVING_ORDERS_DETAIL)
                .setDataSort(STORE_RECEIVING_ORDERS_DETAIL)
                .setOperSubjectGuid(guid)
                .setMsgType(0)
                .setIsDelete(0)
                .setMsgCategory(MsgTypeEnum.MSG_ACTIVITY.getCode())
                .setMsgTitle("优惠券过期提醒")
                .setScenarioDescription("优惠券过期提醒")
                .setTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setMpTitle(MsgTypeEnum.MSG_ACTIVITY.getDes())
                .setPagePath(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletTemplateNo("103")
                .setMpTemplateNo(STORE_RECEIVING_ORDERS_DETAIL)
                .setAppletMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMpMsgContent(STORE_RECEIVING_ORDERS_DETAIL)
                .setMessageContent("【#Brand#】您的卡券包里有XX张优惠券就要到期了，请及时使用！期待您的光临！")
                .setPushRule("优惠券临近3天即将失效提醒");

        List<ShortMessagesConfigDTO> shortExpiredCouponMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesExpiredCouponDTO = new ShortMessagesConfigDTO();
        shortMessagesExpiredCouponDTO.setSmsSign(SMS_SIGN);
        shortMessagesExpiredCouponDTO.setSmsTemplateCode("SMS_479525288");
        shortExpiredCouponMessages.add(shortMessagesExpiredCouponDTO);
        messagesConfigQO12.setShortMessageContentJson(JSON.toJSONString(shortExpiredCouponMessages));

        messagesConfigQOList.add(messagesConfigQO12);
        return messagesConfigQOList;
    }

    private static List<ShortMessagesConfigDTO> getShortMessagesConfigDTOS() {
        List<ShortMessagesConfigDTO> shortMessages = new ArrayList<>();

        ShortMessagesConfigDTO shortMessagesRechargeDTO = new ShortMessagesConfigDTO();
        shortMessagesRechargeDTO.setChangeType(AmountSourceTypeEnum.RECHARGE.getCode());
        shortMessagesRechargeDTO.setSmsSign(SMS_SIGN);
        shortMessagesRechargeDTO.setSmsTemplateCode("SMS_478995597");
        shortMessages.add(shortMessagesRechargeDTO);

        ShortMessagesConfigDTO shortMessagesAdminEditDTO = new ShortMessagesConfigDTO();
        shortMessagesAdminEditDTO.setChangeType(AmountSourceTypeEnum.ADMIN_EDIT.getCode());
        shortMessagesAdminEditDTO.setSmsSign(SMS_SIGN);
        shortMessagesAdminEditDTO.setSmsTemplateCode("SMS_481675058");
        shortMessages.add(shortMessagesAdminEditDTO);

        ShortMessagesConfigDTO shortMessagesRetreatDTO = new ShortMessagesConfigDTO();
        shortMessagesRetreatDTO.setChangeType(AmountSourceTypeEnum.RETREAT_CARD.getCode());
        shortMessagesRetreatDTO.setSmsSign(SMS_SIGN);
        shortMessagesRetreatDTO.setSmsTemplateCode("SMS_479080624");
        shortMessages.add(shortMessagesRetreatDTO);

        ShortMessagesConfigDTO shortMessagesConsumptionDTO = new ShortMessagesConfigDTO();
        shortMessagesConsumptionDTO.setChangeType(AmountSourceTypeEnum.CONSUMPTION.getCode());
        shortMessagesConsumptionDTO.setSmsSign(SMS_SIGN);
        shortMessagesConsumptionDTO.setSmsTemplateCode("SMS_479050617");
        shortMessages.add(shortMessagesConsumptionDTO);

        ShortMessagesConfigDTO shortMessagesSubsidyGrantDTO = new ShortMessagesConfigDTO();
        shortMessagesSubsidyGrantDTO.setChangeType(AmountSourceTypeEnum.SUBSIDY_GRANT.getCode());
        shortMessagesSubsidyGrantDTO.setSmsSign(SMS_SIGN);
        shortMessagesSubsidyGrantDTO.setSmsTemplateCode("SMS_479155622");
        shortMessages.add(shortMessagesSubsidyGrantDTO);

        ShortMessagesConfigDTO shortMessagesSubsidyExpiredDTO = new ShortMessagesConfigDTO();
        shortMessagesSubsidyExpiredDTO.setChangeType(AmountSourceTypeEnum.SUBSIDY_EXPIRED.getCode());
        shortMessagesSubsidyExpiredDTO.setSmsSign(SMS_SIGN);
        shortMessagesSubsidyExpiredDTO.setSmsTemplateCode("SMS_478965589");
        shortMessages.add(shortMessagesSubsidyExpiredDTO);
        return shortMessages;
    }
}
