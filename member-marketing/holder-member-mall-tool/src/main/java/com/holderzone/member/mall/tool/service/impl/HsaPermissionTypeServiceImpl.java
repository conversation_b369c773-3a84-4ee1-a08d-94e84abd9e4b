package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.ReturnModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.dto.user.UserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.permission.SubjectPermissionTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.util.ServletUtils;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.tool.entity.HsaPermission;
import com.holderzone.member.mall.tool.entity.HsaPermissionType;
import com.holderzone.member.common.feign.HolderFeign;
import com.holderzone.member.mall.tool.mapper.HsaPermissionMapper;
import com.holderzone.member.mall.tool.mapper.HsaPermissionTypeMapper;
import com.holderzone.member.mall.tool.service.HsaPermissionService;
import com.holderzone.member.mall.tool.service.HsaPermissionTypeService;
import com.holderzone.member.mall.tool.utils.HttpsClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HsaPermissionTypeServiceImpl extends ServiceImpl<HsaPermissionTypeMapper, HsaPermissionType>
        implements HsaPermissionTypeService {

    // TODO holder改造（已改）
    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private HsaPermissionMapper hsaPermissionMapper;

    @Resource
    private HsaPermissionTypeMapper hsaPermissionTypeMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaPermissionService hsaPermissionService;

    /**
     * 运营主体权限
     */
    private static final int SUBJECT_PERMISSION = 0;

    private static final int STORE_PERMISSION = 1;

    private static final String TEAM_URL = "%s/team/permission/system/getUserPermission";

    private final static String USER_URL = "%s/team/operationSubjectManagement/queryUserInformation";

    @Override
    public List<HsaOperSubjectPermissionVO> getStorePermission(OperSubjectPermissionQO request) {

        //查询所有门店
        PageModel<TeamStoreInfoModel> pageModel = externalSupport.baseServer(ThreadLocalCache.getSystem()).findStorePageInfo(new TeamStoreInfoPageParamModel(ObjectUtil.objToLong(request.getTeamId()),
                "", 0, 9999999));
        List<TeamStoreInfoModel> storeInfoList = pageModel.getContent();
        List<HsaOperSubjectPermissionVO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(storeInfoList)) {
            log.error("holder.findStorePageInfo is null");
            return list;
        }
        //选中门店id 集合
        List<String> checkedStoreList = hsaPermissionMapper
                .selectList(new LambdaQueryWrapper<HsaPermission>()
                        .eq(HsaPermission::getEnterpriseGuid, request.getTeamId())
                        .eq(HsaPermission::getType, STORE_PERMISSION)
                        .eq(HsaPermission::getIsRole, request.getIsRole())
                        .eq(HsaPermission::getPositionGuid, request.getRoleId())
                ).stream()
                .map(HsaPermission::getTypeId)
                .collect(Collectors.toList());
        for (TeamStoreInfoModel storeInfo : storeInfoList) {
            HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
            hsaOperSubjectPermissionVO.setId(ObjectUtil.objToString(storeInfo.getId()));
            hsaOperSubjectPermissionVO.setName(storeInfo.getName());
            hsaOperSubjectPermissionVO.setIs_checked(checkedStoreList.contains(ObjectUtil.objToString(storeInfo.getId())) ? 1 : 0);
            list.add(hsaOperSubjectPermissionVO);
        }
        return list;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updatePermission(HsaOperSubjectPermissionQO request, int type) {

        HsaPermissionType hsaPermissionType = hsaPermissionTypeMapper
                .selectOne(new LambdaQueryWrapper<HsaPermissionType>()
                        .eq(HsaPermissionType::getEnterpriseGuid, request.getTeamId())
                        .eq(HsaPermissionType::getIsRole, request.getIsRole())
                        .eq(HsaPermissionType::getType, type)
                        .eq(HsaPermissionType::getPositionGuid, request.getRoleId()));
        if (Objects.isNull(hsaPermissionType)) {
            hsaPermissionType = new HsaPermissionType();
            hsaPermissionType.setGuid(guidGeneratorUtil.getStringGuid(HsaPermissionType.class.getCanonicalName()));
            hsaPermissionType.setEnterpriseGuid(ObjectUtil.objToString(request.getTeamId()));
            hsaPermissionType.setIsRole(request.getIsRole());
            hsaPermissionType.setType(type);
            hsaPermissionType.setPositionGuid(ObjectUtil.objToString(request.getRoleId()));
            hsaPermissionType.setGmtCreate(LocalDateTime.now());
        }
        hsaPermissionType.setIsAll(request.getIsAll());
        hsaPermissionType.setGmtModified(LocalDateTime.now());
        //保存门店权限类型
        this.saveOrUpdate(hsaPermissionType);
        //保存权限
        saveCheckedStorePermission(request, type);
        return true;
    }

    private void saveCheckedStorePermission(HsaOperSubjectPermissionQO request, int type) {

        //删除之前所有选中的权限
        hsaPermissionMapper.delete(new LambdaQueryWrapper<HsaPermission>()
                .eq(HsaPermission::getEnterpriseGuid, request.getTeamId())
                .eq(HsaPermission::getIsRole, request.getIsRole())
                .eq(HsaPermission::getType, type)
                .eq(HsaPermission::getPositionGuid, request.getRoleId()));
        if (request.getIsAll() == SubjectPermissionTypeEnum.ALL_PERMISSION.getCode()) {
            return;
        }
        List<HsaPermission> list = Lists.newArrayList();
        for (HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO : request.getHolderPermission()) {
            if (ObjectUtil.objToInt(hsaOperSubjectPermissionVO.getIs_checked()) == 0) {
                continue;
            }
            HsaPermission hsaPermission = new HsaPermission();
            hsaPermission.setGuid(guidGeneratorUtil.getStringGuid(HsaPermission.class
                    .getCanonicalName()));
            hsaPermission.setEnterpriseGuid(ObjectUtil.objToString(request.getTeamId()));
            hsaPermission.setPositionGuid(ObjectUtil.objToString(request.getRoleId()));
            hsaPermission.setIsRole(request.getIsRole());
            hsaPermission.setType(type);
            hsaPermission.setTypeId(hsaOperSubjectPermissionVO.getId());
            hsaPermission.setGmtCreate(LocalDateTime.now());
            hsaPermission.setGmtModified(LocalDateTime.now());
            list.add(hsaPermission);
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        hsaPermissionService.saveBatch(list);

    }

    @Override
    public List<HsaOperSubjectPermissionVO> getOperSubjectPermission(OperSubjectPermissionQO request) {

        List<OperSubjectInfo> operSubjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubjectByTeam(request.getTeamId());
        List<HsaOperSubjectPermissionVO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(operSubjectInfoList)) {
            return list;
        }
        //选中运营主体 集合
        List<String> checkedList = hsaPermissionMapper
                .selectList(new LambdaQueryWrapper<HsaPermission>()
                        .eq(HsaPermission::getEnterpriseGuid, request.getTeamId())
                        .eq(HsaPermission::getType, SUBJECT_PERMISSION)
                        .eq(HsaPermission::getIsRole, request.getIsRole())
                        .eq(HsaPermission::getPositionGuid, request.getRoleId()))
                .stream()
                .map(HsaPermission::getTypeId)
                .collect(Collectors.toList());
        for (OperSubjectInfo operSubjectInfo : operSubjectInfoList) {
            HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
            hsaOperSubjectPermissionVO.setId(operSubjectInfo.getOperSubjectGuid());
            hsaOperSubjectPermissionVO.setName(operSubjectInfo.getMultiMemberName());
            hsaOperSubjectPermissionVO.setIs_checked(checkedList.contains(operSubjectInfo.getOperSubjectGuid()) ? 1 : 0);
            list.add(hsaOperSubjectPermissionVO);
        }
        return list;
    }

    @Override
    public MemberSystemPermissionVO getAccountPermission(String identification) {
        MemberSystemPermissionVO memberSystemPermissionVO = new MemberSystemPermissionVO();
        if (ThreadLocalCache.getSystem() == SystemEnum.RETAIL.getCode()) {
            memberSystemPermissionVO.setIsAll(BooleanEnum.TRUE.getCode());
            return memberSystemPermissionVO;
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        // todo 操作权限
        List<MemberSystemPermissionDTO> systemPermission = getSystemPermission(identification, headerUserInfo.getUserGuid());
        memberSystemPermissionVO.setMemberSystemPermissionDTOs(systemPermission);

        //查询当前用户岗位角色id
        RoleAndPostIdDTO roleAndPostIdDTO = externalSupport.baseServer(ThreadLocalCache.getSystem()).findUserRoleAndPost(headerUserInfo.getEnterpriseGuid(), headerUserInfo.getTel());
        if (Objects.isNull(roleAndPostIdDTO)) {
            return memberSystemPermissionVO;
        }
        //配置当前用户运营主体权限
        memberSystemPermissionVO.setPermissionModelDTOS(toOperSubjectPermission(roleAndPostIdDTO, headerUserInfo));

        return memberSystemPermissionVO;
    }

    @Override
    public List<PermissionModelDTO> toOperSubjectPermission(RoleAndPostIdDTO roleAndPostIdDTO, HeaderUserInfo headerUserInfo) {

        // todo 运营主体权限
        //查询运营主体
        List<OperSubjectInfo> operSubjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubjectByTeam(headerUserInfo.getEnterpriseGuid());
        //有权限的运营主体
        List<OperSubjectInfo> permissionOperSubjects = Lists.newArrayList();
        //是否存在全部运营主体
        boolean allOperSubject = false;
        if (!CollectionUtil.isEmpty(roleAndPostIdDTO.getPostIds())) {
            List<HsaPermissionType> operSubjectPermissionType = hsaPermissionTypeMapper
                    .selectList(new LambdaQueryWrapper<HsaPermissionType>()
                            .eq(HsaPermissionType::getEnterpriseGuid, headerUserInfo.getEnterpriseGuid())
                            .eq(HsaPermissionType::getType, SUBJECT_PERMISSION)
                            .eq(HsaPermissionType::getIsRole, 0)
                            .in(HsaPermissionType::getPositionGuid, roleAndPostIdDTO.getPostIds()));
            allOperSubject = operSubjectPermissionType
                    .stream()
                    .anyMatch(hsaPermissionType ->
                            hsaPermissionType.getIsAll() == SubjectPermissionTypeEnum.ALL_PERMISSION.getCode());
            if (!allOperSubject) {
                List<String> operSubjectPermissionGuid = hsaPermissionMapper
                        .selectList(new LambdaQueryWrapper<HsaPermission>()
                                .eq(HsaPermission::getEnterpriseGuid, headerUserInfo.getEnterpriseGuid())
                                .eq(HsaPermission::getType, SUBJECT_PERMISSION)
                                .eq(HsaPermission::getIsRole, 0)
                                .in(HsaPermission::getPositionGuid, roleAndPostIdDTO.getPostIds()))
                        .stream()
                        .map(HsaPermission::getTypeId).collect(Collectors.toList());
                operSubjectInfoList = operSubjectInfoList.stream().filter(item -> operSubjectPermissionGuid.
                        contains(item.getOperSubjectGuid())).collect(Collectors.toList());
            }
            permissionOperSubjects.addAll(operSubjectInfoList);
        }

        if (!CollectionUtil.isEmpty(roleAndPostIdDTO.getEntRoleIds())) {
            //全部运营主体 就不用再次查询了
            if (!allOperSubject) {
                List<HsaPermissionType> operSubjectPermissionType = hsaPermissionTypeMapper
                        .selectList(new LambdaQueryWrapper<HsaPermissionType>()
                                .eq(HsaPermissionType::getEnterpriseGuid, headerUserInfo.getEnterpriseGuid())
                                .eq(HsaPermissionType::getType, SUBJECT_PERMISSION)
                                .eq(HsaPermissionType::getIsRole, 1)
                                .in(HsaPermissionType::getPositionGuid, roleAndPostIdDTO.getEntRoleIds()));
                allOperSubject = operSubjectPermissionType
                        .stream()
                        .anyMatch(hsaPermissionType ->
                                hsaPermissionType.getIsAll() == SubjectPermissionTypeEnum.ALL_PERMISSION.getCode());
                if (!allOperSubject) {
                    List<String> operSubjectPermissionGuid = hsaPermissionMapper
                            .selectList(new LambdaQueryWrapper<HsaPermission>()
                                    .eq(HsaPermission::getEnterpriseGuid, headerUserInfo.getEnterpriseGuid())
                                    .eq(HsaPermission::getType, SUBJECT_PERMISSION)
                                    .eq(HsaPermission::getIsRole, 1)
                                    .in(HsaPermission::getPositionGuid, roleAndPostIdDTO.getPostIds()))
                            .stream()
                            .map(HsaPermission::getTypeId).collect(Collectors.toList());
                    operSubjectInfoList = operSubjectInfoList.stream().filter(item -> operSubjectPermissionGuid.
                            contains(item.getOperSubjectGuid())).collect(Collectors.toList());
                }
                permissionOperSubjects.addAll(operSubjectInfoList);

            }
        }
        permissionOperSubjects = ObjectUtil.listDistinct(permissionOperSubjects);
        return toPermissionModelDTO(permissionOperSubjects);
    }

    private List<PermissionModelDTO> toPermissionModelDTO(List<OperSubjectInfo> operSubjectInfoList) {

        List<PermissionModelDTO> list = new ArrayList<>();

        for (OperSubjectInfo operSubjectInfo : operSubjectInfoList) {
            PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
            permissionModelDTO.setId(operSubjectInfo.getOperSubjectGuid());
            permissionModelDTO.setPermissionName(operSubjectInfo.getMultiMemberName());
            list.add(permissionModelDTO);
        }
        return list;
    }


    /**
     * 获取应用系统操作权限
     *
     * @param identification 标识符
     * @param userGuid       用户guid
     * @return 当前用户拥有的应用权限
     * @see com.holderzone.member.common.enums.SystemPermissionEnum
     */
    private List<MemberSystemPermissionDTO> getSystemPermission(String identification, String userGuid) {
        List<MemberSystemPermissionDTO> systemPermissionList = new ArrayList<>();
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        assert httpServletRequest != null;
        String token = httpServletRequest.getHeader(FilterConstant.TOKEN);
        log.info("getSystemPermission userGuid value is :{}", userGuid);
        if (StringUtils.isEmpty(userGuid)) {
            /**
             *  UserInfo userInfo = queryUserInformation(token, goalgoRequestHost);
             *  bug-demo
             **/
            HeaderUserInfo headerUserInfo = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid());
            userGuid = headerUserInfo.getUserGuid();
        }
        try {
            //通过holder 查询对应模块操作权限
            OperationPermissionQO operationPermissionRequest = new OperationPermissionQO();
            operationPermissionRequest.setIdentifications(Collections.singletonList(identification));
            operationPermissionRequest.setUserId(userGuid);
            operationPermissionRequest.setTeamId(ThreadLocalCache.getEnterpriseGuid());
            log.info("请求权限参数为:{}", JSON.toJSONString(operationPermissionRequest));
            //会员操作权限
            systemPermissionList = externalSupport.baseServer(ThreadLocalCache.getSystem()).listSystemPermission(operationPermissionRequest, token);
        } catch (Exception e) {
            log.error("Get system permission error :{}", e.getMessage());
        }
        return systemPermissionList;
    }

    /**
     * 获取账号信息
     *
     * @return 请求结果
     */
    public UserInfo queryUserInformation(String token, String host) {
        UserInfo userInfo = new UserInfo();
        String httpRequestUrl = String.format(USER_URL, host).intern();
        // 创建httpClient
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建post请求方式实例
        HttpPost httpPost = new HttpPost(httpRequestUrl);

        // 设置请求头 发送的是json数据格式
        httpPost.setHeader(StringConstant.STR_CONTENT_TYPE, StringConstant.STR_APPLICATION_JSON_UTF_8);
        httpPost.setHeader(StringConstant.CONNECTION, StringConstant.CLOSE);
        httpPost.setHeader(StringConstant.LOGIN_TOKEN, token);

        // 执行http的post请求
        CloseableHttpResponse httpResponse;
        String result = null;
        try {
            Optional.ofNullable(ThreadLocalCache.getEnterpriseGuid()).ifPresent(e -> {
                //k-v参数
                List<NameValuePair> params = Collections.singletonList(new BasicNameValuePair("teamId", e));
                EntityBuilder builder = EntityBuilder.create();
                builder.setParameters(params);
                httpPost.setEntity(builder.build());
            });
            httpResponse = httpClient.execute(httpPost);
            result = EntityUtils.toString(httpResponse.getEntity(), StringConstant.UTF_8);
            if (!StringUtils.isEmpty(result) && !result.contains(StringConstant.RETURN_CODE)) {
                userInfo = JSON.parseObject(result, UserInfo.class);
            } else {
                final ReturnModel returnModel = JSON.parseObject(result, ReturnModel.class);
                if (returnModel.getReturnCode() == 0 && returnModel.getData() != null) {
                    userInfo = JSON.parseObject(returnModel.getData(), UserInfo.class);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return userInfo;
    }
}
