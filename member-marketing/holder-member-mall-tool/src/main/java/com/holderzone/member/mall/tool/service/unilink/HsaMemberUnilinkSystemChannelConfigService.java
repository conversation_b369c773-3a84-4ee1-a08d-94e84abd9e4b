package com.holderzone.member.mall.tool.service.unilink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemChannelConfig;

import java.util.List;

/**
 * 会员通-系统渠道配置Service
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface HsaMemberUnilinkSystemChannelConfigService extends IService<HsaMemberUnilinkSystemChannelConfig> {

    /**
     * 查询系统渠道列表
     *
     * @param isIncludeAdmin 是否包含后台，默认不包含
     * @return 系统渠道列表
     */
    List<MemberUnilinkSelectOptionVO> listSystemChannel(Boolean isIncludeAdmin);
} 