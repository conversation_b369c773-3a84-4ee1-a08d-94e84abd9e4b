package com.holderzone.member.mall.tool.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.malltool.BasicPageRespDTO;
import com.holderzone.member.common.dto.malltool.BasicPageTypeRespDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.malltool.PageTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.mall.CheckKeyVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import com.holderzone.member.mall.tool.entity.HsaBasicPage;
import com.holderzone.member.mall.tool.entity.HsaMallPage;
import com.holderzone.member.mall.tool.service.IHsaBasicPageService;
import com.holderzone.member.mall.tool.service.IHsaMallPageService;
import com.holderzone.member.mall.tool.transform.MemberMallToolTransform;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年12月27日 下午5:57
 * @description 链接管理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class LinkManager {

    private IHsaBasicPageService basicPageService;

    private IHsaMallPageService mallPageService;

    // TODO 需要改造(已改)
    private ExternalSupport externalSupport;

    private MemberCommodityFeign commodityFeign;

    /**
     * @return 基础页面列表
     */
    public List<BasicPageTypeRespDTO> listBasicPage() {
        List<HsaBasicPage> pageDOList = basicPageService.list(new LambdaQueryWrapper<HsaBasicPage>()
                .eq(HsaBasicPage::getIsDelete, 0));
        Map<String, List<HsaBasicPage>> pageTypeMap = pageDOList.stream()
                .collect(Collectors.groupingBy(HsaBasicPage::getPageType));
        List<BasicPageTypeRespDTO> respDTOList = new ArrayList<>();
        pageTypeMap.forEach((pageType, pageList) -> {
            BasicPageTypeRespDTO respDTO = new BasicPageTypeRespDTO();
            respDTO.setPageType(pageType);
            PageTypeEnum typeEnum = PageTypeEnum.getEnumByName(pageType);
            if (Objects.isNull(typeEnum)) {
                log.warn("无效页面类型，pageType={}", pageType);
                return;
            }
            respDTO.setPageTypeName(typeEnum.getDes());
            respDTO.setSort(typeEnum.getCode());
            List<BasicPageRespDTO> list = MemberMallToolTransform.INSTANCE.basicPageList2BasicPageDTOList(pageList);
            list.sort(Comparator.comparing(BasicPageRespDTO::getSort));
            respDTO.setBasicPageDTOList(list);
            respDTOList.add(respDTO);
        });
        respDTOList.sort(Comparator.comparing(BasicPageTypeRespDTO::getSort));
        return respDTOList;
    }

    /**
     * 微页面列表
     *
     * @param request 关键字
     * @return 微页面列表
     */
    public List<LinkUniteVO> listMallPage(SingleDataDTO request) {
        request.validate();
        List<HsaMallPage> list = mallPageService.list(new LambdaQueryWrapper<HsaMallPage>()
                .eq(HsaMallPage::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaMallPage::getState, 1)
                .eq(HsaMallPage::getIsDelete, 0)
                .eq(Objects.nonNull(request.getType()), HsaMallPage::getType, request.getType())
                .like(!StringUtils.isEmpty(request.getData()), HsaMallPage::getName, request.getData())
        );
        return MemberMallToolTransform.INSTANCE.mallPageList2LinkUniteVOList(list);
    }

    /**
     * 当前运营主体下启用中的门店
     *
     * @return 店铺主页列表
     */
    public List<LinkUniteVO> listStorePage(SingleDataDTO request) {
        List<StoreInfoVO> storeInfoVOList = getStoreInfoVOS();
        if (CollectionUtils.isEmpty(storeInfoVOList)) {
            return Collections.emptyList();
        }

        // 根据关键字过滤数据
        if (!StringUtils.isEmpty(request.getData())) {
            storeInfoVOList.removeIf(s -> !s.getStore_name().contains(request.getData()));
            if (CollectionUtils.isEmpty(storeInfoVOList)) {
                return Collections.emptyList();
            }
        }

        // 只返回门店信息
        storeInfoVOList.forEach(s -> s.setStall_data(Collections.emptyList()));
        return MemberMallToolTransform.INSTANCE.storeInfoVOList2LinkUniteVOList(storeInfoVOList);
    }

    /**
     * 远程调用获取门店和档口信息
     */
    private List<StoreInfoVO> getStoreInfoVOS() {
        StoreInfoDTO query = new StoreInfoDTO();
        query.setChannel("会员商城");
        query.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
        List<StoreInfoVO> storeStall = externalSupport.storeServer(ThreadLocalCache.getSystem()).getStoreStall(query);
        log.info("[listStorePage]查询主体下的店铺和档口,query={},storeStallResult={}", JacksonUtils.writeValueAsString(query),
                JacksonUtils.writeValueAsString(storeStall));
        return storeStall;
    }

    /**
     * 当前运营主体下启用中的、档口数量≥1个的门店的档口
     *
     * @return 档口列表页列表
     */
    public List<LinkUniteVO> listWindowPage(SingleDataDTO request) {
        List<StoreInfoVO> storeInfoVOList = getStoreInfoVOS();
        if (CollectionUtils.isEmpty(storeInfoVOList)) {
            return Collections.emptyList();
        }
        storeInfoVOList.removeIf(s -> CollectionUtils.isEmpty(s.getStall_data()));

        // 根据关键字过滤数据
        if (!StringUtils.isEmpty(request.getData())) {
            storeInfoVOList.removeIf(s -> !s.getStore_name().contains(request.getData()));
            if (CollectionUtils.isEmpty(storeInfoVOList)) {
                return Collections.emptyList();
            }
        }
        return MemberMallToolTransform.INSTANCE.storeInfoVOList2LinkUniteVOList(storeInfoVOList);
    }

    /**
     * @return 商城分类页列表
     */
    public List<LinkUniteVO> listTypePage(SingleDataDTO request) {
        List<LinkUniteVO> uniteVOList = commodityFeign.listCategory(new CommodityConditionQO());
        // 根据关键字过滤数据
        if (!StringUtils.isEmpty(request.getData())) {
            uniteVOList.removeIf(s -> !s.getName().contains(request.getData()));
        }
        return uniteVOList;
    }

    /**
     * 从crm查询商品信息
     */
    private List<LinkUniteVO> listCategoryByCrm(SingleDataDTO request) {
        QueryProductCategoryCrmQO categoryCrmQO = new QueryProductCategoryCrmQO();
        categoryCrmQO.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
        categoryCrmQO.setSource(true);
        categoryCrmQO.setRank(2);
        List<ProductCrmCategoryVO> dataList = externalSupport.itemServer(ThreadLocalCache.getSystem()).getCommodityCategory(categoryCrmQO);
        log.info("[listTypePage]获取策略单分类接口,categoryCrmQO={},productCategoryResult={}",
                JacksonUtils.writeValueAsString(categoryCrmQO), JacksonUtils.writeValueAsString(dataList));
        if (Objects.isNull(dataList) || CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        // 根据关键字过滤数据
        if (!StringUtils.isEmpty(request.getData())) {
            dataList.removeIf(d -> !d.getCategory_name().contains(request.getData()));
            dataList.forEach(d -> {
                d.getChild_category().removeIf(c -> !c.getCategory_name().contains(request.getData()));
            });
            if (CollectionUtils.isEmpty(dataList)) {
                return Collections.emptyList();
            }
        }

        // 根据分类名称去重
        List<ProductCrmCategoryVO> responseList = filterDuplicateDataByName(dataList);
        return MemberMallToolTransform.INSTANCE.categoryVOList2LinkUniteVOList(responseList);
    }

    /**
     * 根据分类名称去重
     */
    private List<ProductCrmCategoryVO> filterDuplicateDataByName(List<ProductCrmCategoryVO> dataList) {
        List<ProductCrmCategoryVO> responseList = new ArrayList<>();
        Map<String, List<ProductCrmCategoryVO>> categoryNameMap = dataList.stream()
                .collect(Collectors.groupingBy(ProductCrmCategoryVO::getCategory_name));
        categoryNameMap.forEach((name, typeVOList) -> {
            ProductCrmCategoryVO categoryVO = typeVOList.get(0);
            Integer commodityNumber = typeVOList.stream()
                    .map(ProductCrmCategoryVO::getCommodity_number)
                    .reduce(0, Integer::sum);
            categoryVO.setCommodity_number(commodityNumber);
            // 子分类去重
            List<ProductCrmCategoryVO> allChildrenList = typeVOList.stream()
                    .filter(t -> !CollectionUtils.isEmpty(t.getChild_category()))
                    .flatMap(t -> t.getChild_category().stream())
                    .collect(Collectors.toList());
            List<ProductCrmCategoryVO> childrenList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(allChildrenList)) {
                Map<String, List<ProductCrmCategoryVO>> childrenNameMap = allChildrenList.stream()
                        .collect(Collectors.groupingBy(ProductCrmCategoryVO::getCategory_name));
                childrenNameMap.forEach((childrenName, childrenVOList) -> {
                    ProductCrmCategoryVO childrenVO = childrenVOList.get(0);
                    Integer childrenNumber = childrenVOList.stream()
                            .map(ProductCrmCategoryVO::getCommodity_number)
                            .reduce(0, Integer::sum);
                    childrenVO.setCommodity_number(childrenNumber);
                    childrenList.add(childrenVO);
                });
            }
            categoryVO.setChild_category(childrenList);
            responseList.add(categoryVO);
        });
        return responseList;
    }

    /**
     * 查询当前运营主体下关联的所有门店中已关联的【销售管理-微信商城渠道”已发布“的策略单】“已定价”的去重商品数据
     *
     * @return 商品详情列表
     */
    public PageResult pageItemPage(SingleDataDTO request) {
        CommodityConditionQO conditionQO = new CommodityConditionQO();
        conditionQO.setStrategyStatus(2);
        conditionQO.setCommodityState(2);
        conditionQO.setChannel("会员商城");
        conditionQO.setIsDistinct(1);
        // 商品编号，商品名称
        conditionQO.setKeywords(request.getData());
        conditionQO.setType(1);
        conditionQO.setCurrentPage(request.getCurrentPage());
        conditionQO.setPageSize(request.getPageSize());
        PageResult pageResult = commodityFeign.pageCommodity(conditionQO);
        List<CommodityVO> commodityList = JacksonUtils.toObjectList(CommodityVO.class, JacksonUtils.writeValueAsString(pageResult.getRecords()));
        List<LinkUniteVO> list = MemberMallToolTransform.INSTANCE.commodityVOList2LinkUniteVOList(commodityList);
        return pageResult.setRecords(list);
    }

    /**
     * 检查主键
     */
    public CheckKeyVO checkKey() {
        CheckKeyVO vo = new CheckKeyVO();
        SingleDataDTO dto = new SingleDataDTO();
        // 微页面列表key
        List<String> list_mall_page = Lists.newArrayList();
        List<LinkUniteVO> mallPageList = listMallPage(dto);
        if (!CollectionUtils.isEmpty(mallPageList)) {
            list_mall_page = mallPageList.stream()
                    .map(LinkUniteVO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        vo.setList_mall_page(list_mall_page);

        // 店铺主页列表key
        List<String> list_store_page = Lists.newArrayList();
        List<LinkUniteVO> uniteVOList = listStorePage(dto);
        if (!CollectionUtils.isEmpty(uniteVOList)) {
            list_store_page = uniteVOList.stream()
                    .map(LinkUniteVO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        vo.setList_store_page(list_store_page);

        // 档口列表页列表key
        List<String> list_window_page = Lists.newArrayList();
        List<LinkUniteVO> windowPageList = listWindowPage(dto);
        if (!CollectionUtils.isEmpty(windowPageList)) {
            list_window_page = windowPageList.stream()
                    .map(LinkUniteVO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        vo.setList_window_page(list_window_page);

        // 商城分类页列表key
        List<String> list_type_page = Lists.newArrayList();
        List<LinkUniteVO> linkUniteVOList = listTypePage(dto);
        if (!CollectionUtils.isEmpty(linkUniteVOList)) {
            list_type_page = linkUniteVOList.stream()
                    .map(LinkUniteVO::getName)
                    .distinct()
                    .collect(Collectors.toList());
        }
        vo.setList_type_page(list_type_page);

        // 商品详情列表key
        List<String> list_item_page = Lists.newArrayList();
        dto.setPageSize(999);
        dto.setCurrentPage(1);
        PageResult pageResult = pageItemPage(dto);
        List<LinkUniteVO> itemList = JacksonUtils.toObjectList(LinkUniteVO.class, JacksonUtils.writeValueAsString(pageResult.getRecords()));
        if (!CollectionUtils.isEmpty(itemList)) {
            list_item_page = itemList.stream()
                    .map(LinkUniteVO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        vo.setPage_item_page(list_item_page);
        return vo;
    }
}
