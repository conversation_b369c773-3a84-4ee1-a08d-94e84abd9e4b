package com.holderzone.member.mall.tool.entity.wechat;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 收款账户门店适用表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaStoreAccountCredited implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 门店/档口guid
     */
    private String storeGuid;

    /**
     * 门店/档口名称
     */
    private String storeName;

    /**
     * 营业时间
     */
    private String time;

    /**
     * 营业地址
     */
    private String address;

    /**
     * 父guid  档口时存在
     */
    private String parentGuid;

    /**
     * 父名称  档口时存在
     */
    private String parentName;

    /**
     * 收款账户guid
     */
    private String accountCreditedGuid;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 经纬度
     */
    private String addressPoint;

}
