package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.mall.PictureEnum;
import com.holderzone.member.common.qo.mall.DeletePictureQO;
import com.holderzone.member.common.qo.mall.MallPictureQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.mall.MallPictureVO;
import com.holderzone.member.mall.tool.entity.HsaMallPicture;
import com.holderzone.member.mall.tool.mapper.HsaMallPictureMapper;
import com.holderzone.member.mall.tool.service.HsaMallPictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年12月29日 下午5:43
 * @description
 */
@Service
@Slf4j
public class HsaMallPictureServiceImpl extends ServiceImpl<HsaMallPictureMapper, HsaMallPicture> implements HsaMallPictureService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMallPictureMapper hsaMallPictureMapper;

    @Override
    public Boolean saveHsaMallPicture(MallPictureQO request) {
        HsaMallPicture hsaMallPicture = new HsaMallPicture();
        hsaMallPicture.setGuid(guidGeneratorUtil.getStringGuid(HsaMallPicture.class.getSimpleName()));
        hsaMallPicture.setPictureUrl(request.getPictureUrl());
        hsaMallPicture.setOperSubjectGuid(request.getOperSubjectGuid());
        hsaMallPicture.setPictureType(PictureEnum.MY_PICTURE.getCode());
        hsaMallPicture.setIsDelete(BooleanEnum.FALSE.getCode());
        hsaMallPicture.setGmtCreate(LocalDateTime.now());
        hsaMallPicture.setGmtModified(LocalDateTime.now());
        return this.save(hsaMallPicture);
    }

    @Override
    public List<MallPictureVO> getMallPicture(String operSubjectGuid, int pictureType) {

        List<MallPictureVO> mallPictureVOS = new ArrayList<>();
        List<HsaMallPicture> list;
        if (pictureType == PictureEnum.SYSTEM_PICTURE.getCode()) {
            list = hsaMallPictureMapper
                    .selectList(new LambdaQueryWrapper<HsaMallPicture>().eq(HsaMallPicture::getPictureType,pictureType)
                            .orderByDesc(HsaMallPicture::getId));
        } else {
            list = hsaMallPictureMapper
                    .selectList(new LambdaQueryWrapper<HsaMallPicture>()
                            .eq(HsaMallPicture::getPictureType,pictureType)
                            .eq(HsaMallPicture::getOperSubjectGuid, operSubjectGuid)
                            .orderByDesc(HsaMallPicture::getId));
        }
        for (HsaMallPicture hsaMallPicture : list) {
            MallPictureVO mallPictureVO = new MallPictureVO();
            mallPictureVO.setGuid(hsaMallPicture.getGuid());
            mallPictureVO.setPicturePosition(hsaMallPicture.getPicturePosition());
            mallPictureVO.setPictureUrl(hsaMallPicture.getPictureUrl());
            mallPictureVOS.add(mallPictureVO);
        }
        return mallPictureVOS;
    }

    @Override
    public boolean deletePicture(DeletePictureQO request) {

        if (CollectionUtil.isEmpty(request.getGuidList())){
            return false;
        }
        hsaMallPictureMapper.delete(new LambdaQueryWrapper<HsaMallPicture>()
                .eq(HsaMallPicture::getOperSubjectGuid, request.getOperSubjectGuid())
                .in(HsaMallPicture::getGuid, request.getGuidList()));
        return true;
    }
}
