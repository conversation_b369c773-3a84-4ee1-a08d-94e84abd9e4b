package com.holderzone.member.mall.tool.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.qo.tool.MessagesConfigStatusQO;
import com.holderzone.member.common.vo.tool.MessagesConfigVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import com.holderzone.member.mall.tool.service.HsaMessagesConfigService;
import com.holderzone.member.common.util.secret.SecretUtil;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 消息通知配置控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping("/msg")
public class HsaMessagesConfigController {

    @Resource
    private HsaMessagesConfigService hsaMessagesConfigService;


    @ApiOperation("保存消息配置")
    @PostMapping("/saveMessagesConfig")
    void saveMessagesConfig(@RequestBody List<MessagesConfigQO> messagesConfigQO) {
         hsaMessagesConfigService.saveMessagesConfig(messagesConfigQO);
    }

    @ApiOperation("名称查询模板")
    @GetMapping("/getMessagesConfigByName")
    SendMessagesConfigVO getMessagesConfigByName(@RequestParam(value = "operSubjectGuid") String operSubjectGuid,
                                                 @RequestParam(value = "name") String name) {
        return hsaMessagesConfigService.getMessagesConfigByName(operSubjectGuid, name);
    }


    @ApiOperation("修改消息状态")
    @PostMapping("/updateMessagesStatus")
    Result updateMessagesStatus(@RequestBody MessagesConfigStatusQO messagesConfigVO) {
        return Result.success(hsaMessagesConfigService.updateMessagesStatus(messagesConfigVO));
    }


    @ApiOperation("获取消息集合")
    @PostMapping("/getMessagesConfig")
    Result<List<MessagesConfigVO>> getMessagesConfig() {
        return Result.success(hsaMessagesConfigService.getMessagesConfig());
    }

    @ApiOperation("获取消息集合-加密版")
    @PostMapping("/getMessagesConfigBySecret")
    Result<String> getMessagesConfigBySecret() {
        List<MessagesConfigVO> messagesConfig = hsaMessagesConfigService.getMessagesConfig();
        return Result.success(SecretUtil.encrypt(JacksonUtils.writeValueAsString(messagesConfig)));
    }

    /**
     * 手动初始化消息集合
     *
     */
    @ApiOperation("手动初始化消息集合")
    @GetMapping("/initMessagesConfig")
    void initMessagesConfig() {
        hsaMessagesConfigService.initMessagesConfig();
    }

    /**
     * 自动初始化消息集合
     * @param operSubjectGuidList
     */
    @ApiOperation("自动初始化消息集合")
    @PostMapping("/initMessagesBySubjectGuid")
    void initMessagesBySubjectGuid(@RequestBody List<String> operSubjectGuidList){
        hsaMessagesConfigService.initMessagesBySubjectGuid(operSubjectGuidList);
    }
}
