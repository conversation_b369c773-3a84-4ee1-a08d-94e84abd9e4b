package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.feign.MemberMallFeign;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.mall.CommodityPageQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.tool.CategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MallCommodityController
 */
@RestController
@RequestMapping("/mall/commodity")
@ApiModel(description = "商城商品")
public class MallCommodityController {

    @Resource
    private MemberCommodityFeign commodityFeign;

    @Resource
    private MemberMallFeign memberMallFeign;


    /**
     * 查询运营主体下分类
     */
    @PostMapping(value = "/get/commodity/category", produces = "application/json;charset=utf-8")
    Result<List<CategoryVO>> getCommodityCategory() {
        return Result.success(commodityFeign.getCommodityCategory());
    }


    /**
     * 查询商品详情
     */
    @PostMapping(value = "/get/commodity_details", produces = "application/json;charset=utf-8")
    Result<CommodityDetailsVO> getCommodityDetails(@RequestBody CommodityDetailsQO commodityDetailsQO) {
        return Result.success(commodityFeign.getCommodityDetails(commodityDetailsQO));
    }

    @ApiOperation("商品分页列表")
    @PostMapping("/page_commodity")
    public Result<PageResult> pageCommodity(@RequestBody CommodityConditionQO request) {
        request.setStrategyStatus(2);
        request.setCommodityState(2);
        request.setChannel("会员商城");
        request.setIsDistinct(1);
        request.setType(2);
        return Result.success(commodityFeign.pageCommodity(request));
    }

    @ApiOperation("商品列表")
    @PostMapping("/list_commodity")
    public Result<List<CommodityVO>> listCommodity(@RequestBody CommodityConditionQO request) {
        return Result.success(commodityFeign.listCommodity(request));
    }

    @ApiOperation("主体下所有分类")
    @GetMapping("/list_category")
    public Result<List<LinkUniteVO>> listCategory() {
        return Result.success(commodityFeign.listCategory(new CommodityConditionQO()));
    }

    @PostMapping(value = "/get/commodity", produces = "application/json;charset=utf-8")
    public Result findMallBaseProductPage(@RequestBody CommodityPageQO commodityPageQO) {
        return Result.success(commodityFeign.findMallBaseProductPage(commodityPageQO));
    }

    /**
     * 通过运营主体获取商品分享标签格式
     *
     * @return 商品分享格式
     */
    @GetMapping("/get_share_format")
    Result<CommodityShareFormatVO> getShareFormat() {
        return Result.success(memberMallFeign.getShareFormat(ThreadLocalCache.getOperSubjectGuid()).getData());
    }
}
