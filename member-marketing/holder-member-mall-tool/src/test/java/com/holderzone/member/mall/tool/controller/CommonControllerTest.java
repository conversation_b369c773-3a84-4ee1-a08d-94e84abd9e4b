package com.holderzone.member.mall.tool.controller;

import com.google.common.collect.Lists;
import com.holderzone.member.common.dto.crm.CrmBindAppletDTO;
import com.holderzone.member.common.enums.malltool.MsgTypeEnum;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.service.HsaMessagesConfigService;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class CommonControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Resource
    private HsaMessagesConfigService hsaMessagesConfigService;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String COMMON = "/common";

    @Before
    public void setupMockMvc() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    public void listOperSubjectAndApplet() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(COMMON + "/list_oper_subject_and_applet", params, mockMvc);
        System.out.println(content);
    }

    @Test
    public void getBindApplet() {
        CrmBindAppletDTO appletDTO = new CrmBindAppletDTO();
        appletDTO.setPhoneNum("18159021786");
        appletDTO.setCompanyId("2570");
        String content = MockHttpUtil.post(COMMON + "/get_bind_applet_url", appletDTO, mockMvc);
        System.out.println(content);
    }
}