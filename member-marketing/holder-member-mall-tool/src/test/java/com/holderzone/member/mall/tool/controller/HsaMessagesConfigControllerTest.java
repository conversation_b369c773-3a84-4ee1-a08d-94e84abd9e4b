package com.holderzone.member.mall.tool.controller;

import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.qo.tool.MessagesConfigStatusQO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/11/7
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HsaMessagesConfigControllerTest {

    public static final String RESULT = "结果={}";

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String MSG = "/msg";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 初始化消息模版集合
     */
    @Test
    public void initMessagesConfig() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(MSG + "/initMessagesConfig", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("保存消息配置")
    public void saveMessagesConfig() {
        List<MessagesConfigQO> messagesConfigQOList = Lists.newArrayList();

        MessagesConfigQO messagesConfigQO = new MessagesConfigQO();
        messagesConfigQO.setAppletStatus(1);
        messagesConfigQO.setMsgTitle("消息标题");
        messagesConfigQO.setAppletTemplateNo("95463258");
        messagesConfigQO.setAppletTemplateId("45552366875");
        messagesConfigQO.setStatus(1);
        messagesConfigQO.setIsDelete(BooleanEnum.FALSE.getCode());
        messagesConfigQO.setMsgTitle("公众号模板标题");

        messagesConfigQOList.add(messagesConfigQO);
        String content = MockHttpUtil.post(MSG + "/saveMessagesConfig", messagesConfigQOList, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("名称查询模板")
    public void getMessagesConfigByName() {

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("operSubjectGuid", "87");

        params.add("name", "获取公众号模板标题");
        String content = MockHttpUtil.get(MSG + "/getMessagesConfigByName", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("修改消息状态")
    public void updateMessagesStatus() {
        MessagesConfigStatusQO messagesConfigVO = new MessagesConfigStatusQO();
        messagesConfigVO.setOperSubjectGuid("87");
        messagesConfigVO.setStatus(1);
        messagesConfigVO.setAppId("4s5d54as8778x45x45c");
        messagesConfigVO.setAppSecret("zxcxzcqewq787854xc45");
        messagesConfigVO.setTemplateNo("896515");

        String content = MockHttpUtil.post(MSG + "/updateMessagesStatus", messagesConfigVO, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("获取消息集合")
    public void getMessagesConfig() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(MSG + "/getMessagesConfig", params, mockMvc);
        log.info(RESULT, content);
    }
}