package com.holderzone.member.mall.tool.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.malltool.MallPageReqDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.qo.mall.MallPageQO;
import com.holderzone.member.common.qo.tool.PagePopularizeQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.JsonFileUtil;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HsaMallPageControllerTest {

    public static final String RESULT = "结果={}";

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String HSA_MALL_PAGE = "/hsa_mall_page";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    public void saveOrUpdate() {
        MallPageReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("mall/saveOrUpdate.json"), MallPageReqDTO.class);
        String content = MockHttpUtil.post(HSA_MALL_PAGE + "/save_or_update", reqVO, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void get() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("guid", "7014408010365665280");
        String content = MockHttpUtil.get(HSA_MALL_PAGE + "/get", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void deleteBatch() {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setDatas(Collections.singletonList("7014050503541653504"));
        String content = MockHttpUtil.post(HSA_MALL_PAGE + "/delete_batch", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void updateState() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("guid", "7014408010365665280");
        String content = MockHttpUtil.get(HSA_MALL_PAGE + "/update_state", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void queryMallPage() {
        MallPageQO dto = new MallPageQO();
        dto.setOperSubjectGuid("87");
        dto.setKeywords("");
//        dto.setState(-1);
        dto.setCurrentPage(1);
        dto.setPageSize(10);
        String content = MockHttpUtil.post(HSA_MALL_PAGE + "/queryMallPage", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void popularize() {
        PagePopularizeQO dto = new PagePopularizeQO();
        dto.setAppId("wx134d52b853223019");
        dto.setApp_secret("a04c7300636ee44cc25eeb11d3bc9cc7");
        dto.setPageLink("pages/index/index");
        String content = MockHttpUtil.post(HSA_MALL_PAGE + "/popularize", dto, mockMvc);
        log.info(RESULT, content);
    }
}