package com.holderzone.member.mall.tool.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.malltool.BasicPageReqDTO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.JsonFileUtil;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HsaBasicPageControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String HSA_BASIC_PAGE = "/hsa_basic_page";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    public void saveBatch() {
        List<BasicPageReqDTO> reqVOList = JSON.parseArray(JsonFileUtil.read("basic/saveBatch.json"), BasicPageReqDTO.class);
        String content = MockHttpUtil.post(HSA_BASIC_PAGE + "/save_batch", reqVOList, mockMvc);
        log.info("结果={}", content);
    }
}