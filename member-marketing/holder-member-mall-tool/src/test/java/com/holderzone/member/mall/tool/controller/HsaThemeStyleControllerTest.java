package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.ThemeStyleQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HsaThemeStyleControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String HSA_BASIC_PAGE = "/theme";

    public static final String RESULT = "结果={}";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    @ApiOperation("保存主题风格")
    public void saveOrUpdateThemeStyle() {

        ThemeStyleQO request = new ThemeStyleQO();
        request.setColorStyle("蓝色");
        request.setType(1);
        request.setOperSubjectGuid("96");
        String content = MockHttpUtil.post(HSA_BASIC_PAGE + "/saveOrUpdateThemeStyle", request, mockMvc);

        log.info(RESULT, content);
    }
    @Test
    @ApiOperation("查询主题风格")
    public void getThemeStyle() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        params.add("type","1");
        String content = MockHttpUtil.get(HSA_BASIC_PAGE + "/getThemeStyle", params, mockMvc);
        log.info(RESULT, content);
    }
}
