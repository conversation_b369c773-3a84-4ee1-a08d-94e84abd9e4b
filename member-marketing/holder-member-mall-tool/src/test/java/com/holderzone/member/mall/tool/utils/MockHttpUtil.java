package com.holderzone.member.mall.tool.utils;

import com.alibaba.fastjson.JSON;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.MultiValueMap;

/**
 * mock
 */
public class MockHttpUtil {

    private static final String AUTHORIZATION = "token";

    private static final String TOKEN = "8108f6c4-dd74-48e1-8661-25e2f2c2dece";

    private static final String HEADER_OF_OPERSUBJECTGUID = "operSubjectGuid";
    private static final String OPER_SUBJECT_GUID = "96";

    private static final String HEADER_OF_SOURCE = "source";
    private static final String SOURCE = "0";

    public static final String HEADER_OF_USER_INFO = "userInfo";
    public static final String USER_INFO = "{\"enterpriseGuid\":\"2570\",\"multiMemberStatus\":true,\"operSubjectGuid\":\"96\",\"source\":53,\"system\":0}";

    /**
     * get
     */
    public static String get(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder mallToolGetRequest = MockMvcRequestBuilders.get(uri);
            mallToolGetRequest.header(HEADER_OF_OPERSUBJECTGUID, OPER_SUBJECT_GUID);
            mallToolGetRequest.header(AUTHORIZATION, TOKEN);
            mallToolGetRequest.header(HEADER_OF_SOURCE, SOURCE);
            mallToolGetRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            if (params != null) {
                mallToolGetRequest.params(params);
            }
            MvcResult mallToolResult = mockMvc.perform(mallToolGetRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = mallToolResult.getResponse();
            response.setCharacterEncoding("utf-8");
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * post
     */
    public static String post(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder mallToolPostRequest = MockMvcRequestBuilders.post(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            mallToolPostRequest.header(HEADER_OF_OPERSUBJECTGUID, OPER_SUBJECT_GUID);
            mallToolPostRequest.header(AUTHORIZATION, TOKEN);
            mallToolPostRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            mallToolPostRequest.header(HEADER_OF_SOURCE, SOURCE);
            MvcResult mallToolPostResult = mockMvc.perform(mallToolPostRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = mallToolPostResult.getResponse();
            response.setCharacterEncoding("utf-8");
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
