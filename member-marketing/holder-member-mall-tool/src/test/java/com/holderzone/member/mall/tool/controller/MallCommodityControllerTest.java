package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class  MallCommodityControllerTest {

    public static final String RESULT = "结果={}";

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String MALL_COMMODITY = "/mall/commodity";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    public void pageCommodity() {
        CommodityConditionQO dto = new CommodityConditionQO();
        dto.setPageSize(6);
        dto.setCurrentPage(1);
        dto.setKeywords("");
        List<Long> commodityCodeList = new ArrayList<>();
        commodityCodeList.add(16417839000715640L);
        commodityCodeList.add(16417839182320270L);
        dto.setCommodityCodeList(commodityCodeList);
        String content = MockHttpUtil.post(MALL_COMMODITY + "/page_commodity", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listCategory() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(MALL_COMMODITY + "/list_category", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listCommodity() {
        CommodityConditionQO dto = new CommodityConditionQO();
        List<Long> commodityCodeList = new ArrayList<>();
        commodityCodeList.add(16417839390490706L);
        dto.setCommodityCodeList(commodityCodeList);
        String content = MockHttpUtil.post(MALL_COMMODITY + "/list_commodity", dto, mockMvc);
        log.info(RESULT, content);
    }
}