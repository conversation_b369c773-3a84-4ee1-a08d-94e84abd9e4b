package com.holderzone.member.mall.tool.controller.wechat;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.wechat.WechatAccountCreditedQO;
import com.holderzone.member.common.qo.wechat.WechatPayWayQO;
import com.holderzone.member.common.vo.wechat.WechatAccountCreditedVO;
import com.holderzone.member.mall.tool.service.wechat.HsaWechatAccountCreditedService;

import java.util.ArrayList;
import java.util.List;

import com.holderzone.member.mall.tool.service.wechat.HsaWechatPayWayService;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;

@ContextConfiguration(classes = {HsaWechatAccountCreditedController.class})
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class HsaWechatAccountCreditedControllerTest {

    @Autowired
    private HsaWechatAccountCreditedController hsaWechatAccountCreditedController;

    @MockBean
    private HsaWechatAccountCreditedService hsaWechatAccountCreditedService;

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;


    private static final String COMMON = "/wechat/account/credited";


    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * Method under test:
     * {@link HsaWechatAccountCreditedController#saveOrUpdateAccountCredited(WechatAccountCreditedQO)}
     */
    @Test
    public void testSaveOrUpdateAccountCredited() {
        // Arrange
        doNothing().when(hsaWechatAccountCreditedService)
                .saveOrUpdateAccountCredited(Mockito.any());

        WechatAccountCreditedQO wechatAccountCreditedQO = new WechatAccountCreditedQO();
        wechatAccountCreditedQO.setCreditedName("Credited Name");
        wechatAccountCreditedQO.setGuid("1234");
        wechatAccountCreditedQO.setIsDefault(1);
        wechatAccountCreditedQO.setPayCreditedTypeList(new ArrayList<>());
        wechatAccountCreditedQO.setPayMerchantKey("Pay Merchant Key");
        wechatAccountCreditedQO.setPayMerchantNum("Pay Merchant Num");
        wechatAccountCreditedQO.setStoreAccountCreditedQOS(new ArrayList<>());

        String content = MockHttpUtil.post(COMMON + "/wechat/account/credited/save", wechatAccountCreditedQO, mockMvc);

        log.info(content);
    }

    /**
     * Method under test:
     * {@link HsaWechatAccountCreditedController#queryAccountCreditedByGuid(String)}
     */
    @Test
    public void testQueryAccountCreditedByGuid() {
        WechatAccountCreditedVO wechatAccountCreditedVO = getWechatAccountCreditedVO();
        when(hsaWechatAccountCreditedService.queryAccountCreditedByGuid(Mockito.any()))
                .thenReturn(wechatAccountCreditedVO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/wechat/account/credited/get")
                .param("guid", "foo");

        String content = MockHttpUtil.post(COMMON + "/wechat/account/credited/get", wechatAccountCreditedVO, mockMvc);

        log.info(content);
    }

    private static WechatAccountCreditedVO getWechatAccountCreditedVO() {
        WechatAccountCreditedVO wechatAccountCreditedVO = new WechatAccountCreditedVO();
        wechatAccountCreditedVO.setCreditedName("Credited Name");
        wechatAccountCreditedVO.setGuid("1234");
        wechatAccountCreditedVO.setIsCheckCan(1);
        wechatAccountCreditedVO.setIsDefault(1);
        wechatAccountCreditedVO.setPayCreditedTypeList(new ArrayList<>());
        wechatAccountCreditedVO.setPayMerchantKey("Pay Merchant Key");
        wechatAccountCreditedVO.setPayMerchantNum("Pay Merchant Num");
        wechatAccountCreditedVO.setStoreAccountCreditedQOS(new ArrayList<>());
        return wechatAccountCreditedVO;
    }

    /**
     * Method under test:
     * {@link HsaWechatAccountCreditedController#getAccountCreditedPage(PageDTO)}
     */
    @Test
    public void testGetAccountCreditedPage() {
        // Arrange
        when(hsaWechatAccountCreditedService.getAccountCreditedPage(Mockito.any()))
                .thenReturn(new PageResult<>(1, 3, 1));

        // Act and Assert
        PageDTO pageDTO = new PageDTO();
        pageDTO.setCurrentPage(1);
        pageDTO.setPageSize(3);

        String content = MockHttpUtil.post(COMMON + "/wechat/account/credited/get/page", pageDTO, mockMvc);

        log.info(content);
    }

    /**
     * Method under test: {@link HsaWechatAccountCreditedController#delete(String)}
     */
    @Test
    public void testDelete() {
        // Arrange
        when(hsaWechatAccountCreditedService.delete(Mockito.any())).thenReturn(true);

        String content = MockHttpUtil.post(COMMON + "/wechat/account/credited/delete", "delete", mockMvc);

        log.info(content);
    }

    private static final String COMMON_ONE = "/wechat/pay/way";

    public void saveOrUpdateWeChatPayWay() {
        WechatPayWayQO request = new WechatPayWayQO();

        request.setPayWayList(new ArrayList<>());

        String content = MockHttpUtil.post(COMMON_ONE + "/wechat/pay/way/save", request, mockMvc);

        log.info("请求结果：{}", content);
    }

    @ApiOperation("获取配置")
    @GetMapping("/get")
    public void queryByOperSubjectGuid() {

        String content = MockHttpUtil.post(COMMON_ONE + "/wechat/pay/way/get", "", mockMvc);

        log.info("请求结果：{}", content);
    }
}
