package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.qo.mall.MallPageQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class LinkControllerTest {

    public static final String RESULT = "结果={}";

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    private static final String LINK = "/link";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    @Test
    public void listBasicPage() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(LINK + "/list_basic_page", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void pageMallPage() {
        MallPageQO reqVO = new MallPageQO();
        reqVO.setKeywords("俺的");
        String content = MockHttpUtil.post(LINK + "/list_mall_page", reqVO, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listStorePage() {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setData("");
        String content = MockHttpUtil.post(LINK + "/list_store_page", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listWindowPage() {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setData("");
        String content = MockHttpUtil.post(LINK + "/list_window_page", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listTypePage() {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setData("中午");
        String content = MockHttpUtil.post(LINK + "/list_type_page", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void listItemPage() {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setPageSize(10);
        dto.setCurrentPage(1);
        dto.setData("");//曼妥思
        String content = MockHttpUtil.post(LINK + "/page_item_page", dto, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    public void checkKey() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String content = MockHttpUtil.get(LINK + "/check_key", params, mockMvc);
        log.info(RESULT, content);
    }
}