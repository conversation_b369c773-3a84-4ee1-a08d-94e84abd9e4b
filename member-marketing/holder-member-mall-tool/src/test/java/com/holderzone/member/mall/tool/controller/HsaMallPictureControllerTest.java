package com.holderzone.member.mall.tool.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.DeletePictureQO;
import com.holderzone.member.common.qo.mall.MallPictureQO;
import com.holderzone.member.common.qo.mall.ThemeStyleQO;
import com.holderzone.member.mall.tool.HolderSaasMemberMallToolApplication;
import com.holderzone.member.mall.tool.service.HsaAppletNavigationService;
import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasMemberMallToolApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HsaMallPictureControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private static final String COMMON = "/picture";

    public static final String RESULT = "结果={}";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }


    @Test
    @ApiOperation("保存图片")
    public void saveHsaMallPicture() {
        MallPictureQO request = new MallPictureQO();
        request.setOperSubjectGuid("87");
        request.setPictureUrl("url");
        String content = MockHttpUtil.post(COMMON + "/saveHsaMallPicture", request, mockMvc);

        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("查询图片")
    @GetMapping("/getMallPicture")
    public void getMallPicture() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        params.add("operSubjectGuid", "87");
        params.add("pictureType","1");

        String content = MockHttpUtil.get(COMMON + "/getMallPicture", params, mockMvc);
        log.info(RESULT, content);
    }

    @Test
    @ApiOperation("删除图片")
    public void deletePicture() {
        DeletePictureQO request = new DeletePictureQO();

        request.setOperSubjectGuid("87");
        request.setGuidList(Collections.singletonList("7168084868444717056"));

        String content = MockHttpUtil.post(COMMON + "/deletePicture", request, mockMvc);
        log.info(RESULT, content);
    }
}
