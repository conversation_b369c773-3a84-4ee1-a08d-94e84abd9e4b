package com.holderzone.member.mall.tool.controller;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.member.common.qo.mall.NavigationQO;
import com.holderzone.member.mall.tool.service.HsaAppletNavigationService;

import java.util.ArrayList;

import com.holderzone.member.mall.tool.utils.MockHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@ContextConfiguration(classes = {HsaNavigationController.class})
@WebAppConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class HsaNavigationControllerTest {

    @MockBean
    private HsaAppletNavigationService hsaAppletNavigationService;


    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private static final String COMMON = "/navigation";

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * Method under test:
     * {@link HsaNavigationController#saveAppletNavigation(NavigationQO)}
     */
    @Test
    public void testSaveAppletNavigation() {
        // Arrange
        when(hsaAppletNavigationService.saveAppletNavigation(Mockito.any())).thenReturn(true);

        NavigationQO navigationQO = new NavigationQO();
        navigationQO.setAppletNavigationDTOList(new ArrayList<>());
        navigationQO.setType(1);

        String content = MockHttpUtil.post(COMMON + "/saveAppletNavigation", navigationQO, mockMvc);
        log.info("content:{}", content);
    }

    /**
     * Method under test:
     * {@link HsaNavigationController#getAppletNavigation(Integer)}
     */
    @Test
    public void testGetAppletNavigation() {
        // Arrange
        when(hsaAppletNavigationService.getAppletNavigation(Mockito.<Integer>any())).thenReturn(new ArrayList<>());

        String content = MockHttpUtil.post(COMMON + "/getAppletNavigation", 1, mockMvc);
        log.info("content:{}", content);
    }
}
