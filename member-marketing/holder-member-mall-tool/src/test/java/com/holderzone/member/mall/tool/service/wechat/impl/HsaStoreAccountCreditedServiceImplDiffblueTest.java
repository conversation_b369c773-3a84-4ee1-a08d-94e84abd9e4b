package com.holderzone.member.mall.tool.service.wechat.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.member.common.dto.base.QueryStallBasePage;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.base.StallBaseAdapterInfo;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.wechat.StoreAccountCreditedVO;
import com.holderzone.member.mall.tool.mapper.wechat.HsaStoreAccountCreditedMapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {HsaStoreAccountCreditedServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class HsaStoreAccountCreditedServiceImplDiffblueTest {
    @MockBean
    private CrmFeign crmFeign;

    @MockBean
    private HsaStoreAccountCreditedMapper hsaStoreAccountCreditedMapper;

    @Autowired
    private HsaStoreAccountCreditedServiceImpl hsaStoreAccountCreditedServiceImpl;

    /**
     * Method under test:
     * {@link HsaStoreAccountCreditedServiceImpl#queryStore(QueryStoreBasePage)}
     */
    @Test
    public void testQueryStore() {
        // Arrange
        CrmFeignModel<StoreBaseInfo> crmFeignModel = new CrmFeignModel<>();
        crmFeignModel.setData("Data");
        crmFeignModel.setDataList(new ArrayList<>());
        crmFeignModel.setDataList2(new ArrayList<>());
        crmFeignModel.setExceptionMessage("An error occurred");
        crmFeignModel.setReturnCode(1);
        crmFeignModel.setReturnMessage("Return Message");
        when(crmFeign.queryStore(Mockito.<QueryStoreBasePage>any())).thenReturn(crmFeignModel);

        // Act
        List<StoreAccountCreditedVO> actualQueryStoreResult = hsaStoreAccountCreditedServiceImpl
                .queryStore(new QueryStoreBasePage());

        // Assert
        verify(crmFeign).queryStore(Mockito.<QueryStoreBasePage>any());
        assertTrue(actualQueryStoreResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link HsaStoreAccountCreditedServiceImpl#queryStore(QueryStoreBasePage)}
     */
    @Test
    public void testQueryStore2() {
        CrmFeignModel<StoreBaseInfo> crmFeignModel = getInfoCrmFeignModel();

        CrmFeignModel<StallBaseAdapterInfo> crmFeignModel2 = new CrmFeignModel<>();
        crmFeignModel2.setData("Data");
        crmFeignModel2.setDataList(new ArrayList<>());
        crmFeignModel2.setDataList2(new ArrayList<>());
        crmFeignModel2.setExceptionMessage("An error occurred");
        crmFeignModel2.setReturnCode(1);
        crmFeignModel2.setReturnMessage("Return Message");
        when(crmFeign.queryStore(Mockito.<QueryStoreBasePage>any())).thenReturn(crmFeignModel);

        // Act
        List<StoreAccountCreditedVO> actualQueryStoreResult = hsaStoreAccountCreditedServiceImpl
                .queryStore(new QueryStoreBasePage());

        // Assert
        verify(crmFeign).queryStore(Mockito.<QueryStoreBasePage>any());
        assertEquals(1, actualQueryStoreResult.size());
    }

    private static CrmFeignModel<StoreBaseInfo> getInfoCrmFeignModel() {
        StoreBaseInfo storeBaseInfo = getStoreBaseInfo();

        ArrayList<StoreBaseInfo> dataList = new ArrayList<>();
        dataList.add(storeBaseInfo);

        CrmFeignModel<StoreBaseInfo> crmFeignModel = new CrmFeignModel<>();
        crmFeignModel.setData("Data");
        crmFeignModel.setDataList(dataList);
        crmFeignModel.setDataList2(new ArrayList<>());
        crmFeignModel.setExceptionMessage("An error occurred");
        crmFeignModel.setReturnCode(1);
        crmFeignModel.setReturnMessage("Return Message");
        return crmFeignModel;
    }

    private static StoreBaseInfo getStoreBaseInfo() {
        StoreBaseInfo storeBaseInfo = new StoreBaseInfo();
        storeBaseInfo.setAddress("42 Main St One");
        storeBaseInfo.setAddressPoint("42 Main St");
        storeBaseInfo.setBrandId("42");
        storeBaseInfo.setBrandName("brandName");
        storeBaseInfo.setDistance(new BigDecimal("2.3"));
        storeBaseInfo.setId("42");
        storeBaseInfo.setIsFriday(true);
        storeBaseInfo.setIsMonday(true);
        storeBaseInfo.setIsSaturday(true);
        storeBaseInfo.setIsSunday(true);
        storeBaseInfo.setIsThursday(true);
        storeBaseInfo.setIsTuesday(true);
        storeBaseInfo.setIsWednesday(true);
        storeBaseInfo.setOperatingSubjectId("42");
        storeBaseInfo.setOperating_subject_id("Hello from the Dreaming Spires");
        storeBaseInfo.setPhoneNumber("**********");
        storeBaseInfo.setSeriesNum("seriesNum");
        storeBaseInfo.setStallBaseInfoList(new ArrayList<>());
        storeBaseInfo.setStallId("42");
        storeBaseInfo.setStatus("enable");
        storeBaseInfo.setStoreGuid("1234");
        storeBaseInfo.setStoreId("42");
        storeBaseInfo.setTableBasics(new ArrayList<>());
        return storeBaseInfo;
    }

    /**
     * Method under test:
     * {@link HsaStoreAccountCreditedServiceImpl#queryStore(QueryStoreBasePage)}
     */
    @Test
    public void testQueryStore3() {
        CrmFeignModel<StoreBaseInfo> crmFeignModel = getStoreBaseInfoCrmFeignModel();

        CrmFeignModel<StallBaseAdapterInfo> crmFeignModel2 = new CrmFeignModel<>();
        crmFeignModel2.setData("Data");
        crmFeignModel2.setDataList(new ArrayList<>());
        crmFeignModel2.setDataList2(new ArrayList<>());
        crmFeignModel2.setExceptionMessage("An error occurred");
        crmFeignModel2.setReturnCode(1);
        crmFeignModel2.setReturnMessage("Return Message");
        when(crmFeign.queryStore(Mockito.<QueryStoreBasePage>any())).thenReturn(crmFeignModel);

        // Act
        List<StoreAccountCreditedVO> actualQueryStoreResult = hsaStoreAccountCreditedServiceImpl
                .queryStore(new QueryStoreBasePage());

        // Assert
        verify(crmFeign).queryStore(Mockito.<QueryStoreBasePage>any());
        assertEquals(2, actualQueryStoreResult.size());
    }

    private static CrmFeignModel<StoreBaseInfo> getStoreBaseInfoCrmFeignModel() {
        StoreBaseInfo storeBaseInfo = getStoreBaseInfo();

        // Arrange
        StoreBaseInfo storeBaseInfo2 = getStoreBaseInfo();

        ArrayList<StoreBaseInfo> dataList = new ArrayList<>();
        dataList.add(storeBaseInfo2);
        dataList.add(storeBaseInfo);

        CrmFeignModel<StoreBaseInfo> crmFeignModel = new CrmFeignModel<>();
        crmFeignModel.setData("Data");
        crmFeignModel.setDataList(dataList);
        crmFeignModel.setDataList2(new ArrayList<>());
        crmFeignModel.setExceptionMessage("An error occurred");
        crmFeignModel.setReturnCode(1);
        crmFeignModel.setReturnMessage("Return Message");
        return crmFeignModel;
    }
}
